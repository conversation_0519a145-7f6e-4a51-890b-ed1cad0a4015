import {
    mapGptGeneratedSkillsToLinkedInSkillsViaAPI,
    mapGptGeneratedGroupsToLinkedInGroupsViaAPI
} from "./mapKalosDataToLinkedInData";
import { z } from "zod";
import { NonRetriableError } from "inngest";
import { AdvertisingLlmCompletionsService } from "../../../../backend/src/modules/advertising/infrastructure/services/advertisingLlmCompletions.service";
import { PromptExecutionService } from "../../../../backend/src/modules/core/infrastructure/services/promptExecutionService.service";
import { LangfusePromptStorageService } from "../../../../backend/src/modules/core/infrastructure/services/langfusePromptStorage.service";
import { OpenAiCompletionService } from "../../../../backend/src/modules/core/infrastructure/services/openAiCompletion.service";
import { advertisingLangfuseClient } from "../../../../backend/src/modules/advertising/utils/advertisingLangfuseClient";

const generateLinkedInTargetingFromIcpEventBody = z.object({
    organizationId: z.number(),
    segmentId: z.string(),
    campaignId: z.string(),
    positioningKeyPoints: z.string(),
    organizationName: z.string(),
    productCategory: z.string(),
    adTargeting: z.object({
        jobFunction: z.string().nullable().optional(),
        jobSeniority: z.string().nullable().optional(),
        verticals: z.array(z.string()).nullable().optional(),
        annualRevenueLowBound: z.number().nullable().optional(),
        annualRevenueHighBound: z.number().nullable().optional(),
        numberOfEmployeesLowBound: z.number().nullable().optional(),
        numberOfEmployeesHighBound: z.number().nullable().optional(),
    }),
    generatedSkills: z.string().optional(),
    generatedJobTitles: z.string().optional(),
});

export type GenerateLinkedInTargetingFromIcpEventBody = z.infer<
    typeof generateLinkedInTargetingFromIcpEventBody
>;

export type TargetingType = 'skills' | 'groups';

function setupServices() {
    const llmCompletionService = new OpenAiCompletionService();
    const langfusePromptStorageService = new LangfusePromptStorageService(
        advertisingLangfuseClient,
    );
    const promptExecutionService = new PromptExecutionService(
        langfusePromptStorageService,
        llmCompletionService,
    );

    return new AdvertisingLlmCompletionsService(promptExecutionService);
}

function parseGptResponse(response: string, type: TargetingType): string[] {
    let items: string[] = [];

    try {
        const data = JSON.parse(response);
        items = data[type] || data;
    } catch (error) {
        const lines = response.split('\n').filter((line: string) => line.trim());
        items = lines.map((line: string) => line.replace(/^\d+\.\s*/, '').trim()).filter((item: string) => item.length > 0);
    }

    if (items.length === 0) {
        throw new NonRetriableError(`GPT did not generate any ${type}`);
    }

    const MAX_ITEMS = 150;
    if (items.length > MAX_ITEMS) {
        items = items.slice(0, MAX_ITEMS);
    }

    return items;
}

async function generateLinkedInTargeting(
    { step, event }: { step: any; event: any },
    type: TargetingType
) {
    const parsedBody = generateLinkedInTargetingFromIcpEventBody.safeParse(event.data);
    if (!parsedBody.success) {
        throw new NonRetriableError("Invalid event data");
    }
    const body = parsedBody.data;

    const gptStep = step.run(`generate-${type}-with-gpt`, async () => {
        const advertisingLlmService = setupServices();

        const response = type === 'skills'
            ? await advertisingLlmService.generateLinkedInSkillsFromICP({
                positioningKeyPoints: body.positioningKeyPoints,
                organizationName: body.organizationName,
                productCategory: body.productCategory,
                adTargeting: body.adTargeting,
            })
            : await advertisingLlmService.generateLinkedInGroupsFromICP({
                positioningKeyPoints: body.positioningKeyPoints,
                organizationName: body.organizationName,
                productCategory: body.productCategory,
                adTargeting: body.adTargeting,
                generatedSkills: body.generatedSkills,
                generatedJobTitles: body.generatedJobTitles,
            });

        return parseGptResponse(response, type);
    });

    const validatedStep = step.run(`validate-${type}-linkedin`, async () => {
        const gptItems = await gptStep;
        const organizationId = body.organizationId;

        if (type === 'skills') {
            return await mapGptGeneratedSkillsToLinkedInSkillsViaAPI(gptItems, organizationId);
        } else {
            return await mapGptGeneratedGroupsToLinkedInGroupsViaAPI(gptItems, organizationId);
        }
    });

    const processStep = step.run("process-results", async () => {
        const gptItems = await gptStep;
        let validatedItems = await validatedStep;

        const TARGET_COUNT = 50;
        if (validatedItems.length > TARGET_COUNT) {
            validatedItems = validatedItems.slice(0, TARGET_COUNT);
        }

        const successRate = (validatedItems.length / gptItems.length) * 100;

        const resultKey = type === 'skills' ? 'validSkills' : 'validGroups';

        return {
            campaignId: body.campaignId,
            segmentId: body.segmentId,
            [resultKey]: validatedItems,
            totalGenerated: gptItems.length,
            totalValid: validatedItems.length,
            successRate: successRate,
            targetMet: validatedItems.length >= TARGET_COUNT * 0.5,
        };
    });

    return await processStep;
}

export async function generateLinkedInSkillsFromIcpDirect(eventData: GenerateLinkedInTargetingFromIcpEventBody) {
    const mockStep = {
        run: async (stepName: string, fn: () => Promise<any>) => fn()
    };

    const mockEvent = { data: eventData };

    return await generateLinkedInTargeting(
        { step: mockStep, event: mockEvent },
        'skills'
    );
}

export async function generateLinkedInGroupsFromIcpDirect(eventData: GenerateLinkedInTargetingFromIcpEventBody) {
    const mockStep = {
        run: async (stepName: string, fn: () => Promise<any>) => fn()
    };

    const mockEvent = { data: eventData };

    return await generateLinkedInTargeting(
        { step: mockStep, event: mockEvent },
        'groups'
    );
}