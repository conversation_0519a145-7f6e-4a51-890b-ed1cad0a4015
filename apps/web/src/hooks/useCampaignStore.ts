import { useCallback, useMemo } from "react";
import {
  OptimisticRemoval,
  useCampaignCreationStore,
} from "@/stores/campaignCreationStore";

/**
 * Selector hook for audience state
 * Provides optimized access to audience UI state
 */
export const useAudienceState = () =>
  useCampaignCreationStore((state) => state.audienceState);

/**
 * Selector hook for audience actions
 * Provides actions for managing audience UI state
 */
export const useAudienceActions = () => {
  const state = useCampaignCreationStore((state) => state);

  return useMemo(
    () => ({
      toggleCollapse: state.toggleSegmentCollapse,
      addConfirmed: state.addConfirmedSegment,
      setCurrentAudience: state.setCurrentAudience,
      // Optimistic update handlers
      addPendingRemoval: state.addPendingRemoval,
      clearPendingRemovals: state.clearPendingRemovals,
    }),
    [
      state.toggleSegmentCollapse,
      state.addConfirmedSegment,
      state.setCurrentAudience,
      state.addPendingRemoval,
      state.clearPendingRemovals,
    ],
  );
};

/**
 * Selector hook for segments state
 * Provides access to segments UI selections
 */
export const useSegmentsState = () =>
  useCampaignCreationStore((state) => state.segmentsState);

/**
 * Selector hook for segments actions
 * Provides actions for managing segment selections
 */
export const useSegmentsActions = () => {
  const state = useCampaignCreationStore((state) => state);

  return useMemo(
    () => ({
      setSegments: state.setSelectedSegments,
      addSegment: state.addSegment,
      removeSegment: state.removeSegment,
    }),
    [state.setSelectedSegments, state.addSegment, state.removeSegment],
  );
};

/**
 * Selector hook for AB test state
 * Provides access to AB test UI state
 */
export const useABTestState = () =>
  useCampaignCreationStore((state) => state.abTestState);

/**
 * Selector hook for AB test actions
 * Provides actions for managing AB test selections
 */
export const useABTestActions = () => {
  const state = useCampaignCreationStore((state) => state);

  return useMemo(
    () => ({
      setSelectedAds: state.setSelectedAds,
      addSelectedAd: state.addSelectedAd,
      removeSelectedAd: state.removeSelectedAd,
    }),
    [state.setSelectedAds, state.addSelectedAd, state.removeSelectedAd],
  );
};

/**
 * Selector hook for ad copy state
 * Provides access to ad copy form state and tracks unsaved changes
 */
export const useAdCopyState = () =>
  useCampaignCreationStore((state) => state.adCopyState);

/**
 * Selector hook for ad copy actions
 * Provides actions for managing ad copy with unsaved changes tracking
 */
export const useAdCopyActions = () => {
  const state = useCampaignCreationStore((state) => state);

  return useMemo(
    () => ({
      setAdCopy: state.setAdCopy,
      updateAdCopy: state.updateAdCopy,
      setUnsavedChanges: state.setUnsavedChanges,
    }),
    [state.setAdCopy, state.updateAdCopy, state.setUnsavedChanges],
  );
};

/**
 * Selector hook for assets state
 * Provides access to selected assets
 */
export const useAssetsState = () =>
  useCampaignCreationStore((state) => state.assetsState);

/**
 * Selector hook for assets actions
 * Provides actions for managing asset selections
 */
export const useAssetsActions = () => {
  const state = useCampaignCreationStore((state) => state);

  return useMemo(
    () => ({
      setAssets: state.setSelectedAssets,
      addAsset: state.addAsset,
      removeAsset: state.removeAsset,
    }),
    [state.setSelectedAssets, state.addAsset, state.removeAsset],
  );
};

/**
 * Selector hook for preview state
 * Provides access to preview settings like URLs and budgets
 */
export const usePreviewState = () =>
  useCampaignCreationStore((state) => state.previewState);

/**
 * Selector hook for preview actions
 * Provides actions for managing preview settings
 */
export const usePreviewActions = () => {
  const state = useCampaignCreationStore((state) => state);

  return useMemo(
    () => ({
      setDestinationUrls: state.setDestinationUrls,
      updateDestinationUrl: state.updateDestinationUrl,
      setSegmentBudgets: state.setSegmentBudgets,
      updateSegmentBudget: state.updateSegmentBudget,
      setSender: state.setSender,
    }),
    [
      state.setDestinationUrls,
      state.updateDestinationUrl,
      state.setSegmentBudgets,
      state.updateSegmentBudget,
      state.setSender,
    ],
  );
};

/**
 * Selector hook for global error states
 * Provides access to centralized error handling
 */
export const useErrorStates = () =>
  useCampaignCreationStore((state) => state.errorStates);

/**
 * Selector hook for global actions
 * Provides actions for managing global app state
 */
export const useGlobalActions = () => {
  const state = useCampaignCreationStore((state) => state);

  return useMemo(
    () => ({
      setAdAccount: state.setAdAccount,
      setCampaignGroupId: state.setCampaignGroupId,
      setCampaignName: state.setCampaignName,
      resetState: state.resetState,
      setError: state.setError,
      clearErrors: state.clearErrors,
    }),
    [
      state.setAdAccount,
      state.setCampaignGroupId,
      state.setCampaignName,
      state.resetState,
      state.setError,
      state.clearErrors,
    ],
  );
};

/**
 * Custom hook that integrates error handling with tRPC errors
 * Provides consistent error handling across the application
 */
export const useCampaignErrorHandler = () => {
  const setError = useCampaignCreationStore((state) => state.setError);

  const handleTRPCError = useCallback(
    (step: keyof ReturnType<typeof useErrorStates>, error: any) => {
      let errorMessage = "An unexpected error occurred";

      if (error?.message) {
        errorMessage = error.message;
      } else if (error?.shape?.message) {
        errorMessage = error.shape.message;
      }

      setError(step, errorMessage);
    },
    [setError],
  );

  const clearError = useCallback(
    (step: keyof ReturnType<typeof useErrorStates>) => {
      setError(step, null);
    },
    [setError],
  );

  return useMemo(
    () => ({
      handleTRPCError,
      clearError,
    }),
    [handleTRPCError, clearError],
  );
};

/**
 * Custom hook for optimistic UI updates
 * Provides utilities to work with optimistic updates for audience entities
 * @param audienceId The ID of the audience to manage optimistic updates for
 */
export const useOptimisticAudienceUpdates = (audienceId: string) => {
  const pendingRemovals = useCampaignCreationStore(
    (state) =>
      state.audienceState.optimisticUpdates.pendingRemovals[audienceId] || [],
  );
  const { addPendingRemoval, clearPendingRemovals } = useAudienceActions();

  const addOptimisticRemoval = useCallback(
    (entityUrn: string, facetUrn: string, mode: "INCLUDE" | "EXCLUDE") =>
      addPendingRemoval(audienceId, entityUrn, facetUrn, mode),
    [audienceId, addPendingRemoval],
  );

  const clearOptimisticUpdates = useCallback(
    () => clearPendingRemovals(audienceId),
    [audienceId, clearPendingRemovals],
  );

  const isPendingRemoval = useCallback(
    (entityUrn: string) =>
      pendingRemovals.some((removal) => removal.entityUrn === entityUrn),
    [pendingRemovals],
  );

  return useMemo(
    () => ({
      pendingRemovals,
      addOptimisticRemoval,
      clearOptimisticUpdates,
      isPendingRemoval,
    }),
    [
      pendingRemovals,
      addOptimisticRemoval,
      clearOptimisticUpdates,
      isPendingRemoval,
    ],
  );
};
