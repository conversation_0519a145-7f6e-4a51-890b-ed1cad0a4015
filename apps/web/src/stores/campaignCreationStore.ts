import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

/**
 * Type definitions for UI state
 */

// Types for AB test page
export interface SelectedAd {
  campaignGroupSegmentId: string;
  adTopic: string;
}

// Types for ad copy
export interface AdCopy {
  adId: string;
  headline: string;
  introductoryText: string;
  description: string;
}

// Types for destination URLs in preview
export interface DestinationUrl {
  adSegmentId: string;
  url: string;
}

// For optimistic updates in audience entities
export interface OptimisticRemoval {
  entityUrn: string;
  facetUrn: string;
  mode: "INCLUDE" | "EXCLUDE";
}

export interface CampaignCreationState {
  // Shared data - Route parameters
  adAccount: string;
  campaignGroupId: string;
  campaignName: string;

  // Audience page state - only UI state, no server data
  audienceState: {
    collapsedSegments: string[];
    confirmedSegments: string[];
    currentAudienceIds: Record<string, string | null>;
    // For optimistic updates
    optimisticUpdates: {
      pendingRemovals: Record<string, OptimisticRemoval[]>; // audienceId -> pending removals
    };
  };

  // AB Test page state
  abTestState: {
    selectedAds: SelectedAd[];
  };

  // Ad Copy page state
  adCopyState: {
    adCopy: AdCopy[];
    unsavedChanges: boolean; // Track unsaved changes to prevent reload
  };

  // Segments page state
  segmentsState: {
    selectedSegments: string[];
  };

  // Assets/Creative page state
  assetsState: {
    selectedAssets: string[];
    isLoading: boolean;
  };

  // Preview page state
  previewState: {
    destinationUrls: DestinationUrl[];
    segmentBudgets: Record<string, number>;
    sender: string;
  };

  // Error states
  errorStates: {
    segments: string | null;
    audience: string | null;
    abTest: string | null;
    adCopy: string | null;
    assets: string | null;
    preview: string | null;
  };

  // Actions - Shared
  setAdAccount: (adAccount: string) => void;
  setCampaignGroupId: (id: string) => void;
  setCampaignName: (name: string) => void;
  resetState: () => void;

  // Actions - Audience UI State
  setCollapsedSegments: (segmentIds: string[]) => void;
  toggleSegmentCollapse: (segmentId: string) => void;
  addConfirmedSegment: (segmentId: string) => void;
  setCurrentAudience: (segmentId: string, audienceId: string | null) => void;

  // Actions - Optimistic Updates for Audience
  addPendingRemoval: (
    audienceId: string,
    entityUrn: string,
    facetUrn: string,
    mode: "INCLUDE" | "EXCLUDE",
  ) => void;
  clearPendingRemovals: (audienceId: string) => void;

  // Actions - AB Test
  setSelectedAds: (ads: SelectedAd[]) => void;
  addSelectedAd: (ad: SelectedAd) => void;
  removeSelectedAd: (campaignGroupSegmentId: string, adTopic: string) => void;

  // Actions - Ad Copy
  setAdCopy: (adCopy: AdCopy[]) => void;
  updateAdCopy: (adId: string, updates: Partial<Omit<AdCopy, "adId">>) => void;
  setUnsavedChanges: (hasChanges: boolean) => void;

  // Actions - Segments
  setSelectedSegments: (segments: string[]) => void;
  addSegment: (segmentId: string) => void;
  removeSegment: (segmentId: string) => void;

  // Actions - Assets
  setSelectedAssets: (assets: string[]) => void;
  addAsset: (assetId: string) => void;
  removeAsset: (assetId: string) => void;
  setAssetsLoading: (isLoading: boolean) => void;

  // Actions - Preview
  setDestinationUrls: (urls: DestinationUrl[]) => void;
  updateDestinationUrl: (adSegmentId: string, url: string) => void;
  setSegmentBudgets: (budgets: Record<string, number>) => void;
  updateSegmentBudget: (segmentId: string, budget: number) => void;
  setSender: (sender: string) => void;

  // Actions - Error States
  setError: (
    step: keyof CampaignCreationState["errorStates"],
    error: string | null,
  ) => void;
  clearErrors: () => void;
}

/**
 * Default state for the campaign creation store
 */
const initialState: Omit<
  CampaignCreationState,
  | "setAdAccount"
  | "setCampaignGroupId"
  | "setCampaignName"
  | "resetState"
  | "setCollapsedSegments"
  | "toggleSegmentCollapse"
  | "addConfirmedSegment"
  | "setCurrentAudience"
  | "addPendingRemoval"
  | "clearPendingRemovals"
  | "setSelectedAds"
  | "addSelectedAd"
  | "removeSelectedAd"
  | "setAdCopy"
  | "updateAdCopy"
  | "setUnsavedChanges"
  | "setSelectedSegments"
  | "addSegment"
  | "removeSegment"
  | "setSelectedAssets"
  | "addAsset"
  | "removeAsset"
  | "setAssetsLoading"
  | "setDestinationUrls"
  | "updateDestinationUrl"
  | "setSegmentBudgets"
  | "updateSegmentBudget"
  | "setSender"
  | "setError"
  | "clearErrors"
> = {
  adAccount: "",
  campaignGroupId: "",
  campaignName: "",

  audienceState: {
    collapsedSegments: [],
    confirmedSegments: [],
    currentAudienceIds: {},
    optimisticUpdates: {
      pendingRemovals: {},
    },
  },

  abTestState: {
    selectedAds: [],
  },

  adCopyState: {
    adCopy: [],
    unsavedChanges: false,
  },

  segmentsState: {
    selectedSegments: [],
  },

  assetsState: {
    selectedAssets: [],
    isLoading: false,
  },

  previewState: {
    destinationUrls: [],
    segmentBudgets: {},
    sender: "",
  },

  errorStates: {
    segments: null,
    audience: null,
    abTest: null,
    adCopy: null,
    assets: null,
    preview: null,
  },
};

/**
 * Create the Zustand store with devtools and persist middleware
 */
export const useCampaignCreationStore = create<CampaignCreationState>()(
  devtools(
    persist(
      (set) => ({
        ...initialState,

        // Shared actions
        setAdAccount: (adAccount) => set({ adAccount }),
        setCampaignGroupId: (campaignGroupId) => set({ campaignGroupId }),
        setCampaignName: (campaignName) => set({ campaignName }),
        resetState: () => set(initialState),

        // Audience UI state actions
        setCollapsedSegments: (collapsedSegments) =>
          set((state) => ({
            audienceState: { ...state.audienceState, collapsedSegments },
          })),
        toggleSegmentCollapse: (segmentId) =>
          set((state) => {
            const collapsedSegments = state.audienceState.collapsedSegments
              ? [...state.audienceState.collapsedSegments]
              : [];
            const index = collapsedSegments.indexOf(segmentId);

            if (index > -1) {
              collapsedSegments.splice(index, 1);
            } else {
              collapsedSegments.push(segmentId);
            }

            return {
              audienceState: {
                ...state.audienceState,
                collapsedSegments,
              },
            };
          }),
        addConfirmedSegment: (segmentId) =>
          set((state) => {
            if (state.audienceState.confirmedSegments.includes(segmentId)) {
              return state;
            }
            return {
              audienceState: {
                ...state.audienceState,
                confirmedSegments: [
                  ...state.audienceState.confirmedSegments,
                  segmentId,
                ],
              },
            };
          }),
        setCurrentAudience: (segmentId, audienceId) =>
          set((state) => ({
            audienceState: {
              ...state.audienceState,
              currentAudienceIds: {
                ...state.audienceState.currentAudienceIds,
                [segmentId]: audienceId,
              },
            },
          })),

        // Optimistic updates for audience entities
        addPendingRemoval: (audienceId, entityUrn, facetUrn, mode) =>
          set((state) => {
            const pendingRemovals =
              state.audienceState.optimisticUpdates.pendingRemovals[
                audienceId
              ] || [];
            return {
              audienceState: {
                ...state.audienceState,
                optimisticUpdates: {
                  ...state.audienceState.optimisticUpdates,
                  pendingRemovals: {
                    ...state.audienceState.optimisticUpdates.pendingRemovals,
                    [audienceId]: [
                      ...pendingRemovals,
                      { entityUrn, facetUrn, mode },
                    ],
                  },
                },
              },
            };
          }),
        clearPendingRemovals: (audienceId) =>
          set((state) => {
            const newPendingRemovals = {
              ...state.audienceState.optimisticUpdates.pendingRemovals,
            };
            delete newPendingRemovals[audienceId];
            return {
              audienceState: {
                ...state.audienceState,
                optimisticUpdates: {
                  ...state.audienceState.optimisticUpdates,
                  pendingRemovals: newPendingRemovals,
                },
              },
            };
          }),

        // AB Test actions
        setSelectedAds: (selectedAds) =>
          set((state) => ({
            abTestState: {
              ...state.abTestState,
              selectedAds,
            },
          })),
        addSelectedAd: (ad) =>
          set((state) => ({
            abTestState: {
              ...state.abTestState,
              selectedAds: [...state.abTestState.selectedAds, ad],
            },
          })),
        removeSelectedAd: (campaignGroupSegmentId, adTopic) =>
          set((state) => ({
            abTestState: {
              ...state.abTestState,
              selectedAds: state.abTestState.selectedAds.filter(
                (ad) =>
                  !(
                    ad.campaignGroupSegmentId === campaignGroupSegmentId &&
                    ad.adTopic === adTopic
                  ),
              ),
            },
          })),

        // Ad Copy actions
        setAdCopy: (adCopy) =>
          set((state) => ({
            adCopyState: {
              ...state.adCopyState,
              adCopy,
              unsavedChanges: false, // Reset unsaved changes when setting new data
            },
          })),
        updateAdCopy: (adId, updates) =>
          set((state) => {
            const adCopy = [...state.adCopyState.adCopy];
            const index = adCopy.findIndex((ad) => ad.adId === adId);

            if (index > -1) {
              adCopy[index] = { ...adCopy[index], ...updates } as AdCopy;
            }

            return {
              adCopyState: {
                ...state.adCopyState,
                adCopy,
                unsavedChanges: true, // Mark changes as unsaved
              },
            };
          }),
        setUnsavedChanges: (hasChanges) =>
          set((state) => ({
            adCopyState: {
              ...state.adCopyState,
              unsavedChanges: hasChanges,
            },
          })),

        // Segments actions
        setSelectedSegments: (selectedSegments) =>
          set((state) => ({
            segmentsState: {
              ...state.segmentsState,
              selectedSegments,
            },
          })),
        addSegment: (segmentId) =>
          set((state) => ({
            segmentsState: {
              ...state.segmentsState,
              selectedSegments: [
                ...state.segmentsState.selectedSegments,
                segmentId,
              ],
            },
          })),
        removeSegment: (segmentId) =>
          set((state) => ({
            segmentsState: {
              ...state.segmentsState,
              selectedSegments: state.segmentsState.selectedSegments.filter(
                (id) => id !== segmentId,
              ),
            },
          })),

        // Assets actions
        setSelectedAssets: (selectedAssets) =>
          set((state) => ({
            assetsState: {
              ...state.assetsState,
              selectedAssets,
            },
          })),
        addAsset: (assetId) =>
          set((state) => ({
            assetsState: {
              ...state.assetsState,
              selectedAssets: [...state.assetsState.selectedAssets, assetId],
            },
          })),
        removeAsset: (assetId) =>
          set((state) => ({
            assetsState: {
              ...state.assetsState,
              selectedAssets: state.assetsState.selectedAssets.filter(
                (id) => id !== assetId,
              ),
            },
          })),
        setAssetsLoading: (isLoading) =>
          set((state) => ({
            assetsState: {
              ...state.assetsState,
              isLoading,
            },
          })),

        // Preview actions
        setDestinationUrls: (destinationUrls) =>
          set((state) => ({
            previewState: {
              ...state.previewState,
              destinationUrls,
            },
          })),
        updateDestinationUrl: (adSegmentId, url) =>
          set((state) => {
            const destinationUrls = [...state.previewState.destinationUrls];
            const index = destinationUrls.findIndex(
              (item) => item.adSegmentId === adSegmentId,
            );

            if (index > -1 && destinationUrls[index]) {
              destinationUrls[index] = {
                ...destinationUrls[index],
                url,
                adSegmentId: destinationUrls[index].adSegmentId,
              };
            } else {
              // Only add if adSegmentId is defined
              const newDestinationUrl: DestinationUrl = { adSegmentId, url };
              destinationUrls.push(newDestinationUrl);
            }

            return {
              previewState: {
                ...state.previewState,
                destinationUrls,
              },
            };
          }),
        setSegmentBudgets: (segmentBudgets) =>
          set((state) => ({
            previewState: {
              ...state.previewState,
              segmentBudgets,
            },
          })),
        updateSegmentBudget: (segmentId, budget) =>
          set((state) => ({
            previewState: {
              ...state.previewState,
              segmentBudgets: {
                ...state.previewState.segmentBudgets,
                [segmentId]: budget,
              },
            },
          })),
        setSender: (sender) =>
          set((state) => ({
            previewState: {
              ...state.previewState,
              sender,
            },
          })),

        // Error state actions
        setError: (step, error) =>
          set((state) => ({
            errorStates: {
              ...state.errorStates,
              [step]: error,
            },
          })),

        clearErrors: () =>
          set({
            errorStates: {
              segments: null,
              audience: null,
              abTest: null,
              adCopy: null,
              assets: null,
              preview: null,
            },
          }),
      }),
      {
        name: "campaign-creation-storage",
        // Only persist essential data
        partialize: (state) => ({
          adAccount: state.adAccount,
          campaignGroupId: state.campaignGroupId,
          campaignName: state.campaignName,
          audienceState: {
            confirmedSegments: state.audienceState.confirmedSegments,
            currentAudienceIds: state.audienceState.currentAudienceIds,
          },
          abTestState: {
            selectedAds: state.abTestState.selectedAds,
          },
          adCopyState: {
            adCopy: state.adCopyState.adCopy,
          },
          segmentsState: {
            selectedSegments: state.segmentsState.selectedSegments,
          },
          assetsState: {
            selectedAssets: state.assetsState.selectedAssets,
          },
          previewState: {
            destinationUrls: state.previewState.destinationUrls,
            segmentBudgets: state.previewState.segmentBudgets,
            sender: state.previewState.sender,
          },
        }),
      },
    ),
  ),
);
