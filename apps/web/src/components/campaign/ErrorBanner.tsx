"use client";

import { useEffect, useState } from "react";
import { X } from "lucide-react";

import { Alert, AlertDescription, AlertTitle } from "@kalos/ui/alert";
import { cn } from "@kalos/ui/index";

interface ErrorBannerProps {
  error: string | null;
  className?: string;
  onDismiss?: () => void;
}

export function ErrorBanner({ error, className, onDismiss }: ErrorBannerProps) {
  const [visible, setVisible] = useState(false);

  // Show the error when it changes to a non-null value
  useEffect(() => {
    if (error) {
      setVisible(true);
    } else {
      setVisible(false);
    }
  }, [error]);

  if (!error) return null;

  return (
    <Alert 
      variant="destructive" 
      className={cn(
        "transition-all duration-300", 
        visible ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-4",
        className
      )}
    >
      <div className="flex items-start justify-between w-full">
        <div>
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </div>
        {onDismiss && (
          <button 
            onClick={() => {
              setVisible(false);
              setTimeout(() => {
                onDismiss();
              }, 300);
            }}
            className="p-1 hover:bg-red-100 rounded-full"
            aria-label="Dismiss error"
          >
            <X size={16} />
          </button>
        )}
      </div>
    </Alert>
  );
}
