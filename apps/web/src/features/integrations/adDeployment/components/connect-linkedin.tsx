import { useEffect } from "react";
import { api } from "@/trpc/client";
import { useAuth } from "@clerk/nextjs";
import { CheckIcon } from "lucide-react";

import { Button } from "@kalos/ui/button";
import { cn } from "@kalos/ui/index";

export function ConnectLinkedin() {
  // Use v2 router for LinkedIn connection check
  const { userId } = useAuth();

  if (!userId) {
    return <div>Not signed in</div>;
  }

  // Access the organization router with proper type casting
  const linkedinCredentialsQuery =
    api.organization.integrations.doesOrganizationHaveLinkedinConnection.useQuery();

  const handleSignIn = () => {
    const LINKEDIN_URL = getURLWithQueryParams(
      "https://www.linkedin.com/oauth/v2/authorization",
      {
        response_type: "code",
        client_id: process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID,
        state: `${userId}`,
        redirect_uri: `${getBackendUrl()}/api/oauth2/linkedin/callback`,
        scope:
          "rw_ads w_organization_social r_organization_social w_member_social r_ads r_ads_reporting r_marketing_leadgen_automation",
      },
    );
    window.location.href = LINKEDIN_URL;
  };

  useEffect(() => {}, [linkedinCredentialsQuery.data]);

  return (
    <div>
      {!linkedinCredentialsQuery.data?.doesOrganizationHaveLinkedinConnection &&
        !linkedinCredentialsQuery.isLoading && (
          <Button
            variant="outline"
            className="h-16 w-full justify-start"
            onClick={handleSignIn}
          >
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/e/e8/Linkedin-logo-blue-In-square-40px.png"
              alt="new"
              className="mr-2 h-10 w-10"
            />
            LinkedIn
          </Button>
        )}

      {linkedinCredentialsQuery.data?.doesOrganizationHaveLinkedinConnection &&
        linkedinCredentialsQuery.isLoading && (
          <Button
            variant="outline"
            className="h-16 w-full justify-start"
            disabled
          >
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/e/e8/Linkedin-logo-blue-In-square-40px.png"
              alt="new"
              className="mr-2 h-10 w-10"
            />
            LinkedIn
          </Button>
        )}

      {linkedinCredentialsQuery.data?.doesOrganizationHaveLinkedinConnection &&
        !linkedinCredentialsQuery.isLoading && (
          <Button
            variant="outline"
            className={cn(
              linkedinCredentialsQuery.data
                ?.doesOrganizationHaveLinkedinConnection
                ? "disabled:bg-green-100 disabled:opacity-100"
                : "",
              "h-16 w-full justify-start",
            )}
            disabled
          >
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/e/e8/Linkedin-logo-blue-In-square-40px.png"
              alt="new"
              className="mr-2 h-10 w-10"
            />
            LinkedIn
            <div className="flex w-full items-center justify-end">
              <CheckIcon />
            </div>
          </Button>
        )}
    </div>
  );
}
function getBackendUrl() {
  // "https://url"
  const baseUrl = process.env.NEXT_PUBLIC_BACKEND_URL;

  return baseUrl;
}

const getURLWithQueryParams = (base: string, params: Object) => {
  const query = Object.entries(params)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join("&");

  return `${base}?${query}`;
};
