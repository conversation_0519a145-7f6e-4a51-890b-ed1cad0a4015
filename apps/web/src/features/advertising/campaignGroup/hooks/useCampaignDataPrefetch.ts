import { useEffect } from "react";
import { api } from "@/trpc/client";

/**
 * Hook to prefetch all data needed for campaign group creation in parallel
 * @param adAccountId - The ID of the ad account
 */
export function useCampaignDataPrefetch(adAccountId: string) {
  const apiUtils = api.useUtils();

  useEffect(() => {
    if (!adAccountId) return;

    // Create a batch of prefetch operations
    const prefetchPromises = [
      // Prefetch lead gen forms
      apiUtils.v2.ads.linkedInApi.leadGenForms.getForAccount.prefetch({
        adAccountId,
      }),

      // Prefetch ad accounts
      apiUtils.v2.ads.adAccounts.getForOrganization.prefetch(),

      // We can't directly warm up mutations, but we can ensure
      // all dependencies are loaded for faster response

      // Add any other data needed for the campaign creation process
      // that would otherwise be fetched later
    ];

    // Execute all prefetch operations in parallel
    Promise.all(prefetchPromises).catch((error) => {
      console.error("Error prefetching campaign data:", error);
    });
  }, [adAccountId, apiUtils]);

  return null;
}
