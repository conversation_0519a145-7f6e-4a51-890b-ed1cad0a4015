import { stat } from "fs";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { z } from "zod";

import { campaignHandlers } from "@kalos/database/handlers/campaign";
import { campaignGroupHandlers } from "@kalos/database/handlers/campaignGroup";
import { campaignGroupSegmentHandlers } from "@kalos/database/handlers/campaignGroupSegment";
import { linkedInAdAccountHandlers } from "@kalos/database/handlers/linkedInAdAccount";
import { s } from "@kalos/database/handlers/opportunity";
import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

const schema = z.object({
  organizationId: z.number(),
  campaignGroupSegmentId: z.string(),
  stage: z.enum(["COMPLETED", "RUNNING"]).optional(),
  endDateEpochMs: z.number().optional(),
  budget: z.number().optional(),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const bodyParseRes = schema.safeParse(body);

    if (!bodyParseRes.success) {
      return NextResponse.json(
        { error: "Invalid request body" },
        { status: 400 },
      );
    }

    const {
      organizationId,
      campaignGroupSegmentId,
      stage,
      endDateEpochMs,
      budget,
    } = bodyParseRes.data;
    console.log(
      "updating campaign group segment",
      campaignGroupSegmentId,
      "stage",
      stage,
      "endDateEpochMs",
      endDateEpochMs,
      "budget",
      budget,
    );

    const campaignGroupSegment =
      await campaignGroupSegmentHandlers.select.one.byId(
        campaignGroupSegmentId,
      );
    if (!campaignGroupSegment) {
      return NextResponse.json(
        { error: "Campaign group segment not found" },
        { status: 404 },
      );
    }
    // For below operations, we update linkedin side first, then update our database
    const linkedInClient =
      await getLinkedInApiClientFromOrganizationId(organizationId);
    if (!linkedInClient) {
      return NextResponse.json(
        { error: "LinkedIn client not found" },
        { status: 404 },
      );
    }

    const linkedInAds =
      await campaignGroupSegmentHandlers.select.many.linkedInAdByDateRangeByCampaignGroupSegmentId(
        campaignGroupSegmentId,
      );
    // Get all the campaigns, ads, and stats in this campaign group segment
    const creativeLinkedInUrns = [
      ...new Set(
        linkedInAds
          .map((ad) => {
            const urn = ad.creativeLinkedInUrnWithPrefix;
            if (!urn) return urn;
            const parts = urn.split(":");
            return parts[parts.length - 1];
          })
          .filter((urn): urn is string => urn !== null),
      ),
    ];
    console.log("creativeLinkedInUrns", creativeLinkedInUrns);

    const campaignGroup =
      await campaignGroupHandlers.select.oneByCampaignGroupSegmentId(
        campaignGroupSegmentId,
      );
    if (!campaignGroup) {
      return NextResponse.json(
        { error: "Campaign group not found" },
        { status: 404 },
      );
    }
    const campaignGroupOriginalEndDate = campaignGroup.endDate;

    // // Hack, hand fix the start and end date
    // const campaignGroupWithFixedDates = {
    //   ...campaignGroup,
    //   startDate: new Date("2024-01-01").toISOString().split("T")[0],
    //   endDate: new Date("2024-01-01").toISOString().split("T")[0],
    // };
    // console.log("campaignGroup", campaignGroupWithFixedDates);

    const campaignGroupSegmentDetails =
      await campaignGroupSegmentHandlers.select.one.campaignGroupSegmentDetails(
        campaignGroupSegmentId,
      );
    console.log("campaignGroupSegmentDetails", campaignGroupSegmentDetails);

    const dataForCreatives = (
      (await linkedInClient.getAnalyticsForCreatives(
        creativeLinkedInUrns,
        new Date("2024-01-01"),
      )) as any
    ).elements;
    const analyticsForCreatives = linkedInAds.map((creative) => {
      const analyticsForCreative = dataForCreatives.find((analytics: any) =>
        analytics.pivotValues.includes(
          `urn:li:sponsoredCreative:${creative.creativeLinkedInUrnWithPrefix?.split(":").pop()}`,
        ),
      );

      return {
        ...creative,
        clicks: Number(analyticsForCreative?.clicks ?? 0),
        impressions: Number(analyticsForCreative?.impressions ?? 0),
        costInLocalCurrency: Number(
          analyticsForCreative?.costInLocalCurrency ?? 0,
        ),
        costInUsd: Number(analyticsForCreative?.costInUsd ?? 0),
      };
    });

    const linkedInAdAccount =
      await linkedInAdAccountHandlers.select.one.byOrganizationId(
        organizationId,
      );

    if (!linkedInAdAccount?.[0]?.linkedInUrn) {
      return NextResponse.json(
        { error: "LinkedIn ad account not found" },
        { status: 404 },
      );
    }

    const adAccountLinkedInUrn = linkedInAdAccount[0].linkedInUrn.toString();

    const campaignLinkedInUrns = [
      ...new Set(
        linkedInAds
          .filter((ad) => ad.campaignLinkedInUrn !== null)
          .map((ad) => ad.campaignLinkedInUrn!),
      ),
    ];
    if (endDateEpochMs) {
      // For endDate, update all the campaigns in this campaign group segment

      // Update end date for each campaign
      const campaigns = await linkedInClient.getCampaigns(
        Number(adAccountLinkedInUrn),
        campaignLinkedInUrns.map((urn) => Number(urn)),
      );
      for (const campaignUrn of campaignLinkedInUrns) {
        const campaignStatus = campaigns.results[campaignUrn]?.["status"];
        const campaignStartDateEpochMs =
          campaigns.results[campaignUrn]?.["runSchedule"]?.["start"] ?? 0;
        console.log(
          "campaignUrn",
          campaignUrn,
          "campaignStatus",
          campaignStatus,
          "campaignStartDateEpochMs",
          campaignStartDateEpochMs,
          "endDateEpochMs",
          endDateEpochMs,
        );
        await linkedInClient.updateCampaignEndDate(
          adAccountLinkedInUrn,
          campaignUrn,
          campaignStartDateEpochMs,
          endDateEpochMs,
        );
      }
      // If this endDate is after the endDate of the parent campaign group,
      // we should update the endDate of the parent campaign group
      // Depending on which stage the linkedin campaign is in,
      // this might or might not affect what happens next
      // Right now, mainly focusing on rebooting an concluded campaign.
      if (
        new Date(endDateEpochMs) >
        new Date(campaignGroup?.endDate ?? endDateEpochMs)
      ) {
        await linkedInClient.updateCampaignGroupEndDate(
          adAccountLinkedInUrn,
          campaignGroup.linkedInUrn!,
          new Date(endDateEpochMs),
        );
      }
    }

    if (budget) {
      // For budget, if the new budget is less than spent, we should not allow it
      const totalSpent = analyticsForCreatives.reduce(
        (acc, curr) => acc + curr.costInUsd,
        0,
      );
      if (budget < totalSpent) {
        return NextResponse.json(
          { error: "New budget is less than spent" },
          { status: 400 },
        );
      }

      // Update daily budget for each campaign
      // Also update the daily budget based on new remaining budget over the number of days left
      for (const campaignUrn of campaignLinkedInUrns) {
        console.log("updateCampaignBudget", campaignUrn, budget);
        await linkedInClient.updateCampaignBudget(
          adAccountLinkedInUrn,
          campaignUrn,
          budget,
        );
      }
      await campaignGroupSegmentHandlers.update.budgetForCampaignGroupSegment(
        campaignGroupSegmentId,
        budget,
      );
    }

    // Update the status of the parent campaign group if setting to RUNNING.
    // If current campaign group segment endDate has passed, we are rebooting.
    // Current idea is to continue running the winner AD from before.

    if (stage === "COMPLETED") {
      for (const campaignUrn of campaignLinkedInUrns) {
        await linkedInClient.updateCampaignStatus(
          adAccountLinkedInUrn,
          campaignUrn,
          "PAUSED",
        );
      }
      for (const ad of analyticsForCreatives) {
        await linkedInClient.updateAdStatus(
          adAccountLinkedInUrn,
          ad.creativeLinkedInUrnWithPrefix!,
          "PAUSED",
        );
      }
    } else if (stage === "RUNNING") {
      if (
        endDateEpochMs &&
        new Date(endDateEpochMs) > campaignGroupOriginalEndDate
      ) {
        // Find the best performing ad based on impressions and clicks
        const bestPerformingAd = analyticsForCreatives.reduce(
          (best, current) => {
            const bestCTR =
              (best?.impressions ?? 0) > 0
                ? (best?.clicks ?? 0) / (best?.impressions ?? 0)
                : 0;
            const currentCTR =
              (current.impressions ?? 0) > 0
                ? (current.clicks ?? 0) / (current.impressions ?? 0)
                : 0;
            return currentCTR > bestCTR ? current : best;
          },
          analyticsForCreatives[0],
        );

        // Set the best performing one to active
        for (const ad of analyticsForCreatives) {
          if (
            ad.creativeLinkedInUrnWithPrefix ===
            bestPerformingAd?.creativeLinkedInUrnWithPrefix
          ) {
            await linkedInClient.updateAdStatus(
              adAccountLinkedInUrn,
              ad.creativeLinkedInUrnWithPrefix!,
              "ACTIVE",
            );
            await linkedInClient.updateCampaignStatus(
              adAccountLinkedInUrn,
              ad.campaignLinkedInUrn!,
              "ACTIVE",
            );
            break;
          }
        }
      }
    } else {
      // not going to change anything for the stage
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error updating campaign group segment:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
