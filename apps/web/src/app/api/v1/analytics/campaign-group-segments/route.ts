import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { z } from "zod";

import { campaignGroupSegmentHandlers } from "@kalos/database/handlers/campaignGroupSegment";
import { linkedInAdAccountHandlers } from "@kalos/database/handlers/linkedInAdAccount";
import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

const schema = z.object({
  organizationId: z.number(),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const bodyParseRes = schema.safeParse(body);

    if (!bodyParseRes.success) {
      return NextResponse.json(
        { error: "Invalid request body" },
        { status: 400 },
      );
    }

    const { organizationId } = bodyParseRes.data;
    const segmentCampaignGroupCampaignLinkedInAds =
      await campaignGroupSegmentHandlers.select.many.segmentCampaignGroupCampaignLinkedInAdByDateRange(
        organizationId,
      );
    const campaignLinkedInUrnToSegmentId =
      segmentCampaignGroupCampaignLinkedInAds.reduce<Record<string, string>>(
        (acc, segment) => {
          if (segment.campaignLinkedInUrn) {
            acc[segment.campaignLinkedInUrn] = segment.campaignGroupSegmentId;
          }
          return acc;
        },
        {},
      );
    console.log(
      "campaignUrnToSegmentId mapping",
      campaignLinkedInUrnToSegmentId,
    );

    const creativeLinkedInUrns = [
      ...new Set(
        segmentCampaignGroupCampaignLinkedInAds
          .map((segment) => {
            const urn = segment.creativeLinkedInUrnWithPrefix;
            if (!urn) return urn;
            const parts = urn.split(":");
            return parts[parts.length - 1];
          })
          .filter((urn): urn is string => urn !== null),
      ),
    ];
    console.log("creativeLinkedInUrns", creativeLinkedInUrns);
    const adAccounts =
      await linkedInAdAccountHandlers.select.one.byOrganizationId(
        organizationId,
      );
    if (!adAccounts?.[0]?.linkedInUrn) {
      return NextResponse.json(
        { error: "No ad account found" },
        { status: 404 },
      );
    }
    const adAccountUrn = adAccounts[0]!.linkedInUrn!;

    const campaignGroupLinkedInUrns = [
      ...new Set(
        segmentCampaignGroupCampaignLinkedInAds.map(
          (segment) => segment.campaignGroupLinkedInUrn,
        ),
      ),
    ];
    console.log("campaignGroupLinkedInUrns", campaignGroupLinkedInUrns);
    const campaignLinkedInUrns = [
      ...new Set(
        segmentCampaignGroupCampaignLinkedInAds.map(
          (segment) => segment.campaignLinkedInUrn,
        ),
      ),
    ];
    console.log("campaignLinkedInUrns", campaignLinkedInUrns);

    const client = await getLinkedInApiClientFromOrganizationId(organizationId);
    if (!client) {
      return NextResponse.json(
        { error: "LinkedIn client not found" },
        { status: 404 },
      );
    }
    if (creativeLinkedInUrns.length === 0) {
      return NextResponse.json([], { status: 200 });
    }
    const analytics = await client.getAnalyticsForCreatives(
      creativeLinkedInUrns,
      new Date("2024-05-01"),
    );
    const analyticsData = (analytics as any).elements;
    const analyticsForCreatives = segmentCampaignGroupCampaignLinkedInAds.map(
      (creative) => {
        const analyticsForCreative = analyticsData.find((analytics: any) =>
          analytics.pivotValues.includes(
            `urn:li:sponsoredCreative:${creative.creativeLinkedInUrnWithPrefix?.split(":").pop()}`,
          ),
        );

        return {
          ...creative,
          clicks: Number(analyticsForCreative?.clicks ?? 0),
          impressions: Number(analyticsForCreative?.impressions ?? 0),
          costInLocalCurrency: Number(
            analyticsForCreative?.costInLocalCurrency ?? 0,
          ),
          costInUsd: Number(analyticsForCreative?.costInUsd ?? 0),
          oneClickLeads: Number(analyticsForCreative?.oneClickLeads ?? 0),
        };
      },
    );

    // debug prints
    // const linkedCampaignGroups = await client.getCampaignGroup(
    //   adAccounts[0]!.toString(),
    //   campaignGroupLinkedInUrns.filter(
    //     (urn): urn is string => urn !== null,
    //   )[0]!,
    // );
    // console.log("linkedCampaignGroups", linkedCampaignGroups);

    const linkedCampaigns = await client.getCampaigns(
      adAccountUrn,
      campaignLinkedInUrns
        .filter((urn): urn is string => urn !== null)
        .map(Number),
    );
    console.log("linkedCampaigns", JSON.stringify(linkedCampaigns, null, 2));
    // Map campaign statuses from LinkedIn API response
    const campaignStatuses = Object.values(linkedCampaigns.results).reduce(
      (acc: Record<string, string>, campaign) => {
        acc[campaign.id.toString()] = campaign.status;
        return acc;
      },
      {},
    );

    // Create mapping from campaign group segment ID to campaign status
    const campaignGroupSegmentDerivedStatuses = analyticsForCreatives.reduce(
      (acc: Record<string, string>, creative) => {
        // Skip if we don't have campaign URN or segment ID
        if (!creative.campaignLinkedInUrn || !creative.campaignGroupSegmentId) {
          return acc;
        }

        const campaignStatus = campaignStatuses[creative.campaignLinkedInUrn];
        const segmentId = creative.campaignGroupSegmentId;

        // Initialize array for this segment if not exists
        if (!acc[segmentId]) {
          acc[segmentId] = "COMPLETED"; // Start assuming all completed
        }

        // If any campaign is not completed, mark segment as not completed
        if (campaignStatus === "ACTIVE") {
          acc[segmentId] = "RUNNING";
        }

        return acc;
      },
      {},
    );

    console.log(
      "Campaign group segment statuses:",
      campaignGroupSegmentDerivedStatuses,
    );

    // Add campaign status to analytics data
    const analyticsForCreativesWithStatus = analyticsForCreatives.map(
      (creative) => ({
        ...creative,
        campaignLinkedInStatus: campaignStatuses[creative.campaignLinkedInUrn!],
        campaignGroupSegmentDerivedStatus:
          campaignGroupSegmentDerivedStatuses[creative.campaignGroupSegmentId!],
      }),
    );
    // const linkedinCreatives = await client.getAdCreatives(
    //   adAccounts[0]!.toString(),
    //   creativeLinkedInUrns.filter((urn): urn is string => urn !== null),
    // );
    // console.log(
    //   "linkedinCreatives",
    //   JSON.stringify(linkedinCreatives, null, 2),
    // );
    // const uniqueShareUrns = new Set<string>();

    // Object.values(linkedinCreatives.results).forEach((creative: any) => {
    //   if (creative?.content?.reference?.startsWith("urn:li:share:")) {
    //     uniqueShareUrns.add(creative.content.reference);
    //   }
    // });

    // const shareUrns = Array.from(uniqueShareUrns)
    //   .map((urn) => urn.split(":").pop())
    //   .filter((urn): urn is string => urn !== null && urn !== undefined);

    // console.log("Unique share URNs:", shareUrns);

    // const posts = await client.batchGetPosts(shareUrns);
    // console.log("posts", JSON.stringify(posts, null, 2));

    console.log(
      "analyticsForCreativesWithStatus",
      JSON.stringify(analyticsForCreativesWithStatus, null, 2),
    );
    return NextResponse.json(analyticsForCreativesWithStatus);
  } catch (error) {
    console.error("Error fetching LinkedIn analytics:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
