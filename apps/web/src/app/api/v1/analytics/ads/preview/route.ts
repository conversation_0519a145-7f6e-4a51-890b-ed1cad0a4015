import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { z } from "zod";

import { linkedInAdAccountHandlers } from "@kalos/database/handlers/linkedInAdAccount";
import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

const schema = z.object({
  organizationId: z.number(),
  adCreativeLinkedInUrn: z.string(),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const bodyParseRes = schema.safeParse(body);
    if (!bodyParseRes.success) {
      return NextResponse.json(
        { error: bodyParseRes.error.message },
        { status: 400 },
      );
    }

    const { organizationId, adCreativeLinkedInUrn } = bodyParseRes.data;

    const linkedInClient =
      await getLinkedInApiClientFromOrganizationId(organizationId);
    if (!linkedInClient) {
      return NextResponse.json(
        { error: "LinkedIn client not found" },
        { status: 404 },
      );
    }
    const adAccounts =
      await linkedInAdAccountHandlers.select.one.byOrganizationId(
        organizationId,
      );
    if (!adAccounts?.[0]?.linkedInUrn) {
      return NextResponse.json(
        { error: "Ad account not found" },
        { status: 404 },
      );
    }
    const adAccountUrn = adAccounts[0].linkedInUrn;
    const adPreviews = await linkedInClient.getAdPreview(
      adCreativeLinkedInUrn,
      adAccountUrn.toString(),
    );
    const previews = adPreviews.elements.reduce(
      (acc, preview) => {
        const contentPresentationType =
          preview.placement.linkedin.contentPresentationType;
        const iframeSrcMatch = preview.preview.match(/src='([^']+)'/);
        if (iframeSrcMatch) {
          acc[contentPresentationType] = iframeSrcMatch[1] ?? "";
        }
        return acc;
      },
      {} as Record<string, string>,
    );
    return NextResponse.json(previews);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
