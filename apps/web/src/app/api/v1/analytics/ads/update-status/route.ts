import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { z } from "zod";

import { adCopyHandlers } from "@kalos/database/handlers/adCopy";
import { linkedInAdAccountHandlers } from "@kalos/database/handlers/linkedInAdAccount";
import { linkedInAdDeploymentHandlers } from "@kalos/database/handlers/linkedInAdDeployment";
import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

const schema = z.object({
  organizationId: z.number(),
  adLinkedInUrn: z.string(),
  status: z.enum(["PAUSED", "ACTIVE"]),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const bodyParseRes = schema.safeParse(body);

    if (!bodyParseRes.success) {
      return NextResponse.json(
        { error: "Invalid request body" },
        { status: 400 },
      );
    }
    const { organizationId, adLinkedInUrn, status } = bodyParseRes.data;
    const linkedInApiClient =
      await getLinkedInApiClientFromOrganizationId(organizationId);
    if (!linkedInApiClient) {
      return NextResponse.json(
        { error: "LinkedIn API client not found" },
        { status: 404 },
      );
    }
    const adDeployment =
      await linkedInAdDeploymentHandlers.select.byLinkedInUrn(adLinkedInUrn);
    if (!adDeployment) {
      return NextResponse.json(
        { error: "Ad deployment not found" },
        { status: 404 },
      );
    }
    const adAccount =
      await linkedInAdAccountHandlers.select.one.byOrganizationId(
        organizationId,
      );
    if (!adAccount[0]?.linkedInUrn) {
      return NextResponse.json(
        { error: "Ad account not found" },
        { status: 404 },
      );
    }
    const adAccountLinkedInUrn = adAccount[0].linkedInUrn;

    await linkedInApiClient.updateAdStatus(
      adAccountLinkedInUrn.toString(),
      adLinkedInUrn,
      status,
    );
    await linkedInAdDeploymentHandlers.update.one(adDeployment.id, {
      linkedInStatus: status,
    });
    return NextResponse.json({ message: "Ad status updated" }, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
