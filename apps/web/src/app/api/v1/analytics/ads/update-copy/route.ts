import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { z } from "zod";

import { adCopyHandlers } from "@kalos/database/handlers/adCopy";
import { linkedInAdAccountHandlers } from "@kalos/database/handlers/linkedInAdAccount";
import { linkedInAdDeploymentHandlers } from "@kalos/database/handlers/linkedInAdDeployment";
import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

const schema = z.object({
  organizationId: z.number(),
  linkedInAdId: z.string(),
  adTitle: z.string(),
  adDescription: z.string(),
  adBody: z.string(),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const bodyParseRes = schema.safeParse(body);

    if (!bodyParseRes.success) {
      return NextResponse.json(
        { error: "Invalid request body" },
        { status: 400 },
      );
    }

    const { organizationId, linkedInAdId, adTitle, adDescription, adBody } =
      bodyParseRes.data;

    // Find the linkedin creative URN from
    const linkedInAdDeployment =
      await linkedInAdDeploymentHandlers.select.byLinkedInAdId(linkedInAdId);

    if (
      !linkedInAdDeployment ||
      linkedInAdDeployment.length === 0 ||
      !linkedInAdDeployment[0]
    ) {
      return NextResponse.json(
        { error: "LinkedIn ad deployment not found" },
        { status: 404 },
      );
    }

    const linkedInApiClient =
      await getLinkedInApiClientFromOrganizationId(organizationId);

    if (!linkedInApiClient) {
      return NextResponse.json(
        { error: "LinkedIn client not found" },
        { status: 404 },
      );
    }
    const linkedInAdAccount =
      await linkedInAdAccountHandlers.select.one.byOrganizationId(
        organizationId,
      );

    if (
      !linkedInAdAccount ||
      linkedInAdAccount.length === 0 ||
      !linkedInAdAccount[0]
    ) {
      return NextResponse.json(
        { error: "LinkedIn ad account not found" },
        { status: 404 },
      );
    }

    const adCreativeUrn = linkedInAdDeployment[0].linkedInUrn?.toString();
    const adCreatives = await linkedInApiClient.getAdCreatives(
      linkedInAdAccount[0].linkedInUrn?.toString(),
      [adCreativeUrn],
    );

    if (!adCreatives) {
      return NextResponse.json(
        { error: "LinkedIn ad creative not found" },
        { status: 404 },
      );
    }

    const adCreative = adCreatives.results[adCreativeUrn];
    const adCopyPostUrn = adCreative?.content?.reference;

    if (!adCopyPostUrn) {
      return NextResponse.json(
        { error: "LinkedIn ad copy post URN not found" },
        { status: 404 },
      );
    }

    console.log(
      "updateAdCopyPost",
      adCopyPostUrn,
      adTitle,
      adDescription,
      adBody,
    );
    await linkedInApiClient.updateAdCopyPost(
      adCopyPostUrn,
      adTitle,
      adDescription,
      adBody,
    );

    await adCopyHandlers.update.one(linkedInAdId, {
      title: adTitle,
      description: adDescription,
      body: adBody,
    });

    return NextResponse.json({ message: "Ad copy updated" }, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
