import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { z } from "zod";

import { campaignGroupHandlers } from "@kalos/database/handlers/campaignGroup";
import { campaignGroupSegmentHandlers } from "@kalos/database/handlers/campaignGroupSegment";
import { linkedInAdAccountHandlers } from "@kalos/database/handlers/linkedInAdAccount";
import { linkedInAdTestRoundHandlers } from "@kalos/database/handlers/linkedInAdTestRound";
import { linkedInCampaignTestRoundHandlers } from "@kalos/database/handlers/linkedInCampaignTestRound";
import { linkedInStageHandlers } from "@kalos/database/handlers/linkedInDeployment/linkedInStage";
import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

const schema = z.object({
  organizationId: z.number(),
  campaignGroupSegmentId: z.string(),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const bodyParseRes = schema.safeParse(body);

    if (!bodyParseRes.success) {
      return NextResponse.json(
        { error: "Invalid request body" },
        { status: 400 },
      );
    }

    const { organizationId, campaignGroupSegmentId } = bodyParseRes.data;
    const linkedInAds =
      await campaignGroupSegmentHandlers.select.many.linkedInAdByDateRangeByCampaignGroupSegmentId(
        campaignGroupSegmentId,
      );
    const creativeLinkedInUrns = [
      ...new Set(
        linkedInAds
          .map((ad) => {
            const urn = ad.creativeLinkedInUrnWithPrefix;
            if (!urn) return urn;
            const parts = urn.split(":");
            return parts[parts.length - 1];
          })
          .filter((urn): urn is string => urn !== null),
      ),
    ];
    console.log("creativeLinkedInUrns", creativeLinkedInUrns);

    const campaignGroup =
      await campaignGroupHandlers.select.oneByCampaignGroupSegmentId(
        campaignGroupSegmentId,
      );
    // Hack, hand fix the start and end date
    const campaignGroupWithFixedDates = {
      ...campaignGroup,
      startDate: new Date(campaignGroup?.startDate ?? "2024-05-01")
        .toISOString()
        .split("T")[0],
      endDate: new Date(campaignGroup?.endDate ?? new Date())
        .toISOString()
        .split("T")[0],
    };
    console.log("campaignGroup", campaignGroupWithFixedDates);

    const campaignGroupSegmentDetails =
      await campaignGroupSegmentHandlers.select.one.campaignGroupSegmentDetails(
        campaignGroupSegmentId,
      );
    console.log(
      "campaignGroupSegmentDetails",
      JSON.stringify(campaignGroupSegmentDetails, null, 2),
    );
    const campaignGroupSegmentStage =
      await linkedInStageHandlers.select.currentForCampaignGroupSegment(
        campaignGroupSegmentId,
      );
    // If not running stage right now, it can be null, FE will handle this
    // TODO: this needs to be better handled
    const completedCampaignTests =
      await linkedInCampaignTestRoundHandlers.select.completedForCampaignGroupSegment(
        campaignGroupSegmentId,
      );
    console.log("completedCampaigns", completedCampaignTests);
    const disqualifiedCampaigns = new Set(
      completedCampaignTests.map((campaign) =>
        campaign.winnerCampaign === "CURRENT_BEST"
          ? campaign.contenderCampaignId
          : campaign.currentBestCampaignId,
      ),
    );
    console.log("disqualifiedCampaigns", disqualifiedCampaigns);

    const completedAdTests =
      await linkedInAdTestRoundHandlers.select.completedForCampaignGroupSegment(
        campaignGroupSegmentId,
      );
    console.log("completedAdTests", completedAdTests);
    const disqualifiedAds = new Set(
      completedAdTests.map((ad) =>
        ad.winnerAd === "CURRENT_BEST" ? ad.contenderAdId : ad.currentBestAdId,
      ),
    );
    console.log("disqualifiedAds", disqualifiedAds);

    const client = await getLinkedInApiClientFromOrganizationId(organizationId);
    if (!client) {
      return NextResponse.json(
        { error: "LinkedIn client not found" },
        { status: 404 },
      );
    }
    const adAccounts =
      await linkedInAdAccountHandlers.select.one.byOrganizationId(
        organizationId,
      );
    const adAccountUrn = adAccounts[0]?.linkedInUrn;
    if (!adAccountUrn) {
      return NextResponse.json(
        { error: "Ad account not found" },
        { status: 404 },
      );
    }
    const campaignLinkedInUrns = [
      ...new Set(
        linkedInAds
          .map((ad) => ad.campaignLinkedInUrn)
          .filter((urn): urn is string => urn !== null),
      ),
    ];
    console.log("campaignLinkedInUrns", campaignLinkedInUrns);
    const campaignDetailsFromLinkedIn = await client.getCampaigns(
      adAccountUrn,
      campaignLinkedInUrns.map((urn) => Number(urn)),
    );

    const creativesLinkedIn = await client.getAdCreatives(
      adAccountUrn.toString(),
      creativeLinkedInUrns,
    );

    const dataForCreatives = (
      (await client.getAnalyticsForCreatives(
        creativeLinkedInUrns,
        new Date("2024-05-01"),
      )) as any
    ).elements;
    const analyticsForCreatives = linkedInAds.map((creative) => {
      const analyticsForCreative = dataForCreatives.find((analytics: any) =>
        analytics.pivotValues.includes(
          `urn:li:sponsoredCreative:${creative.creativeLinkedInUrnWithPrefix?.split(":").pop()}`,
        ),
      );

      const campaignDetails =
        campaignDetailsFromLinkedIn.results[creative.campaignLinkedInUrn ?? ""];

      return {
        ...creative,
        clicks: Number(analyticsForCreative?.clicks ?? 0),
        impressions: Number(analyticsForCreative?.impressions ?? 0),
        costInLocalCurrency: Number(
          analyticsForCreative?.costInLocalCurrency ?? 0,
        ),
        costInUsd: Number(analyticsForCreative?.costInUsd ?? 0),
        oneClickLeads: Number(analyticsForCreative?.oneClickLeads ?? 0),
        adCreativeLinkedInStatus:
          creativesLinkedIn.results[creative.creativeLinkedInUrnWithPrefix]
            ?.intendedStatus,
        adCreativeHoldStatus:
          creativesLinkedIn.results[creative.creativeLinkedInUrnWithPrefix]
            ?.servingHoldReasons,
        campaignEndDateEpochMs: campaignDetails?.runSchedule?.end,
        campaignLinkedInStatus: campaignDetails?.status,
        adDisqualified: disqualifiedAds.has(creative.adValuePropId),
        campaignDisqualified: disqualifiedCampaigns.has(creative.campaignId),
      };
    });

    console.log(
      "analytics/ads/route",
      JSON.stringify(
        {
          ads: analyticsForCreatives,
          campaignGroup: campaignGroupWithFixedDates,
          campaignGroupSegmentDetails,
          campaignGroupSegmentStage,
        },
        null,
        2,
      ),
    );
    return NextResponse.json({
      ads: analyticsForCreatives,
      campaignGroup: campaignGroupWithFixedDates,
      campaignGroupSegmentDetails,
      campaignGroupSegmentStage,
      adAccountUrn,
    });
  } catch (error) {
    console.error("Error fetching LinkedIn analytics:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
