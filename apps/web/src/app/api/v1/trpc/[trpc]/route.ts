// This function can run for a maximum of 5 seconds
import * as process from "process";
import type { NextRequest } from "next/server";
import { fetchRequestHandler } from "@trpc/server/adapters/fetch";

import { createTRPCContext, trpcRouter } from "@kalos/api";

export const maxDuration = 300;

const createContext = async () => {
  return createTRPCContext();
};

const handler = (req: NextRequest) =>
  fetchRequestHandler({
    endpoint: "/api/v1/trpc",
    req,
    router: trpcRouter,
    createContext: () => createContext(),
    onError:
      process.env.NODE_ENV === "development"
        ? ({ path, error }) => {
            console.error(
              `❌ tRPC failed on ${path ?? "<no-path>"}: ${error.message}`,
            );
          }
        : undefined,
  });

export { handler as GET, handler as POST };
