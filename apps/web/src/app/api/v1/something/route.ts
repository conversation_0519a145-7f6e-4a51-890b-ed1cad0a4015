import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { api } from "@/trpc/server";
import { z } from "zod";

import { adCopyHandlers } from "@kalos/database/handlers/adCopy";
import { adExampleHandler } from "@kalos/database/handlers/adExample";
import { adStyleGuideHandlers } from "@kalos/database/handlers/adStyleGuide";
import { linkedInAdHanders } from "@kalos/database/handlers/linkedInAd";
import { organizationHandlers } from "@kalos/database/handlers/organization";
import { generateAdCopy } from "@kalos/llms/index";

function iteratorToStream(iterator: any) {
  return new ReadableStream({
    async pull(controller) {
      const { value, done } = await iterator.next();

      if (done) {
        controller.close();
      } else {
        controller.enqueue(value);
      }
    },
  });
}

const schema = z.object({
  adSite: z.string(),
  topic: z.string(),
  targetAudience: z.array(z.string()),
  valuePropositions: z.array(z.string()),
  productPositioning: z.string(),
  adId: z.string(),
  organizationId: z.number(),
  campaignGroupSegmentId: z.string(),
});

export async function POST(req: NextRequest) {
  const body = await req.json();
  const bodyParseRes = schema.safeParse(body);
  if (bodyParseRes.error) {
    return NextResponse.json({}, { status: 400 });
  }
  const parsedBdoy = bodyParseRes.data;
  console.log(parsedBdoy);

  const valueProps = await linkedInAdHanders.select.many.selectValuePropsForAd(
    parsedBdoy.adId,
  );

  const prop = valueProps.find((v) => v.title === parsedBdoy.topic);
  const topic = !prop
    ? parsedBdoy.topic
    : `${prop.title}${prop.description == null ? "" : `: ${prop.description}`}`;
  const exampleAds = await adExampleHandler.getAdExamplesForOrganization(
    parsedBdoy.organizationId,
  );
  const styleGuide = await adStyleGuideHandlers.select.one.byOrganizationId(
    parsedBdoy.organizationId,
  );
  const org = await organizationHandlers.select.one(parsedBdoy.organizationId);
  if (!org) {
    throw "organization does not exist";
  }
  const res = generateAdCopy(
    {
      company: org.name,
      ad_site: parsedBdoy.adSite,
      topic: topic,
      target_audience: parsedBdoy.targetAudience.join("\n"),
      value_propositions: parsedBdoy.valuePropositions.join("\n"),
      product_positioning: parsedBdoy.productPositioning,
      valuePropId: parsedBdoy.adId,
      example_ads:
        exampleAds.length > 0
          ? exampleAds.map((each) => each.example).join("\n")
          : "No example Ads provided",
      style_guide: styleGuide?.styleGuide ?? "No style guide provided",
    },
    parsedBdoy.campaignGroupSegmentId,
  );
  const stream = iteratorToStream(res);
  return new Response(stream);
}
