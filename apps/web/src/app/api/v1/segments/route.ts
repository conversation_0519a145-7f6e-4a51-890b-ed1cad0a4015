import { segmentHandlers } from "@kalos/database/handlers/segment";
import { segmentVerticalHandlers } from "@kalos/database/handlers/segmentVertical";

import { getUuid } from "../../../../../../../packages/utils/src/uuid";

export async function GET() {
  const segmentsToInsert: {
    id: string;
    organizationId: number;
    jobFunctionId: number;
    annualRevenueLowBound: number;
    annualRevenueHighBound: number;
  }[] = [];

  const revenueRanges: {
    annualRevenueLowBound: number;
    annualRevenueHighBound: number;
  }[] = [
    {
      annualRevenueHighBound: 100000000,
      annualRevenueLowBound: 500000000,
    },
    {
      annualRevenueHighBound: 1000000,
      annualRevenueLowBound: 100000000,
    },
  ];

  const jobFunction = [4, 8];

  for (const revenueRange of revenueRanges) {
    for (const job of jobFunction) {
      segmentsToInsert.push({
        id: getUuid(),
        organizationId: 3,
        jobFunctionId: job,
        annualRevenueLowBound: revenueRange.annualRevenueLowBound,
        annualRevenueHighBound: revenueRange.annualRevenueHighBound,
      });
    }
  }

  await segmentHandlers.insert.many(segmentsToInsert);

  const verticals = [
    "E-Commerce",
    "Hospitality",
    "Subscription",
    "Marketplace",
    "Financial Services",
    "Computer & Software",
  ];

  for (const vertical of verticals) {
    for (const segment of segmentsToInsert) {
      await segmentVerticalHandlers.insert.one({
        name: vertical,
        segmentId: segment.id,
      });
    }
  }
}
