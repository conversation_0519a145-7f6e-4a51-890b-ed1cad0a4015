import type { NextRequest } from "next/server";
import { redirect } from "next/navigation";
import { NextResponse } from "next/server";
import { api } from "@/trpc/server";
import { auth, getAuth } from "@clerk/nextjs/server";

import { advertisingService } from "@kalos/advertising";
import { linkedInOAuthHandlers } from "@kalos/database/handlers/linkedInOAuth";
import { userHandlers } from "@kalos/database/handlers/user";
import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";
import { linkedInErrorHandler } from "@kalos/linkedin-api";

import { organizationUser } from "../../../../../../../../packages/database/src/schema/organizationUser";

interface TokenResponse {
  access_token: string;
  expires_in: number;
  refresh_token: string;
  error_description?: string;
}
export async function GET(req: NextRequest) {
  const url = new URL(req.url);
  const code = url.searchParams.get("code");
  const state = url.searchParams.get("state");

  if (!code) {
    return NextResponse.json(
      { error: "Authorization code or state missing" },
      { status: 400 },
    );
  }

  const params = new URLSearchParams({
    grant_type: "authorization_code",
    code: code,
    client_id: "86yfe63sb37g1s",
    redirect_uri: `https://${getBaseUrl()}/api/oauth2/linkedin/callback`,
    client_secret: "25c30ah0CLSdgnWi",
  });
  try {
    const tokenResponse = await fetch(
      "https://www.linkedin.com/oauth/v2/accessToken",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: params.toString(),
      },
    );

    const tokenData: TokenResponse =
      (await tokenResponse.json()) as TokenResponse;
    console.log(tokenResponse);

    if (tokenResponse.ok) {
      const accessToken = tokenData.access_token;
      const { userId } = auth();
      if (!userId) {
        throw "no user";
      }

      const org = await api.user.organizationUser.get.organization();

      await linkedInOAuthHandlers.insert.one({
        organizationUserId: userId,
        organizationId: org.organizationId,
        refreshToken: tokenData.refresh_token,
      });

      const client = await getLinkedInApiClientFromOrganizationId(
        org.organizationId,
      );

      if (!client) {
        return NextResponse.json({});
      }
    } else {
      // Notify OAuth error
      await linkedInErrorHandler.handleOAuthError(
        {
          error_description: tokenData.error_description || "Failed to get access token",
        },
        {
          stage: "authorization_code_exchange",
          metadata: {
            code: !!code,
            state: !!state,
            httpStatus: tokenResponse.status,
            httpStatusText: tokenResponse.statusText,
          },
        },
        tokenResponse.status
      );

      return NextResponse.json(
        { error: tokenData.error_description || "Failed to get access token" },
        { status: 400 },
      );
    }
  } catch (error) {
    console.error("Error exchanging authorization code:", error);
    
    // Notify OAuth error
    await linkedInErrorHandler.handleOAuthError(
      error,
      {
        stage: "authorization_code_exchange",
        metadata: {
          code: !!code,
          state: !!state,
          errorType: "exception",
        },
      },
      500
    );

    return NextResponse.json(
      { error: "Error exchanging authorization code" },
      { status: 500 },
    );
  }
  redirect("/advertising/new");
}

function getBaseUrl() {
  if (process.env.NEXT_PUBLIC_VERCEL_ENV === "production") {
    return "app.getkalos.com";
  }
  const baseUrl = process.env.NEXT_PUBLIC_VERCEL_BRANCH_URL;
  return baseUrl;
}
