import { redirect } from "next/navigation";
import { NextRequest, NextResponse } from "next/server";
import { redirectUri } from "@/app/utils/salesforceAuthUtils";
import { auth } from "@clerk/nextjs/server";
import { Connection, OAuth2 } from "jsforce";

import { createSalesforceConnection } from "@kalos/salesforce/connection";

export async function GET(request: NextRequest) {
  console.log("CALLBACK");
  const url = new URL(request.url);
  const code = url.searchParams.get("code");

  if (!code) {
    return NextResponse.json(
      { error: "No code provided", code: code, url: url },
      { status: 400 },
    );
  }

  const userId = auth().userId;
  if (userId == null) {
    return NextResponse.json({ error: "Not Authenticated" }, { status: 401 });
  }

  const connectionCreated = await createSalesforceConnection(
    userId,
    redirectUri,
    code,
  );
  if (!connectionCreated.success) {
    return NextResponse.json(
      { error: connectionCreated.errorMessage },
      { status: 500 },
    );
  }
  redirect("/onboarding/integrations");
}
