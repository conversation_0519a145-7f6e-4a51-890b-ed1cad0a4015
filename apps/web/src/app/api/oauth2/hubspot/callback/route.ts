import type { NextRequest } from "next/server";
import { redirect } from "next/navigation";
import { NextResponse } from "next/server";
import { api } from "@/trpc/server";
import { auth } from "@clerk/nextjs/server";

import { hubspotOauthHandlers } from "@kalos/database/handlers/hubspotOauth";

import { createUuid } from "../../../../../../../../backend/src/modules/core/utils/uuid";
import { CrmCredentialRepository } from "../../../../../../../../backend/src/modules/crm/infrastructure/repositories/crmCredential.repository";

export function getBaseUrl() {
  if (process.env.NEXT_PUBLIC_VERCEL_ENV === "production") {
    return "https://app.getkalos.com";
  }
  if (process.env.NEXT_PUBLIC_VERCEL_BRANCH_URL) {
    return `http://${process.env.NEXT_PUBLIC_VERCEL_BRANCH_URL}`;
  }
  if (typeof window !== "undefined") {
    return window.location.origin;
  }
  return `http://localhost:${process.env.PORT ?? 3000}`;
}

const redirectUri = getBaseUrl() + "/api/oauth2/hubspot/callback";

export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const code = url.searchParams.get("code");
  if (!code) {
    return NextResponse.json({ error: "No code provided" }, { status: 400 });
  }

  const clientId = process.env.HUBSPOT_CLIENT_ID;
  if (!clientId) {
    return NextResponse.json(
      { error: "No client id provided" },
      { status: 400 },
    );
  }

  const clientSecret = process.env.HUBSPOT_CLIENT_SECRET;
  if (!clientSecret) {
    return NextResponse.json(
      { error: "No client secret provided" },
      { status: 400 },
    );
  }

  console.log("ALL GOOD, GETTING TOKEN");

  const params = new URLSearchParams({
    grant_type: "authorization_code",
    code: code,
    client_id: clientId,
    redirect_uri: redirectUri,
    client_secret: clientSecret,
  });

  const res = await fetch("https://api.hubapi.com/oauth/v1/token", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: params.toString(),
  });

  console.log("TOKEN RESPONSE", res);

  const { userId } = auth();
  if (!userId) {
    throw "no user";
  }

  const org = await api.user.organizationUser.get.organization();
  if (!org) {
    return NextResponse.json(
      { error: "No organization found" },
      { status: 400 },
    );
  }

  const data = (await res.json()) as {
    access_token: string;
    refresh_token: string;
    expires_in: number;
  };

  // TODO-BR: we need a service file
  const crmCredentialRepository = new CrmCredentialRepository();

  await crmCredentialRepository.createOne({
    id: createUuid(),
    organizationId: org.organizationId,
    organizationUserId: userId,
    crmType: "hubspot",
    refreshToken: data.refresh_token,
  });

  redirect("/");
}
