import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import {
  ClerkProvider,
} from '@clerk/nextjs'
import "./globals.css";
import { TRPCReactProvider } from "@/trpc/provider";
import {NavBar} from "@kalos/ui/navbar"
const inter = Inter({ subsets: ["latin"] });
import WinLossIcon from "@kalos/ui/icons/win-loss-icon"
import CompanyIcon from "@kalos/ui/icons/comapny-icon";
import Link from "next/link";
import { GlobeIcon } from "@radix-ui/react-icons";
import { PHProvider } from "@/posthog/posthog";
import dynamic from "next/dynamic";
export const metadata: Metadata = {
  title: "Kalos",
  description: "",
};

const PostHogPageView = dynamic(() => import('./../posthog/posthog-page-view'), {
  ssr: false,
})


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
        <html lang="en">
          <body className={inter.className}>
            {children}
          </body>
        </html>
    </ClerkProvider>
  );
}
