"use client";

import posthog from "posthog-js";
import { useFeatureFlagEnabled } from "posthog-js/react";

interface FeatureFlagProps {
  flag: string;
  children: React.ReactNode;
}

export const FeatureFlag: React.FC<FeatureFlagProps> = ({ flag, children }) => {
  if (process.env["NEXT_PUBLIC_VERCEL_ENV"] == "production") {
    const featureFlag = useFeatureFlagEnabled(flag);
    if (!featureFlag) {
      return null;
    }
    return children;
  }
  return children;
};
