import { auth } from "@clerk/nextjs/server";

import { AdminOrganizationTable } from "./adminOrganizationTable";

export default async function AdminPage() {
  const user = auth();

  if (
    user.userId != "user_2hpfJ7nV9VwkpT8o4pmV3vkLsd0" &&
    user.userId != "user_2gvKecrxU1ugrxInkMeeqc4KJsp" &&
    user.userId != "user_2p2YdEOYsl2oCmgvKrj8AWAf5Td" &&
    user.userId != "user_2xmOIJw6DG9Xs3koIfkJXhsrfT3" &&
    user.userId !== "user_2w3C3bW4CfKzD2TSka2dUFYt9pu" &&
    user.userId !== "user_2xdvevtuXmoks4qguWUaOSHhmL7" &&
    user.userId !== "user_2xdve9cKqaPnMFF8Fr3rt9etjDG" &&
    user.userId !== "user_2yI7bjxJLeRjphdosN4lAfjhcGq"
  ) {
    return null;
  }

  return (
    <div className="flex h-full w-full flex-col items-center justify-center">
      <AdminOrganizationTable />
    </div>
  );
}
