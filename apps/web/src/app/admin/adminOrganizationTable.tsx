"use client";

import type { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";
import { api } from "@/trpc/client";
import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { Loader2, Settings } from "lucide-react";



import { Badge } from "@kalos/ui/badge";
import { Button } from "@kalos/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@kalos/ui/card";
import { cn } from "@kalos/ui/index";
import { DataTable } from "@kalos/ui/table/datatable";





export function AdminOrganizationTable() {
  const organizationQuery = api.v2.core.admin.getAllOrganizations.useQuery();

  const columns: ColumnDef<{
    organizationId: number;
    name: string;
  }>[] = [
    {
      header: "Organization Name",
      cell: ({ row }) => {
        return (
          <Link href={`/admin/organization/${row.original.organizationId}`}>
            {row.original.name}
          </Link>
        );
      },
    },
    {
      header: "Is Onboarded",
      cell: ({ row }) => {
        const isOnboarded = api.opportunity.isOnboarded.useQuery({
          organizationId: row.original.organizationId,
        });
        console.log(
          `isOnboarded: ${isOnboarded.data} for ${row.original.name}`,
        );
        return (
          <>
            {!isOnboarded.isLoading && !isOnboarded.isError && (
              <Badge
                variant={"secondary"}
                className={cn(
                  isOnboarded.data == true ? "bg-green-200" : "bg-red-200",
                  "gap-2 border",
                )}
              >
                <div
                  className={cn(
                    isOnboarded.data == true ? "bg-green-500" : "bg-red-500",
                    "h-3 w-3 rounded-full border",
                  )}
                ></div>
                {isOnboarded.data == true
                  ? "Yes"
                  : isOnboarded.data == false
                    ? "No"
                    : "ERR"}
              </Badge>
            )}
          </>
        );
      },
    },
    {
      header: "Slack Settings",
      cell: ({ row }) => {
        const slackSettings =
          api.v2.core.admin.getOrganizationSlackSettings.useQuery({
            organizationId: row.original.organizationId,
          });

        const getDayOfWeekLabel = (day: number) => {
          const days = [
            "",
            "Mon",
            "Tue",
            "Wed",
            "Thu",
            "Fri",
            "Sat",
            "Sun",
            "Everyday",
          ];
          return days[day] || "Mon";
        };

        const getFrequencyLabel = (freq: string) => {
          const frequencies: Record<string, string> = {
            hourly: "Hourly",
            daily: "Daily",
            weekly: "Weekly",
            biweekly: "Bi-weekly",
            monthly: "Monthly",
          };
          return frequencies[freq] || "Weekly";
        };

        const isActive =
          slackSettings.data?.isConfigured &&
          slackSettings.data?.slackNotificationsEnabled;
        const isConfigured = slackSettings.data?.isConfigured;
        const isEnabled = slackSettings.data?.slackNotificationsEnabled ?? true;

        return (
          <div className="flex items-center gap-2">
            {!slackSettings.isLoading && !slackSettings.isError && (
              <div className="flex flex-col gap-1">
                <Badge
                  variant={"secondary"}
                  className={cn(
                    isActive
                      ? "bg-green-200"
                      : isEnabled
                        ? "bg-yellow-200"
                        : "bg-gray-200",
                    "gap-2 border",
                  )}
                >
                  <div
                    className={cn(
                      isActive
                        ? "bg-green-500"
                        : isEnabled
                          ? "bg-yellow-500"
                          : "bg-gray-500",
                      "h-3 w-3 rounded-full border",
                    )}
                  ></div>
                  {isActive
                    ? "Active"
                    : isEnabled
                      ? "Not Configured"
                      : "Disabled"}
                </Badge>
              </div>
            )}
            <Link
              href={`/admin/organization/${row.original.organizationId}/slack-settings`}
            >
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: organizationQuery.data ?? [],
    columns,
    enableRowSelection: false,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="flex h-screen w-screen flex-col items-center justify-center">
      <Card className="h-fit w-full md:w-1/2">
        <CardHeader>
          <CardTitle className="text-xl">Organizations</CardTitle>
        </CardHeader>
        <CardContent className="px-0 py-0 pt-4">
          {organizationQuery.data && (
            <DataTable
              columns={columns}
              table={table}
              rowHeight="16"
              noHover={false}
            />
          )}
          {organizationQuery.isLoading && (
            <Loader2 className="h-10 w-10 animate-spin" />
          )}
        </CardContent>
      </Card>
    </div>
  );
}