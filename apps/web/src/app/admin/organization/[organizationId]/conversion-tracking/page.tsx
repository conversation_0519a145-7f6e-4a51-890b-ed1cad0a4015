"use client";

import { useEffect, useState } from "react";
import { api } from "@/trpc/client";
import { Loader2, TrashIcon } from "lucide-react";

import { But<PERSON> } from "@kalos/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@kalos/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@kalos/ui/select";

export default function ConversionTrackingPage({
  params,
}: {
  params: { organizationId: string };
}) {
  const conversionsQuery = api.v2.ads.admin.getOrganizationConversions.useQuery(
    {
      organizationId: Number(params.organizationId),
    },
  );

  return (
    <div className="flex h-full w-full flex-col items-center justify-center">
      <Card className="w-full sm:w-3/4">
        <CardHeader>
          <CardTitle>Conversion Tracking</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            {conversionsQuery.data?.map((conversion) => (
              <ConversionTracking
                key={conversion.id}
                id={conversion.id}
                conversionUrn={conversion.conversionUrn}
                name={conversion.name}
                organizationId={Number(params.organizationId)}
              />
            ))}
          </div>
          <div className="flex justify-end pt-4">
            <AddOne organizationId={Number(params.organizationId)} />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function ConversionTracking({
  id,
  conversionUrn,
  name,
  organizationId,
}: {
  id: string;
  conversionUrn: string;
  name: string;
  organizationId: number;
}) {
  const apiUtils = api.useUtils();
  const deleteConversionMutation =
    api.v2.ads.admin.deleteOrganizationConversion.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.ads.admin.getOrganizationConversions.invalidate();
        setIsDeleting(false);
      },
    });

  const [isDeleting, setIsDeleting] = useState(false);

  return (
    <div className="flex flex-col gap-4 border px-2 py-4 ">
      <div className="flex items-center justify-between">
        <div className="flex flex-col items-start ">
          <div className="text-lg font-medium">{name}</div>
          <div className="text-sm text-gray-500">{conversionUrn}</div>
        </div>
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              setIsDeleting(true);
              deleteConversionMutation.mutate({
                id: id,
                organizationId: organizationId,
              });
            }}
          >
            {isDeleting ? <Loader2 className="animate-spin" /> : <TrashIcon />}
          </Button>
        </div>
      </div>
    </div>
  );
}

function AddOne({ organizationId }: { organizationId: number }) {
  const apiUtils = api.useUtils();
  const conversionsForAdAccountQuery =
    api.v2.ads.admin.getConversionsForAdAccount.useQuery({
      organizationId: organizationId,
    });

  const [id, setId] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const createConversionMutation =
    api.v2.ads.admin.createOrganizationConversion.useMutation({
      onSuccess: async () => {
        setIsLoading(false);
        await apiUtils.v2.ads.admin.getOrganizationConversions.invalidate();
        setIsOpen(false);
      },
    });

  useEffect(() => {
    if (isOpen) {
      setId(null);
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button>Add One</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add One</DialogTitle>
        </DialogHeader>
        <Select onValueChange={(value) => setId(value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select a conversion" />
          </SelectTrigger>
          <SelectContent>
            {conversionsForAdAccountQuery.data?.elements.map((conversion) => (
              <SelectItem key={conversion.id} value={conversion.id.toString()}>
                {conversion.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button
          disabled={!id || isLoading}
          onClick={() => {
            setIsLoading(true);
            createConversionMutation.mutate({
              conversionUrn: `urn:lla:llaPartnerConversion:${id}`,
              name:
                conversionsForAdAccountQuery.data?.elements.find(
                  (conversion) => conversion.id === Number(id),
                )?.name ?? "",
              organizationId: organizationId,
            });
          }}
        >
          {isLoading ? "Submitting..." : "Submit"}
        </Button>
      </DialogContent>
    </Dialog>
  );
}
