"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";
import { ArrowLeft, Save, TestTube } from "lucide-react";



import { Badge } from "@kalos/ui/badge";
import { But<PERSON> } from "@kalos/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@kalos/ui/card";
import { cn } from "@kalos/ui/index";
import { Input } from "@kalos/ui/input";
import { Label } from "@kalos/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@kalos/ui/select";
import { Switch } from "@kalos/ui/switch";





interface SlackSettingsPageProps {
  params: { organizationId: string };
}

const DAYS_OF_WEEK = [
  { value: 1, label: "Monday" },
  { value: 2, label: "Tuesday" },
  { value: 3, label: "Wednesday" },
  { value: 4, label: "Thursday" },
  { value: 5, label: "Friday" },
  { value: 6, label: "Saturday" },
  { value: 7, label: "Sunday" },
  { value: 8, label: "Everyday" },
];

const FREQUENCY_OPTIONS = [
  { value: "hourly", label: "Hourly" },
  { value: "daily", label: "Daily" },
  { value: "weekly", label: "Weekly" },
  { value: "biweekly", label: "Bi-weekly" },
  { value: "monthly", label: "Monthly" },
];

export default function SlackSettingsPage({ params }: SlackSettingsPageProps) {
  const organizationId = parseInt(params.organizationId);
  const router = useRouter();

  const [webhookUrl, setWebhookUrl] = useState("");
  const [enabled, setEnabled] = useState(true);
  const [dayOfWeek, setDayOfWeek] = useState(1);
  const [frequency, setFrequency] = useState<
    "hourly" | "daily" | "weekly" | "biweekly" | "monthly"
  >("weekly");
  const [leadNotificationsEnabled, setLeadNotificationsEnabled] =
    useState(true);

  // Get current organization data
  const organizationQuery = api.v2.core.admin.getAllOrganizations.useQuery();
  const organization = organizationQuery.data?.find(
    (org) => org.organizationId === organizationId,
  );

  // Get current Slack settings
  const slackSettingsQuery =
    api.v2.core.admin.getOrganizationSlackSettings.useQuery({
      organizationId,
    });

  // Update settings mutation
  const updateSettingsMutation =
    api.v2.core.admin.updateOrganizationSlackSettings.useMutation({
      onSuccess: () => {
        slackSettingsQuery.refetch();
      },
    });

  // Test weekly notification mutation
  const testWeeklyNotificationMutation =
    api.v2.core.admin.sendTestWeeklyNotification.useMutation();

  // Test webhook mutation
  const testWebhookMutation = api.v2.core.admin.testSlackWebhook.useMutation();

  // Test lead notification mutation
  const testLeadNotificationMutation =
    api.v2.core.admin.testLeadNotification.useMutation();

  // Initialize form values when data loads
  useEffect(() => {
    if (slackSettingsQuery.data) {
      setWebhookUrl(slackSettingsQuery.data.slackNotificationWebhookUrl || "");
      setEnabled(slackSettingsQuery.data.slackNotificationsEnabled ?? true);
      setDayOfWeek(slackSettingsQuery.data.slackNotificationDayOfWeek ?? 1);
      setFrequency(
        slackSettingsQuery.data.slackNotificationFrequency ?? "weekly",
      );
      setLeadNotificationsEnabled(
        slackSettingsQuery.data.slackLeadNotificationsEnabled ?? true,
      );
    }
  }, [slackSettingsQuery.data]);

  const handleSave = async () => {
    try {
      await updateSettingsMutation.mutateAsync({
        organizationId,
        webhookUrl: webhookUrl || null,
        enabled,
        dayOfWeek,
        frequency,
        leadNotificationsEnabled,
      });
      // Show success message or redirect
    } catch (error) {
      console.error("Error updating settings:", error);
    }
  };

  const handleTestWebhook = async () => {
    try {
      const result = await testWebhookMutation.mutateAsync({
        organizationId,
      });

      if (result.success) {
        alert("Test message sent successfully! Check your Slack channel.");
      } else {
        alert("Test failed: Unknown error");
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      alert(`Test failed: ${errorMessage}`);
    }
  };

  const handleTestWeeklyNotification = async () => {
    try {
      const result = await testWeeklyNotificationMutation.mutateAsync({
        organizationId,
      });

      if (result.success) {
        alert(
          "Weekly notification test sent successfully! Check your Slack channel for the performance report.",
        );
      } else {
        alert(`Test failed: ${result.error || "Unknown error"}`);
      }
    } catch (error) {
      alert(
        `Test failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  };

  const handleTestLeadNotification = async () => {
    try {
      const result = await testLeadNotificationMutation.mutateAsync({
        organizationId,
      });

      if (result.success) {
        alert(
          `Lead notification test sent successfully! Check your Slack channel.\n\n${result.message}`,
        );
      } else {
        alert("Test failed: Unknown error");
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      alert(`Test failed: ${errorMessage}`);
    }
  };

  const isValidUrl = () => {
    if (!webhookUrl) return true; // Empty is valid (will clear the webhook)
    try {
      new URL(webhookUrl);
      return webhookUrl.includes("hooks.slack.com");
    } catch {
      return false;
    }
  };

  const hasChanges =
    webhookUrl !==
      (slackSettingsQuery.data?.slackNotificationWebhookUrl || "") ||
    enabled !== (slackSettingsQuery.data?.slackNotificationsEnabled ?? true) ||
    dayOfWeek !== (slackSettingsQuery.data?.slackNotificationDayOfWeek ?? 1) ||
    frequency !==
      (slackSettingsQuery.data?.slackNotificationFrequency ?? "weekly") ||
    leadNotificationsEnabled !==
      (slackSettingsQuery.data?.slackLeadNotificationsEnabled ?? true);

  const getDayOfWeekLabel = (day: number) => {
    return DAYS_OF_WEEK.find((d) => d.value === day)?.label || "Monday";
  };

  const getFrequencyLabel = (freq: string) => {
    return FREQUENCY_OPTIONS.find((f) => f.value === freq)?.label || "Weekly";
  };

  // Filter day options based on frequency
  const getAvailableDays = () => {
    if (frequency === "hourly" || frequency === "daily") {
      return DAYS_OF_WEEK; // Include "Everyday" option for hourly and daily
    } else {
      return DAYS_OF_WEEK.filter((day) => day.value !== 8); // Exclude "Everyday" for other frequencies
    }
  };

  // Auto-set day to "Everyday" when hourly or daily is selected
  useEffect(() => {
    if ((frequency === "hourly" || frequency === "daily") && dayOfWeek !== 8) {
      setDayOfWeek(8); // Default to "Everyday" for hourly/daily
    } else if (
      frequency !== "hourly" &&
      frequency !== "daily" &&
      dayOfWeek === 8
    ) {
      setDayOfWeek(1); // Reset to Monday for other frequencies
    }
  }, [frequency, dayOfWeek]);

  return (
    <div className="container mx-auto max-w-4xl p-6">
      <div className="mb-6">
        <Link
          href={`/admin/organization/${organizationId}`}
          className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to {organization?.name || "Organization"}
        </Link>
      </div>

      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Slack Settings</h1>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <Badge
                variant="secondary"
                className={cn(
                  slackSettingsQuery.data?.isConfigured && enabled
                    ? "bg-green-200"
                    : "bg-yellow-200",
                  "gap-2 border",
                )}
              >
                <div
                  className={cn(
                    slackSettingsQuery.data?.isConfigured && enabled
                      ? "bg-green-500"
                      : "bg-yellow-500",
                    "h-3 w-3 rounded-full border",
                  )}
                />
                {slackSettingsQuery.data?.isConfigured && enabled
                  ? "Active"
                  : enabled
                    ? "Not Configured"
                    : "Disabled"}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="webhookUrl">Channel Webhook URL</Label>
              <Input
                id="webhookUrl"
                type="url"
                placeholder="https://hooks.slack.com/services/..."
                value={webhookUrl}
                onChange={(e) => setWebhookUrl(e.target.value)}
                className={cn(
                  !isValidUrl() && "border-red-500 focus:border-red-500",
                )}
              />
              {!isValidUrl() && (
                <p className="text-sm text-red-500">
                  Please enter a valid Channel webhook URL
                </p>
              )}
              <p className="text-sm text-muted-foreground">
                To get a webhook URL, go to your Slack workspace settings → Apps
                → Incoming Webhooks
              </p>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              <div className="space-y-2">
                <Label htmlFor="enabled" className="text-sm font-medium">
                  Insights Notifications
                </Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="enabled"
                    checked={enabled}
                    onCheckedChange={setEnabled}
                  />
                  <Label
                    htmlFor="enabled"
                    className="text-sm text-muted-foreground"
                  >
                    {enabled ? "Enabled" : "Disabled"}
                  </Label>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="frequency" className="text-sm font-medium">
                  Frequency
                </Label>
                <Select
                  value={frequency}
                  onValueChange={(
                    value:
                      | "hourly"
                      | "daily"
                      | "weekly"
                      | "biweekly"
                      | "monthly",
                  ) => setFrequency(value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    {FREQUENCY_OPTIONS.map((freq) => (
                      <SelectItem key={freq.value} value={freq.value}>
                        {freq.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="dayOfWeek" className="text-sm font-medium">
                  Day of Week
                </Label>
                <Select
                  value={dayOfWeek.toString()}
                  onValueChange={(value) => setDayOfWeek(parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select day" />
                  </SelectTrigger>
                  <SelectContent>
                    {getAvailableDays().map((day) => (
                      <SelectItem key={day.value} value={day.value.toString()}>
                        {day.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="leadNotificationsEnabled"
                className="text-sm font-medium"
              >
                15-minute Lead Notifications
              </Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="leadNotificationsEnabled"
                  checked={leadNotificationsEnabled}
                  onCheckedChange={setLeadNotificationsEnabled}
                />
                <Label
                  htmlFor="leadNotificationsEnabled"
                  className="text-sm text-muted-foreground"
                >
                  {leadNotificationsEnabled ? "Enabled" : "Disabled"}
                </Label>
              </div>
            </div>

            <div className="flex gap-3">
              <Button
                onClick={handleSave}
                disabled={
                  !isValidUrl() ||
                  !hasChanges ||
                  updateSettingsMutation.isPending
                }
              >
                <Save className="mr-2 h-4 w-4" />
                {updateSettingsMutation.isPending
                  ? "Saving..."
                  : "Save Settings"}
              </Button>

              <Button
                variant="outline"
                onClick={handleTestWebhook}
                disabled={
                  !webhookUrl ||
                  !isValidUrl() ||
                  !enabled ||
                  testWebhookMutation.isPending
                }
              >
                <TestTube className="mr-2 h-4 w-4" />
                {testWebhookMutation.isPending ? "Testing..." : "Test Webhook"}
              </Button>

              <Button
                variant="outline"
                onClick={handleTestWeeklyNotification}
                disabled={
                  !webhookUrl ||
                  !isValidUrl() ||
                  !enabled ||
                  testWeeklyNotificationMutation.isPending
                }
              >
                <TestTube className="mr-2 h-4 w-4" />
                {testWeeklyNotificationMutation.isPending
                  ? "Testing..."
                  : "Test Weekly Notification"}
              </Button>

              <Button
                variant="outline"
                onClick={handleTestLeadNotification}
                disabled={
                  !webhookUrl ||
                  !isValidUrl() ||
                  !enabled ||
                  testLeadNotificationMutation.isPending
                }
              >
                <TestTube className="mr-2 h-4 w-4" />
                {testLeadNotificationMutation.isPending
                  ? "Testing..."
                  : "Test Lead Notification"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}