"use client";

import type { z } from "zod";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";

import { Card, CardContent, CardHeader, CardTitle } from "@kalos/ui/card";

import type { adminSegmentFormSchema } from "../segmentForm";
import { SegmentForm } from "../segmentForm";

export default function AdminOrganizationSegmentsNewPage({
  params,
}: {
  params: { organizationId: string };
}) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const verticalsQuery = api.v2.core.admin.getVerticals.useQuery({
    organizationId: parseInt(params.organizationId),
  });
  const router = useRouter();
  const apiUtils = api.useUtils();

  const createSegmentMutation = api.v2.core.admin.createSegment.useMutation({
    onSuccess: async () => {
      setIsSubmitting(false);
      await apiUtils.v2.core.admin.invalidate();
      router.push(`/admin/organization/${params.organizationId}/segments`);
    },
  });

  const onSubmit = (values: z.infer<typeof adminSegmentFormSchema>) => {
    console.log(values);
    setIsSubmitting(true);
    createSegmentMutation.mutate({
      organizationId: parseInt(params.organizationId),
      verticals: values.verticals.map((v) => v.name),
      name: values.name,
      annualRevenueLowBound: values.annualRevenueLowBound ?? null,
      annualRevenueHighBound: values.annualRevenueHighBound ?? null,
      numberOfEmployeesLowBound: values.numberOfEmployeesLowBound ?? null,
      numberOfEmployeesHighBound: values.numberOfEmployeesHighBound ?? null,
      jobFunction: values.jobFunction ?? null,
      jobSeniority: values.jobSeniority ?? null,
      subVertical: null,
      annualContractValueHighBound: null,
      annualContractValueLowBound: null,
    });
  };

  return (
    <div className="flex h-full w-full flex-col items-center justify-center">
      <Card className="w-full w-full sm:w-3/4">
        <CardHeader>
          <CardTitle>Create Segment</CardTitle>
        </CardHeader>
        <CardContent>
          <SegmentForm
            verticals={verticalsQuery.data ?? []}
            onSubmit={onSubmit}
            isSubmitting={isSubmitting}
          />
        </CardContent>
      </Card>
    </div>
  );
}
