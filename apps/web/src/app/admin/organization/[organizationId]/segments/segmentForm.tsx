"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Loader2, PlusI<PERSON>, Trash2 } from "lucide-react";
import { useFieldArray, useForm } from "react-hook-form";
import { z } from "zod";

import { <PERSON><PERSON> } from "@kalos/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@kalos/ui/form";
import { Input } from "@kalos/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@kalos/ui/select";

import type { JobFunction } from "../../../../../../../../backend/src/modules/core/domain/valueObjects/jobFunction";
import type { JobSeniority } from "../../../../../../../../backend/src/modules/core/domain/valueObjects/jobSenitory";

export const adminSegmentFormSchema = z.object({
  name: z.string().optional(),
  verticals: z.array(
    z.object({
      name: z.string(),
    }),
  ),
  annualRevenueLowBound: z.number().optional().nullable(),
  annualRevenueHighBound: z.number().optional().nullable(),
  numberOfEmployeesLowBound: z.number().optional().nullable(),
  numberOfEmployeesHighBound: z.number().optional().nullable(),
  jobFunction: z
    .union([
      z.literal("operations"),
      z.literal("compliance"),
      z.literal("risk_managment_and_fruad"),
      z.literal("marketing"),
      z.literal("product_management"),
      z.literal("finance"),
      z.literal("sales"),
      z.literal("data"),
      z.literal("engineering"),
      z.literal("customer_support"),
      z.literal("other"),
      z.literal("unknown"),
    ])
    .optional()
    .nullable(),
  jobSeniority: z
    .enum([
      "entry",
      "senior",
      "manager",
      "director_or_vice_president",
      "cxo",
      "unknown",
    ])
    .optional()
    .nullable(),
});

const useAdminSegmentForm = (
  initState?: z.infer<typeof adminSegmentFormSchema>,
) => {
  const form = useForm<z.infer<typeof adminSegmentFormSchema>>({
    resolver: zodResolver(adminSegmentFormSchema),
    defaultValues: initState
      ? {
          name: initState.name ?? undefined,
          verticals: initState.verticals,
          annualRevenueLowBound: initState.annualRevenueLowBound ?? undefined,
          annualRevenueHighBound: initState.annualRevenueHighBound ?? undefined,
          numberOfEmployeesLowBound:
            initState.numberOfEmployeesLowBound ?? undefined,
          numberOfEmployeesHighBound:
            initState.numberOfEmployeesHighBound ?? undefined,
          jobFunction: initState.jobFunction ?? undefined,
          jobSeniority: initState.jobSeniority ?? undefined,
        }
      : {
          name: "",
          verticals: [],
          annualRevenueLowBound: undefined,
          annualRevenueHighBound: undefined,
          numberOfEmployeesLowBound: undefined,
          numberOfEmployeesHighBound: undefined,
          jobFunction: undefined,
          jobSeniority: undefined,
        },
  });

  return form;
};

export function SegmentForm({
  initState,
  verticals,
  onSubmit,
  isSubmitting,
}: {
  initState?: z.infer<typeof adminSegmentFormSchema>;
  verticals: string[];
  onSubmit: (values: z.infer<typeof adminSegmentFormSchema>) => void;
  isSubmitting: boolean;
}) {
  const form = useAdminSegmentForm(initState);

  const verticalsFieldArray = useFieldArray({
    control: form.control,
    name: "verticals",
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="verticals"
          render={() => (
            <FormItem className="flex flex-col gap-2">
              <FormLabel>Verticals</FormLabel>
              <FormControl>
                <>
                  {verticalsFieldArray.fields.length > 0 && (
                    <div className="flex flex-col gap-2">
                      {verticalsFieldArray.fields.map((field, index) => {
                        return (
                          <div
                            key={field.id}
                            className="flex w-full items-center justify-start gap-2"
                          >
                            <Select
                              key={field.id}
                              onValueChange={(value) => {
                                verticalsFieldArray.update(index, {
                                  name: value,
                                });
                              }}
                              value={field.name}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select verticals" />
                              </SelectTrigger>
                              <SelectContent>
                                {verticals.map((vertical) => (
                                  <SelectItem key={vertical} value={vertical}>
                                    {vertical}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <Button
                              variant="outline"
                              size="icon"
                              type="button"
                              onClick={() => {
                                verticalsFieldArray.remove(index);
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        );
                      })}
                      <div className="flex w-full items-center justify-end gap-2">
                        <Button
                          variant="outline"
                          size="icon"
                          type="button"
                          onClick={() =>
                            verticalsFieldArray.append({ name: "" })
                          }
                        >
                          <PlusIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                  {verticalsFieldArray.fields.length === 0 && (
                    <Button
                      variant="outline"
                      type="button"
                      onClick={() => verticalsFieldArray.append({ name: "" })}
                    >
                      Add Vertical
                    </Button>
                  )}
                </>
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="annualRevenueLowBound"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Annual Revenue Low Bound</FormLabel>
              <FormControl>
                <Input
                  value={field.value ?? undefined}
                  onChange={(e) => {
                    if (e.target.value === "") {
                      form.setValue("annualRevenueLowBound", null);
                    } else {
                      form.setValue(
                        "annualRevenueLowBound",
                        parseInt(e.target.value),
                      );
                    }
                  }}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="annualRevenueHighBound"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Annual Revenue High Bound</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  value={field.value ?? undefined}
                  onChange={(e) =>
                    form.setValue(
                      "annualRevenueHighBound",
                      e.target.value === "" ? null : parseInt(e.target.value),
                    )
                  }
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="numberOfEmployeesLowBound"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Number of Employees Low Bound</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  value={field.value ?? undefined}
                  onChange={(e) =>
                    form.setValue(
                      "numberOfEmployeesLowBound",
                      e.target.value === "" ? null : parseInt(e.target.value),
                    )
                  }
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="numberOfEmployeesHighBound"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Number of Employees High Bound</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  value={field.value ?? undefined}
                  onChange={(e) =>
                    form.setValue(
                      "numberOfEmployeesHighBound",
                      e.target.value === "" ? null : parseInt(e.target.value),
                    )
                  }
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="jobFunction"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Job Function</FormLabel>
              <FormControl>
                <div className="flex items-center justify-start gap-2">
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value as JobFunction);
                    }}
                    value={field.value ?? ""}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select job function" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="operations">Operations</SelectItem>
                      <SelectItem value="compliance">Compliance</SelectItem>
                      <SelectItem value="risk_managment_and_fruad">
                        Risk Management and Fraud
                      </SelectItem>
                      <SelectItem value="marketing">Marketing</SelectItem>
                      <SelectItem value="product_management">
                        Product Management
                      </SelectItem>
                      <SelectItem value="finance">Finance</SelectItem>
                      <SelectItem value="sales">Sales</SelectItem>
                      <SelectItem value="data">Data</SelectItem>
                      <SelectItem value="engineering">Engineering</SelectItem>
                      <SelectItem value="customer_support">
                        Customer Support
                      </SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                      <SelectItem value="unknown">Unknown</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => {
                      field.onChange(null);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                    Clear
                  </Button>
                </div>
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="jobSeniority"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Job Seniority</FormLabel>
              <FormControl>
                <div className="flex items-center justify-start gap-2">
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value as JobSeniority);
                    }}
                    value={field.value ?? ""}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select job function" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="entry">Entry</SelectItem>
                      <SelectItem value="senior">Senior</SelectItem>
                      <SelectItem value="manager">Manager</SelectItem>
                      <SelectItem value="director_or_vice_president">
                        Director or Vice President
                      </SelectItem>
                      <SelectItem value="cxo">CxO</SelectItem>
                      <SelectItem value="unknown">Unknown</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => {
                      field.onChange(null);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                    Clear
                  </Button>
                </div>
              </FormControl>
            </FormItem>
          )}
        />
        <div className="flex w-full items-center justify-end">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Submitting...
              </>
            ) : (
              "Submit"
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
