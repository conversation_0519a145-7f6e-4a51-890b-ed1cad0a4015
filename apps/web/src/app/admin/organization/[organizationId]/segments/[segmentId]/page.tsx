"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";
import { Check, Loader2, Pencil, Plus, Trash2, X } from "lucide-react";

import { Button } from "@kalos/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@kalos/ui/card";
import {
  Di<PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";
import { Input } from "@kalos/ui/input";

export default function AdminOrganizationSegmentsPage({
  params,
}: {
  params: { organizationId: string; segmentId: string };
}) {
  const segmentQuery = api.v2.core.admin.getSegment.useQuery({
    segmentId: params.segmentId,
  });

  const valuePropsQuery = api.v2.core.admin.getValuePropsForSegment.useQuery({
    segmentId: params.segmentId,
  });

  const router = useRouter();
  return (
    <div className="flex h-full w-full flex-col items-center justify-center gap-4">
      <Card className="w-full w-full sm:w-3/4">
        <CardHeader>
          <CardTitle className="flex w-full items-center justify-between">
            Segment Details
            <Button
              variant={"outline"}
              onClick={() => {
                router.push(
                  `/admin/organization/${params.organizationId}/segments/${params.segmentId}/edit`,
                );
              }}
            >
              Edit
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            {segmentQuery.data?.verticals &&
              segmentQuery.data.verticals.length > 0 && (
                <div className="flex flex-col">
                  <h2 className="text-xs font-medium">Verticals</h2>
                  <h3 className="text-base font-medium">
                    {segmentQuery.data?.verticals.join(", ")}
                  </h3>
                </div>
              )}
            {segmentQuery.data?.annualRevenueLowBound && (
              <div className="flex flex-col">
                <h2 className="text-xs font-medium">
                  Annual Revenue Low Bound
                </h2>
                <h3 className="text-base font-medium">
                  {segmentQuery.data?.annualRevenueLowBound}
                </h3>
              </div>
            )}
            {segmentQuery.data?.annualRevenueHighBound && (
              <div className="flex flex-col">
                <h2 className="text-xs font-medium">
                  Annual Revenue High Bound
                </h2>
                <h3 className="text-base font-medium">
                  {segmentQuery.data?.annualRevenueHighBound}
                </h3>
              </div>
            )}
            {segmentQuery.data?.numberOfEmployeesLowBound && (
              <div className="flex flex-col">
                <h2 className="text-xs font-medium">
                  Number of Employees Low Bound
                </h2>
                <h3 className="text-base font-medium">
                  {segmentQuery.data?.numberOfEmployeesLowBound}
                </h3>
              </div>
            )}
            {segmentQuery.data?.numberOfEmployeesHighBound && (
              <div className="flex flex-col">
                <h2 className="text-xs font-medium">
                  Number of Employees High Bound
                </h2>
                <h3 className="text-base font-medium">
                  {segmentQuery.data?.numberOfEmployeesHighBound}
                </h3>
              </div>
            )}
            {segmentQuery.data?.jobFunction && (
              <div className="flex flex-col">
                <h2 className="text-xs font-medium">Job Function</h2>
                <h3 className="text-base font-medium">
                  {segmentQuery.data?.jobFunction}
                </h3>
              </div>
            )}
            {segmentQuery.data?.jobSeniority && (
              <div className="flex flex-col">
                <h2 className="text-xs font-medium">Job Seniority</h2>
                <h3 className="text-base font-medium">
                  {segmentQuery.data?.jobSeniority}
                </h3>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      <Card className="w-full w-full sm:w-3/4">
        <CardHeader>
          <CardTitle className="flex w-full items-center justify-between">
            Value Props
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            {valuePropsQuery.data
              ?.sort((a, b) => a.id.localeCompare(b.id))
              .map((valueProp, index) => (
                <ValuePropCard
                  key={valueProp.id}
                  valueProp={valueProp}
                  index={index}
                />
              ))}
          </div>
          <div className="flex justify-end py-8">
            <CreateValuePropDialog segmentId={params.segmentId} />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function CreateValuePropDialog({ segmentId }: { segmentId: string }) {
  const apiUtils = api.useUtils();
  const createValuePropMutation =
    api.v2.core.admin.createSegmentValueProp.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.core.admin.invalidate();
        setIsCreating(false);
        setIsOpen(false);
      },
    });

  const [valueProp, setValueProp] = useState("");

  const [isOpen, setIsOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  useEffect(() => {
    if (!isOpen) {
      setValueProp("");
    }
  }, [isOpen]);

  const handleCreateValueProp = () => {
    setIsCreating(true);
    createValuePropMutation.mutate({
      segmentId: segmentId,
      name: valueProp,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className="w-full">
          <Plus className="h-5 w-5" />
          Add Value Prop
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Value Prop</DialogTitle>
        </DialogHeader>
        <Input
          placeholder="Value Prop"
          value={valueProp}
          onChange={(e) => setValueProp(e.target.value)}
        />
        <DialogFooter>
          <Button onClick={handleCreateValueProp} disabled={isCreating}>
            {isCreating ? <Loader2 className="h-4 w-4 animate-spin" /> : "Add"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

function ValuePropCard({
  valueProp,
  index,
}: {
  valueProp: {
    id: string;
    name: string;
  };
  index: number;
}) {
  const [isDeleting, setIsDeleting] = useState(false);
  const apiUtils = api.useUtils();
  const deleteValuePropMutation =
    api.v2.core.admin.deleteSegmentValueProp.useMutation({
      onSuccess: () => {
        apiUtils.v2.core.admin.invalidate();
        setIsDeleting(false);
      },
    });

  const updateValuePropMutation =
    api.v2.core.admin.editSegmentValueProp.useMutation({
      onSuccess: () => {
        apiUtils.v2.core.admin.invalidate();
        setIsEditing(false);
      },
    });
  const [name, setName] = useState(valueProp.name);

  const [isEditing, setIsEditing] = useState(false);
  return (
    <div key={valueProp.id} className="flex flex-col">
      <h2 className=" flex w-full items-center justify-between font-medium">
        {index + 1}.{" "}
        {isEditing ? (
          <Input value={name} onChange={(e) => setName(e.target.value)} />
        ) : (
          valueProp.name
        )}
        {!isEditing && (
          <div className="flex items-center gap-2">
            <Button
              variant={"outline"}
              size={"icon"}
              className="h-5 w-5"
              onClick={() => setIsEditing(true)}
            >
              <Pencil className="h-5 w-5" />
            </Button>
            <Button
              variant={"outline"}
              size={"icon"}
              className="h-5 w-5"
              disabled={isDeleting}
              onClick={() => {
                setIsDeleting(true);
                deleteValuePropMutation.mutate({ valuePropId: valueProp.id });
              }}
            >
              {isDeleting ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <Trash2 className="h-5 w-5" />
              )}
            </Button>
          </div>
        )}
        {isEditing && (
          <div className="flex items-center gap-2">
            <Button
              variant={"outline"}
              size={"icon"}
              className="h-5 w-5"
              onClick={() => setIsEditing(false)}
            >
              <X className="h-5 w-5" />
            </Button>
            <Button
              variant={"outline"}
              size={"icon"}
              className="h-5 w-5"
              onClick={() => {
                updateValuePropMutation.mutate({
                  valuePropId: valueProp.id,
                  name,
                });
              }}
            >
              <Check className="h-5 w-5" />
            </Button>
          </div>
        )}
      </h2>
    </div>
  );
}
