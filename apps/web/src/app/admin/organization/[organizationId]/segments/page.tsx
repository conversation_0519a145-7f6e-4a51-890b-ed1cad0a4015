"use client";

import type { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";
import { api } from "@/trpc/client";
import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { PlusIcon, Trash2 } from "lucide-react";

import { Badge } from "@kalos/ui/badge";
import { Button } from "@kalos/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";
import { cn } from "@kalos/ui/index";
import { DataTable } from "@kalos/ui/table/datatable";

/*
export type GetSegmentsForOrganizationResponseDto = {

}[];
*/
export default function AdminOrganizationSegmentsPage({
  params,
}: {
  params: { organizationId: string };
}) {
  const segmentsQuery = api.v2.core.admin.getSegmentsForOrganization.useQuery({
    organizationId: parseInt(params.organizationId),
  });
  const x = segmentsQuery.data;

  const columns: ColumnDef<{
    id: string;
    name?: string | null;
    verticals: string[];
    annualRevenueLowBound?: number | null;
    annualRevenueHighBound?: number | null;
    numberOfEmployeesLowBound?: number | null;
    numberOfEmployeesHighBound?: number | null;
    annualContractValueLowBound?: number | null;
    annualContractValueHighBound?: number | null;
    jobFunction?: string | null;
    jobSeniority?: string | null;
    status: "GENERATING" | "ACTIVE" | "ARCHIVED";
  }>[] = [
    {
      header: "Name",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-2">
            <Link
              href={`/admin/organization/${params.organizationId}/segments/${row.original.id}`}
            >
              {row.original.name}
            </Link>
          </div>
        );
      },
    },
    {
      header: "Status",
      cell: ({ row }) => {
        return (
          <Badge
            variant={"secondary"}
            className={cn(
              row.original.status === "ACTIVE" && "bg-green-200",
              row.original.status === "GENERATING" && "bg-yellow-200",
              row.original.status === "ARCHIVED" && "bg-red-200",
              "gap-2 border",
            )}
          >
            <div
              className={cn(
                row.original.status === "ACTIVE" && "bg-green-500",
                row.original.status === "GENERATING" && "bg-yellow-500",
                row.original.status === "ARCHIVED" && "bg-red-500",
                "h-3 w-3 rounded-full border",
              )}
            ></div>
            {row.original.status}
          </Badge>
        );
      },
    },
    {
      header: "Actions",
      cell: ({ row }) => {
        const apiUtils = api.useUtils();
        const deleteSegmentMutation =
          api.v2.core.admin.updateSegmentStatus.useMutation({
            onMutate: async ({ segmentId, status }) => {
              // Cancel outgoing refetches
              await apiUtils.v2.core.admin.getSegmentsForOrganization.cancel();

              // Snapshot the previous value
              const previousSegments =
                apiUtils.v2.core.admin.getSegmentsForOrganization.getData({
                  organizationId: parseInt(params.organizationId),
                });

              // Optimistically update the segments
              apiUtils.v2.core.admin.getSegmentsForOrganization.setData(
                { organizationId: parseInt(params.organizationId) },
                (old) =>
                  old?.map((segment) =>
                    segment.id === segmentId ? { ...segment, status } : segment,
                  ),
              );

              return { previousSegments };
            },
            onError: (err, variables, context) => {
              // If the mutation fails, roll back to the previous value
              if (context?.previousSegments) {
                apiUtils.v2.core.admin.getSegmentsForOrganization.setData(
                  { organizationId: parseInt(params.organizationId) },
                  context.previousSegments,
                );
              }
            },
            onSettled: () => {
              // Always refetch after error or success to ensure data consistency
              apiUtils.v2.core.admin.getSegmentsForOrganization.invalidate();
            },
          });
        return (
          <Dialog>
            <DialogTrigger asChild>
              <Button variant={"ghost"} size={"icon"} className="h-4 w-4">
                <Trash2 className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  Are you sure you want to delete this segment?
                </DialogTitle>
              </DialogHeader>
              <div className="flex items-center justify-end gap-2">
                <Button variant={"secondary"}>Cancel</Button>
                <Button
                  variant={"destructive"}
                  onClick={() => {
                    deleteSegmentMutation.mutate({
                      segmentId: row.original.id,
                      status: "ARCHIVED",
                    });
                  }}
                >
                  Delete
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        );
      },
    },
  ];

  const table = useReactTable({
    data: segmentsQuery.data ?? [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="flex h-full w-full flex-col justify-start">
      <div className="sticky inset-x-0 top-0 flex h-16 w-full items-center justify-between border-b bg-white px-6">
        <h1 className="text-xl font-medium">Segments</h1>
        <Link
          href={`/admin/organization/${params.organizationId}/segments/new`}
        >
          <Button>
            <PlusIcon className="mr-2 h-4 w-4" /> New Segment
          </Button>
        </Link>
      </div>
      <div className="h-full w-full overflow-auto bg-background px-6 pt-8">
        <DataTable columns={columns} table={table} noHover={false} />
      </div>
    </div>
  );
}
