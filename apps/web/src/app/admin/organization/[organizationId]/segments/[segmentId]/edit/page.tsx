"use client";

import type { z } from "zod";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";

import { Card, CardContent, CardHeader, CardTitle } from "@kalos/ui/card";

import type { adminSegmentFormSchema } from "../../segmentForm";
import { SegmentForm } from "../../segmentForm";

export default function AdminOrganizationSegmentsEditPage({
  params,
}: {
  params: { organizationId: string; segmentId: string };
}) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const verticalsQuery = api.v2.core.admin.getVerticals.useQuery({
    organizationId: parseInt(params.organizationId),
  });
  const router = useRouter();
  const apiUtils = api.useUtils();

  const segmentQuery = api.v2.core.admin.getSegment.useQuery({
    segmentId: params.segmentId,
  });

  const updateSegmentMutation = api.v2.core.admin.editSegment.useMutation({
    onSuccess: async () => {
      setIsSubmitting(false);
      await apiUtils.v2.core.admin.invalidate();
      router.push(`/admin/organization/${params.organizationId}/segments`);
    },
  });

  const onSubmit = (values: z.infer<typeof adminSegmentFormSchema>) => {
    console.log(values);
    setIsSubmitting(true);
    updateSegmentMutation.mutate({
      organizationId: parseInt(params.organizationId),
      id: params.segmentId,
      verticals: values.verticals.map((v) => v.name),
      name: values.name,
      annualRevenueLowBound: values.annualRevenueLowBound ?? null,
      annualRevenueHighBound: values.annualRevenueHighBound ?? null,
      numberOfEmployeesLowBound: values.numberOfEmployeesLowBound ?? null,
      numberOfEmployeesHighBound: values.numberOfEmployeesHighBound ?? null,
      jobFunction: values.jobFunction ?? null,
      jobSeniority: values.jobSeniority ?? null,
      subVertical: null,
      annualContractValueHighBound: null,
      annualContractValueLowBound: null,
    });
  };

  return (
    <div className="flex h-full w-full flex-col items-center justify-center">
      <Card className="w-full w-full sm:w-3/4">
        <CardHeader>
          <CardTitle>Edit Segment</CardTitle>
        </CardHeader>
        <CardContent>
          {segmentQuery.data && (
            <SegmentForm
              verticals={verticalsQuery.data ?? []}
              onSubmit={onSubmit}
              isSubmitting={isSubmitting}
              initState={{
                verticals:
                  segmentQuery.data?.verticals.map((v) => ({
                    name: v,
                  })) ?? [],
                name: segmentQuery.data?.name ?? "",
                annualRevenueLowBound:
                  segmentQuery.data?.annualRevenueLowBound ?? undefined,
                annualRevenueHighBound:
                  segmentQuery.data?.annualRevenueHighBound ?? undefined,
                numberOfEmployeesLowBound:
                  segmentQuery.data?.numberOfEmployeesLowBound ?? undefined,
                numberOfEmployeesHighBound:
                  segmentQuery.data?.numberOfEmployeesHighBound ?? undefined,
                jobFunction: segmentQuery.data?.jobFunction ?? undefined,
                jobSeniority: segmentQuery.data?.jobSeniority ?? undefined,
              }}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
