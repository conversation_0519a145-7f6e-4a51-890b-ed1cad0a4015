import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import Link from "next/link";
import { TRPCReactProvider } from "@/trpc/provider";
import { auth } from "@clerk/nextjs/server";
import { GlobeIcon, SwitchIcon } from "@radix-ui/react-icons";
import { BuildingIcon, CalculatorIcon, LightbulbIcon } from "lucide-react";

import CompanyIcon from "@kalos/ui/icons/comapny-icon";
import { NavBar } from "@kalos/ui/navbar";

import { FeatureFlag } from "../utils/feature-flag";

export const metadata: Metadata = {
  title: "Kalos",
  description: "",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { userId } = auth();
  if (!userId) {
    return null;
  } else if (
    userId !== "user_2gvKecrxU1ugrxInkMeeqc4KJsp" &&
    userId !== "user_2hpfJ7nV9VwkpT8o4pmV3vkLsd0" &&
    userId !== "user_2p2YdEOYsl2oCmgvKrj8AWAf5Td" &&
    userId !== "user_2xmOIJw6DG9Xs3koIfkJXhsrfT3" &&
    userId !== "user_2w3C3bW4CfKzD2TSka2dUFYt9pu" &&
    userId !== "user_2xdve9cKqaPnMFF8Fr3rt9etjDG" &&
    userId !== "user_2xdvevtuXmoks4qguWUaOSHhmL7" &&
    userId !== "user_2yI7bjxJLeRjphdosN4lAfjhcGq"
  ) {
    return null;
  }
  return <TRPCReactProvider>{children}</TRPCReactProvider>;
}
