"use client";

import { useState } from "react";
import { api } from "@/trpc/client";

import { Button } from "@kalos/ui/button";
import { Card, CardDescription, CardHeader, CardTitle } from "@kalos/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@kalos/ui/select";

export default function SwitchMePage() {
  const apiUtils = api.useUtils();
  const myOrg = api.v2.core.user.getUser.useQuery();
  const organizations = api.v2.core.admin.getAllOrganizations.useQuery();
  const switchMe = api.v2.core.admin.switchOrganization.useMutation({
    onSuccess: async () => {
      await apiUtils.invalidate();
      alert("Organization switched");
    },
    onError: () => {
      alert("Failed to switch organization");
    },
  });
  const [selectedOrganizationId, setSelectedOrganizationId] = useState<
    number | null
  >(null);

  return (
    <div className="flex h-full w-full items-center justify-center">
      <Card className="w-full max-w-md p-4">
        <CardHeader>
          <CardTitle>Switch Organization</CardTitle>
          <CardDescription>
            You are currently in{" "}
            {
              organizations.data?.find(
                (each) => each.organizationId == myOrg.data?.organizationId,
              )?.name
            }
          </CardDescription>
        </CardHeader>
        <Select
          onValueChange={(value) => setSelectedOrganizationId(Number(value))}
          defaultValue={selectedOrganizationId?.toString()}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select an organization" />
          </SelectTrigger>
          <SelectContent>
            {organizations.data?.map((organization) => (
              <SelectItem value={organization.organizationId.toString()}>
                {organization.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <div className="flex justify-end">
          <Button
            disabled={!selectedOrganizationId}
            onClick={() => {
              if (
                selectedOrganizationId &&
                !isNaN(selectedOrganizationId) &&
                selectedOrganizationId > 0
              ) {
                switchMe.mutate({ organizationId: selectedOrganizationId });
              }
            }}
          >
            Switch
          </Button>
        </div>
      </Card>
    </div>
  );
}
