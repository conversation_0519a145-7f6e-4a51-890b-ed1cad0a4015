"use client";

import { useMemo, useState } from "react";

import { api } from "../../../trpc/client";

type TimeRange = "1h" | "6h" | "24h" | "7d" | "30d";
type Severity = "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";

const severityColors = {
  CRITICAL: "bg-red-100 text-red-800 border-red-200",
  HIGH: "bg-orange-100 text-orange-800 border-orange-200",
  MEDIUM: "bg-yellow-100 text-yellow-800 border-yellow-200",
  LOW: "bg-blue-100 text-blue-800 border-blue-200",
};

const severityIcons = {
  CRITICAL: "🚨",
  HIGH: "⚠️",
  MEDIUM: "🔶",
  LOW: "ℹ️",
};

export default function ErrorLogsPage() {
  const [timeRange, setTimeRange] = useState<TimeRange>("24h");
  const [selectedSeverity, setSelectedSeverity] = useState<
    Severity | undefined
  >();
  const [selectedErrorType, setSelectedErrorType] = useState<
    string | undefined
  >();
  const [selectedOrgId, setSelectedOrgId] = useState<number | undefined>();
  const [selectedStatusCode, setSelectedStatusCode] = useState<
    string | undefined
  >();
  const [currentPage, setCurrentPage] = useState(0);
  const pageSize = 20;

  // Memoize the date range calculation to prevent infinite re-fetching
  const dateRange = useMemo(() => {
    const endDate = new Date();
    const startDate = new Date(Date.now() - getTimeRangeMs(timeRange));
    return { startDate, endDate };
  }, [timeRange]);

  // Get error stats for the dashboard
  const { data: stats, isLoading: statsLoading } =
    api.v2.shared.errorLogs.getErrorStats.useQuery({
      timeRange,
      organizationId: selectedOrgId,
    });

  // Get error logs with filters
  const { data: errorLogsData, isLoading: logsLoading } =
    api.v2.shared.errorLogs.getErrorLogs.useQuery({
      severity: selectedSeverity,
      errorType: selectedErrorType,
      organizationId: selectedOrgId,
      statusCode: selectedStatusCode,
      offset: currentPage * pageSize,
      limit: pageSize,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
    });

  function getTimeRangeMs(range: TimeRange): number {
    switch (range) {
      case "1h":
        return 60 * 60 * 1000;
      case "6h":
        return 6 * 60 * 60 * 1000;
      case "24h":
        return 24 * 60 * 60 * 1000;
      case "7d":
        return 7 * 24 * 60 * 60 * 1000;
      case "30d":
        return 30 * 24 * 60 * 60 * 1000;
    }
  }

  function formatDate(date: Date): string {
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(new Date(date));
  }

  if (statsLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="mb-6 h-8 w-1/4 rounded bg-gray-200"></div>
          <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 rounded bg-gray-200"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-7xl p-6">
      <div className="mb-6">
        <h1 className="mb-2 text-3xl font-bold text-gray-900">
          Error Logs Dashboard
        </h1>
        <p className="text-gray-600">
          Monitor and analyze system errors across all services
        </p>
      </div>

      {/* Time Range Selector */}
      <div className="mb-6">
        <label className="mb-2 block text-sm font-medium text-gray-700">
          Time Range
        </label>
        <div className="flex space-x-2">
          {(["1h", "6h", "24h", "7d", "30d"] as TimeRange[]).map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`rounded-md px-3 py-2 text-sm font-medium ${
                timeRange === range
                  ? "bg-blue-600 text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-4">
          <div className="rounded-lg border bg-white p-6 shadow">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
                  📊
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">
                  Total Errors
                </p>
                <p className="text-2xl font-semibold text-gray-900">
                  {stats.totalErrors}
                </p>
              </div>
            </div>
          </div>

          <div className="rounded-lg border bg-white p-6 shadow">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-red-100">
                  🚨
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Critical</p>
                <p className="text-2xl font-semibold text-red-600">
                  {stats.severityBreakdown.critical}
                </p>
              </div>
            </div>
          </div>

          <div className="rounded-lg border bg-white p-6 shadow">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-orange-100">
                  ⚠️
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">High</p>
                <p className="text-2xl font-semibold text-orange-600">
                  {stats.severityBreakdown.high}
                </p>
              </div>
            </div>
          </div>

          <div className="rounded-lg border bg-white p-6 shadow">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-yellow-100">
                  🔶
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">
                  Medium + Low
                </p>
                <p className="text-2xl font-semibold text-yellow-600">
                  {stats.severityBreakdown.medium + stats.severityBreakdown.low}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="mb-6 rounded-lg border bg-white p-4 shadow">
        <h3 className="mb-4 text-lg font-medium text-gray-900">Filters</h3>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Severity
            </label>
            <select
              value={selectedSeverity || ""}
              onChange={(e) =>
                setSelectedSeverity((e.target.value as Severity) || undefined)
              }
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
            >
              <option value="">All Severities</option>
              <option value="CRITICAL">Critical</option>
              <option value="HIGH">High</option>
              <option value="MEDIUM">Medium</option>
              <option value="LOW">Low</option>
            </select>
          </div>

          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Error Type
            </label>
            <select
              value={selectedErrorType || ""}
              onChange={(e) =>
                setSelectedErrorType(e.target.value || undefined)
              }
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
            >
              <option value="">All Types</option>
              {stats &&
                Object.keys(stats.errorsByType).map((type) => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
            </select>
          </div>

          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Status Code
            </label>
            <select
              value={selectedStatusCode || ""}
              onChange={(e) =>
                setSelectedStatusCode(e.target.value || undefined)
              }
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
            >
              <option value="">All Status Codes</option>
              <option value="200">200 - OK</option>
              <option value="201">201 - Created</option>
              <option value="400">400 - Bad Request</option>
              <option value="401">401 - Unauthorized</option>
              <option value="403">403 - Forbidden</option>
              <option value="404">404 - Not Found</option>
              <option value="429">429 - Rate Limited</option>
              <option value="500">500 - Internal Error</option>
              <option value="502">502 - Bad Gateway</option>
              <option value="503">503 - Service Unavailable</option>
            </select>
          </div>

          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Organization ID
            </label>
            <input
              type="number"
              value={selectedOrgId || ""}
              onChange={(e) =>
                setSelectedOrgId(
                  e.target.value ? parseInt(e.target.value) : undefined,
                )
              }
              placeholder="All Organizations"
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
            />
          </div>
        </div>
      </div>

      {/* Error Logs Table */}
      <div className="rounded-lg border bg-white shadow">
        <div className="border-b border-gray-200 px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900">Recent Errors</h3>
        </div>

        {logsLoading ? (
          <div className="p-6">
            <div className="animate-pulse space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 rounded bg-gray-200"></div>
              ))}
            </div>
          </div>
        ) : errorLogsData?.errorLogs.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            No errors found for the selected filters.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Severity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Title
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Organization
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Status Code
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Details
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {errorLogsData?.errorLogs.map((error) => (
                  <tr key={error.id} className="hover:bg-gray-50">
                    <td className="whitespace-nowrap px-6 py-4">
                      <span
                        className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium ${
                          severityColors[error.severity as Severity] ||
                          severityColors.MEDIUM
                        }`}
                      >
                        {severityIcons[error.severity as Severity]}{" "}
                        {error.severity}
                      </span>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                      {error.errorType}
                    </td>
                    <td className="max-w-xs truncate px-6 py-4 text-sm text-gray-900">
                      {error.title}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {error.organizationId || "System"}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {error.statusCode || "-"}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {formatDate(error.createdAt)}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      <details className="cursor-pointer">
                        <summary className="text-blue-600 hover:text-blue-800">
                          View Details
                        </summary>
                        <div className="mt-2 rounded bg-gray-50 p-3 text-xs">
                          <p>
                            <strong>Description:</strong> {error.description}
                          </p>
                          {error.endpoint && (
                            <p>
                              <strong>Endpoint:</strong> {error.endpoint}
                            </p>
                          )}
                          {error.method && (
                            <p>
                              <strong>Method:</strong> {error.method}
                            </p>
                          )}
                          {error.statusCode && (
                            <p>
                              <strong>Status Code:</strong> {error.statusCode}
                            </p>
                          )}
                          {!!error.metadata && (
                            <div>
                              <strong>Metadata:</strong>
                              <pre className="mt-1 overflow-x-auto rounded bg-gray-100 p-2 text-xs">
                                {JSON.stringify(error.metadata as any, null, 2)}
                              </pre>
                            </div>
                          )}
                        </div>
                      </details>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {errorLogsData && errorLogsData.errorLogs.length > 0 && (
          <div className="flex items-center justify-between border-t border-gray-200 px-6 py-4">
            <div className="text-sm text-gray-700">
              Showing {currentPage * pageSize + 1} to{" "}
              {Math.min(
                (currentPage + 1) * pageSize,
                currentPage * pageSize + errorLogsData.errorLogs.length,
              )}{" "}
              results
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                disabled={currentPage === 0}
                className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={!errorLogsData.pagination.hasMore}
                className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}