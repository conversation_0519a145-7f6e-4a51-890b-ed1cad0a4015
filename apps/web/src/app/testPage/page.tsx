"use client";

import type { DragEndEvent } from "@dnd-kit/core";
import { useEffect, useState } from "react";
import { DndContext } from "@dnd-kit/core";

import { Draggable } from "./Draggable";
import { Droppable } from "./Droppable";

export default function TestPage() {
  return (
    <div>
      <Test />
      <Test />
    </div>
  );
}
function Test() {
  const [audienceTargets, setAudienceTargets] = useState<
    { groupId: string; audienceTargetId: string; audienceTargetName: string }[]
  >([
    { groupId: "1", audienceTargetId: "a", audienceTargetName: "VP Product" },
    { groupId: "2", audienceTargetId: "b", audienceTargetName: "Banking" },
    { groupId: "1", audienceTargetId: "c", audienceTargetName: "CEO" },
  ]);

  const [audienceTargetGroups, setAudienceTargetGroups] = useState<string[]>(
    [],
  );

  const [draggables, setDraggables] = useState(
    audienceTargets.map((target) => (
      <Draggable id={target.audienceTargetId}>
        {target.audienceTargetName}
      </Draggable>
    )),
  );

  useEffect(() => {
    console.log(audienceTargets);
    setAudienceTargetGroups([
      ...new Set(audienceTargets.map((group) => group.groupId)),
    ]);
    setDraggables(
      audienceTargets.map((target) => (
        <Draggable id={target.audienceTargetId}>
          {target.audienceTargetName}
        </Draggable>
      )),
    );
  }, [audienceTargets]);

  return (
    <div className="flex h-screen flex-col items-center justify-center">
      <DndContext onDragEnd={handleDragEnd}>
        {audienceTargetGroups
          .sort((a, b) => (a > b ? -1 : 1))
          .map((group) => {
            return (
              <Droppable id={group}>
                {group}
                {draggables
                  .filter(
                    (each) =>
                      audienceTargets.find(
                        (target) => target.audienceTargetId === each.props.id,
                      )?.groupId === group,
                  )
                  .map((each) => {
                    return each;
                  })}
              </Droppable>
            );
          })}
      </DndContext>
    </div>
  );
  /*

        {!isDropped ? draggableMarkup : null}
<Droppable id="droppable1">
          {isDropped ? draggableMarkup : "Drop here"}
        </Droppable>
        <Droppable id="droppable2">
          {isDropped ? draggableMarkup : "Drop here"}
        </Droppable>
*/
  function handleDragEnd(event: DragEndEvent) {
    if (event.over) {
      const audienceTargetId = event.active.id;
      const groupId = event.over.id;
      const audienceTarget = audienceTargets.find(
        (target) => target.audienceTargetId === audienceTargetId,
      );

      const otherAudienceTargets = audienceTargets.filter(
        (target) => target.audienceTargetId !== audienceTargetId,
      );

      if (audienceTarget) {
        const updatedAudienceTarget = {
          ...audienceTarget,
          groupId: groupId.toString(),
        };
        console.log("setting audience targets");

        setAudienceTargets([...otherAudienceTargets, updatedAudienceTarget]);
      }
    }
  }
}
