"use client";

export default function Home() {
  const handleSignIn = () => {
    window.location.href = LINKEDIN_URL;
  };

  return (
    <main className="flex min-h-screen flex-col items-center justify-between p-24">
      <button
        onClick={handleSignIn}
        style={{ backgroundColor: "white", color: "black", padding: 10 }}
      >
        Login with LinkedIn
      </button>
    </main>
  );
}
const getURLWithQueryParams = (base: string, params: Object) => {
  const query = Object.entries(params)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join("&");
  console.log(`${base}?${query}`);
  return `${base}?${query}`;
};

const baseUrl = process.env.NEXT_PUBLIC_VERCEL_URL;

const LINKEDIN_URL = getURLWithQueryParams(
  "https://www.linkedin.com/oauth/v2/authorization",
  {
    response_type: "code",
    client_id: "86yfe63sb37g1s",

    redirect_uri: `https://${baseUrl}/api/oauth2/linkedin/callback`,
    scope:
      "rw_ads w_organization_social r_organization_social w_member_social r_ads",
  },
);
