import { redirect } from "next/navigation";

export function getBaseUrl() {
  if (process.env.NEXT_PUBLIC_VERCEL_ENV === "production") {
    return "https://app.getkalos.com";
  }
  if (process.env.NEXT_PUBLIC_VERCEL_BRANCH_URL) {
    return `https://${process.env.NEXT_PUBLIC_VERCEL_BRANCH_URL}`;
  }
  if (typeof window !== "undefined") {
    return window.location.origin;
  }
  return `http://localhost:${process.env.PORT ?? 3000}`;
}

const redirectUri = getBaseUrl() + "/api/oauth2/hubspot/callback";

export default function HubspotLogin() {
  const CLIENT_ID = process.env.HUBSPOT_CLIENT_ID;
  if (!CLIENT_ID) {
    throw new Error("HUBSPOT_CLIENT_ID is not set");
  }
  const CLIENT_SECRET = process.env.HUBSPOT_CLIENT_SECRET;
  if (!CLIENT_SECRET) {
    throw new Error("HUBSPOT_CLIENT_SECRET is not set");
  }
  const scopes = [
    "crm.objects.companies.read",
    "crm.objects.contacts.read",
    "crm.objects.deals.read",
    "crm.objects.leads.read",
    "crm.objects.owners.read",
    "crm.objects.users.read",
    "crm.schemas.companies.read",
    "crm.schemas.contacts.read",
    "crm.schemas.deals.read",
    "oauth",
  ];
  const authUrl =
    "https://app.hubspot.com/oauth/authorize" +
    `?client_id=${encodeURIComponent(CLIENT_ID)}` +
    `&scope=${encodeURIComponent(scopes.join(" "))}` +
    `&redirect_uri=${encodeURIComponent(redirectUri)}`;
  return redirect(authUrl);
}
