"use client";

import Link from "next/link";
import ConnectIntegrations from "@/features/organization/onbaording/components/connect-integrations";

import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
} from "@kalos/ui/accordion";
import { Button } from "@kalos/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@kalos/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";

export default function Page() {
  return (
    <div className="flex h-full w-full flex-col items-center justify-start bg-background py-4 pb-4">
      <div className=" w-full space-y-3 py-7 text-center">
        <h1 className="text-2xl font-medium">Connect Integrations</h1>
      </div>
      <ConnectIntegrations />
    </div>
  );
}
