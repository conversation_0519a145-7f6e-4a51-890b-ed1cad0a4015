import Link from "next/link";
import { OrganizationIsOnboardedBoundry } from "@/features/organization/components/organization-is-onboarded-boundry";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <OrganizationIsOnboardedBoundry onboardStatus="isNotOnboarded">
      <div className="flex h-full w-full flex-col justify-start">
        <div className="sticky inset-x-0 top-0 flex h-16 w-full items-center justify-between border-b bg-white px-6">
          <div className="flex h-16 w-full items-center justify-center px-4">
            <div className="flex flex-shrink-0 items-center justify-start gap-0.5 text-2xl font-medium">
              <div className="flex h-[30px] w-[30px]  items-center justify-center rounded-md bg-primary text-background">
                K
              </div>
              <h1 className="foreground">alos</h1>
            </div>
          </div>
        </div>
        {children}
      </div>
    </OrganizationIsOnboardedBoundry>
  );
}
