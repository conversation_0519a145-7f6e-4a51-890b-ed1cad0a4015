"use client";

import { Fragment, useEffect, useState } from "react";
import { api } from "@/trpc/client";
import { getBaseUrl } from "@/trpc/provider";
import { z } from "zod";

export default function Tst() {
  const [adCopy, setAdCopy] = useState<{
    title: string;
    body: string;
    done: boolean;
  }>({
    title: "",
    body: "",
    done: false,
  });
  async function getAdCopy() {
    const response = await fetch(`${getBaseUrl()}/stream`, {
      method: "GET",
      headers: {
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json",
      },
    });
    if (!response.ok || !response.body) {
      throw response.statusText;
    }

    // Here we start prepping for the streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    const loopRunner = true;

    while (loopRunner) {
      // Here we start reading the stream, until its done.
      const { value, done } = await reader.read();
      if (done) {
        break;
      }
      const decodedChunk = decoder
        .decode(value, { stream: true })
        .split("!JSON_LINE_END!");
      for (const each of decodedChunk) {
        try {
          const json = JSON.parse(each);
          setAdCopy((prev) => ({
            title: prev.title + json.title,
            body: prev.body + json.body,
            done: prev.done + json.done,
          }));
        } catch (e) {}
      }
    }
  }

  useEffect(() => {
    getAdCopy();
  }, []);

  return (
    <div>
      <div>{adCopy.title}</div>
      <div>{adCopy.body}</div>
    </div>
  );
}
