"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";

export default function WinLossLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const account = api.user.organizationUser.get.organization.useQuery();
  const router = useRouter();

  useEffect(() => {
    if (
      account.data !== undefined &&
      account.data.organizationId !== 2 &&
      account.data.organizationId !== 3
    ) {
      router.push("/advertising/analytics");
    }
  }, [account.data]);
  return (
    <div className="flex h-full w-full flex-col justify-start">
      <div className="sticky inset-x-0 top-0 flex h-16 w-full items-center justify-between border-b bg-white px-6">
        <h1 className="text-xl font-medium">Segments</h1>
      </div>
      {account.isLoading && <h1>Loading...</h1>}
      {account.data?.organizationId &&
        [2, 3].includes(account.data.organizationId) && (
          <div className="h-full w-full overflow-auto bg-background px-6">
            {children}
          </div>
        )}
      {account.isError && <h1>Error</h1>}
      {account.data && ![2, 3].includes(account.data.organizationId) && (
        <h1>You are not authorized to access this page</h1>
      )}
    </div>
  );
}
