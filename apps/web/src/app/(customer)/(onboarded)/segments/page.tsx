import { unstable_cache } from "next/cache";
import { SegmentsTable } from "@/features/segments/components/segment-table";
import { api } from "@/trpc/server";

/**
 * Cached function to fetch segment data
 * Prevents repeated data fetching that might cause re-render loops
 */
const getSegmentsData = unstable_cache(
  async () => {
    try {
      return await api.segment.getAll();
    } catch (error) {
      console.error("Failed to fetch segments:", error);
      return [];
    }
  },
  ["segments-data"],
  { revalidate: 60 }, // Cache for 60 seconds
);

export default async function SegmentsPage() {
  const segmentData = await getSegmentsData();

  return (
    <div className="flex h-full w-full flex-col space-y-2">
      <SegmentsTable data={segmentData} />
      <div className="flex w-full flex-col justify-end pb-2 text-center text-xs">
        <PERSON><PERSON> is in beta and can make mistakes. Check important info.
      </div>
    </div>
  );
}
