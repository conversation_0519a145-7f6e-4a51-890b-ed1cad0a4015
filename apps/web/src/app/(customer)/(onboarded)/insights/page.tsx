import { Segments } from "@/features/segments/components/segments";
import { api } from "@/trpc/server";

import { crmUserHandlers } from "@kalos/database/handlers/crmUser";

export const maxDuration = 300;

export default async function SegmentPage({
  searchParams,
}: {
  searchParams: {
    vertical?: string;
    acv?: string;
    subVertical?: string;
    employees?: string;
    arrayValue?: string;
    prospectRevenue?: string;
  };
}) {
  const [
    industires,
    subVerticals,
    customStringField,
    userOrganization,
    opportunityCustomFields,
  ] = await Promise.all([
    api.account.metadata.verticals(),
    api.account.metadata.subVerticals(),
    api.account.metadata.customFieldValues(),
    api.user.organizationUser.get.organization(),
    api.opportunity.getCustomStringFields(),
  ]);
  const industriesStringArray: string[] = [];
  const subVerticalsStringArray: string[] = [];
  const segmentsStringArray: string[] = [];
  const accountStatusesStringArray: string[] = [];
  const crmUsers = await crmUserHandlers.select.many.forOrganization(
    userOrganization.organizationId,
  );
  for (const each of industires) {
    if (each.industry !== null) {
      industriesStringArray.push(each.industry);
    }
  }

  for (const each of subVerticals) {
    if (each.industry !== null) {
      subVerticalsStringArray.push(each.industry);
    }
  }
  console.log(customStringField.stringLists);

  return (
    <div className="h-full w-full">
      <Segments
        industires={industriesStringArray}
        organizationId={userOrganization.organizationId}
        crmUsers={crmUsers}
        segments={segmentsStringArray}
        accountStatuses={accountStatusesStringArray}
        subVerticals={subVerticalsStringArray}
        customStringFields={customStringField.strings}
        customListFields={customStringField.stringLists}
        initState={{
          vertical: searchParams.vertical ?? "",
          subVertical: searchParams.subVertical ?? "",
          employees: searchParams.employees ?? "",
          acv: searchParams.acv ?? "",
          arrayValue: searchParams.arrayValue ?? "",
          prospectRevenue: searchParams.prospectRevenue ?? "",
        }}
        opportunityCustomStringFields={opportunityCustomFields.strings}
      />
      <footer className="flex h-full w-full flex-col justify-end pb-2 text-center text-xs">
        Kalos is in beta and can make mistakes. Check important info.
      </footer>
    </div>
  );
}
