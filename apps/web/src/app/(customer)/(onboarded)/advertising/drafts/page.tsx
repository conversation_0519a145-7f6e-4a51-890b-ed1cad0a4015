"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import Link from "next/link";
import { api } from "@/trpc/client";
import {
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Loader2Icon, TrashIcon } from "lucide-react";

import { Button } from "@kalos/ui/button";
import { DataTable } from "@kalos/ui/table/datatable";

export default function CampaignGroupsPage() {
  const adPrograms = api.v2.ads.linkedInAdProgram.getForOrganization.useQuery();
  return (
    <div className="flex h-full w-full flex-col items-start justify-start">
      {adPrograms.isLoading && <div>Loading...</div>}
      {adPrograms.data && (
        <CampaignGroupTable
          adPrograms={
            adPrograms.data
              ?.filter((adProgram) => adProgram.status === "DRAFT")
              .sort((a, b) => {
                if (!a.dateCreated || !b.dateCreated) return 0;
                return b.dateCreated.getTime() - a.dateCreated.getTime();
              }) ?? []
          }
        />
      )}
    </div>
  );
}
interface CampaignGroup {
  id: string;
  title: string;
  dateCreated?: Date | null;
  linkedInAdAccountId: string;
}

export function CampaignGroupTable({
  adPrograms,
}: {
  adPrograms: CampaignGroup[];
}) {
  const deleteAdProgram = api.v2.ads.linkedInAdProgram.updateStatus.useMutation(
    {
      onSuccess: () => {
        apiUtils.v2.ads.invalidate();
      },
      onMutate: async ({ id }) => {
        // Cancel outgoing refetches
        await apiUtils.v2.ads.linkedInAdProgram.getForOrganization.cancel();

        // Snapshot the previous value
        const previousData =
          apiUtils.v2.ads.linkedInAdProgram.getForOrganization.getData();

        // Optimistically update to the new value
        apiUtils.v2.ads.linkedInAdProgram.getForOrganization.setData(
          undefined,
          (old) => {
            return old?.filter((program) => program.id !== id);
          },
        );

        // Return a context object with the snapshotted value
        return { previousData };
      },
      onError: (err, variables, context) => {
        // If the mutation fails, use the context we returned above
        apiUtils.v2.ads.linkedInAdProgram.getForOrganization.setData(
          undefined,
          context?.previousData,
        );
      },
    },
  );

  const apiUtils = api.useUtils();
  const columns: ColumnDef<CampaignGroup>[] = [
    {
      accessorKey: "title",
      header: "Ad Program",
      cell: ({ row }) => {
        return (
          <Link
            href={`/advertising/adAccounts/${row.original.linkedInAdAccountId}/campaignGroups/${row.original.id}/edit`}
          >
            {row.original.title}
          </Link>
        );
      },
    },
    {
      accessorKey: "dateCreated",
      header: "Date Created",
      cell: ({ row }) => {
        return <h1>{row.original.dateCreated?.toLocaleDateString()}</h1>;
      },
    },
    {
      accessorKey: "id",
      header: "",
      cell: ({ row }) => {
        const [isDeleting, setIsDeleting] = useState(false);
        const handleDelete = () => {
          setIsDeleting(true);
          deleteAdProgram.mutate({
            id: row.original.id,
            status: "ARCHIVED_DRAFT",
          });
        };

        return (
          <Button
            variant="ghost"
            size="icon"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <Loader2Icon className="animate-spin" />
            ) : (
              <TrashIcon />
            )}
          </Button>
        );
      },
    },
  ];

  const table = useReactTable({
    data: adPrograms,
    columns,
    enableRowSelection: false,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  return (
    <div className="flex w-full flex-col gap-4 bg-white px-6 py-8">
      <DataTable table={table} columns={columns} noHover={false} />
    </div>
  );
}
