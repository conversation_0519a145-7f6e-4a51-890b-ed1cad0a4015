import type { ColumnDef } from "@tanstack/react-table";
import React, { useMemo } from "react";
import {
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import { Badge } from "@kalos/ui/badge";
import { Button } from "@kalos/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";
import { Skeleton } from "@kalos/ui/skeleton";
import { DataTable } from "@kalos/ui/table/datatable";

import { AbTestType, AdFormatType, Metrics } from "../_types";

// Types based on your API schemas
type AdVariant = {
  sponsoredCreativeId: string;
  adVarients: Record<string, any>; // e.g., {valueProp: "I like Pizza"}
  adFormat: AdFormatType;
  goal?: "Awareness" | "LeadGen";
  metrics?: Metrics;
  title?: string;
  description?: string;
  imageUrl?: string;
  targetingParams?: string; // Field to store targeting parameters if available
  campaign?: string; // Field for campaign URNs similar to upcoming goals
};

/**
 * Parse campaign URN string into human-readable targeting parameters
 * This uses the exact same logic as UpcomingLearningGoalsTable
 */
const parseCampaignUrns = (campaign?: string): string[] => {
  if (!campaign) return [];

  // Split the comma-separated URNs
  return campaign
    .split(",")
    .map((urn) => {
      // Extract the targeting parameter from the URN
      // Format could be like: urn:li:adTargetingFacet:jobFunctions
      const match = urn.trim().match(/urn:li:adTargetingFacet:(\w+)/);
      if (!match || !match[1]) {
        return null;
      }

      // Convert to human-readable format (camelCase to Title Case with spaces)
      const param = match[1];

      // Format different known parameters
      switch (param) {
        case "jobFunctions":
          return "Job Function";
        case "industries":
          return "Industry";
        case "revenue":
          return "Annual Revenue";
        case "companies":
          return "Companies";
        case "skills":
          return "Skills";
        case "locations":
          return "Locations";
        default:
          // For unknown parameters, use generic formatting
          return param
            .replace(/([A-Z])/g, " $1") // Add space before capital letters
            .replace(/^./, (str) => str.toUpperCase()) // Capitalize first letter
            .trim(); // Remove any extra spaces
      }
    })
    .filter(Boolean) as string[]; // Remove any null values
};

/**
 * Extract campaign URNs from ad variant data structure
 * Look for URN patterns in any adVarient value
 */
const extractCampaignData = (variant: AdVariant): string | undefined => {
  // Option 3: Look for URN patterns in any adVarient value
  if (variant.adVarients) {
    const allValues = Object.values(variant.adVarients);
    const urnValue = allValues.find(
      (value) =>
        typeof value === "string" && value.includes("urn:li:adTargetingFacet:"),
    );

    if (urnValue) {
      return urnValue as string;
    }
  }

  return undefined;
};

/**
 * Render targeting parameter badges
 */
const renderAudienceTargetingBadges = (
  variant: AdVariant,
  rowIndex: number,
) => {
  const campaignData = extractCampaignData(variant);

  // If we found campaign data, parse it for URNs
  if (campaignData) {
    const params = parseCampaignUrns(campaignData);

    if (params.length > 0) {
      return (
        <div className="flex flex-col  gap-2 whitespace-nowrap">
          {/* <div className="">{"Audience " + (rowIndex + 1)}</div> */}
          <div className="">
            {params.map((param, index) => (
              <React.Fragment key={`${param}-${index}`}>
                {/* {index > 0 && <span className="text-xs"></span>} */}
                {/* <Badge
                variant="outline"
                className="whitespace-nowrap border-gray-500 bg-gray-50 text-gray-500"
              > */}
                {index + 1 === params.length ? param : param + ", "}

                {/* </Badge> */}
              </React.Fragment>
            ))}
          </div>
        </div>
      );
    }
  }

  return null;
};

//TODO-BR: update type for variant
const renderAdCreativeTitle = (variant: AdVariant, rowIndex: number) => {
  const adVariantData = variant.adVarients;
  return (
    <div className="flex items-center gap-2">
      {adVariantData?.adCreativeMetadata?.adCreative?.fileName}
    </div>
  );
};

const renderAdCreative = (variant: AdVariant, rowIndex) => {
  const adVariantData = variant.adVarients;

  if (adVariantData.adCreativeMetadata?.adCreative?.fileType === "VIDEO") {
    if (adVariantData.adCreativeMetadata?.presignedUrl) {
      return (
        <div className="flex items-center gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="ghost" className="h-16 w-16 p-0">
                <video>
                  <source
                    width="64"
                    height="64"
                    src={adVariantData?.adCreativeMetadata?.presignedUrl}
                  ></source>
                </video>
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogTitle>Creative Preview</DialogTitle>
              <video controls>
                <source
                  src={adVariantData?.adCreativeMetadata?.presignedUrl}
                  className="p-2"
                />
              </video>
            </DialogContent>
          </Dialog>
        </div>
      );
    }
  }

  if (adVariantData.adCreativeMetadata?.presignedUrl) {
    return (
      <div className="flex items-center gap-2">
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="ghost" className="h-16 w-16 p-0">
              <img
                width="64"
                height="64"
                src={adVariantData?.adCreativeMetadata?.presignedUrl}
              ></img>
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogTitle>Creative Preview</DialogTitle>
            <img
              src={adVariantData?.adCreativeMetadata?.presignedUrl}
              className="p-2"
            />
          </DialogContent>
        </Dialog>
      </div>
    );
  }

  return <></>;
};

const renderConversationSubject = (variant: AdVariant, rowIndex: number) => {
  const adVariantData = variant.adVarients;
  return (
    <div className="flex items-center gap-2">
      {adVariantData.messageCopyContent}
    </div>
  );
};

const renderSocialPostBodyCopy = (variant: AdVariant, rowIndex: number) => {
  const adVariantData = variant.adVarients;
  return (
    <div className="flex items-center gap-2">
      {adVariantData.socialPostBodyCopy}
    </div>
  );
};

const renderSocialPostCallToAction = (variant: AdVariant, rowIndex: number) => {
  const adVariantData = variant.adVarients;
  return (
    <div className="flex items-center gap-2">{adVariantData.callToAction}</div>
  );
};

const renderConversationMessageCopy = (
  variant: AdVariant,
  rowIndex: number,
) => {
  const adVariantData = variant.adVarients;
  return (
    <div className="flex items-center gap-2">
      {adVariantData.messageCopyContent}
    </div>
  );
};

const renderConversationCallToAction = (
  variant: AdVariant,
  rowIndex: number,
) => {
  const adVariantData = variant.adVarients;
  return (
    <div className="flex items-center gap-2">{adVariantData.callToAction}</div>
  );
};

const renderValuePropTitle = (variant: AdVariant, rowIndex: number) => {
  const adVarients = variant.adVarients;

  return (
    <div className="flex flex-row items-center gap-2 text-wrap">
      {adVarients.valueProp}
    </div>
  );
};

const renderAdTitle = (
  variant: AdVariant,
  stageType: AbTestType,
  rowIndex: number,
) => {
  if (stageType === "audience") {
    return renderAudienceTargetingBadges(variant, rowIndex);
  }

  if (stageType === "valueProp") {
    return renderValuePropTitle(variant, rowIndex);
  }

  if (stageType === "creative") {
    return renderAdCreativeTitle(variant, rowIndex);
  }

  if (stageType === "conversationSubject") {
    return renderConversationSubject(variant, rowIndex);
  }

  if (stageType === "conversationMessageCopy") {
    return renderConversationMessageCopy(variant, rowIndex);
  }

  if (stageType === "socialPostBodyCopy") {
    return renderSocialPostBodyCopy(variant, rowIndex);
  }

  if (stageType === "socialPostCallToAction") {
    return renderSocialPostCallToAction(variant, rowIndex);
  }

  if (stageType === "conversationCallToAction") {
    return renderConversationCallToAction(variant, rowIndex);
  }
};

const renderTargetingBadges = (variant: AdVariant) => {
  // Extract campaign data from various possible locations
  const campaignData = extractCampaignData(variant);

  // If we found campaign data, parse it for URNs
  if (campaignData) {
    const params = parseCampaignUrns(campaignData);

    if (params.length > 0) {
      return (
        <div className="flex flex-row items-center gap-2 whitespace-nowrap">
          {params.map((param, index) => (
            <React.Fragment key={`${param}-${index}`}>
              {index > 0 && <span className="text-xs">x</span>}
              <Badge
                variant="outline"
                className="whitespace-nowrap bg-gray-50 text-gray-700"
              >
                {param}
              </Badge>
            </React.Fragment>
          ))}
        </div>
      );
    }
  }

  // Fall back to our original parse function if no campaign URNs
  const targetingParams: string[] = [];

  // Extract targeting info from adVarients if available
  if (variant.adVarients) {
    // Check for specific targeting fields in adVarients
    if (variant.adVarients.industry) {
      targetingParams.push("Industry");
    }
    if (variant.adVarients.jobFunction) {
      targetingParams.push("Job Function");
    }
    if (variant.adVarients.location) {
      targetingParams.push("Location");
    }
    if (variant.adVarients.companies) {
      targetingParams.push("Companies");
    }
    if (variant.adVarients.skills) {
      targetingParams.push("Skills");
    }
    if (variant.adVarients.revenue) {
      targetingParams.push("Annual Revenue");
    }
  }

  // If we couldn't extract any params, provide some defaults
  if (targetingParams.length === 0) {
    // Add some default targeting parameters based on ad type
    if (variant.adFormat === "SINGLE_IMAGE") {
      targetingParams.push("Job Function");
    } else if (variant.adFormat === "SPONSORED_CONVERSATION") {
      targetingParams.push("Industry");
    }
  }

  if (targetingParams.length === 0) {
    return (
      <span className="text-xs text-muted-foreground">
        No targeting parameters
      </span>
    );
  }

  return (
    <div className="flex flex-row items-center gap-2 whitespace-nowrap">
      {targetingParams.map((param, index) => (
        <React.Fragment key={`${param}-${index}`}>
          {index > 0 && <span className="text-xs">x</span>}
          <Badge
            variant="outline"
            className="whitespace-nowrap bg-gray-50 text-gray-700"
          >
            {param}
          </Badge>
        </React.Fragment>
      ))}
    </div>
  );
};

/**
 * Active Ads Table Component
 * Displays all currently active ads with metrics using the shared DataTable component
 */
export default function ActiveAdsTable({
  ads,
  stageType,
  adFormat = "SINGLE_IMAGE",
  goal = "AWARENESS",
  tableDataIsLoading = false,
}: {
  ads: AdVariant[];
  stageType: AbTestType;
  adFormat: AdFormatType;
  goal?: "AWARENESS" | "LEAD_GENERATION";
  tableDataIsLoading: boolean;
}) {
  // Define all possible columns for the active ads table
  const allColumns: ColumnDef<AdVariant>[] = [
    // Ad Title column with targeting badges (position 0)
    {
      accessorKey: "adTargeting",
      header: "Ad Title",
      id: "adTitle",
      minSize: 250,
      maxSize: 300,
      cell: ({ row }) => {
        // Get the ad variant data
        const variant = row.original;
        const rowIndex = row.index;
        return (
          <div className="flex flex-row items-center gap-2 whitespace-nowrap pr-2">
            {renderAdTitle(variant, stageType, rowIndex)}
          </div>
        );
      },
    },
    {
      accessorKey: "adStatus",
      header: "Status",
      id: "adStatus",
      minSize: 250,
      maxSize: 350,
      cell: ({ row }) => {
        // Get the ad variant data
        const variant = row.original;
        const rowIndex = row.index;
        return (
          <div className="flex flex-row items-center gap-2 whitespace-nowrap pr-2">
            {variant.adVarients.status === "IN_PROGRESS" && (
              <Badge
                variant="outline"
                className="ml-1 border-solid bg-primary text-white"
              >
                Live
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "adCreativePreview",
      header: "",
      id: "adCreativePreview",
      minSize: 250,
      maxSize: 350,
      cell: ({ row }) => {
        // Get the ad variant data
        const variant = row.original;
        const rowIndex = row.index;
        return (
          <div className="flex flex-row items-center gap-2 whitespace-nowrap pr-2">
            {renderAdCreative(variant, rowIndex)}
          </div>
        );
      },
    },

    {
      accessorFn: (row) => row.metrics?.costInUsd || 0,
      id: "costInUsd",
      header: "Spend",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? `$${value.toFixed(2)}` : 0;
      },
    },
    {
      accessorFn: (row) => row.metrics?.impressions || 0,
      id: "impressions",
      header: "Impressions",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? value.toLocaleString() : 0;
      },
    },
    // CPM - for SingleImage format
    {
      accessorFn: (row) => row.metrics?.cpm || 0,
      id: "cpm",
      header: "CPM",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? `$${value.toFixed(2)}` : 0;
      },
    },
    // Clicks - for SingleImage format
    {
      accessorFn: (row) => row.metrics?.clicks || 0,
      id: "clicks",
      header: "Clicks",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? value : 0;
      },
    },
    // CTR - for SingleImage format
    {
      accessorFn: (row) => row.metrics?.ctr || 0,
      id: "ctr",
      header: "CTR",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? `${value.toFixed(2)}%` : 0;
      },
    },
    // CPC - for SingleImage format
    {
      accessorFn: (row) => row.metrics?.cpc || 0,
      id: "cpc",
      header: "CPC",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? `$${value.toFixed(2)}` : 0;
      },
    },

    {
      // accessorFn: (row) => row.metrics?.clickToOpenRate || 0,
      id: "videosFirstQuartile",
      header: "Videos @ 25%",
      cell: ({ row }) => {
        const videoData =
          row.original.metrics?.videoFirstQuartileCompletions || 0;
        return videoData;
      },
    },
    {
      // accessorFn: (row) => row.metrics?.clickToOpenRate || 0,
      id: "videosMidpoint",
      header: "Videos @ 50%",
      cell: ({ row }) => {
        const videoData = row.original.metrics?.videoMidpointCompletions || 0;
        return videoData;
      },
    },
    {
      // accessorFn: (row) => row.metrics?.clickToOpenRate || 0,
      id: "videosThirdQuartile",
      header: "Videos @ 75%",
      cell: ({ row }) => {
        const videoData =
          row.original.metrics?.videoThirdQuartileCompletions || 0;
        return videoData;
      },
    },
    {
      // accessorFn: (row) => row.metrics?.clickToOpenRate || 0,
      id: "videoCompletionRate",
      header: "Completion Rate",
      cell: ({ row }) => {
        const impressions = row.original.metrics?.impressions || 0;
        const videoCompletions = row.original.metrics?.videoCompletions || 0;

        if (impressions === 0 || videoCompletions === 0) {
          return 0;
        }
        return `${((videoCompletions / impressions) * 100).toFixed(2)}%`;
      },
    },
    // Total Social Actions - for SingleImage format
    {
      accessorFn: (row) => row.metrics?.totalEngagements || 0,
      id: "totalEngagements",
      header: "Total Engagements",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? value : 0;
      },
    },
    // Clicks to Landing Page - for SingleImage format
    {
      // accessorFn: (row) => row.metrics?.landingPageClicks || 0,
      id: "clicksToLandingPage",
      header: "Clicks to Landing Page",
      cell: ({ row }) => {
        const value = row.original.metrics?.landingPageClicks || 0;
        return value > 0 ? value : 0;
      },
    },

    // Lead Form Opens - for both formats with LeadGen goal

    // Sends - for Conversation format
    {
      accessorFn: (row) => row.metrics?.sends || 0,
      id: "sends",
      header: "Sends",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? value : 0;
      },
    },
    // Opens - for Conversation format
    {
      accessorFn: (row) => row.metrics?.opens || 0,
      id: "opens",
      header: "Opens",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? value : 0;
      },
    },
    // Open Rate - for Conversation format
    {
      // accessorFn: (row) => row.metrics?.openRate || 0,
      id: "openRate",
      header: "Open Rate",
      cell: ({ row }) => {
        const sends = row.original.metrics?.sends || 0;
        const opens = row.original.metrics?.opens || 0;

        if (sends === 0 || opens === 0) {
          return 0;
        }

        return `${((opens / sends) * 100).toFixed(2)}%`;
      },
    },
    // Button Clicks - for Conversation format
    {
      accessorFn: (row) => row.metrics?.actionClicks || 0,
      id: "buttonClicks",
      header: "Button Clicks",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? value : 0;
      },
    },
    // Click to Open Rate - for Conversation format
    {
      // accessorFn: (row) => row.metrics?.clickToOpenRate || 0,
      id: "clickToOpenRate",
      header: "Click to Open Rate",
      cell: ({ row }) => {
        const opens = row.original.metrics?.opens || 0;
        const clicks = row.original.metrics?.actionClicks || 0;

        if (clicks === 0 || opens === 0) {
          return 0;
        }

        return `${((clicks / opens) * 100).toFixed(2)}%`;
      },
    },

    {
      accessorFn: (row) => row.metrics?.oneClickLeadFormOpens || 0,
      id: "leadFormOpens",
      header: "Lead Form Opens",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? value : 0;
      },
    },
    // Lead Form Completion Rate - for both formats with LeadGen goal
    {
      // accessorFn: (row) => row.metrics?.leadFormCompletionRate || 0,
      id: "leadFormCompletionRate",
      header: "Lead Form Completion Rate",
      cell: ({ row }) => {
        const opens = row.original.metrics?.oneClickLeadFormOpens || 0;
        const leads = row.original.metrics?.leads || 0;
        if (opens == 0 || leads == 0) {
          return 0;
        }
        return `${((leads / opens) * 100).toFixed(2)}%`;
      },
    },
    // Leads - for both formats with LeadGen goal
    {
      accessorFn: (row) => row.metrics?.leads || 0,
      id: "leads",
      header: "Leads",
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return value > 0 ? value : 0;
      },
    },
  ];

  // Filter columns based on ad format and goal
  const filteredColumns = allColumns.filter((column) => {
    // Default columns
    if (
      ["adTitle", "adCreativePreview", "adStatus"].includes(column.id as string)
    ) {
      return true;
    }

    // Scenario 1: SingleImage + Awareness
    // if (effectiveAdFormat === "SingleImage" && effectiveGoal === "Awareness") {
    if (adFormat === "SINGLE_IMAGE" && goal === "AWARENESS") {
      return [
        "costInUsd",
        "impressions",
        "cpm",
        "clicks",
        "ctr",
        "cpc",
        "totalEngagements",
        "clicksToLandingPage",
      ].includes(column.id as string);
    }

    // Scenario 2: SingleImage + LeadGen
    if (adFormat === "SINGLE_IMAGE" && goal === "LEAD_GENERATION") {
      return [
        "costInUsd",
        "impressions",
        "cpm",
        "clicks",
        "ctr",
        "cpc",
        "totalEngagements",
        "clicksToLandingPage",
        "leadFormOpens",
        "leadFormCompletionRate",
        "leads",
      ].includes(column.id as string);
    }

    // Scenario 3: Conversation + LeadGen
    if (
      (adFormat === "SPONSORED_CONVERSATION" && goal === "LEAD_GENERATION") ||
      (adFormat === "SPONSORED_INMAIL" && goal === "LEAD_GENERATION")
    ) {
      return [
        "costInUsd",
        "sends",
        "opens",
        "openRate",
        "buttonClicks",
        "clickToOpenRate",
        "leadFormOpens",
        "leadFormCompletionRate",
        "leads",
      ].includes(column.id as string);
    }

    if (adFormat === "VIDEO" && goal === "AWARENESS") {
      return [
        "costInUsd",
        "impressions",
        "cpm",
        "videosFirstQuartile", // TODO
        "videosMidpoint", // TODO
        "videosThirdQuartile", // TODO
        "videoCompletionRate",
        "totalEngagements",
        "clicksToLandingPage",
      ].includes(column.id as string);
    }

    if (adFormat === "DOCUMENT" && goal === "LEAD_GENERATION") {
      return [
        "costInUsd",
        "impressions",
        "cpm",
        "clicks",
        "ctr",
        "cpc",
        "totalEngagements",
        "clicksToLandingPage",
        "leadFormOpens",
        "leadFormCompletionRate",
        "leads",
      ].includes(column.id as string);
    }

    // Default to showing the column
    return [
      "costInUsd",
      "impressions",
      "cpm",
      "clicks",
      "ctr",
      "cpc",
      "totalEngagements",
      "clicksToLandingPage",
      "leadFormOpens",
      "leadFormCompletionRate",
      "leads",
      "videoFirstQuartile", // TODO
      "videoMidpoint", // TODO
      "videoThirdQuartile", // TODO
      "videoCompletionRate",
      "totalEngagements",
    ];
  });

  const skeletonColumns = useMemo(() => {
    return [...filteredColumns].map((column) => ({
      ...column,
      cell: column.cell
        ? () => <Skeleton className="h-4 w-24 rounded bg-gray-200" />
        : undefined,
    }));
  }, [filteredColumns]);

  const loadingTableData = useMemo(() => {
    return Array(3).fill({
      sponsoredCreativeId: "",
      adVarients: {},
      adFormat: "abc",
      goal: "abc",
      title: "abc",
      description: "abc",
      metrics: {},
    });
  }, []);

  const table = useReactTable({
    data: tableDataIsLoading ? loadingTableData : (ads ?? []),
    columns: tableDataIsLoading ? skeletonColumns : filteredColumns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    columnResizeMode: "onChange",
    enableColumnResizing: true,
  });

  return (
    <div className="w-full overflow-x-auto">
      <div className="max-w-[100%]">
        <DataTable
          table={table}
          columns={filteredColumns}
          noResultsMessage="No active ads in this experiment"
          noHover={false}
        />
      </div>
    </div>
  );
}
