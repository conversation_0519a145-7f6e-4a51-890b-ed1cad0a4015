import { Skeleton } from "@kalos/ui/skeleton";

/**
 * Skeleton loading state for segment detail page
 */
export default function SegmentDetailSkeleton() {
  return (
    <div className="flex min-h-screen w-full flex-col">
      <main className="flex-1 p-4 md:p-6">
        {/* Header Skeleton */}
        <div className="mb-6 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <Skeleton className="mb-2 h-8 w-64" />
            <Skeleton className="h-4 w-48" />
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <Skeleton className="mb-1 h-6 w-24" />
              <Skeleton className="h-4 w-32" />
            </div>
            <Skeleton className="h-10 w-24" />
          </div>
        </div>

        {/* Tabs Skeleton */}
        <div className="mb-4">
          <div className="mb-4 flex justify-between">
            <Skeleton className="h-10 w-48" />
            <Skeleton className="h-10 w-32" />
          </div>

          {/* Table Skeleton */}
          <Skeleton className="mb-4 h-8 w-64" />
          <div className="rounded-md border">
            <div className="p-4">
              <div className="space-y-3">
                <Skeleton className="h-6 w-full" />
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-24 w-full" />
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
