import { start } from "node:repl";
import React, { useTransition } from "react";
import { usePathname, useRouter } from "next/navigation";
import { api } from "@/trpc/client";
import { Loader, LoaderCircle, LoaderPinwheel } from "lucide-react";

// Stats data
const stats = [
  {
    value: 23,
    label: "#Key Results",
    change: 3.2,
    trend: "up",
  },
  {
    value: 5576,
    label: "#A/B tests for Ads",
    change: 12.8,
    trend: "down",
  },
  {
    value: 55,
    label: "Sales Accepted Leads",
    change: 0,
    trend: "neutral",
  },
  {
    value: 29,
    label: "Closed Won Opportunities",
    change: 0,
    trend: "neutral",
  },
];

export interface MetricsCardsProps {
  value: number;
  label: string;
  change: number;
  trend: string;
  currency: boolean;
}

export type MetricsCardsArray = MetricsCardsProps[];

interface MetricsCardsComponentProps {
  stats: MetricsCardsArray;
}

export default function MetricsCards({ stats }: MetricsCardsComponentProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [isPending, startTransition] = useTransition();
  return (
    <div>
      <div className="grid grid-cols-2 gap-4  md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, i) => (
          <div
            key={i}
            onClick={() => {
              if (
                stat.label.toLocaleLowerCase().includes("leads") &&
                pathname !== "/advertising/leads"
              ) {
                startTransition(() => {
                  router.push("/advertising/leads");
                });
              }
            }}
            className={
              "flex flex-col rounded-md border p-4 transition-shadow" +
              (stat.label.toLocaleLowerCase().includes("leads") &&
              pathname !== "/advertising/leads"
                ? " hover:cursor-pointer hover:shadow-md"
                : "")
            }
          >
            <div className="mt-2 flex flex-row items-center justify-between">
              {stat.currency ? (
                <span className="text-2xl font-bold">
                  {stat.value.toLocaleString("en-US", {
                    style: "currency",
                    currency: "USD",
                  })}
                </span>
              ) : (
                <span className="text-2xl font-bold">{stat.value}</span>
              )}
              {/* {isPending && (
                <LoaderCircle
                  size={"20"}
                  className="animate-spin justify-self-end"
                />
              )} */}
            </div>

            <span className="text-sm text-muted-foreground">{stat.label}</span>

            <div className="mt-2 flex items-center ">
              <span
                className={`text-xs ${
                  stat.trend === "up"
                    ? "text-green-500"
                    : stat.trend === "down"
                      ? "text-red-500"
                      : "text-gray-500"
                }`}
              >
                {stat.trend === "up" && "↑ "}
                {stat.trend === "down" && "↓ "}
                {/* {stat.change > 0 ? `${stat.change}%` : "0.0%"} */}
              </span>
              <span className="ml-1 text-xs text-muted-foreground"> </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
