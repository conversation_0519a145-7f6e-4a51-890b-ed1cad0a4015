/**
 * Empty state component for when there's no data
 */
export default function EmptyState({ message }: { message: string }) {
  return (
    <div className="flex h-64 w-full flex-col items-center justify-center rounded-md border border-dashed bg-background p-8 text-center">
      <div className="mb-4 rounded-full bg-muted p-3">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <rect width="18" height="12" x="3" y="6" rx="2" />
          <path d="m16 16-4-4-4 4" />
        </svg>
      </div>
      <h3 className="mb-1 text-lg font-semibold">No data to display</h3>
      <p className="mb-4 text-sm text-muted-foreground">{message}</p>
    </div>
  );
}
