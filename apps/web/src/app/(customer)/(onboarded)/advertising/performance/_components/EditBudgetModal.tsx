import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { api } from "@/trpc/client";
import { LoaderCircleIcon } from "lucide-react";
import { z } from "zod";

import { But<PERSON> } from "@kalos/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@kalos/ui/dialog";
import { Input } from "@kalos/ui/input";
import { Label } from "@kalos/ui/label";

import { SegmentDetail } from "../_types";

interface EditBudgetModalProps {
  segmentDetail: SegmentDetail;
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}

const budgetUpdateSchema = z.object({
  budget: z.number().min(1, "Budget must be at least $1"),
});

export default function EditBudgetModal({
  segmentDetail,
  isOpen,
  setIsOpen,
}: EditBudgetModalProps) {
  const [budget, setBudget] = useState<string>("");
  const [errors, setErrors] = useState<{ budget?: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const router = useRouter();
  const searchParams = useSearchParams();
  const apiUtils = api.useUtils();

  // Fetch ad program details to determine type
  const adProgramQuery = api.v2.ads.linkedInAdProgram.getOne.useQuery(
    {
      id: segmentDetail.adProgramId,
    },
    {
      enabled: isOpen && !!segmentDetail.adProgramId,
    },
  );

  // Update budget mutation
  const updateBudgetMutation = api.v2.ads.linkedInCampaignGroup.updateBudget.useMutation({
    onSuccess: async (_, variables) => {
      // Invalidate the campaign group budget query to refetch latest data
      await apiUtils.v2.ads.linkedInCampaignGroup.getCampaignGroupBudget.invalidate({
        adSegmentId: segmentDetail.adSegmentId,
      });
      
      // Update URL parameter as backup
      const params = new URLSearchParams(searchParams.toString());
      params.set('budget', variables.totalBudget.toString());
      router.replace(`${window.location.pathname}?${params.toString()}`, { scroll: false });
      
      setIsOpen(false);
    },
    onError: (error) => {
      console.error("Budget update error:", error);
      setErrors({ budget: error.message });
    },
    onSettled: () => {
      setIsSubmitting(false);
    },
  });

  // Initialize budget value when modal opens
  useEffect(() => {
    if (isOpen) {
      setBudget(segmentDetail.budget.toString());
      setErrors({});
    }
  }, [isOpen, segmentDetail.budget]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrors({});
    setIsSubmitting(true);

    try {
      const parsedBudget = parseFloat(budget);
      
      const validationResult = budgetUpdateSchema.safeParse({
        budget: parsedBudget,
      });

      if (!validationResult.success) {
        const fieldErrors: { budget?: string } = {};
        validationResult.error.errors.forEach((error) => {
          if (error.path[0] === "budget") {
            fieldErrors.budget = error.message;
          }
        });
        setErrors(fieldErrors);
        setIsSubmitting(false);
        return;
      }

      // Update the budget
      updateBudgetMutation.mutate({
        adSegmentId: segmentDetail.adSegmentId,
        totalBudget: parsedBudget,
      });
    } catch (error) {
      setErrors({ budget: "Invalid budget format" });
      setIsSubmitting(false);
    }
  };

  const getBudgetLabel = () => {
    if (adProgramQuery.isLoading) return "Budget";
    return adProgramQuery.data?.type === "EVERGREEN" 
      ? "Monthly Budget" 
      : "Total Budget";
  };

  const getBudgetDescription = () => {
    if (adProgramQuery.isLoading) return "";
    if (adProgramQuery.data?.type === "EVERGREEN") {
      return "Enter the monthly budget for this campaign group.";
    }
    
    const startDate = adProgramQuery.data?.startDatetime ? new Date(adProgramQuery.data.startDatetime) : null;
    const endDate = adProgramQuery.data?.endDatetime ? new Date(adProgramQuery.data.endDatetime) : null;
    
    if (startDate && endDate) {
      const durationInDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      return `Enter the total budget for this campaign group.`;
    }
    
    return "Enter the total budget for this campaign group.";
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Budget</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="budget">
              {getBudgetLabel()}
              {adProgramQuery.isLoading && (
                <LoaderCircleIcon className="ml-2 inline-block h-4 w-4 animate-spin" />
              )}
            </Label>
            <Input
              id="budget"
              type="number"
              step="0.01"
              min="1"
              value={budget}
              onChange={(e) => setBudget(e.target.value)}
              placeholder="Enter budget amount"
              disabled={isSubmitting || adProgramQuery.isLoading}
            />
            {errors.budget && (
              <p className="text-sm text-red-600">{errors.budget}</p>
            )}
            <p className="text-sm text-gray-600">
              {getBudgetDescription()}
            </p>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                isSubmitting || 
                adProgramQuery.isLoading || 
                !budget ||
                isNaN(parseFloat(budget))
              }
            >
              {isSubmitting && (
                <LoaderCircleIcon className="mr-2 h-4 w-4 animate-spin" />
              )}
              Update Budget
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
} 