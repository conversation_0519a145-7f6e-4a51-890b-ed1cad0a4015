import { useEffect, useMemo, useState } from "react";
import { api } from "@/trpc/client";
import { TRPCClientErrorLike } from "@trpc/client";

import {
  CurrentRunningAbTest,
  LearningGoal,
  RawUpcomingTestApiResponse,
  SegmentDetail,
} from "../_types";
import {
  mapSegmentApiResponse,
  mapUpcomingTestsToLearningGoals,
} from "./mappers";

/**
 * Custom hook to fetch and manage data for a single ad segment
 * Focuses only on AB test data since segment details come from URL params
 */
export function useAdSegmentPageData(
  adSegmentId: string,
  adFormat: string,
  adProgramId?: string,
  fromDate?: Date,
  toDate?: Date,
) {
  // Get current running AB test for this specific ad segment
  const currentAbTestQuery = api.v2.ads.abTest.getCurrentRunningAbTest.useQuery(
    {
      adSegmentId,
      fromDate: fromDate?.toISOString(),
      toDate: toDate?.toISOString(),
      adFormat: adFormat,
    },
    {
      enabled: !!adSegmentId,
      refetchInterval: 60000, // Refresh data every minute
    },
  );

  // Debug logging
  useEffect(() => {
    console.log("[DEBUG] Ad Segment ID:", adSegmentId);
    console.log("[DEBUG] Date range:", { fromDate, toDate });
    console.log("[DEBUG] AB Test data:", currentAbTestQuery.data);
  }, [adSegmentId, fromDate, toDate, currentAbTestQuery.data]);

  return {
    currentAbTest: currentAbTestQuery.data as
      | CurrentRunningAbTest
      | null
      | undefined,
    isLoading: currentAbTestQuery.isLoading,
    isError: currentAbTestQuery.isError,
    error: currentAbTestQuery.error,
  };
}

/**
 * Custom hook to fetch and manage segment page data (legacy - will be replaced)
 */
export function useSegmentPageData(segmentId: string) {
  // State for storing all tests from all segments
  const [allAbTests, setAllAbTests] = useState<Array<any>>([]);
  const [abTestsLoading, setAbTestsLoading] = useState(false);
  const [abTestsError, setAbTestsError] = useState<any>(null);

  // Get segment details using the core segment ID
  const segmentDetailQuery =
    api.v2.ads.adSegment.getSegmentDetailWithAds.useQuery(
      { segmentId },
      { enabled: !!segmentId },
    );

  // Get ad segments for this core segment - using our new endpoint
  const adSegmentsQuery =
    api.v2.ads.adSegment.getAllAdSegmentsForSegment.useQuery(
      { segmentId },
      { enabled: !!segmentId },
    );

  // Get all ad segment IDs
  const adSegmentIds = useMemo(
    () => adSegmentsQuery.data?.map((segment) => segment.id) || [],
    [adSegmentsQuery.data],
  );

  // Find segment ID that matches the target (if it exists)
  const targetAdSegmentId = useMemo(() => {
    // Look for our specific test segment
    const targetId = "01955def-1ee5-7226-8bcf-6deb823d36b4";
    return (
      adSegmentsQuery.data?.find((segment) => segment.id === targetId)?.id ||
      adSegmentIds[0] ||
      null
    );
  }, [adSegmentsQuery.data, adSegmentIds]);

  const currentAbTestQuery = api.v2.ads.abTest.getCurrentRunningAbTest.useQuery(
    { adSegmentId: targetAdSegmentId || "" },
    {
      enabled: !!targetAdSegmentId,
      refetchInterval: 60000, // Refresh data every minute
    },
  );

  useEffect(() => {
    if (currentAbTestQuery.data) {
      setAllAbTests([
        { ...currentAbTestQuery.data, adSegmentId: targetAdSegmentId },
      ]);
      setAbTestsLoading(false);
    } else if (currentAbTestQuery.isLoading) {
      setAbTestsLoading(true);
    } else if (currentAbTestQuery.isError) {
      setAbTestsError(currentAbTestQuery.error);
      setAbTestsLoading(false);
    }
  }, [
    currentAbTestQuery.data,
    currentAbTestQuery.isLoading,
    currentAbTestQuery.isError,
    currentAbTestQuery.error,
    targetAdSegmentId,
  ]);

  // Add debug logging
  useEffect(() => {
    console.log("[DEBUG] Core segment ID:", segmentId);
    console.log("[DEBUG] Segment detail data:", segmentDetailQuery.data);
    console.log("[DEBUG] Ad segments found:", adSegmentsQuery.data);
    console.log("[DEBUG] All ad segment IDs:", adSegmentIds);
    console.log("[DEBUG] Target ad segment ID:", targetAdSegmentId);
    console.log("[DEBUG] Single AB test data:", currentAbTestQuery.data);
    console.log("[DEBUG] All AB tests:", allAbTests);

    if (
      segmentDetailQuery.data &&
      allAbTests.length === 0 &&
      !abTestsLoading &&
      !currentAbTestQuery.isLoading
    ) {
      console.log("[DEBUG] This segment exists but has no active experiments");
    }
  }, [
    segmentId,
    segmentDetailQuery.data,
    adSegmentsQuery.data,
    adSegmentIds,
    targetAdSegmentId,
    currentAbTestQuery.data,
    allAbTests,
    abTestsLoading,
    currentAbTestQuery.isLoading,
  ]);

  // Transform segment data if available
  const segmentDetail = useMemo(() => {
    if (!segmentDetailQuery.data) return undefined;
    return mapSegmentApiResponse(segmentDetailQuery.data as any, segmentId);
  }, [segmentDetailQuery.data, segmentId]);

  return {
    segmentDetail,
    adSegmentIds,
    targetAdSegmentId,
    currentAbTest: currentAbTestQuery.data as
      | CurrentRunningAbTest
      | null
      | undefined,
    allAbTests, //! All AB tests across all segments (for now just the main one) -- to braxton: change this to get all ab tests
    isLoading:
      segmentDetailQuery.isLoading ||
      adSegmentsQuery.isLoading ||
      abTestsLoading ||
      (!!targetAdSegmentId && currentAbTestQuery.isLoading),
    isError:
      segmentDetailQuery.isError ||
      adSegmentsQuery.isError ||
      !!abTestsError ||
      (!!targetAdSegmentId && currentAbTestQuery.isError),
    error:
      segmentDetailQuery.error ||
      adSegmentsQuery.error ||
      abTestsError ||
      currentAbTestQuery.error,
  };
}

export function useUpcomingTestsNew(adSegmentId: string): {
  tests: LearningGoal[];
  isLoading: boolean;
  isError: boolean;
  error: Error | null | TRPCClientErrorLike<any>;
} {
  const isValidAdSegmentId = !!adSegmentId && adSegmentId.length > 0;

  const upcomingGoalsQuery =
    api.v2.ads.abTest.getUpcomingRunningAbTests.useQuery(
      { adSegmentId },
      { enabled: isValidAdSegmentId },
    );

  for (const test of upcomingGoalsQuery?.data || []) {
    if (test.type === "valueProp") {
      const valuePropQuery =
        api.v2.ads.adSegmentValueProp.getAdSegmentValuePropsForAdSegment.useQuery(
          {
            adSegmentId: adSegmentId,
          },
        );

      console.log("valuePropQuery", valuePropQuery.data);
    }
  }

  return {
    tests: [],
    isLoading: upcomingGoalsQuery.isLoading,
    isError: upcomingGoalsQuery.isError,
    error: upcomingGoalsQuery.error || null,
  };
}

/**
 * Custom hook to fetch and transform upcoming tests data
 */
export function useUpcomingTests(adSegmentId: string): {
  tests: LearningGoal[];
  isLoading: boolean;
  isError: boolean;
  error: Error | null | TRPCClientErrorLike<any>;
} {
  // Make sure adSegmentId is valid before querying
  const isValidAdSegmentId = !!adSegmentId && adSegmentId.length > 0;

  const upcomingGoalsQuery =
    api.v2.ads.abTest.getUpcomingRunningAbTests.useQuery(
      { adSegmentId },
      {
        enabled: isValidAdSegmentId,
        refetchInterval: 300000, // Refresh every 5 minutes
        retry: 1,
        retryDelay: 4000,
      },
    );

  console.log("upcomingGoalsQuery", upcomingGoalsQuery.data);

  // for (const test of upcomingGoalsQuery?.data || []) {
  //   console.log("TEST", test);
  //   if (test.stageType === "valuePropTest") {
  //     const valuePropQuery =
  //       api.v2.ads.adSegmentValueProp.getAdSegmentValuePropsForAdSegment.useQuery(
  //         {
  //           adSegmentId: adSegmentId,
  //         },
  //       );

  //     console.log("valuePropQuery", valuePropQuery.data);
  //   }
  // }

  // Transform data to learning goals format
  const tests = useMemo(() => {
    if (upcomingGoalsQuery.isError || !upcomingGoalsQuery.data) {
      console.log(
        "[DEBUG] Returning empty array for upcoming tests due to error or missing data",
      );
      return [];
    }

    try {
      const safeData = upcomingGoalsQuery.data as unknown as
        | RawUpcomingTestApiResponse[]
        | undefined;

      if (!Array.isArray(safeData)) {
        console.log(
          "[DEBUG] API response is not an array, returning empty array",
        );
        return [];
      }

      const mappedTests = mapUpcomingTestsToLearningGoals(safeData);
      console.log("***** mappedTests", mappedTests);

      const validTests = mappedTests;

      console.log(
        `[DEBUG] Filtered ${mappedTests.length - validTests.length} tests without valid campaign data`,
      );
      return validTests;
    } catch (error) {
      console.error("[ERROR] Failed to map upcoming tests:", error);
      return [];
    }
  }, [upcomingGoalsQuery.data, upcomingGoalsQuery.isError]);

  return {
    tests: tests || [],
    isLoading: upcomingGoalsQuery.isLoading,
    isError: upcomingGoalsQuery.isError,
    error: upcomingGoalsQuery.error || null,
  };
}
