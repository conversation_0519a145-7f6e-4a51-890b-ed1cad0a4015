import {
  AbTestType,
  LearningGoal,
  Metrics,
  RawMetrics,
  RawUpcomingTestApiResponse,
  SegmentApiResponse,
  SegmentDetail,
  UpcomingTestApiResponse,
} from "../_types";

/**
 * Maps raw metrics to standardized metrics format
 */
export function mapMetrics(rawMetrics?: RawMetrics): Metrics {
  if (!rawMetrics) return {};

  // Calculate CPM: (Total Cost in USD / Total Impressions) × 1000
  const cpm =
    rawMetrics.impressions && rawMetrics.costInUsd && rawMetrics.impressions > 0
      ? (rawMetrics.costInUsd / rawMetrics.impressions) * 1000
      : undefined;

  // Calculate CPC: Total Cost in USD / Total Clicks
  const cpc =
    rawMetrics.clicks && rawMetrics.costInUsd && rawMetrics.clicks > 0
      ? rawMetrics.costInUsd / rawMetrics.clicks
      : undefined;

  return {
    impressions: rawMetrics.impressions,
    clicks: rawMetrics.clicks,
    leads: rawMetrics.oneClickLeads,
    spend: rawMetrics.costInUsd,
    engagements: rawMetrics.externalWebsiteConversions,
    landingPageClicks: rawMetrics.landingPageClicks,
    actionClicks: rawMetrics.actionClicks,
    totalEngagements: rawMetrics.totalEngagements,
    costInUsd: rawMetrics.costInUsd,
    opens: rawMetrics.opens,
    sends: rawMetrics.sends,
    oneClickLeadFormOpens: rawMetrics.oneClickLeadFormOpens,
    videoStarts: rawMetrics.videoStarts,
    videoCompletions: rawMetrics.videoCompletions,
    videoFirstQuartileCompletions: rawMetrics.videoFirstQuartileCompletions,
    videoMidpointCompletions: rawMetrics.videoMidpointCompletions,
    videoThirdQuartileCompletions: rawMetrics.videoThirdQuartileCompletions,
    // Calculate CTR if we have both impressions and clicks
    ctr:
      rawMetrics.impressions && rawMetrics.clicks
        ? (rawMetrics.clicks / rawMetrics.impressions) * 100
        : undefined,
    // Include calculated CPC and CPM metrics
    cpc,
    cpm,
  };
}

/**
 * Maps ad segment, core segment, and ad program data to the SegmentDetail type used by components
 */
export function mapAdSegmentToSegmentDetail({
  adSegment,
  segment,
  adProgram,
}: {
  adSegment: any;
  segment: any;
  adProgram: any;
}): SegmentDetail {
  // Check if we have valid start dates
  const startDate = adProgram.startDatetime
    ? new Date(adProgram.startDatetime)
    : new Date();

  // Format start date
  const formattedStartDate = startDate.toISOString();

  // Check if we have valid end dates
  const endDate = adProgram.endDatetime
    ? new Date(adProgram.endDatetime)
    : undefined;

  // Format end date if it exists
  const formattedEndDate = endDate ? endDate.toISOString() : undefined;

  return {
    adProgramId: adProgram.id,
    adSegmentId: adSegment.id,
    name: segment.name || adSegment.name || "Unnamed Segment",
    budget: adProgram.totalBudget || 0,
    startDate: formattedStartDate,
    endDate: formattedEndDate,
    status: adSegment.status as "ACTIVE" | "PAUSED" | "COMPLETED",
    metrics: {
      totalImpressions: 0, // Add metrics when available
      totalClicks: 0,
      totalEngagements: 0,
      totalLeads: 0,
      totalSpent: 0,
    },
  };
}

/**
 * Maps segment API response to the SegmentDetail type used by components
 */
export function mapSegmentApiResponse(
  segmentDetail: SegmentApiResponse,
  segmentId: string,
): SegmentDetail {
  return {
    adProgramId: segmentId,
    adSegmentId: segmentId,
    name: segmentDetail.segment?.name || "Unnamed Segment",
    budget: segmentDetail.metrics?.totalBudget || 0,
    startDate: new Date().toISOString(),
    endDate: undefined,
    status: (segmentDetail.segment?.status === "ACTIVE"
      ? "ACTIVE"
      : segmentDetail.segment?.status === "ARCHIVED"
        ? "COMPLETED"
        : "PAUSED") as "ACTIVE" | "PAUSED" | "COMPLETED",
    metrics: {
      totalImpressions: segmentDetail.metrics?.totalImpressions || 0,
      totalClicks: segmentDetail.metrics?.totalClicks || 0,
      totalEngagements: segmentDetail.metrics?.totalEngagements || 0,
      totalLeads: segmentDetail.metrics?.totalLeads || 0,
      totalSpent: segmentDetail.metrics?.totalSpent || 0,
    },
  };
}

/**
 * Maps a stage type from the API to a display type
 */
export function mapStageTypeToDisplayType(stageType: string): AbTestType {
  if (stageType === "valuePropTest") return "valueProp";
  if (stageType === "audienceTest") return "audience";
  if (stageType === "creativeTest") return "creative";
  if (stageType === "conversationSubjectTest") return "conversationSubject";
  return "copy"; // Default fallback
}

/**
 * Maps test types to display names
 */
export function getTestTypeDisplayName(type: AbTestType): string {
  switch (type) {
    case "valueProp":
      return "Value Prop";
    case "audience":
      return "Audience";
    case "creative":
      return "Creative";
    case "conversationSubject":
    case "copy":
      return "Copy";
    default:
      return "Unknown";
  }
}

/**
 * Returns the appropriate style for a test type badge
 */
export function getTestTypeBadgeStyle(type: string): string {
  switch (type) {
    case "valueProp":
      return "bg-blue-50 text-blue-700";
    case "audience":
      return "bg-amber-50 text-amber-700";
    case "creative":
      return "bg-green-50 text-green-700";
    case "copy":
    case "conversationSubject":
      return "bg-purple-50 text-purple-700";
    default:
      return "bg-gray-50 text-gray-700";
  }
}

/**
 * Transforms data from the API to the learning goals format
 */
export function mapUpcomingTestsToLearningGoals(
  testData: RawUpcomingTestApiResponse[] | undefined,
): LearningGoal[] {
  if (!testData || !Array.isArray(testData) || testData.length === 0) {
    console.log(
      "[DEBUG] mapUpcomingTestsToLearningGoals received empty or invalid data:",
      testData,
    );
    return [];
  }

  console.log(
    "[DEBUG] mapUpcomingTestsToLearningGoals received raw data:",
    testData,
  );

  try {
    const mappedGoals = testData.map((test, index) => {
      if (!test || !test.stageId || !test.stageType) {
        console.warn(
          "[WARNING] Incomplete test data found, skipping item:",
          test,
        );
        return {
          id: `missing-id-${index}`,
          index: index + 1,
          testTypes: ["copy" as AbTestType], // Explicitly cast as AbTestType
          format: "Unknown",
          title: "Unknown Test",
        };
      }

      const displayType = mapStageTypeToDisplayType(test.stageType);
      const title = getTestTypeDisplayName(displayType);

      // Extract ad title and campaign from ad variants if available
      let adTitle = title;
      let campaign: string | undefined;

      if (
        test.ads &&
        Array.isArray(test.ads) &&
        test.ads.length > 0 &&
        test.ads[0] &&
        test.ads[0].adVarients
      ) {
        const adVarients = test.ads[0].adVarients;
        // Log the available ad variants for debugging
        console.log(`[DEBUG] Ad variants for test ${index}:`, adVarients);

        // Extract campaign information if available
        if (adVarients.campaign) {
          campaign = adVarients.campaign;
          console.log(
            `[DEBUG] Found campaign data for test ${index}:`,
            campaign,
          );
        }

        // Try to extract title from ad variants based on test type
        if (displayType === "valueProp" && adVarients.valueProp) {
          adTitle = adVarients.valueProp;
        } else if (displayType === "audience" && adVarients.audience) {
          adTitle = adVarients.audience;
        } else if (displayType === "creative" && adVarients.creative) {
          adTitle = adVarients.creative;
        } else if (displayType === "copy" && adVarients.copy) {
          adTitle = adVarients.copy;
        }
      }

      const mappedGoal = {
        stageId: test.stageId,
        id: test.stageId,
        index: index + 1,
        testTypes: [displayType],
        format: "Single Image", // Default format
        title: adTitle,
        campaign,
        valueProps: test.valueProps,
        creatives: test.creatives,
        conversationSubjects: test.conversationSubjects,
        startingIn: calculateStartingDays(index),
        dateRange: formatDateRange(
          calculateStartDate(index),
          calculateEndDate(index),
        ),
      };

      console.log(`[DEBUG] Mapped test ${index} to learning goal:`, mappedGoal);
      return mappedGoal;
    });

    console.log("[DEBUG] Final mapped learning goals:", mappedGoals);
    return mappedGoals;
  } catch (error) {
    console.error("[ERROR] Failed to map upcoming tests:", error);
    return [];
  }
}

/**
 * Calculate how many days until a test starts
 */
export function calculateStartingDays(index: number): number {
  return (index + 1) * 7; // Each test starts 7 days after the previous
}

/**
 * Calculate the start date for a test
 */
export function calculateStartDate(index: number): Date {
  return new Date(Date.now() + (index + 1) * 7 * 24 * 60 * 60 * 1000);
}

/**
 * Calculate the end date for a test
 */
export function calculateEndDate(index: number): Date {
  return new Date(Date.now() + (index + 2) * 7 * 24 * 60 * 60 * 1000);
}

/**
 * Format a date range as a string
 */
export function formatDateRange(startDate: Date, endDate: Date): string {
  return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
}
