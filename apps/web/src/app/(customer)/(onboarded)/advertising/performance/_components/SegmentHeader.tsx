import { useEffect, useState, useMemo } from "react";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";
import { Audience } from "@/app/(customer)/(onboarded)/advertising/adAccounts/[adAccount]/campaignGroups/[campaignGroupId]/audience/_components/Audience";
import { api } from "@/trpc/client";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@radix-ui/react-dropdown-menu";
import {
  ArrowLeft,
  ChevronDown,
  DollarSign,
  Edit,
  Info,
  LoaderCircleIcon,
} from "lucide-react";

import { Badge } from "@kalos/ui/badge";
import { Button } from "@kalos/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@kalos/ui/dialog";

import { SegmentDetail } from "../_types";
import EditBudgetModal from "./EditBudgetModal";

interface SegmentHeaderProps {
  segmentDetail: SegmentDetail;
  router: AppRouterInstance;
  adProgramId?: string;
}

/**
 * Segment Header Component
 * Displays segment title, status, and metrics
 */
export default function SegmentHeader({
  segmentDetail,
  router,
}: SegmentHeaderProps) {
  const [isEditAudienceOpen, setIsEditAudienceOpen] = useState(false);
  const [isEditBudgetOpen, setIsEditBudgetOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // Fix hydration by ensuring client-side rendering for date-dependent content
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Memoize date calculations to prevent hydration mismatches
  const monthDateRange = useMemo(() => {
    const now = new Date();
    return {
      fromDate: new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0),
      toDate: new Date(now.getFullYear(), now.getMonth() + 1, 1, 0, 0, 0, 0),
    };
  }, []);

  const handleAudienceClick = () => {
    setIsEditAudienceOpen(true);
    setIsDropdownOpen(false);
  };

  const handleBudgetClick = () => {
    console.log("[DEBUG] Budget button clicked");
    setIsEditBudgetOpen(true);
    setIsDropdownOpen(false);
  };

  const handleDialogClose = (open: boolean) => {
    setIsEditAudienceOpen(open);
    setIsDropdownOpen(false);
  };

  const apiUtils = api.useUtils();

  const getCampaignGroupMonthAnalytics =
    api.v2.ads.linkedInCampaignGroup.getAnalyticsForOneCampaignGroup.useQuery(
      {
        linkedInAdSegment: segmentDetail.adSegmentId,
        fromDate: monthDateRange.fromDate,
        toDate: monthDateRange.toDate,
      },
      {
        enabled: !!segmentDetail.adSegmentId && !segmentDetail.endDate && isClient,
      },
    );

  // Prefetch facets that will be needed for the dialog - only when audience modal is open
  const facetsQuery =
    api.v2.ads.linkedInApi.adTargeting.getAdTargetingFacets.useQuery(
      undefined,
      {
        enabled: isEditAudienceOpen, // Only fetch when audience modal is open
      }
    );

  // Format currency with proper client-side rendering
  const formatCurrency = (amount: number) => {
    if (!isClient) return `$${amount.toFixed(2)}`;
    return amount.toLocaleString(undefined, {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  // Safe format for numbers that might be objects
  const safeFormatCurrency = (value: number | any) => {
    const numValue = typeof value === 'number' ? value : 0;
    return formatCurrency(numValue);
  };

  return (
    <>
      <div className="mb-6 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <div className="flex items-start gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
              className="mr-2"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>

            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <h1 className="text-2xl font-bold tracking-tight">
                  {segmentDetail.name}
                </h1>
                <Badge variant="outline" className="bg-blue-50 text-blue-700">
                  {segmentDetail.status}
                </Badge>
              </div>

              <p className="text-sm text-muted-foreground">
                {isClient ? (
                  <>
                    {new Date(segmentDetail.startDate).toLocaleDateString()} to{" "}
                    {segmentDetail.endDate
                      ? new Date(segmentDetail.endDate).toLocaleDateString()
                      : "Evergreen"}
                  </>
                ) : (
                  "Loading dates..."
                )}
              </p>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <div className="space-y-0.5 text-right">
            <div>
              <span className="text-sm text-muted-foreground">Total:</span>{" "}
              <span className="text-base font-semibold">
                {safeFormatCurrency(segmentDetail.metrics.totalSpent)}
              </span>
            </div>
            {!segmentDetail.endDate && (
              <div>
                <span className="text-sm text-muted-foreground">
                  This Month:
                </span>
                {" "}
                <span className="text-base font-semibold">
                  {getCampaignGroupMonthAnalytics.isLoading ? (
                    <LoaderCircleIcon className="mb-1 inline-block h-4 w-4 animate-spin align-middle" />
                  ) : getCampaignGroupMonthAnalytics.isError ? (
                    <span className="text-red-500">Error</span>
                  ) : (
                    safeFormatCurrency(getCampaignGroupMonthAnalytics.data?.costInUsd)
                  )}
                </span>
              </div>
            )}

            <div>
              <span className="text-sm text-muted-foreground">Budget:</span>{" "}
              <span className="text-base font-semibold">
                {safeFormatCurrency(segmentDetail.budget)}
              </span>
            </div>
          </div>

          <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <Button className="flex items-center gap-2">
                <Edit className="h-4 w-4" />
                Edit
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-[200px] space-y-1 rounded-md border bg-white p-2 shadow-md"
            >
              <DropdownMenuItem className="flex cursor-pointer items-center rounded-sm px-3 py-2 text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none">
                <div
                  role="button"
                  onClick={handleAudienceClick}
                  className="flex w-full cursor-pointer items-center"
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Audience
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem className="flex cursor-pointer items-center rounded-sm px-3 py-2 text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none">
                <div
                  role="button"
                  onClick={handleBudgetClick}
                  className="flex w-full cursor-pointer items-center"
                >
                  <DollarSign className="mr-2 h-4 w-4" />
                  Edit Budget
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Dialog open={isEditAudienceOpen} onOpenChange={handleDialogClose}>
        <DialogContent className="min-w-[900px]">
          <DialogHeader>
            <DialogTitle>Edit Audience</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <p className="flex items-center gap-2">
                <Info></Info> Changes will apply to this campaign and will be
                default settings for future campaigns
              </p>
            </div>
            {isEditAudienceOpen && (
              <div className="max-h-[600px] overflow-y-auto">
                <Audience
                  adSegmentId={segmentDetail.adSegmentId}
                  adAccount={segmentDetail.adProgramId.split("/")[1] || ""}
                  adProgramId={segmentDetail.adProgramId}
                  prefetchedAudiences={undefined}
                  prefetchedFacets={facetsQuery.data}
                />
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Budget Modal - Only render when client-side and data is available */}
      {isClient && segmentDetail.adProgramId && (
        <EditBudgetModal
          segmentDetail={segmentDetail}
          isOpen={isEditBudgetOpen}
          setIsOpen={setIsEditBudgetOpen}
        />
      )}
    </>
  );
}
