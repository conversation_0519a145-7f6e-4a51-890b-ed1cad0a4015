import type { ColumnDef } from "@tanstack/react-table";
import React, { useEffect, useState } from "react";
import { api } from "@/trpc/client";
import { ImageIcon, PaddingIcon } from "@radix-ui/react-icons";
import {
  getCoreRowModel,
  getExpandedRowModel, // Add this import
  useReactTable,
} from "@tanstack/react-table";
import { ChevronDownIcon, ChevronRightIcon } from "lucide-react";

import { Badge } from "@kalos/ui/badge";
import { Button } from "@kalos/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";
import { DataTable } from "@kalos/ui/table/datatable"; // Use standard DataTable

import { AdFormatType } from "../_types";

interface UpcomingTest {
  stageId: string;
  stageType: string;
  creatives?: string[];
  conversationSubjects?: {
    id: string;
    content: string;
    type: string;
  }[];
  valueProps?: { valueProp: string; id: string }[];
  testData: ChildRow[];
  title?: string;
}

interface ChildRow {
  title: string;
  id: string;
  index: number;
  fileType?: string;
  presignedUrl?: string;
}

function UpcomingLearningGoals({
  adSegmentId,
  adProgramId,
  adFormat,
}: {
  adSegmentId: string;
  adProgramId?: string;
  adFormat: AdFormatType;
}) {
  const [groupedData, setGroupedData] = useState<UpcomingTest[]>([]);
  const [expanded, setExpanded] = useState(true);
  const upcomingGoalsQuery =
    api.v2.ads.abTest.getUpcomingRunningAbTests.useQuery(
      { adSegmentId, adProgramId, adFormat },
      {
        refetchInterval: 300000,
        retry: 1,
        retryDelay: 4000,
      },
    );

  useEffect(() => {
    const tests = upcomingGoalsQuery.data || [];

    console.log("Raw data from API:", tests);
    const data: UpcomingTest[] = tests.map((test) => {
      let testData: ChildRow[] = [];
      let testType: string;

      if (test.valueProps) {
        testData = test.valueProps.map((v, i) => ({
          title: v.valueProp,
          id: v.id,
          index: i,
        }));
        testType = "Value Prop";
      } else if (test.stageType === "creativeTest") {
        testData = (test.creatives || []).map((creative, i) => ({
          title: creative.fileName,
          fileType: creative.fileType,
          presignedUrl: creative.presignedUrl,
          id: creative.id,
          index: i,
        }));
        testType = "Creative";
      } else if (test.stageType === "conversationSubjectTest") {
        testData = (test.conversationSubjects || []).map((conversation, i) => ({
          title: conversation.content,
          id: conversation.id,
          index: i,
        }));
        testType = "Conversation Subject";
      } else {
        testData = (test.conversationSubjects || []).map((c, i) => ({
          title: c,
          id: `${test.stageId}-copy-${i}`,
          index: i,
        }));
        testType = "Copy";
      }

      return {
        stageId: test.stageId,
        stageType: testType,
        testData,
        title: `${test.stageType} Test`,
      };
    });

    console.log("[UPCOMING GOALS]", data);

    setGroupedData(data);
  }, [upcomingGoalsQuery.data]);

  const columns: ColumnDef<UpcomingTest | ChildRow>[] = [
    {
      id: "expander",
      header: "Ad Title",
      size: 40,
      maxSize: 60,
      cell: ({ row }) => {
        // const isParent = row.getCanExpand();

        const isParent = !!row.original.stageType;
        // Calculate indentation based on row depth
        const paddingLeft = `${row.depth * 1.5}rem`;

        if (isParent) {
          const original = row.original as UpcomingTest;

          return (
            <div
              className="flex min-h-12 cursor-pointer items-center gap-2"
              style={{ paddingLeft }}
              onClick={() => (isParent ? row.toggleExpanded() : undefined)}
            >
              {row.getIsExpanded() ? (
                <ChevronDownIcon className="h-4 w-4" />
              ) : (
                <ChevronRightIcon className="h-4 w-4" />
              )}
              <div className="text-md flex">
                Learning Goal:{" "}
                <Badge
                  variant="outline"
                  className="ml-2 rounded-full bg-gray-50 text-gray-700"
                >
                  {original.stageType === "Value Prop" ? (
                    <div className="flex">
                      <PaddingIcon className="mr-2 text-indigo-500"></PaddingIcon>
                      {original.stageType}
                    </div>
                  ) : (
                    <div className="flex">
                      <ImageIcon className="mr-2 text-indigo-500" />
                      {original.stageType}{" "}
                    </div>
                  )}
                </Badge>
              </div>
            </div>
          );
        } else {
          const original = row.original as ChildRow;

          if (original.presignedUrl) {
            return (
              <div className="ml-4 flex items-center gap-2">
                <span className="text-muted-foreground">
                  {original.index + 1}.
                </span>{" "}
                {original.title}
              </div>
            );
          } else {
            return (
              <div className="ml-4 flex items-center gap-2">
                <span className="text-muted-foreground">
                  {original.index + 1}.
                </span>{" "}
                {original.title}
              </div>
            );
          }
        }
      },
    },
    {
      accessorKey: "preview",
      header: "",
      size: 100,
      minSize: 60,
      cell: ({ row }) => {
        const original = row.original as UpcomingTest | ChildRow;
        const isParent = row.getCanExpand() && original.stageType;
        return (
          <div>
            {original.presignedUrl && (
              <div>
                {" "}
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="ghost" className="h-16 w-16 p-0">
                      <img
                        width="64"
                        height="64"
                        src={original.presignedUrl}
                      ></img>
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogTitle>Creative Preview</DialogTitle>
                    <img src={original.presignedUrl} className="p-2" />
                  </DialogContent>
                </Dialog>
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "title",
      header: "",
      cell: ({ row }) => {
        const original = row.original as UpcomingTest | ChildRow;
        const isParent = row.getCanExpand() && original.stageType;
        return (
          <div>
            {!isParent && (
              <div></div>
              // <Button className="bg-blue-50 text-blue-500">Preview</Button>
            )}
          </div>
        );
      },
    },
  ];

  // Updated table configuration
  const table = useReactTable({
    data: groupedData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSubRows: (row) => (row as any).testData,
    state: { expanded },
    onExpandedChange: setExpanded,
    getRowCanExpand: (row) => !!(row.original as any).testData?.length,
    getExpandedRowModel: getExpandedRowModel(), // Add this to include expanded rows
  });

  if (upcomingGoalsQuery.isLoading) {
    return (
      <div className="mt-8">
        <h3 className="mb-4 text-lg font-semibold">Upcoming Ads</h3>
        <span>Loading...</span>
      </div>
    );
  }

  if (upcomingGoalsQuery.isError) {
    return (
      <div className="mt-8">
        <h3 className="mb-4 text-lg font-semibold">Upcoming Ads</h3>
        <div className="p-4 text-muted-foreground">
          Error retrieving upcoming ads. Please try again
        </div>
      </div>
    );
  }

  return (
    <div className="mt-8 w-full">
      <h3 className="mb-4 text-lg">Upcoming Ads</h3>
      <div className="w-full overflow-x-auto">
        <div className="max-w-[100%]">
          {" "}
          {/* or whatever min width you need */}
          <DataTable
            table={table}
            columns={columns}
            noResultsMessage="New ads will be available once current experiments finish. Blue will notify you when they’re ready."
            noHover={false}
          />
        </div>
      </div>
    </div>
  );
}

export default UpcomingLearningGoals;
