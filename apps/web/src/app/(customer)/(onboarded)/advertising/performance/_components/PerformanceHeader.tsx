import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";

import { Card, CardContent, CardHeader, CardTitle } from "@kalos/ui/card";

export default function PerformanceHeader() {
  const router = useRouter();
  const [selectedAdAccount, setSelectedAdAccount] = useState<string | null>(
    null,
  );

  const adAccountsQuery = api.v2.ads.adAccounts.getForOrganization.useQuery();

  useEffect(() => {
    if (adAccountsQuery.data?.length && !selectedAdAccount) {
      setSelectedAdAccount(adAccountsQuery.data[0]?.id || null);
    }
  }, [adAccountsQuery.data, selectedAdAccount]);

  const leadFormsQuery =
    api.v2.ads.linkedInApi.leadGenForms.getForAccount.useQuery(
      {
        adAccountId: selectedAdAccount ?? "",
      },
      {
        enabled: !!selectedAdAccount,
      },
    );

  return (
    <div className="flex flex-col gap-4">
      <h4 className="text-2xl">Impact</h4>
      <div className="flex items-center justify-between">
        <Card
          onClick={() => {
            router.push(`/advertising/leads`);
          }}
          className="min-h-[114px] w-1/4 cursor-pointer rounded-md border border-gray-200 hover:bg-gray-100"
        >
          <CardHeader>
            <CardTitle className="text-2xl font-bold">23</CardTitle>
          </CardHeader>
          <CardContent>
            {/* <h4 className="text-2xl font-bold">33</h4> */}
            <p className="text-sm text-gray-500">Key Results</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
