export default function PerformanceLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="flex h-screen w-screen flex-col overflow-hidden">
      <div className="sticky top-0 z-10 flex h-16 w-full items-center justify-between border-b bg-white px-6">
        <h1 className="text-xl font-medium">Performance</h1>
      </div>
      <div className="flex-1 overflow-y-auto bg-white">
        <div className="w-full max-w-[100vw] overflow-x-hidden">{children}</div>
      </div>
    </div>
  );
}
