// Dummy data for ad segment detail page testing
export const dummyAdSegment = {
  id: "ad-segment-123",
  segmentId: "segment-abc",
  adProgramId: "program-xyz",
  status: "ACTIVE",
  ready: true,
};

export const dummySegment = {
  id: "segment-abc",
  name: "Finance Decision Makers",
  status: "ACTIVE",
  organizationId: 123,
  verticals: ["Finance", "Banking"],
};

export const dummyAdProgram = {
  id: "program-xyz",
  title: "Q1 Product Launch",
  startDatetime: "2024-01-01T00:00:00.000Z",
  endDatetime: "2024-04-01T00:00:00.000Z",
  objectiveType: "LEAD_GENERATION",
  adFormat: {
    format: "Single Image",
  },
  totalBudget: 10000,
  status: "ACTIVE",
  type: "EVENT_DRIVEN",
  linkedInAdAccountId: "account-123",
};

export const dummyAbTest = {
  stageId: "stage-123",
  type: "creative",
  status: "IN_PROGRESS",
  rounds: [
    {
      id: "round-1",
      abTestId: "abtest-1",
      currentBestId: "ad-1",
      contenderId: "ad-2",
      roundIndex: 0,
      status: "IN_PROGRESS",
      roundDays: [
        {
          id: "day-1",
          abTestRoundId: "round-1",
          dayIndex: 0,
          deploymentConfigId: "deploy-1",
          status: "IN_PROGRESS",
          currentBestResult: 2.5,
          contenderResult: 3.2,
        },
      ],
    },
  ],
  ads: [
    {
      id: "ad-1",
      urn: "urn:li:sponsoredCreative:*********",
      adVarients: {
        title: "Introducing Our New Platform",
        description: "Save time and boost productivity with our AI-powered solution.",
        imageUrl: "https://example.com/image1.jpg",
      },
      metrics: {
        impressions: 5000,
        clicks: 150,
        oneClickLeads: 25,
        costInUsd: 750,
        externalWebsiteConversions: 10,
      },
    },
    {
      id: "ad-2",
      urn: "urn:li:sponsoredCreative:987654321",
      adVarients: {
        title: "Transform Your Workflow",
        description: "Discover how our solution can streamline your daily tasks.",
        imageUrl: "https://example.com/image2.jpg",
      },
      metrics: {
        impressions: 4800,
        clicks: 180,
        oneClickLeads: 30,
        costInUsd: 720,
        externalWebsiteConversions: 12,
      },
    },
  ],
};
