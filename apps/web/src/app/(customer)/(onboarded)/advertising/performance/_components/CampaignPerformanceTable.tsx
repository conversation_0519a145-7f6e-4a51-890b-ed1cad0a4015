"use client";

import { Fragment, useCallback, useEffect, useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";

import { Skeleton } from "@kalos/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@kalos/ui/table/table";

import {
  AdFormat,
  AdFormatType,
  AdProgramObjectiveType,
  AdVariant,
  CurrentRunningAbTest,
  Metrics,
  RawMetrics,
} from "../_types";

/**
 * Represents the data structure for a campaign's performance metrics
 */
type CampaignData = {
  id: string;
  segmentId: string;
  adProgramId: string;
  goals: string;
  objectiveType: AdProgramObjectiveType;
  status:
    | "ACTIVE"
    | "PAUSED"
    | "COMPLETED"
    | "DRAFT"
    | "CANCELLED"
    | "PENDING_DELETION"
    | "ARCHIVED"
    | "REMOVED";
  campaignGroup: {
    name: string;
    type: AdFormatType;
  };
  weekLearnings: {
    audience: string;
    valueProp: string;
    ctr: number;
    ctrTrend: "up" | "down";
  };
  demos: number;
  closedWon: number;
  timeline: {
    days: number;
    endDate: string;
    startDate: string;
  };
  keyResult: {
    value: number;
    type: "Engagements" | "Leads";
    rate?: number;
  };
  spent: {
    value: number;
    budget: number;
  };
};

/**
 * Type for LinkedIn Ad Program data
 */
interface AdProgramAndCampaignGroup {
  adProgram: {
    id: string;
    title?: string;
    startDatetime: Date | string;
    endDatetime?: Date | string | null;
    objectiveType: AdProgramObjectiveType;
    adFormat: AdFormat;
    totalBudget?: number | null;
    monthlyBudget?: number | null;
    status:
      | "ACTIVE"
      | "DRAFT"
      | "ARCHIVED_DRAFT"
      | "ARCHIVED_ACTIVE"
      | "COMPLETED";
    type?: "EVERGREEN" | "EVENT_DRIVEN";
    linkedInAdAccountId?: string;
  };
  campaignGroup: {
    linkedInAdSegmentId: string;
    linkedInCampaignGroupUrn: string;
    name: string;
    status:
      | "ACTIVE"
      | "DRAFT"
      | "ARCHIVED"
      | "PAUSED"
      | "CANCELLED"
      | "PENDING_DELETION"
      | "REMOVED";
    totalBudget: number;
  };
}

/**
 * Represents the relationship between an ad and a segment
 */
interface AdSegment {
  id: string;
  segmentId: string;
  adProgramId: string;
  ready: boolean;
}

/**
 * Represents the data structure for a segment
 */
interface SegmentData {
  id: string;
  status: "ACTIVE" | "GENERATING" | "ARCHIVED";
  organizationId?: number;
  name?: string | null;
  verticals: string[];
}

/**
 * Campaign Performance Table component displays ad campaigns and their performance metrics
 *
 * @param isLoading - Whether the data is currently loading
 * @param adPrograms - Array of ad programs to display
 * @param adAccountId - LinkedIn ad account ID
 * @param periodMonths - Number of months to include in the performance data
 * @param status - Filter for campaign status
 */
export function CampaignPerformanceTable({
  isLoading = false,
  adPrograms = [],
  adAccountId = "",
  periodMonths = 6,
  status = "ACTIVE",
  fromDate,
  toDate,
}: {
  isLoading?: boolean;
  adPrograms: AdProgramAndCampaignGroup[];
  adAccountId: string;
  periodMonths: number;
  status?: string;
  fromDate?: Date;
  toDate?: Date;
}) {
  const router = useRouter();
  const [selectedAdAccount, setSelectedAdAccount] = useState<string | null>(
    null,
  );

  const adAccountsQuery = api.v2.ads.adAccounts.getForOrganization.useQuery();

  useEffect(() => {
    if (adAccountsQuery.data?.length && !selectedAdAccount) {
      setSelectedAdAccount(adAccountsQuery.data[0]?.id || null);
    }
  }, [adAccountsQuery.data, selectedAdAccount]);

  const adProgramIds = useMemo(
    () => adPrograms.map((p) => p.adProgram.id).filter(Boolean),
    [adPrograms],
  );

  const allSegmentsQuery =
    api.v2.ads.adSegment.getAllAdSegmentsForAdPrograms.useQuery(
      { adProgramIds },
      {
        enabled: !isLoading && adProgramIds.length > 0,
      },
    );

  const allSegmentDetailsQuery =
    api.v2.core.segment.getSegmentsForOrganization.useQuery(undefined, {
      enabled: !isLoading && adPrograms.length > 0,
    });

  /**
   * Groups segments by ad program, limiting to max 2 segments per program
   */
  const adSegmentsByProgram = useMemo(() => {
    if (!allSegmentsQuery.data) return {};
    const result: Record<string, string[]> = {};

    allSegmentsQuery.data.forEach((segment) => {
      if (!segment.adProgramId) return;

      if (!result[segment.adProgramId]) {
        result[segment.adProgramId] = [];
      }

      result[segment.adProgramId]!.push(segment.id);
    });

    return result;
  }, [allSegmentsQuery.data]);

  const limitedSegmentIds = useMemo(() => {
    return Object.values(adSegmentsByProgram).flat();
  }, [adSegmentsByProgram]);

  const campaignGroupAnalytics =
    api.v2.ads.linkedInCampaignGroup.getAnalyticsForBatchCampaignGroups.useQuery(
      {
        linkedInAdSegments: limitedSegmentIds,
        fromDate: fromDate,
        toDate: toDate,
      },
      { enabled: limitedSegmentIds.length > 0 },
    );

  /**
   * Processes the raw data into the structured campaign data format
   * Calculates metrics for each campaign by combining data from multiple sources
   */
  const campaignData = useMemo(() => {
    if (
      isLoading ||
      !allSegmentsQuery.data ||
      !allSegmentDetailsQuery.data ||
      adPrograms.length === 0 ||
      campaignGroupAnalytics.isLoading ||
      !campaignGroupAnalytics.data
    ) {
      return [];
    }

    const result: CampaignData[] = [];

    const adProgramBySegmentId = new Map<string, (typeof adPrograms)[number]>();
    for (const prog of adPrograms) {
      adProgramBySegmentId.set(prog.campaignGroup.linkedInAdSegmentId, prog);
    }

    const segmentDetailsById = new Map<string, SegmentData>();
    for (const detail of allSegmentDetailsQuery.data) {
      segmentDetailsById.set(detail.id, detail);
    }

    const campaignAnalyticsByLinkedInAdSegmentId = campaignGroupAnalytics.data;

    const campaignMap: Record<string, boolean> = {};

    allSegmentsQuery.data.forEach((segment: AdSegment) => {
      const adProgramAndCampaignGroup = adProgramBySegmentId.get(segment.id);

      if (!adProgramAndCampaignGroup) {
        console.log("No ad program found for segment", segment);
        return;
      }

      if (
        campaignMap[
          adProgramAndCampaignGroup.campaignGroup.linkedInCampaignGroupUrn
        ]
      ) {
        return;
      } else {
        campaignMap[
          adProgramAndCampaignGroup.campaignGroup.linkedInCampaignGroupUrn
        ] = true;
      }

      const adProgram = adProgramAndCampaignGroup.adProgram;
      const campaignGroup = adProgramAndCampaignGroup.campaignGroup;

      const segmentData = segmentDetailsById.get(segment.segmentId);

      if (segmentData) {
        const startDate =
          adProgram.startDatetime instanceof Date
            ? adProgram.startDatetime
            : new Date(adProgram.startDatetime);

        const endDate = adProgram.endDatetime
          ? adProgram.endDatetime instanceof Date
            ? adProgram.endDatetime
            : new Date(adProgram.endDatetime)
          : null;

        const formattedStartDate = new Intl.DateTimeFormat("en-US", {
          month: "numeric",
          day: "numeric",
          year: "2-digit",
        }).format(startDate);

        let formattedEndDate;
        let daysActive;

        if (endDate) {
          formattedEndDate = new Intl.DateTimeFormat("en-US", {
            month: "numeric",
            day: "numeric",
            year: "2-digit",
          }).format(endDate);

          daysActive = Math.ceil(
            (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24),
          );
        } else {
          formattedEndDate = null;

          daysActive = Math.ceil(
            (new Date().getTime() - startDate.getTime()) /
              (1000 * 60 * 60 * 24),
          );
        }

        const keyResultType: "Engagements" | "Leads" =
          adProgram.objectiveType === "LEAD_GENERATION"
            ? "Leads"
            : "Engagements";

        const goalText =
          adProgram.objectiveType === "LEAD_GENERATION"
            ? "Lead Generation"
            : "Awareness";

        const campaignAnalytics =
          campaignAnalyticsByLinkedInAdSegmentId[segment.id];

        const totalEngagements = campaignAnalytics?.totalEngagements || 0;
        const totalLeads = campaignAnalytics?.oneClickLeads || 0;
        const totalSpent = campaignAnalytics?.costInUsd || 0;

        const budget =
          adProgram.type === "EVENT_DRIVEN"
            ? adProgram.totalBudget
            : adProgram.monthlyBudget;

        result.push({
          id: segment.id,
          segmentId: segment.segmentId,
          goals: goalText,
          status: campaignGroup.status,
          objectiveType: adProgram.objectiveType,
          adProgramId: adProgram.id,
          campaignGroup: {
            name: `${adProgram.title || ""} - ${segmentData.name || ""}`,
            type: adProgram.adFormat.format,
          },
          weekLearnings: {
            audience: segmentData.name || "",
            valueProp: "",
            ctr: 0,
            ctrTrend: "up",
          },
          demos: 0,
          closedWon: 0,
          timeline: {
            days: daysActive,
            endDate: formattedEndDate || "",
            startDate: formattedStartDate,
          },
          keyResult: {
            value: keyResultType === "Leads" ? totalLeads : totalEngagements,
            type: keyResultType,
            rate: keyResultType === "Leads" ? totalLeads : undefined,
          },
          spent: {
            value: totalSpent,
            budget: budget || 0,
          },
        });
      }
    });

    return result;
  }, [
    adPrograms,
    isLoading,
    allSegmentsQuery.data,
    allSegmentDetailsQuery.data,
    campaignGroupAnalytics.data,
  ]);

  /**
   * Groups campaign data by their goal types for display in the table
   */
  const groupedByGoals = useMemo(() => {
    const groups: Record<string, CampaignData[]> = {};

    campaignData.forEach((campaign) => {
      const goalKey = campaign.goals || "Unknown";
      if (!groups[goalKey]) {
        groups[goalKey] = [];
      }
      groups[goalKey].push(campaign);
    });

    return groups;
  }, [campaignData]);
  /**
   * Handles navigation to the campaign detail page when a row is clicked
   *
   * @param campaign - The campaign data for the clicked row
   */
  const handleRowClick = useCallback(
    (campaign: CampaignData) => {
      router.push(
        `/advertising/performance/${campaign.id}?` +
          new URLSearchParams({
            name: campaign.campaignGroup.name,
            budget: campaign.spent.budget.toString(),
            totalSpent: campaign.spent.value.toFixed(2).toString(),
            format: campaign.campaignGroup.type,
            objectiveType: campaign.objectiveType,
            segmentId: campaign.segmentId,
            startDate: campaign.timeline.startDate,
            endDate: campaign.timeline.endDate || "",
            status: campaign.status,
            adProgramId: campaign.adProgramId,
          }).toString(),
      );
    },
    [router],
  );
  const isQueryLoading =
    isLoading ||
    allSegmentsQuery.isLoading ||
    allSegmentDetailsQuery.isLoading ||
    campaignGroupAnalytics.isLoading;

  const hasQueryError =
    allSegmentsQuery.isError ||
    allSegmentDetailsQuery.isError ||
    campaignGroupAnalytics.isError;

  if (isQueryLoading) {
    return <LoadingTable />;
  }

  if (hasQueryError) {
    return (
      <div className="flex h-48 items-center justify-center rounded-md border">
        <p className="text-destructive">
          Error loading campaign data. Please try again later.
        </p>
      </div>
    );
  }

  if (campaignData.length === 0) {
    return (
      <div className="flex h-48 items-center justify-center rounded-md border">
        <p className="text-muted-foreground">No campaign data available.</p>
      </div>
    );
  }

  return (
    <div className="flex  w-full flex-col">
      <div className="flex-grow overflow-auto">
        <div className="max-h-[60vh] overflow-y-auto rounded-md border">
          <Table>
            <TableHeader className="sticky top-0 z-20">
              <TableRow>
                <TableHead>Goal</TableHead>
                <TableHead>Campaign Group</TableHead>
                <TableHead>Key Result</TableHead>
                <TableHead>Total Spend</TableHead>
                <TableHead>Start Date</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Object.entries(groupedByGoals).map(([goalType, campaigns]) => (
                <Fragment key={goalType}>
                  {campaigns.map((campaign, index) => (
                    <TableRow
                      key={campaign.id}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleRowClick(campaign)}
                    >
                      <TableCell>{index === 0 ? goalType : ""}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>{campaign.campaignGroup.name}</span>
                          <span className="text-xs text-muted-foreground">
                            {campaign.campaignGroup.type}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>
                            {campaign.keyResult.value} {campaign.keyResult.type}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>${campaign.spent.value.toFixed(2)}</TableCell>
                      <TableCell>{campaign.timeline.startDate}</TableCell>
                      <TableCell className="text-blue-500">
                        View Details
                      </TableCell>
                    </TableRow>
                  ))}
                </Fragment>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}

/**
 * Displays a loading skeleton while campaign data is being fetched
 */
function LoadingTable() {
  return (
    <div className=" w-full overflow-hidden rounded-md border">
      <Table className="">
        <TableHeader>
          <TableRow className="align-middle">
            <TableHead>Goals</TableHead>
            <TableHead>Campaign group</TableHead>
            <TableHead>Timeline</TableHead>
            <TableHead>Key Result</TableHead>
            <TableHead>Spent/Budget</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: 10 }).map((_, index) => (
            <TableRow key={index} className="h-6 align-middle">
              <TableCell className="h-6 py-0.5 align-middle">
                <Skeleton className="h-3 w-24 bg-gray-200" />
              </TableCell>
              <TableCell className="h-6  py-0.5 align-middle">
                <div className="flex flex-col gap-[2px]">
                  <Skeleton className="h-3 w-28 bg-gray-200" />
                  <Skeleton className="h-2 w-20 bg-gray-200" />
                </div>
              </TableCell>
              <TableCell className="h-6  py-0.5 align-middle">
                <div className="flex flex-col gap-[2px]">
                  <Skeleton className="h-3 w-10 bg-gray-200" />
                  <Skeleton className="h-4 w-20 bg-gray-200" />
                </div>
              </TableCell>
              <TableCell className="h-6  py-0.5 align-middle">
                <div className="flex flex-col gap-[2px]">
                  <Skeleton className="h-2 w-8 bg-gray-200" />
                  <Skeleton className="h-4 w-20 bg-gray-200" />
                </div>
              </TableCell>
              <TableCell className="h-6  py-0.5 align-middle">
                <div className="flex items-center justify-between gap-[2px]">
                  <div className="flex flex-col gap-[2px]">
                    <Skeleton className="h-2 w-16 bg-gray-200" />
                    <Skeleton className="h-2 w-20 bg-gray-200" />
                  </div>
                  <Skeleton className="h-2 w-4 bg-gray-200" />
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
