"use client";

import { useState } from "react";
import { CaseStudyTable } from "@/features/advertising/assets/components/caseStudyTable";
import { AdCreativeTableConfig } from "@/features/advertising/assets/components/creative-table-config";
import { ExampleAdTable } from "@/features/advertising/assets/components/exmapleAdTable";
import { UploadPositioning } from "@/features/advertising/components/upload-positioning";
import { UploadStyleGuide } from "@/features/advertising/components/upload-style-guide";

import { Button } from "@kalos/ui/button";
import { cn } from "@kalos/ui/index";

export default function AssetsPage() {
  const [state, setState] = useState<
    "creative" | "positioning" | "examples_ads" | "styleGuide" | "caseStudy"
  >("creative");
  return (
    <div className="flex h-full w-full flex-col justify-start bg-white px-6 py-8">
      <div className="flex items-center justify-between">
        <div className="flex w-fit items-center justify-start space-x-2 rounded-md  bg-secondary px-2 py-1.5">
          <Button
            onClick={() => setState("creative")}
            className={cn(
              state == "creative"
                ? "bg-white text-primary hover:bg-white"
                : "bg-secondary text-muted-foreground hover:bg-secondary",
              "h-6 w-fit border-0 text-xs font-medium shadow-none",
            )}
          >
            Creative
          </Button>
          <Button
            onClick={() => setState("positioning")}
            className={cn(
              state == "positioning"
                ? "bg-white text-primary  hover:bg-white"
                : "bg-secondary text-muted-foreground hover:bg-secondary",
              "h-6 w-fit border-0 text-xs font-medium shadow-none",
            )}
          >
            Positioning
          </Button>
          <Button
            onClick={() => setState("examples_ads")}
            className={cn(
              state == "examples_ads"
                ? "bg-white text-primary  hover:bg-white"
                : "bg-secondary text-muted-foreground hover:bg-secondary",
              "h-6 w-fit border-0 text-xs font-medium shadow-none",
            )}
          >
            Example Ads
          </Button>
          <Button
            onClick={() => setState("styleGuide")}
            className={cn(
              state == "styleGuide"
                ? "bg-white text-primary  hover:bg-white"
                : "bg-secondary text-muted-foreground hover:bg-secondary",
              "h-6 w-fit border-0 text-xs font-medium shadow-none",
            )}
          >
            Style Guide
          </Button>
          <Button
            onClick={() => setState("caseStudy")}
            className={cn(
              state == "caseStudy"
                ? "bg-white text-primary  hover:bg-white"
                : "bg-secondary text-muted-foreground hover:bg-secondary",
              "h-6 w-fit border-0 text-xs font-medium shadow-none",
            )}
          >
            Case Studies
          </Button>
        </div>
      </div>
      <div className="flex h-full w-full flex-col justify-start">
        {state === "creative" && <AdCreativeTableConfig />}
        {state === "positioning" && <UploadPositioning />}
        {state === "examples_ads" && <ExampleAdTable />}
        {state === "styleGuide" && <UploadStyleGuide />}
        {state === "caseStudy" && <CaseStudyTable />}
      </div>
    </div>
  );
}
