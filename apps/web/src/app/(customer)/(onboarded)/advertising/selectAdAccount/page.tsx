"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";
import { ReloadIcon } from "@radix-ui/react-icons";

import { Button } from "@kalos/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@kalos/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@kalos/ui/select";

export default function SelectAdAccountPage() {
  const adAccountQuery = api.v2.ads.adAccounts.getForOrganization.useQuery();
  const linkedInAdAccounts =
    api.v2.ads.adAccounts.linkedInApi.getLinkedInAdAccountsForUser.useQuery();

  const apiUtils = api.useUtils();
  const setAdAccountForOrganization =
    api.v2.ads.adAccounts.setForOrganization.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.ads.adAccounts.getForOrganization.invalidate();
      },
    });
  const linkedInOauth = api.v2.ads.linkedInUser.getForUser.useQuery();
  const router = useRouter();
  useEffect(() => {
    if (
      adAccountQuery.data !== undefined &&
      adAccountQuery.data[0] !== undefined
    ) {
      router.push(
        `/advertising/adAccounts/${adAccountQuery.data[0].id}/campaignGroups/new`,
      );
    }
  }, [adAccountQuery.data]);

  useEffect(() => {
    if (linkedInOauth.data == null) {
      router.push("/advertising/new");
    }
  }, [linkedInOauth.data]);

  const [selectedAdAccountId, setSelectedAdAccountId] = useState<string>("");

  const handleSubmit = () => {
    setAdAccountForOrganization.mutate({
      linkedInAdAccountUrn: selectedAdAccountId,
    });
  };

  return (
    <div className="flex h-full w-full flex-col items-center justify-center">
      {adAccountQuery.isLoading && <div>loading...</div>}
      {adAccountQuery.data !== undefined && adAccountQuery.data[0] == null && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg ">
              Select a LinkedIn AD account
            </CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center gap-y-4">
            <Select
              value={selectedAdAccountId}
              onValueChange={(value) => setSelectedAdAccountId(value)}
            >
              <SelectTrigger className="w-96">
                <SelectValue placeholder="Select an ad account" />
              </SelectTrigger>
              <SelectContent>
                {linkedInAdAccounts.data?.map((adAccount) => (
                  <SelectItem
                    key={adAccount.adAccountUrn}
                    value={adAccount.adAccountUrn}
                  >
                    {adAccount.adAccountName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="flex w-full items-center justify-end">
              <Button
                onClick={handleSubmit}
                disabled={
                  selectedAdAccountId == "" ||
                  setAdAccountForOrganization.isPending ||
                  linkedInAdAccounts.isLoading ||
                  adAccountQuery.isLoading
                }
              >
                {setAdAccountForOrganization.isPending && (
                  <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                )}
                Submit
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
