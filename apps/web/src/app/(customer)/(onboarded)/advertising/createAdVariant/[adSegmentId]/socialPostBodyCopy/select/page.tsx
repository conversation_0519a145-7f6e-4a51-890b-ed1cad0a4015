"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";

import { OngoingTestingThing } from "../../_components/selectComponent";

export default function SelectMessageCopyPage({
  params,
}: {
  params: {
    adSegmentId: string;
  };
}) {
  const adSegment = api.v2.ads.adSegment.getOne.useQuery({
    adSegmentId: params.adSegmentId,
  });

  return (
    <>
      {adSegment.data && (
        <SelectBodyCopy
          adSegmentId={params.adSegmentId}
          segmentId={adSegment.data.segmentId}
        />
      )}
    </>
  );
}

function SelectBodyCopy({
  adSegmentId,
  segmentId,
}: {
  adSegmentId: string;
  segmentId: string;
}) {
  const [selectedSocialPostBodyTypes, setSelectedSocialPostBodyTypes] =
    useState<string[]>([]);

  const socialPostBodyCopiesQuery =
    api.v2.ads.adSegmentSelectedSocialPostBodyType.getAdSegmentSelectedSocialPostBodyTypesForAdSegment.useQuery(
      {
        adSegmentId,
      },
    );

  useEffect(() => {
    if (socialPostBodyCopiesQuery.data) {
      setSelectedSocialPostBodyTypes(
        socialPostBodyCopiesQuery.data.map((each) => each.type),
      );
    }
  }, [socialPostBodyCopiesQuery.data]);

  const existingSocialPostCopies = ["Short", "Long", "Funny and Catchy"];

  const apiUtils = api.useUtils();

  const router = useRouter();

  const setSocialPostBodyTypesMutation =
    api.v2.ads.adSegmentSelectedSocialPostBodyType.setSelectedSocialPostBodyTypesForSingleAdSegment.useMutation(
      {
        onSuccess: async () => {
          await apiUtils.v2.ads.invalidate();
          router.push(
            `/advertising/createAdVariant/${adSegmentId}/socialPostBodyCopy/copy`,
          );
        },
      },
    );

  const bestValuePropQuery = api.v2.ads.adSegmentBestVariant.getOne.useQuery({
    adSegmentId: adSegmentId,
    adFormatType: "valueProp",
  });

  const onSubmit = () => {
    if (!bestValuePropQuery.data) {
      return;
    }
    setSocialPostBodyTypesMutation.mutate({
      adSegmentId: adSegmentId,
      socialPostBodyTypes: selectedSocialPostBodyTypes,
      valuePropId: bestValuePropQuery.data.variantId,
    });
  };

  return (
    <OngoingTestingThing
      title="Copy Testing"
      description="Select the types of copy to test. View copy on the next screen."
      variantType="Copy"
      adSegmentId={adSegmentId}
      selectedVariants={selectedSocialPostBodyTypes}
      setSelectedVarients={setSelectedSocialPostBodyTypes}
      preExistingVarients={existingSocialPostCopies}
      customVarients={
        socialPostBodyCopiesQuery.data
          ?.filter(
            (each) => !existingSocialPostCopies.find((v) => v === each.type),
          )
          .map((each) => each.type) ?? []
      }
      onSubmit={onSubmit}
      isLoading={!socialPostBodyCopiesQuery.data}
    />
  );
}
