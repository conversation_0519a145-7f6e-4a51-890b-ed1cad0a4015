"use client";

import { api } from "@/trpc/client";

export default function CreateAdVariantLayout({
  params,
  children,
}: {
  params: { adSegmentId: string };
  children: React.ReactNode;
}) {
  const adSegment = api.v2.ads.adSegment.getOne.useQuery({
    adSegmentId: params.adSegmentId,
  });

  const adProgram = api.v2.ads.linkedInAdProgram.getOne.useQuery(
    {
      id: adSegment.data?.linkedInAdProgramId ?? "",
    },
    {
      enabled: !!adSegment.data?.linkedInAdProgramId,
    },
  );

  const segment = api.v2.core.segment.getSegment.useQuery(
    {
      segmentId: adSegment.data?.segmentId ?? "",
    },
    {
      enabled: !!adSegment.data?.segmentId,
    },
  );

  return (
    <div className="flex h-full w-full flex-col items-start justify-start">
      <div className="flex h-16 w-full items-center justify-center border-b bg-white">
        <h1 className="self-center text-lg font-medium">
          {adProgram.data?.title} - {segment.data?.name}
        </h1>
      </div>
      <div className="flex h-full w-full flex-col items-start justify-start overflow-y-auto">
        {children}
      </div>
    </div>
  );
}
