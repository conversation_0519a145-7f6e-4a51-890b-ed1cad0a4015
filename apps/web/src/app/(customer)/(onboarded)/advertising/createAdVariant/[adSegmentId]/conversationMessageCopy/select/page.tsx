"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";

import { OngoingTestingThing } from "../../_components/selectComponent";

export default function SelectMessageCopyPage({
  params,
}: {
  params: {
    adSegmentId: string;
  };
}) {
  const adSegment = api.v2.ads.adSegment.getOne.useQuery({
    adSegmentId: params.adSegmentId,
  });

  return (
    <>
      {adSegment.data && (
        <SelectMessageCopy
          adSegmentId={params.adSegmentId}
          segmentId={adSegment.data.segmentId}
        />
      )}
    </>
  );
}

function SelectMessageCopy({
  adSegmentId,
  segmentId,
}: {
  adSegmentId: string;
  segmentId: string;
}) {
  const [
    selectedConversationMessageTypes,
    setSelectedConversationMessageTypes,
  ] = useState<string[]>([]);

  const conversationMessageCopiesQuery =
    api.v2.ads.adSegmentSelectedConversationMessageType.getAdSegmentSelectedConversationMessageTypesForAdSegment.useQuery(
      {
        adSegmentId,
      },
    );

  useEffect(() => {
    if (conversationMessageCopiesQuery.data) {
      setSelectedConversationMessageTypes(
        conversationMessageCopiesQuery.data.map((each) => each.type),
      );
    }
  }, [conversationMessageCopiesQuery.data]);

  const existingConversationMessageCopies = ["Short", "Long"];

  const apiUtils = api.useUtils();

  const router = useRouter();

  const setConversationMessageTypesMutation =
    api.v2.ads.adSegmentSelectedConversationMessageType.setSelectedConversationMessageTypesForSingleAdSegment.useMutation(
      {
        onSuccess: async () => {
          await apiUtils.v2.ads.invalidate();
          router.push(
            `/advertising/createAdVariant/${adSegmentId}/conversationMessageCopy/copy`,
          );
        },
      },
    );

  const bestValuePropQuery = api.v2.ads.adSegmentBestVariant.getOne.useQuery({
    adSegmentId: adSegmentId,
    adFormatType: "valueProp",
  });

  const bestConversationSubjectQuery =
    api.v2.ads.adSegmentBestVariant.getOne.useQuery({
      adSegmentId: adSegmentId,
      adFormatType: "conversationSubject",
    });

  const conversationSubjectCopyQuery =
    api.v2.ads.conversationSubjectCopy.getOne.useQuery(
      {
        conversationSubjectCopyId:
          bestConversationSubjectQuery.data?.variantId ?? "",
        status: "ACTIVE",
      },
      {
        enabled:
          !!bestConversationSubjectQuery.data && !!bestValuePropQuery.data,
      },
    );

  const conversationSubjecToUseQuery =
    api.v2.ads.conversationSubjectCopy.getOneByValuePorpIdAndType.useQuery(
      {
        valuePropId: bestValuePropQuery.data?.variantId ?? "",
        type: conversationSubjectCopyQuery.data?.type ?? "",
        status: "ACTIVE",
      },
      {
        enabled:
          !!conversationSubjectCopyQuery.data &&
          !!bestConversationSubjectQuery.data &&
          !!bestValuePropQuery.data,
      },
    );

  const onSubmit = () => {
    setConversationMessageTypesMutation.mutate({
      adSegmentId: adSegmentId,
      conversationMessageCopyTypes: selectedConversationMessageTypes,
      valuePropId: bestValuePropQuery.data?.variantId ?? "",
      conversationSubjectCopyType:
        conversationSubjectCopyQuery.data?.type ?? "",
    });
  };

  return (
    <OngoingTestingThing
      title="Message Testing"
      description="Select the types of messages to test. View copy on the next screen."
      variantType="Conversation Message"
      adSegmentId={adSegmentId}
      selectedVariants={selectedConversationMessageTypes}
      setSelectedVarients={setSelectedConversationMessageTypes}
      preExistingVarients={existingConversationMessageCopies}
      customVarients={
        conversationMessageCopiesQuery.data
          ?.filter(
            (each) =>
              !existingConversationMessageCopies.find((v) => v === each.type),
          )
          .map((each) => each.type) ?? []
      }
      onSubmit={onSubmit}
      isLoading={!conversationMessageCopiesQuery.data}
    />
  );
}
