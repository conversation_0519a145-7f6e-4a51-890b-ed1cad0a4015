"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";

import { OngoingTestingThing } from "../../_components/selectComponent";

export default function SelectValuePropPage({
  params,
}: {
  params: {
    adSegmentId: string;
  };
}) {
  const adSegment = api.v2.ads.adSegment.getOne.useQuery({
    adSegmentId: params.adSegmentId,
  });

  return (
    <>
      {adSegment.data && (
        <SelectValueProp
          adSegmentId={params.adSegmentId}
          segmentId={adSegment.data.segmentId}
        />
      )}
    </>
  );
}

function SelectValueProp({
  adSegmentId,
  segmentId,
}: {
  adSegmentId: string;
  segmentId: string;
}) {
  const [selectedValueProps, setSelectedValueProps] = useState<string[]>([]);

  const valuePropsQuery =
    api.v2.ads.adSegmentValueProp.getAdSegmentValuePropsForAdSegment.useQuery(
      {
        adSegmentId,
        status: "DRAFT",
      },
      {
        enabled: !!adSegmentId,
      },
    );

  const activeValuePropsQuery =
    api.v2.ads.adSegmentValueProp.getAdSegmentValuePropsForAdSegment.useQuery(
      {
        adSegmentId,
        status: "ACTIVE",
      },
      {
        enabled: !!adSegmentId,
      },
    );

  useEffect(() => {
    if (valuePropsQuery.data) {
      setSelectedValueProps(valuePropsQuery.data.map((each) => each.valueProp));
    }
  }, [valuePropsQuery.data]);

  const preExistingValuePropsQuery =
    api.v2.core.segment.getSegmentValuePropForSegment.useQuery(
      {
        segmentId,
      },
      {
        enabled: !!segmentId,
      },
    );

  const adSegmentQuery = api.v2.ads.adSegment.getOne.useQuery({
    adSegmentId,
  });

  const apiUtils = api.useUtils();

  const router = useRouter();

  const setValuePropsMutation =
    api.v2.ads.adSegmentValueProp.setAdSegmentValuePropsForOneAdSegment.useMutation(
      {
        onSuccess: async () => {
          await apiUtils.v2.ads.invalidate();
          router.push(
            `/advertising/createAdVariant/${adSegmentId}/valueProp/copy`,
          );
        },
      },
    );

  const onSubmit = () => {
    setValuePropsMutation.mutate({
      adSegmentId: adSegmentId,
      valuePropIds: selectedValueProps,
    });
  };

  return (
    <OngoingTestingThing
      title="Value Proposition Testing"
      description="Select the value props to test. View copy on the next screen."
      variantType="Value Prop"
      adSegmentId={adSegmentId}
      selectedVariants={selectedValueProps}
      setSelectedVarients={setSelectedValueProps}
      preExistingVarients={
        preExistingValuePropsQuery.data
          ?.map((each) => each.name)
          .filter(
            (each) =>
              !activeValuePropsQuery.data?.find((v) => v.valueProp === each),
          ) ?? []
      }
      customVarients={
        valuePropsQuery.data
          ?.filter(
            (each) =>
              !preExistingValuePropsQuery.data?.find(
                (v) => v.name === each.valueProp,
              ),
          )
          .map((each) => each.valueProp) ?? []
      }
      onSubmit={onSubmit}
      isLoading={!valuePropsQuery.data || !preExistingValuePropsQuery.data}
    />
  );
}
