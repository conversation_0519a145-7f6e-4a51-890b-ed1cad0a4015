"use client";

import { SetStateAction, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";
import { getBaseUrl } from "@/trpc/provider";

import {
  AdCardShell,
  AdComponent,
  AdText,
  SegmentComponent,
} from "../../_components/AdComponent";

export default function ConversationMessageCopyPage({
  params,
}: {
  params: {
    adSegmentId: string;
  };
}) {
  const adSegmentQuery = api.v2.ads.adSegment.getOne.useQuery({
    adSegmentId: params.adSegmentId,
  });

  const bestValuePropQuery = api.v2.ads.adSegmentBestVariant.getOne.useQuery({
    adSegmentId: params.adSegmentId,
    adFormatType: "valueProp",
  });

  const bestConversationSubjectQuery =
    api.v2.ads.adSegmentBestVariant.getOne.useQuery({
      adSegmentId: params.adSegmentId,
      adFormatType: "conversationSubject",
    });

  const bestConversationMessageQuery =
    api.v2.ads.adSegmentBestVariant.getOne.useQuery({
      adSegmentId: params.adSegmentId,
      adFormatType: "conversationMessageCopy",
    });

  const conversationSubjectCopyQuery =
    api.v2.ads.conversationSubjectCopy.getOne.useQuery(
      {
        conversationSubjectCopyId:
          bestConversationSubjectQuery.data?.variantId ?? "",
        status: "ACTIVE",
      },
      {
        enabled:
          !!bestConversationSubjectQuery.data && !!bestValuePropQuery.data,
      },
    );

  const conversationMessageCopyQuery =
    api.v2.ads.conversationMessageCopy.getOne.useQuery(
      {
        conversationMessageCopyId:
          bestConversationMessageQuery.data?.variantId ?? "",
        status: "ACTIVE",
      },
      {
        enabled:
          !!bestConversationMessageQuery.data && !!bestValuePropQuery.data,
      },
    );

  const conversationMessageCopyToUserQuery =
    api.v2.ads.conversationMessageCopy.getOneByValuePorpIdAndType.useQuery(
      {
        valuePropId: bestValuePropQuery.data?.variantId ?? "",
        subjectType: conversationSubjectCopyQuery.data?.type ?? "",
        messageType: conversationMessageCopyQuery.data?.type ?? "",
        status: "ACTIVE",
      },
      {
        enabled:
          !!conversationSubjectCopyQuery.data &&
          !!bestConversationMessageQuery.data &&
          !!bestValuePropQuery.data,
      },
    );

  return (
    <>
      {adSegmentQuery.data &&
        conversationMessageCopyToUserQuery.data &&
        bestValuePropQuery.data &&
        bestConversationSubjectQuery.data && (
          <ConversationMessageCopyThings
            adSegment={adSegmentQuery.data}
            valuePropId={conversationMessageCopyToUserQuery.data.valuePropId}
            conversationSubjectCopyType={
              conversationSubjectCopyQuery.data?.type ?? ""
            }
            conversationMessageCopyType={
              conversationMessageCopyQuery.data?.type ?? ""
            }
          />
        )}
    </>
  );
}
function ConversationMessageCopyThings({
  adSegment,
  valuePropId,
  conversationSubjectCopyType,
  conversationMessageCopyType,
}: {
  adSegment: {
    id: string;
    segmentId: string;
    linkedInAdProgramId: string;
    ready: boolean;
  };
  valuePropId: string;
  conversationSubjectCopyType: string;
  conversationMessageCopyType: string;
}) {
  const variantsQuery =
    api.v2.ads.adSegmentSelectedConversationCallToActionType.getAdSegmentSelectedConversationCallToActionTypesForAdSegment.useQuery(
      {
        adSegmentId: adSegment.id,
      },
    );

  const router = useRouter();

  const apiUtils = api.useUtils();
  const createNewVarientsMutation =
    api.v2.ads.adSegment.createNewVarients.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.ads.invalidate();
        router.push(`/advertising/performance/${adSegment.id}`);
      },
    });

  return (
    <AdComponent
      title="Conversation Call to Action"
      description="Create a conversation call to action for your ad segment."
      onSubmit={() => {
        if (!variantsQuery.data) {
          alert("No variants found");
          return;
        }
        createNewVarientsMutation.mutate({
          adSegmentId: adSegment.id,
          adFormatType: "SPONSORED_INMAIL",
          data: {
            type: "ctaCopy",
            data: {
              subjectType: conversationSubjectCopyType,
              valuePropId: valuePropId,
              messageCopyType: conversationMessageCopyType,
              data: variantsQuery.data.map((each) => ({
                ctaType: each.type,
              })),
            },
          },
        });
      }}
      goToPreviousStep={() => {
        router.push(
          `/advertising/createAdVariant/${adSegment.id}/conversationCallToActionCopy/select`,
        );
      }}
    >
      <div>
        <SegmentComponent
          segment={{
            id: adSegment.id,
            adProgramId: adSegment.linkedInAdProgramId,
            segmentId: adSegment.segmentId,
          }}
        >
          {variantsQuery.data?.map((variant) => (
            <ConversationMessageCopyVariant
              adSegmentId={adSegment.id}
              key={variant.id}
              variant={variant.type}
              valuePropId={valuePropId}
              conversationSubjectCopyType={conversationSubjectCopyType}
              conversationMessageCopyType={conversationMessageCopyType}
            />
          ))}
        </SegmentComponent>
      </div>
    </AdComponent>
  );
}

function ConversationMessageCopyVariant({
  variant,
  adSegmentId,
  valuePropId,
  conversationSubjectCopyType,
  conversationMessageCopyType,
}: {
  variant: string;
  adSegmentId: string;
  valuePropId: string;
  conversationSubjectCopyType: string;
  conversationMessageCopyType: string;
}) {
  const [adCopy, setAdCopy] = useState({
    data: "",
    done: false,
  });

  async function getAdCopy() {
    const response = await fetch(`${getBaseUrl()}/stream-copy-variant`, {
      method: "POST",
      headers: {
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        adFormatType: "SPONSORED_INMAIL",
        data: {
          copyVariantTypes: "conversationCallToAction",
          standardCopy: "",
          type: variant,
          valuePropId: valuePropId,
          subjectType: conversationSubjectCopyType,
          messageType: conversationMessageCopyType,
        },
      }),
    });
    if (!response.ok || !response.body) {
      throw response.statusText;
    }

    setAdCopy({
      data: "",
      done: false,
    });

    // Here we start prepping for the streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    const loopRunner = true;

    while (loopRunner) {
      // Here we start reading the stream, until its done.
      const { value, done } = await reader.read();
      if (done) {
        break;
      }
      const decodedChunk = decoder
        .decode(value, { stream: true })
        .split("!JSON_LINE_END!");
      for (const each of decodedChunk) {
        try {
          const json = JSON.parse(each);
          console.log("JSON", json);
          setAdCopy((prev) => ({
            data: prev.data + json.data,
            done: json.done,
          }));
        } catch (e) {}
      }
    }
  }

  useEffect(() => {
    getAdCopy();
  }, []);

  const updateCopyMutation =
    api.v2.ads.conversationCopy.updateConversationCallToActionCopy.useMutation();

  const [isSubmittingCopyUpdate, setIsSubmittingCopyUpdate] = useState(false);

  async function submitFeedback(feedback: string) {
    const response = await fetch(`${getBaseUrl()}/stream-refined`, {
      method: "POST",
      headers: {
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        baseCopy: adCopy.data,
        feedback: feedback,
        config: {
          type: "conversation",
          field: "subject",
        },
      }),
    });
    if (!response.ok || !response.body) {
      throw response.statusText;
    }

    setAdCopy({
      data: "",
      done: false,
    });

    // Here we start prepping for the streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    const loopRunner = true;

    while (loopRunner) {
      // Here we start reading the stream, until its done.
      const { value, done } = await reader.read();
      if (done) {
        break;
      }
      const decodedChunk = decoder.decode(value, { stream: true });
      for (const each of decodedChunk) {
        try {
          setAdCopy((prev) => ({
            ...prev,
            data: prev.data + each,
          }));
        } catch (e) {}
      }
    }
  }

  const bestValuePropQuery = api.v2.ads.adSegmentBestVariant.getOne.useQuery({
    adSegmentId: adSegmentId,
    adFormatType: "valueProp",
  });

  const bestConversationSubjectQuery =
    api.v2.ads.adSegmentBestVariant.getOne.useQuery({
      adSegmentId: adSegmentId,
      adFormatType: "conversationSubject",
    });

  const bestConversationMessageQuery =
    api.v2.ads.adSegmentBestVariant.getOne.useQuery({
      adSegmentId: adSegmentId,
      adFormatType: "conversationMessageCopy",
    });

  const conversationSubjectCopyQuery =
    api.v2.ads.conversationSubjectCopy.getOne.useQuery(
      {
        conversationSubjectCopyId:
          bestConversationSubjectQuery.data?.variantId ?? "",
        status: "ACTIVE",
      },
      {
        enabled:
          !!bestConversationSubjectQuery.data && !!bestValuePropQuery.data,
      },
    );

  const conversationMessageCopyQuery =
    api.v2.ads.conversationMessageCopy.getOne.useQuery(
      {
        conversationMessageCopyId:
          bestConversationMessageQuery.data?.variantId ?? "",
        status: "ACTIVE",
      },
      {
        enabled:
          !!bestConversationMessageQuery.data && !!bestValuePropQuery.data,
      },
    );

  const conversationMessageCopyToUserQuery =
    api.v2.ads.conversationMessageCopy.getOneByValuePorpIdAndType.useQuery(
      {
        valuePropId: bestValuePropQuery.data?.variantId ?? "",
        subjectType: conversationSubjectCopyQuery.data?.type ?? "",
        messageType: conversationMessageCopyQuery.data?.type ?? "",
        status: "ACTIVE",
      },
      {
        enabled:
          !!conversationSubjectCopyQuery.data &&
          !!bestConversationMessageQuery.data &&
          !!bestValuePropQuery.data,
      },
    );

  return (
    <AdCardShell varient={variant}>
      <AdText
        otherThings={[
          {
            data: conversationMessageCopyToUserQuery.data?.content ?? "",
            variantType: "Message",
          },
        ]}
        otherThingsBellow={[
          {
            data: conversationSubjectCopyQuery.data?.content ?? "",
            variantType: "Subject",
          },
        ]}
        varientType={"CTA"}
        adCopy={adCopy}
        setAdCopy={setAdCopy}
        updateCopy={() => {
          setIsSubmittingCopyUpdate(true);
          updateCopyMutation.mutate({
            linkedInAdSegmentValuePropId: valuePropId,
            content: adCopy.data,
            type: variant,
            subjectType: conversationSubjectCopyType,
            messageType: conversationMessageCopyType,
          });
          setIsSubmittingCopyUpdate(false);
        }}
        isSubmittingCopyUpdate={updateCopyMutation.isPending}
        submitCopyFeedback={submitFeedback}
        refetchAdCopy={() => {
          getAdCopy();
        }}
      />
    </AdCardShell>
  );
}
