"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";
import { getBaseUrl } from "@/trpc/provider";

import {
  AdCardShell,
  AdComponent,
  AdText,
  SegmentComponent,
} from "../../_components/AdComponent";

export default function SocialPostBodyCopyPage({
  params,
}: {
  params: {
    adSegmentId: string;
  };
}) {
  const adSegmentQuery = api.v2.ads.adSegment.getOne.useQuery({
    adSegmentId: params.adSegmentId,
  });

  const bestValuePropQuery = api.v2.ads.adSegmentBestVariant.getOne.useQuery({
    adSegmentId: params.adSegmentId,
    adFormatType: "valueProp",
  });

  return (
    <>
      {adSegmentQuery.data && bestValuePropQuery.data && (
        <SocialPostBodyCopyThings
          adSegment={adSegmentQuery.data}
          valuePropId={bestValuePropQuery.data.variantId}
        />
      )}
    </>
  );
}

function SocialPostBodyCopyThings({
  adSegment,
  valuePropId,
}: {
  adSegment: {
    id: string;
    segmentId: string;
    linkedInAdProgramId: string;
    ready: boolean;
  };
  valuePropId: string;
}) {
  const variantsQuery =
    api.v2.ads.adSegmentSelectedSocialPostBodyType.getAdSegmentSelectedSocialPostBodyTypesForAdSegment.useQuery(
      {
        adSegmentId: adSegment.id,
      },
    );

  const router = useRouter();

  const apiUtils = api.useUtils();

  const createNewVarientsMutation =
    api.v2.ads.adSegment.createNewVarients.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.ads.invalidate();
        router.push(`/advertising/performance/${adSegment.id}`);
      },
    });

  return (
    <AdComponent
      title="Social Post Body Copy"
      description="Create a social post body copy for your ad segment."
      onSubmit={() => {
        if (!variantsQuery.data) {
          alert("No variants found");
          return;
        }
        createNewVarientsMutation.mutate({
          adSegmentId: adSegment.id,
          adFormatType: "SPONSORED_CONTENT",
          data: {
            type: "socialPostBodyCopy",
            data: {
              valuePropId: valuePropId,
              data: variantsQuery.data.map((each) => ({
                socialPostBodyCopyType: each.type,
              })),
            },
          },
        });
      }}
      goToPreviousStep={() => {
        router.push(
          `/advertising/createAdVariant/${adSegment.id}/socialPostBodyCopy/select`,
        );
      }}
    >
      <div>
        <SegmentComponent
          segment={{
            id: adSegment.id,
            adProgramId: adSegment.linkedInAdProgramId,
            segmentId: adSegment.segmentId,
          }}
        >
          {variantsQuery.data?.map((variant) => (
            <SocialPostBodyCopyVariant
              key={variant.id}
              variant={variant.type}
              valuePropId={valuePropId}
            />
          ))}
        </SegmentComponent>
      </div>
    </AdComponent>
  );
}

function SocialPostBodyCopyVariant({
  variant,
  valuePropId,
}: {
  variant: string;
  valuePropId: string;
}) {
  const [adCopy, setAdCopy] = useState({
    data: "",
    done: false,
  });
  console.log(variant);

  async function getAdCopy() {
    const response = await fetch(`${getBaseUrl()}/stream-copy-variant`, {
      method: "POST",
      headers: {
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        adFormatType: "SPONSORED_CONTENT",
        data: {
          copyVariantTypes: "socialPostBody",
          standardCopy: "",
          type: variant,
          valuePropId: valuePropId,
        },
      }),
    });
    if (!response.ok || !response.body) {
      throw response.statusText;
    }

    setAdCopy({
      data: "",
      done: false,
    });

    // Here we start prepping for the streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    const loopRunner = true;

    while (loopRunner) {
      // Here we start reading the stream, until its done.
      const { value, done } = await reader.read();
      if (done) {
        break;
      }
      const decodedChunk = decoder
        .decode(value, { stream: true })
        .split("!JSON_LINE_END!");
      for (const each of decodedChunk) {
        try {
          const json = JSON.parse(each);
          console.log("JSON", json);
          setAdCopy((prev) => ({
            data: prev.data + json.data,
            done: json.done,
          }));
        } catch (e) {}
      }
    }
  }

  useEffect(() => {
    getAdCopy();
  }, []);

  const updateCopyMutation =
    api.v2.ads.socialPostBaseCopy.updateSocialPostBodyCopy.useMutation();

  const [isSubmittingCopyUpdate, setIsSubmittingCopyUpdate] = useState(false);

  async function submitFeedback(feedback: string) {
    const response = await fetch(`${getBaseUrl()}/stream-refined`, {
      method: "POST",
      headers: {
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        baseCopy: adCopy.data,
        feedback: feedback,
        config: {
          type: "socialPost",
          field: "body",
        },
      }),
    });
    if (!response.ok || !response.body) {
      throw response.statusText;
    }

    setAdCopy({
      data: "",
      done: false,
    });

    // Here we start prepping for the streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    const loopRunner = true;

    while (loopRunner) {
      // Here we start reading the stream, until its done.
      const { value, done } = await reader.read();
      if (done) {
        break;
      }
      const decodedChunk = decoder.decode(value, { stream: true });
      for (const each of decodedChunk) {
        try {
          setAdCopy((prev) => ({
            ...prev,
            data: prev.data + each,
          }));
        } catch (e) {}
      }
    }
  }

  const ctaForSocialPostQuery =
    api.v2.ads.socialPostBaseCopy.getCtaForSocialPost.useQuery({
      valuePropId: valuePropId,
      socialPostCopyType: "Standard",
      status: "DRAFT",
    });

  return (
    <AdCardShell varient={variant}>
      {ctaForSocialPostQuery.data && (
        <AdText
          otherThings={[]}
          otherThingsBellow={[
            {
              data: ctaForSocialPostQuery.data.callToAction,
              variantType: "CTA",
            },
          ]}
          varientType={"Body"}
          adCopy={adCopy}
          setAdCopy={setAdCopy}
          updateCopy={() => {
            setIsSubmittingCopyUpdate(true);
            updateCopyMutation.mutate({
              valuePropId: valuePropId,
              body: adCopy.data,
              type: variant,
            });
            setIsSubmittingCopyUpdate(false);
          }}
          isSubmittingCopyUpdate={updateCopyMutation.isPending}
          submitCopyFeedback={submitFeedback}
          refetchAdCopy={() => {
            getAdCopy();
          }}
        />
      )}
    </AdCardShell>
  );
}
