"use client";

import { Dispatch, SetStateAction, useEffect, useState } from "react";
import Link from "next/link";
import { snakeCaseToWords } from "@/app/utils/snakeCaseToWords";
import { api } from "@/trpc/client";
import { ReloadIcon } from "@radix-ui/react-icons";
import { ArrowLeft, ArrowRight, PlusIcon } from "lucide-react";

import { Button } from "@kalos/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@kalos/ui/card";
import { Checkbox } from "@kalos/ui/checkbox";
import { Input } from "@kalos/ui/input";
import { Skeleton } from "@kalos/ui/skeleton";

export function SelectVariantShell({
  title,
  description,
  children,
  previousButtonAction,
  previousButtonText,
  previousButtonDisabled,
  nextButtonAction,
  nextButtonText,
  nextButtonDisabled,
}: {
  title: string;
  description: string;
  children: React.ReactNode;
  previousButtonAction: () => void;
  previousButtonText: string;
  previousButtonDisabled?: boolean;
  nextButtonAction: () => void;
  nextButtonText: string;
  nextButtonDisabled?: boolean;
}) {
  return (
    <div className="flex h-full w-full flex-col items-start justify-between">
      <div className="flex w-full flex-1 flex-col items-start justify-start space-y-4 overflow-y-scroll px-4 py-6 pb-24">
        <div className="flex w-full flex-col items-start justify-start">
          <h1 className="text-xl font-medium">{title}</h1>
          <h2 className="text-base">{description}</h2>
        </div>
        {children}
      </div>
      <div className="fixed bottom-0 left-0 right-0 z-50 flex h-16 w-full items-center justify-between border-t bg-background p-4 shadow-md">
        <Button
          onClick={previousButtonAction}
          disabled={previousButtonDisabled}
          className="border-1 border border-blue-200 bg-blue-100 text-primary hover:bg-blue-200"
        >
          {previousButtonText}
          <ArrowLeft width="16" className="mr-2" />
        </Button>
        <Button onClick={nextButtonAction} disabled={nextButtonDisabled}>
          {nextButtonText}
          <ArrowRight width="16" className="ml-2" />
        </Button>
      </div>
    </div>
  );
}

export function OngoingTestingThing({
  adSegmentId,
  selectedVariants,
  setSelectedVarients,
  preExistingVarients,
  customVarients,
  onSubmit,
  isLoading,
  variantType,
  title,
  description,
}: {
  adSegmentId: string;
  selectedVariants: string[];
  setSelectedVarients: Dispatch<SetStateAction<string[]>>;
  preExistingVarients: string[];
  customVarients: string[];
  onSubmit: () => void;
  isLoading: boolean;
  variantType: string;
  title: string;
  description: string;
}) {
  const adSegment = api.v2.ads.adSegment.getOne.useQuery({
    adSegmentId: adSegmentId,
  });

  const [userAdded, setUserAdded] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (isSubmitting) {
      setUserAdded([]);
    }
  }, [isSubmitting]);

  function doOnSubmit() {
    setIsSubmitting(true);
    onSubmit();
    setIsSubmitting(false);
  }

  return (
    <SelectVariantShell
      title={title}
      description={description}
      previousButtonAction={() => {}}
      previousButtonText="Previous"
      nextButtonAction={doOnSubmit}
      nextButtonText="Next"
      nextButtonDisabled={isSubmitting || isLoading}
    >
      <OngoingTestingCard segmentId={adSegment.data?.segmentId ?? ""}>
        <OngoingTestingCheckbox
          selectedVarients={selectedVariants}
          preExistingVarients={preExistingVarients}
          customVarients={customVarients}
          userAddedVarients={userAdded}
          setUserAddedVarients={setUserAdded}
          setSelectedVarients={setSelectedVarients}
          disabled={isSubmitting || isLoading}
          isLoading={isLoading}
        />
      </OngoingTestingCard>
    </SelectVariantShell>
  );
}

export function OngoingTestingCard({
  segmentId,
  children,
}: {
  segmentId: string;
  children: React.ReactNode;
}) {
  const sQuery = api.v2.core.segment.getSegment.useQuery({
    segmentId: segmentId,
  });
  useEffect(() => {
    if (sQuery.data) {
      console.log(sQuery.data);
    }
  }, [sQuery.data]);
  return (
    <Card className="h-[280] w-[424px]  px-4 py-4">
      <CardHeader>
        <CardTitle>
          {sQuery.data ? (
            <SegmentDetails
              row={{
                original: sQuery.data,
              }}
            />
          ) : (
            <Skeleton className="h-[20px] w-[100px]" />
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  );
}

export function OngoingTestingCheckbox({
  selectedVarients,
  preExistingVarients,
  customVarients,
  userAddedVarients,
  setUserAddedVarients,
  setSelectedVarients,
  disabled,
  isLoading,
}: {
  selectedVarients: string[];
  preExistingVarients: string[];
  customVarients: string[];
  userAddedVarients: string[];
  setUserAddedVarients: Dispatch<SetStateAction<string[]>>;
  setSelectedVarients: Dispatch<SetStateAction<string[]>>;
  disabled: boolean;
  isLoading: boolean;
}) {
  return (
    <>
      {isLoading ? (
        <>
          {Array.from({ length: 10 }).map((_, index) => (
            <Skeleton key={index} className="h-[20px] w-[100px]" />
          ))}
        </>
      ) : (
        <>
          {preExistingVarients.map((each) => (
            <div
              key={each}
              className="flex items-center justify-start space-x-2"
            >
              <Checkbox
                disabled={disabled}
                checked={
                  selectedVarients.find(
                    (selectedVariant) => selectedVariant === each,
                  ) !== undefined
                }
                onCheckedChange={(checked) => {
                  if (checked) {
                    setSelectedVarients([...selectedVarients, each]);
                  } else {
                    setSelectedVarients(
                      selectedVarients.filter(
                        (selectedVarient) => selectedVarient !== each,
                      ),
                    );
                  }
                }}
              />
              <p>{each}</p>
            </div>
          ))}
          {customVarients
            .filter((each) => !preExistingVarients.find((v) => v === each))
            .map((each, index) => (
              <div
                key={each}
                className="flex items-center justify-start space-x-2"
              >
                <Checkbox
                  disabled={disabled}
                  checked={
                    selectedVarients.find(
                      (selectedVarient) => selectedVarient === each,
                    ) !== undefined
                  }
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedVarients([...selectedVarients, each]);
                    } else {
                      setSelectedVarients(
                        selectedVarients.filter(
                          (selectedVarient) => selectedVarient !== each,
                        ),
                      );
                    }
                  }}
                />
                <p>{each}</p>
              </div>
            ))}

          {userAddedVarients.map((each, index) => (
            <div
              key={index}
              className="flex items-center justify-start space-x-2"
            >
              <Checkbox
                checked={
                  selectedVarients.find(
                    (selectedVarient) => selectedVarient === each,
                  ) !== undefined
                }
                disabled={each === "" || disabled}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setSelectedVarients([...selectedVarients, each]);
                  } else {
                    setSelectedVarients(
                      selectedVarients.filter(
                        (selectedVarient) => selectedVarient !== each,
                      ),
                    );
                  }
                }}
              />
              <Input
                onChange={(event) => {
                  const newVarients = [...userAddedVarients];
                  newVarients[index] = event.target.value;
                  setUserAddedVarients(newVarients);
                }}
                className="h-7 w-full"
                value={userAddedVarients[index]}
              />
            </div>
          ))}

          <Button
            variant="ghost"
            disabled={disabled}
            className="hover:bg-transpatent p-0 text-xs text-primary hover:text-blue-300"
            onClick={() => {
              setUserAddedVarients([...userAddedVarients, ""]);
            }}
          >
            <PlusIcon className="m-0 mr-2 p-0" height="16" width="16" /> Add
            more
          </Button>
        </>
      )}
    </>
  );
}

function SegmentDetails({
  row,
}: {
  row: {
    original: {
      id: string;
      name?: string | null;
      verticals: string[];
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
      annualContractValueLowBound?: number | null;
      annualContractValueHighBound?: number | null;
      jobFunction?: string | null;
      jobSeniority?: string | null;
    };
  };
}) {
  if (!row.original.name) {
    const nameArray: string[] = [];
    if (row.original.jobFunction) {
      nameArray.push(snakeCaseToWords(row.original.jobFunction));
    }
    if (row.original.jobSeniority) {
      nameArray.push(snakeCaseToWords(row.original.jobSeniority));
    }

    if (row.original.verticals) {
      nameArray.push(row.original.verticals.join(", "));
    }

    if (
      row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualContractValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }
    if (
      row.original.annualContractValueLowBound &&
      !row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ ACV`,
      );
    }
    if (
      !row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualContractValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }

    if (
      row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }
    if (
      row.original.annualRevenueLowBound &&
      !row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ prospect revenue`,
      );
    }
    if (
      !row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }

    if (
      row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-${row.original.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }
    if (
      row.original.numberOfEmployeesLowBound &&
      !row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ employees`,
      );
    }
    if (
      !row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `<${row.original.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }

    let acvQueryParam = "";
    if (
      row.original.annualContractValueHighBound == 20000 &&
      row.original.annualContractValueLowBound == undefined
    ) {
      acvQueryParam = "1";
    } else if (
      row.original.annualContractValueHighBound == 50000 &&
      row.original.annualContractValueLowBound == 20000
    ) {
      acvQueryParam = "2";
    } else if (
      row.original.annualContractValueHighBound == 100000 &&
      row.original.annualContractValueLowBound == 50000
    ) {
      acvQueryParam = "3";
    } else if (
      row.original.annualContractValueHighBound == undefined &&
      row.original.annualContractValueLowBound == 100000
    ) {
      acvQueryParam = "4";
    }

    let empoyeesQueryParam = "";
    if (
      row.original.numberOfEmployeesHighBound == 100 &&
      row.original.numberOfEmployeesLowBound == undefined
    ) {
      empoyeesQueryParam = "1";
    } else if (
      row.original.numberOfEmployeesHighBound == 500 &&
      row.original.numberOfEmployeesLowBound == 100
    ) {
      empoyeesQueryParam = "2";
    } else if (
      row.original.numberOfEmployeesHighBound == undefined &&
      row.original.numberOfEmployeesLowBound == 500
    ) {
      empoyeesQueryParam = "3";
    }

    let prospectRevenue = "";
    if (
      row.original.annualRevenueLowBound == 1000000 &&
      row.original.annualRevenueHighBound == 10000000
    ) {
      prospectRevenue = "1";
    } else if (
      row.original.annualRevenueLowBound == 10000000 &&
      row.original.annualRevenueHighBound == 200000000
    ) {
      prospectRevenue = "2";
    } else if (
      row.original.annualRevenueLowBound == 200000000 &&
      row.original.annualRevenueHighBound == undefined
    ) {
      prospectRevenue = "3";
    }

    // Search params
    const params = new URLSearchParams({
      acv: acvQueryParam,
      vertical: row.original.verticals?.join(",") ?? "",
      employees: empoyeesQueryParam,
      prospectRevenue: prospectRevenue,
    });

    if (nameArray.length == 0) {
      return (
        <div className="space-y-1">
          <Link href={`/}`} className="w-[80px]">
            All
          </Link>
        </div>
      );
    }
    return (
      <div className="w-[380px] space-y-1 text-wrap ">
        <span className="50 w-[380px] text-wrap">{nameArray.join(" • ")}</span>
      </div>
    );
  }
  return (
    <div className="space-y-1">
      <span className="w-[80px]">{row.original.name}</span>
    </div>
  );
}
