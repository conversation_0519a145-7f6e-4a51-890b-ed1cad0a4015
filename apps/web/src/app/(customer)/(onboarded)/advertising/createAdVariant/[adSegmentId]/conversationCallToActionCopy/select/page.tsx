"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";

import { OngoingTestingThing } from "../../_components/selectComponent";

export default function SelectConversationCallToActionCopyPage({
  params,
}: {
  params: {
    adSegmentId: string;
  };
}) {
  const adSegment = api.v2.ads.adSegment.getOne.useQuery({
    adSegmentId: params.adSegmentId,
  });

  return (
    <>
      {adSegment.data && (
        <SelectConversationCallToActionCopy
          adSegmentId={params.adSegmentId}
          segmentId={adSegment.data.segmentId}
        />
      )}
    </>
  );
}

function SelectConversationCallToActionCopy({
  adSegmentId,
  segmentId,
}: {
  adSegmentId: string;
  segmentId: string;
}) {
  const [
    selectedConversationCallToActionTypes,
    setSelectedConversationCallToActionTypes,
  ] = useState<string[]>([]);

  const conversationCallToActionCopiesQuery =
    api.v2.ads.adSegmentSelectedConversationCallToActionType.getAdSegmentSelectedConversationCallToActionTypesForAdSegment.useQuery(
      {
        adSegmentId,
      },
    );

  useEffect(() => {
    if (conversationCallToActionCopiesQuery.data) {
      setSelectedConversationCallToActionTypes(
        conversationCallToActionCopiesQuery.data.map((each) => each.type),
      );
    }
  }, [conversationCallToActionCopiesQuery.data]);

  const existingConversationCallToActionCopies = ["Funny", "One Word"];

  const apiUtils = api.useUtils();

  const router = useRouter();

  const setConversationCallToActionTypesMutation =
    api.v2.ads.adSegmentSelectedConversationCallToActionType.setSelectedConversationCallToActionTypesForSingleAdSegment.useMutation(
      {
        onSuccess: async () => {
          await apiUtils.v2.ads.invalidate();
          router.push(
            `/advertising/createAdVariant/${adSegmentId}/conversationCallToActionCopy/copy`,
          );
        },
      },
    );

  const bestValuePropQuery = api.v2.ads.adSegmentBestVariant.getOne.useQuery({
    adSegmentId: adSegmentId,
    adFormatType: "valueProp",
  });

  const bestConversationSubjectQuery =
    api.v2.ads.adSegmentBestVariant.getOne.useQuery({
      adSegmentId: adSegmentId,
      adFormatType: "conversationSubject",
    });

  const bestConversationMessageQuery =
    api.v2.ads.adSegmentBestVariant.getOne.useQuery({
      adSegmentId: adSegmentId,
      adFormatType: "conversationMessageCopy",
    });

  const conversationSubjectCopyQuery =
    api.v2.ads.conversationSubjectCopy.getOne.useQuery(
      {
        conversationSubjectCopyId:
          bestConversationSubjectQuery.data?.variantId ?? "",
        status: "ACTIVE",
      },
      {
        enabled:
          !!bestConversationSubjectQuery.data && !!bestValuePropQuery.data,
      },
    );

  const conversationMessageCopyQuery =
    api.v2.ads.conversationMessageCopy.getOne.useQuery(
      {
        conversationMessageCopyId:
          bestConversationMessageQuery.data?.variantId ?? "",
        status: "ACTIVE",
      },
      {
        enabled:
          !!bestConversationMessageQuery.data && !!bestValuePropQuery.data,
      },
    );

  const onSubmit = () => {
    setConversationCallToActionTypesMutation.mutate({
      adSegmentId: adSegmentId,
      conversationCallToActionCopyTypes: selectedConversationCallToActionTypes,
      valuePropId: bestValuePropQuery.data?.variantId ?? "",
      conversationSubjectCopyType:
        conversationSubjectCopyQuery.data?.type ?? "",
      conversationMessageCopyType:
        conversationMessageCopyQuery.data?.type ?? "",
    });
  };

  return (
    <OngoingTestingThing
      title="Call to Action Testing"
      description="Select the types of call to actions to test. View copy on the next screen."
      variantType="CTA"
      adSegmentId={adSegmentId}
      selectedVariants={selectedConversationCallToActionTypes}
      setSelectedVarients={setSelectedConversationCallToActionTypes}
      preExistingVarients={existingConversationCallToActionCopies}
      customVarients={
        conversationCallToActionCopiesQuery.data
          ?.filter(
            (each) =>
              !existingConversationCallToActionCopies.find(
                (v) => v === each.type,
              ),
          )
          .map((each) => each.type) ?? []
      }
      onSubmit={onSubmit}
      isLoading={!conversationCallToActionCopiesQuery.data}
    />
  );
}
