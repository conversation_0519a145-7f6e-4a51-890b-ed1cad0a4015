"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";

import { AdComponent } from "../../_components/AdComponent";

export default function ConversationMessageCopyPage({
  params,
}: {
  params: {
    adSegmentId: string;
  };
}) {
  const variantsQuery =
    api.v2.ads.adSegmentSelectedConversationMessageType.getAdSegmentSelectedConversationMessageTypesForAdSegment.useQuery(
      {
        adSegmentId: params.adSegmentId,
      },
    );

  const bestConversationSubjectQuery =
    api.v2.ads.adSegmentBestVariant.getOne.useQuery({
      adSegmentId: params.adSegmentId,
      adFormatType: "conversationSubject",
    });

  const bestValuePropQuery = api.v2.ads.adSegmentBestVariant.getOne.useQuery({
    adSegmentId: params.adSegmentId,
    adFormatType: "valueProp",
  });

  const [isSaving, setIsSaving] = useState(false);
  const conversationSubjectCopyQuery =
    api.v2.ads.conversationSubjectCopy.getOne.useQuery(
      {
        conversationSubjectCopyId:
          bestConversationSubjectQuery.data?.variantId ?? "",
        status: "ACTIVE",
      },
      {
        enabled: !!bestConversationSubjectQuery.data,
      },
    );

  const conversationMessageCopyMutation =
    api.v2.ads.conversationCopy.updateConversationMessageCopy.useMutation({
      onSuccess: () => {
        setIsSaving(false);
      },
    });

  function onUpdateConversationMessageCopy(copy: string, varientType: string) {
    setIsSaving(true);
    if (!bestConversationSubjectQuery.data) {
      return;
    }
    conversationMessageCopyMutation.mutate({
      type: varientType,
      content: copy,
      linkedInAdSegmentValuePropId: bestValuePropQuery.data?.variantId ?? "",
      subjectType: conversationSubjectCopyQuery.data?.type ?? "",
      status: "DRAFT",
    });
  }

  const finalParentQuery =
    api.v2.ads.conversationSubjectCopy.getOneByValuePorpIdAndType.useQuery(
      {
        valuePropId: bestValuePropQuery.data?.variantId ?? "",
        type: conversationSubjectCopyQuery.data?.type ?? "",
        status: "ACTIVE",
      },
      {
        enabled:
          !!bestValuePropQuery.data && !!conversationSubjectCopyQuery.data,
      },
    );

  const router = useRouter();

  return (
    <>
      {bestConversationSubjectQuery.data && finalParentQuery.data && (
        <AdComponent
          adSegmentId={params.adSegmentId}
          variants={variantsQuery.data?.map((each) => each.type) ?? []}
          variantType={{
            adFormat: "SPONSORED_INMAIL",
            varientType: "conversationMessage",
          }}
          onSubmit={function (): void {
            alert("hisafasdf");
          }}
          updateCopy={onUpdateConversationMessageCopy}
          isSaving={isSaving}
          setIsSaving={setIsSaving}
          goToPreviousStep={() => {
            router.push(
              `/advertising/createAdVariant/${params.adSegmentId}/conversationMessageCopy/select`,
            );
          }}
          parentId={finalParentQuery.data.id}
        />
      )}
    </>
  );
}
