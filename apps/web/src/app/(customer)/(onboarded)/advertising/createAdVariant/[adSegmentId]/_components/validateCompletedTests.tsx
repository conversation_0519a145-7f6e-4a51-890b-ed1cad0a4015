"use client";

import { api } from "@/trpc/client";

export function ValidateCompletedTests({
  adSegmentId,
  children,
  adFormatType,
}: {
  adSegmentId: string;
  children: React.ReactNode;
  adFormatType:
    | "audience"
    | "valueProp"
    | "creative"
    | "conversationSubject"
    | "socialPostBodyCopy"
    | "conversationCallToAction"
    | "conversationMessageCopy"
    | "socialPostCallToAction";
}) {
  const adSegmentBestVariantQuery =
    api.v2.ads.adSegmentBestVariant.getOne.useQuery({
      adSegmentId: adSegmentId,
      adFormatType: adFormatType,
    });

  return (
    <>
      {adSegmentBestVariantQuery.data && children}
      {adSegmentBestVariantQuery.isLoading && (
        <div className="flex h-full w-full items-center justify-center">
          <div className="text-gray-500">Loading...</div>
        </div>
      )}
      {adSegmentBestVariantQuery.isError && (
        <div className="flex h-full w-full items-center justify-center">
          <div className="text-red-500">
            Error loading ad segment best variant
          </div>
        </div>
      )}
      {!adSegmentBestVariantQuery.data &&
        !adSegmentBestVariantQuery.isLoading &&
        !adSegmentBestVariantQuery.isError && (
          <div className="flex h-full w-full items-center justify-center">
            <div className="text-gray-500">
              {camelCaseToWords(adFormatType)} test not finished yet
            </div>
          </div>
        )}
    </>
  );
}

function camelCaseToWords(camelCase: string) {
  return camelCase
    .replace(/([a-z])([A-Z])/g, "$1 $2") // Add a space before each uppercase letter.
    .replace(/^./, (str) => str.toUpperCase()) // Capitalize the first letter of the string.
    .replace(/ ([a-z])/g, (match) => match.toUpperCase()); // Capitalize any letter after a space.
}
