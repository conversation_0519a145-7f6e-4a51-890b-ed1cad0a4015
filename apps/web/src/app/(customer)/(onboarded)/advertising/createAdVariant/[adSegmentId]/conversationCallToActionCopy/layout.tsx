"use client";

import { api } from "@/trpc/client";

import { ValidateCompletedTests } from "../_components/validateCompletedTests";

export default function ValuePropLayout({
  params,
  children,
}: {
  params: {
    adSegmentId: string;
  };
  children: React.ReactNode;
}) {
  return (
    <ValidateCompletedTests
      adSegmentId={params.adSegmentId}
      adFormatType="conversationMessageCopy"
    >
      <ValidateCompletedTests
        adSegmentId={params.adSegmentId}
        adFormatType="conversationSubject"
      >
        <ValidateCompletedTests
          adSegmentId={params.adSegmentId}
          adFormatType="valueProp"
        >
          {children}
        </ValidateCompletedTests>
      </ValidateCompletedTests>
    </ValidateCompletedTests>
  );
}
