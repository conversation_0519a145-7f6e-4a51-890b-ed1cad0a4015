"use client";

import { SetStateAction, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";
import { getBaseUrl } from "@/trpc/provider";

import {
  AdCardShell,
  AdComponent,
  AdText,
  SegmentComponent,
} from "../../_components/AdComponent";

export default function ConversationMessageCopyPage({
  params,
}: {
  params: {
    adSegmentId: string;
  };
}) {
  const adSegmentQuery = api.v2.ads.adSegment.getOne.useQuery({
    adSegmentId: params.adSegmentId,
  });

  const bestValuePropQuery = api.v2.ads.adSegmentBestVariant.getOne.useQuery({
    adSegmentId: params.adSegmentId,
    adFormatType: "valueProp",
  });

  const bestConversationSubjectQuery =
    api.v2.ads.adSegmentBestVariant.getOne.useQuery({
      adSegmentId: params.adSegmentId,
      adFormatType: "conversationSubject",
    });

  const conversationSubjectCopyQuery =
    api.v2.ads.conversationSubjectCopy.getOne.useQuery(
      {
        conversationSubjectCopyId:
          bestConversationSubjectQuery.data?.variantId ?? "",
        status: "ACTIVE",
      },
      {
        enabled:
          !!bestConversationSubjectQuery.data && !!bestValuePropQuery.data,
      },
    );

  const conversationSubjecToUseQuery =
    api.v2.ads.conversationSubjectCopy.getOneByValuePorpIdAndType.useQuery(
      {
        valuePropId: bestValuePropQuery.data?.variantId ?? "",
        type: conversationSubjectCopyQuery.data?.type ?? "",
        status: "ACTIVE",
      },
      {
        enabled:
          !!conversationSubjectCopyQuery.data &&
          !!bestConversationSubjectQuery.data &&
          !!bestValuePropQuery.data,
      },
    );

  return (
    <>
      {adSegmentQuery.data &&
        conversationSubjecToUseQuery.data &&
        bestValuePropQuery.data &&
        bestConversationSubjectQuery.data && (
          <ConversationMessageCopyThings
            adSegment={adSegmentQuery.data}
            valuePropId={conversationSubjecToUseQuery.data.valuePropId}
            conversationSubjectCopyType={conversationSubjecToUseQuery.data.type}
          />
        )}
    </>
  );
}
function ConversationMessageCopyThings({
  adSegment,
  valuePropId,
  conversationSubjectCopyType,
}: {
  adSegment: {
    id: string;
    segmentId: string;
    linkedInAdProgramId: string;
    ready: boolean;
  };
  valuePropId: string;
  conversationSubjectCopyType: string;
}) {
  const variantsQuery =
    api.v2.ads.adSegmentSelectedConversationMessageType.getAdSegmentSelectedConversationMessageTypesForAdSegment.useQuery(
      {
        adSegmentId: adSegment.id,
      },
    );

  const router = useRouter();

  const apiUtils = api.useUtils();
  const createNewVarientsMutation =
    api.v2.ads.adSegment.createNewVarients.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.ads.invalidate();
        router.push(`/advertising/performance/${adSegment.id}`);
      },
    });

  return (
    <AdComponent
      title="Conversation Message Copy"
      description="Create a conversation message copy for your ad segment."
      onSubmit={() => {
        if (!variantsQuery.data) {
          alert("No variants found");
          return;
        }
        createNewVarientsMutation.mutate({
          adSegmentId: adSegment.id,
          adFormatType: "SPONSORED_INMAIL",
          data: {
            type: "messageCopy",
            data: {
              subjectType: conversationSubjectCopyType,
              valuePropId: valuePropId,
              data: variantsQuery.data.map((each) => ({
                messageCopyType: each.type,
                ctaType: "standard",
              })),
            },
          },
        });
      }}
      goToPreviousStep={() => {
        router.push(
          `/advertising/createAdVariant/${adSegment.id}/conversationMessageCopy/select`,
        );
      }}
    >
      <div>
        <SegmentComponent
          segment={{
            id: adSegment.id,
            adProgramId: adSegment.linkedInAdProgramId,
            segmentId: adSegment.segmentId,
          }}
        >
          {variantsQuery.data?.map((variant) => (
            <ConversationMessageCopyVariant
              key={variant.id}
              adSegmentId={adSegment.id}
              variant={variant.type}
              valuePropId={valuePropId}
              conversationSubjectCopyType={conversationSubjectCopyType}
            />
          ))}
        </SegmentComponent>
      </div>
    </AdComponent>
  );
}

function ConversationMessageCopyVariant({
  adSegmentId,
  variant,
  valuePropId,
  conversationSubjectCopyType,
}: {
  adSegmentId: string;
  variant: string;
  valuePropId: string;
  conversationSubjectCopyType: string;
}) {
  const [adCopy, setAdCopy] = useState({
    data: "",
    done: false,
  });

  async function getAdCopy() {
    const response = await fetch(`${getBaseUrl()}/stream-copy-variant`, {
      method: "POST",
      headers: {
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        adFormatType: "SPONSORED_INMAIL",
        data: {
          copyVariantTypes: "conversationMessage",
          standardCopy: "",
          type: variant,
          valuePropId: valuePropId,
          subjectType: conversationSubjectCopyType,
        },
      }),
    });
    if (!response.ok || !response.body) {
      throw response.statusText;
    }

    setAdCopy({
      data: "",
      done: false,
    });

    // Here we start prepping for the streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    const loopRunner = true;

    while (loopRunner) {
      // Here we start reading the stream, until its done.
      const { value, done } = await reader.read();
      if (done) {
        break;
      }
      const decodedChunk = decoder
        .decode(value, { stream: true })
        .split("!JSON_LINE_END!");
      for (const each of decodedChunk) {
        try {
          const json = JSON.parse(each);
          console.log("JSON", json);
          setAdCopy((prev) => ({
            data: prev.data + json.data,
            done: json.done,
          }));
        } catch (e) {}
      }
    }
  }

  useEffect(() => {
    getAdCopy();
  }, []);

  const updateCopyMutation =
    api.v2.ads.conversationCopy.updateConversationMessageCopy.useMutation();

  const [isSubmittingCopyUpdate, setIsSubmittingCopyUpdate] = useState(false);

  async function submitFeedback(feedback: string) {
    const response = await fetch(`${getBaseUrl()}/stream-refined`, {
      method: "POST",
      headers: {
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        baseCopy: adCopy.data,
        feedback: feedback,
        config: {
          type: "conversation",
          field: "message",
        },
      }),
    });
    if (!response.ok || !response.body) {
      throw response.statusText;
    }

    setAdCopy({
      data: "",
      done: false,
    });

    // Here we start prepping for the streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    const loopRunner = true;

    while (loopRunner) {
      // Here we start reading the stream, until its done.
      const { value, done } = await reader.read();
      if (done) {
        break;
      }
      const decodedChunk = decoder.decode(value, { stream: true });
      for (const each of decodedChunk) {
        try {
          setAdCopy((prev) => ({
            ...prev,
            data: prev.data + each,
          }));
        } catch (e) {}
      }
    }
  }

  const bestValuePropQuery = api.v2.ads.adSegmentBestVariant.getOne.useQuery({
    adSegmentId: adSegmentId,
    adFormatType: "valueProp",
  });

  const bestConversationSubjectQuery =
    api.v2.ads.adSegmentBestVariant.getOne.useQuery({
      adSegmentId: adSegmentId,
      adFormatType: "conversationSubject",
    });

  const conversationSubjectCopyQuery =
    api.v2.ads.conversationSubjectCopy.getOne.useQuery(
      {
        conversationSubjectCopyId:
          bestConversationSubjectQuery.data?.variantId ?? "",
        status: "ACTIVE",
      },
      {
        enabled:
          !!bestConversationSubjectQuery.data && !!bestValuePropQuery.data,
      },
    );

  const conversationSubjecToUseQuery =
    api.v2.ads.conversationSubjectCopy.getOneByValuePorpIdAndType.useQuery(
      {
        valuePropId: bestValuePropQuery.data?.variantId ?? "",
        type: conversationSubjectCopyQuery.data?.type ?? "",
        status: "ACTIVE",
      },
      {
        enabled:
          !!conversationSubjectCopyQuery.data &&
          !!bestConversationSubjectQuery.data &&
          !!bestValuePropQuery.data,
      },
    );

  return (
    <AdCardShell varient={variant}>
      <AdText
        otherThings={[
          {
            data: conversationSubjectCopyQuery.data?.content ?? "",
            variantType: "Subject",
          },
        ]}
        otherThingsBellow={[]}
        varientType={"Message"}
        adCopy={adCopy}
        setAdCopy={setAdCopy}
        updateCopy={() => {
          setIsSubmittingCopyUpdate(true);
          updateCopyMutation.mutate({
            linkedInAdSegmentValuePropId: valuePropId,
            content: adCopy.data,
            type: variant,
            subjectType: conversationSubjectCopyType,
          });
          setIsSubmittingCopyUpdate(false);
        }}
        isSubmittingCopyUpdate={updateCopyMutation.isPending}
        submitCopyFeedback={submitFeedback}
        refetchAdCopy={() => {
          getAdCopy();
        }}
      />
    </AdCardShell>
  );
}
