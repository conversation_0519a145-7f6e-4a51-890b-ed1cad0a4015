"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";

import { OngoingTestingThing } from "../../_components/selectComponent";

export default function SelectCtaPage({
  params,
}: {
  params: {
    adSegmentId: string;
  };
}) {
  const adSegment = api.v2.ads.adSegment.getOne.useQuery({
    adSegmentId: params.adSegmentId,
  });

  return (
    <>
      {adSegment.data && (
        <SelectCta
          adSegmentId={params.adSegmentId}
          segmentId={adSegment.data.segmentId}
        />
      )}
    </>
  );
}

function SelectCta({
  adSegmentId,
  segmentId,
}: {
  adSegmentId: string;
  segmentId: string;
}) {
  const [selectedCtaTypes, setSelectedCtaTypes] = useState<string[]>([]);

  const ctas =
    api.v2.ads.adSegmentSelectedSocialPostCallToActionType.getAdSegmentSelectedSocialPostCallToActionTypesForAdSegment.useQuery(
      {
        adSegmentId,
      },
    );

  useEffect(() => {
    if (ctas.data) {
      setSelectedCtaTypes(ctas.data.map((each) => each.type));
    }
  }, [ctas.data]);

  const existingSocialPostCopies = ["Funny", "Catchy"];

  const apiUtils = api.useUtils();

  const router = useRouter();

  const setCtaTypesMutation =
    api.v2.ads.adSegmentSelectedSocialPostCallToActionType.setSelectedSocialPostCallToActionTypesForSingleAdSegment.useMutation(
      {
        onSuccess: async () => {
          await apiUtils.v2.ads.invalidate();
          router.push(
            `/advertising/createAdVariant/${adSegmentId}/socialPostCallToAction/copy`,
          );
        },
      },
    );

  const bestValuePropQuery = api.v2.ads.adSegmentBestVariant.getOne.useQuery({
    adSegmentId: adSegmentId,
    adFormatType: "valueProp",
  });

  const onSubmit = () => {
    if (!bestValuePropQuery.data) {
      return;
    }
    setCtaTypesMutation.mutate({
      adSegmentId: adSegmentId,
      socialPostCallToActionTypes: selectedCtaTypes,
      valuePropId: bestValuePropQuery.data.variantId,
    });
  };

  return (
    <OngoingTestingThing
      title="Call to Action Testing"
      description="Select the types of call to action to test. View copy on the next screen."
      variantType="CTA"
      adSegmentId={adSegmentId}
      selectedVariants={selectedCtaTypes}
      setSelectedVarients={setSelectedCtaTypes}
      preExistingVarients={existingSocialPostCopies}
      customVarients={
        ctas.data
          ?.filter(
            (each) => !existingSocialPostCopies.find((v) => v === each.type),
          )
          .map((each) => each.type) ?? []
      }
      onSubmit={onSubmit}
      isLoading={!ctas.data}
    />
  );
}
