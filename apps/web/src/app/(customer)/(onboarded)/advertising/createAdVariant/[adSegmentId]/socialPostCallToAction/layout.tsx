"use client";

import { api } from "@/trpc/client";

import { ValidateCompletedTests } from "../_components/validateCompletedTests";

export default function ValuePropLayout({
  params,
  children,
}: {
  params: {
    adSegmentId: string;
  };
  children: React.ReactNode;
}) {
  const adSegment = api.v2.ads.adSegment.getOne.useQuery({
    adSegmentId: params.adSegmentId,
  });
  const adProgram = api.v2.ads.linkedInAdProgram.getOne.useQuery(
    {
      id: adSegment.data?.linkedInAdProgramId ?? "",
    },
    {
      enabled: !!adSegment.data?.linkedInAdProgramId,
    },
  );
  const adFormat = adProgram.data?.adFormat;
  return (
    <>
      {adFormat && (
        <ValidateCompletedTests
          adSegmentId={params.adSegmentId}
          adFormatType="socialPostBodyCopy"
        >
          <ValidateCompletedTests
            adSegmentId={params.adSegmentId}
            adFormatType="valueProp"
          >
            {adFormat.type == "SPONSORED_CONTENT" ? (
              <>{children}</>
            ) : (
              <>
                <h1 className="h-full w-full text-center text-2xl font-bold">
                  You can't create a social post body copy for a sponsored
                  conversation or sponsored inmail ad program.
                </h1>
              </>
            )}
          </ValidateCompletedTests>
        </ValidateCompletedTests>
      )}
    </>
  );
}
