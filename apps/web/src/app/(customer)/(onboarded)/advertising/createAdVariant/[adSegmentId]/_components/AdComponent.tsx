"use client";

import React, {
  Dispatch,
  SetStateAction,
  useEffect,
  useRef,
  useState,
} from "react";
import { snakeCaseToWords } from "@/app/utils/snakeCaseToWords";
import { api } from "@/trpc/client";
import { getBaseUrl } from "@/trpc/provider";
import { ArrowLeft, WandIcon } from "lucide-react";

import { Button } from "@kalos/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@kalos/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";
import { cn } from "@kalos/ui/index";
import { Popover, PopoverContent, PopoverTrigger } from "@kalos/ui/popover";
import { Textarea } from "@kalos/ui/textarea";

import { SegmentDetails } from "../../../adAccounts/[adAccount]/campaignGroups/[campaignGroupId]/ad/_components/SegmentDetails";

export function AdComponent({
  onSubmit,
  title,
  description,
  goToPreviousStep,
  children,
}: {
  onSubmit: () => void;
  title: string;
  description: string;
  goToPreviousStep: () => void;
  children: React.ReactNode;
}) {
  return (
    <div className="justify-strat flex w-full flex-col items-start">
      <div className="flex w-full flex-col items-start justify-start px-4 py-6">
        <h1 className="text-xl font-medium">{title}</h1>
        <h2 className="text-base">{description}</h2>
      </div>
      <div className="flex h-[calc(100vh-64px)] w-full flex-col items-start justify-start space-y-4 overflow-auto px-4 pb-20">
        {children}
      </div>

      <div className="sticky bottom-0 left-0 right-0 z-10 flex h-16 w-full items-center justify-between border-t bg-background p-4">
        <Button
          onClick={goToPreviousStep}
          className="border-1 border border-blue-200 bg-blue-100 text-primary hover:bg-blue-200"
        >
          <ArrowLeft width="16" className="mr-2" /> Previous
        </Button>
        <Button onClick={onSubmit}>Submit</Button>
      </div>
    </div>
  );
}

export function SegmentComponent({
  segment,
  children,
}: {
  segment: {
    id: string;
    adProgramId: string;
    segmentId: string;
  };
  children: React.ReactNode;
}) {
  const segmentQuery = api.v2.core.segment.getSegment.useQuery({
    segmentId: segment.segmentId,
  });

  return (
    <Card className="w-full">
      <CardHeader className="w-full">
        <CardTitle className="flex w-full items-center justify-between">
          <div className="flex items-center">
            {segmentQuery.data && (
              <SegmentDetails row={{ original: segmentQuery.data }} />
            )}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent
        data-content-segment-container
        className="flex w-full flex-wrap items-start justify-start gap-4"
      >
        {children}
      </CardContent>
    </Card>
  );
}

export function AdCardShell({
  varient,
  children,
}: {
  varient: string;
  children: React.ReactNode;
}) {
  return (
    <Card className="self-stretch">
      <CardHeader>
        <CardTitle>{snakeCaseToWords(varient)}</CardTitle>
      </CardHeader>
      <CardContent className=" h-full">{children}</CardContent>
    </Card>
  );
}

export function AdText({
  otherThings,
  otherThingsBellow,
  varientType,
  adCopy,
  setAdCopy,
  isSubmittingCopyUpdate,
  submitCopyFeedback,
  updateCopy,
  refetchAdCopy,
}: {
  otherThings: {
    data: string;
    variantType: string;
  }[];
  otherThingsBellow: {
    data: string;
    variantType: string;
  }[];
  varientType: string;
  adCopy: {
    data: string;
    done: boolean;
  };
  setAdCopy: Dispatch<
    SetStateAction<{
      data: string;
      done: boolean;
    }>
  >;
  updateCopy: () => void;
  isSubmittingCopyUpdate: boolean;
  submitCopyFeedback: (feedback: string) => void;
  refetchAdCopy: () => void;
}) {
  const [generating, setGenerating] = useState(true);

  useEffect(() => {
    console.log("AD COPY", adCopy);
    console.log("AD COPY DONE", adCopy.done);
    if (adCopy.done == true) {
      console.log("AD COPY DONE");
      setGenerating(false);
    }
  }, [adCopy]);

  useEffect(() => {
    if (!isSubmittingCopyUpdate) {
      setIsEditing(false);
    }
  }, [isSubmittingCopyUpdate]);

  const [isEditing, setIsEditing] = useState(false);

  const paragraphRef = useRef<HTMLParagraphElement>(null);
  const [textareaHeight, setTextareaHeight] = useState<number | undefined>(
    undefined,
  );

  // Update height when content changes or editing mode changes
  useEffect(() => {
    if (paragraphRef.current) {
      const height = paragraphRef.current.offsetHeight;
      const computedStyle = window.getComputedStyle(paragraphRef.current);
      console.log("Paragraph height:", height);
      console.log("Paragraph computed height:", computedStyle.height);
      console.log("Paragraph line height:", computedStyle.lineHeight);
      console.log("Paragraph padding:", computedStyle.padding);
      setTextareaHeight(height);
    }
  }, [isEditing, adCopy.data]);

  const [isFeedbackOpen, setIsFeedbackOpen] = useState(false);
  const [feedback, setFeedback] = useState("");

  async function submitFeedback() {
    setIsFeedbackOpen(false);
    submitCopyFeedback(feedback);
    setFeedback("");
  }

  return (
    <div
      className={cn(
        generating ? "opacity-50" : "",
        "flex  w-[480px] flex-col items-start justify-between ",
      )}
    >
      <div className="flex w-full flex-col justify-start space-y-2 ">
        <div className="flex items-center justify-between">
          {isEditing && (
            <>
              <Popover open={isFeedbackOpen} onOpenChange={setIsFeedbackOpen}>
                <PopoverTrigger asChild>
                  <Button className="border border-blue-500 bg-blue-200 text-black">
                    <WandIcon width="16" height="16" className="mr-1" />
                    Ask Blue to Rewrite
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="flex flex-col items-start justify-start space-y-2">
                  <Textarea
                    value={feedback}
                    onChange={(e) => setFeedback(e.target.value)}
                  />
                  <div className="flex w-full items-center justify-between gap-2">
                    <div className="flex items-center justify-center gap-2"></div>
                    <Button onClick={submitFeedback}>Submit</Button>
                  </div>
                </PopoverContent>
              </Popover>
              <div className="flex items-center justify-end gap-2">
                <Button
                  variant="outline"
                  disabled={isSubmittingCopyUpdate || generating}
                  onClick={() => {
                    refetchAdCopy();
                    setIsEditing(false);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  disabled={isSubmittingCopyUpdate || generating}
                  onClick={() => {
                    updateCopy();
                  }}
                >
                  Save
                </Button>
              </div>
            </>
          )}
        </div>
        <div className="flex items-center justify-end gap-2">
          {!isEditing && (
            <Button
              disabled={generating || isSubmittingCopyUpdate}
              onClick={() => setIsEditing(true)}
            >
              Edit
            </Button>
          )}
        </div>

        <div className="flex w-full flex-col justify-start space-y-2 pb-4 pt-2">
          {!isEditing &&
            otherThings.map((each) => (
              <>
                <h1 className="text-sm font-semibold">{each.variantType}</h1>
                <p
                  ref={paragraphRef}
                  className="min-h-[1px] w-full resize-none p-0 text-sm leading-normal shadow-none"
                  style={{
                    position: isEditing ? "absolute" : "static",
                    visibility: isEditing ? "hidden" : "visible",
                    boxSizing: "border-box",
                    lineHeight: "21px",
                  }}
                >
                  {each.data.split("\n").map((line, index) => (
                    <React.Fragment key={index}>
                      {line}
                      <br />
                    </React.Fragment>
                  ))}
                </p>
              </>
            ))}
          <h1 className="text-sm font-semibold">{varientType}</h1>
          <p
            ref={paragraphRef}
            className="min-h-[1px] w-full resize-none p-0 text-sm leading-normal shadow-none"
            style={{
              position: isEditing ? "absolute" : "static",
              visibility: isEditing ? "hidden" : "visible",
              boxSizing: "border-box",
              lineHeight: "21px",
            }}
          >
            {adCopy.data.split("\n").map((line, index) => (
              <React.Fragment key={index}>
                {line}
                <br />
              </React.Fragment>
            ))}
          </p>
          {isEditing && (
            <Textarea
              value={adCopy.data}
              onChange={(e) => setAdCopy({ ...adCopy, data: e.target.value })}
              style={{
                height: `310px`, // Exact height from debug
                minHeight: "100px",
                padding: "8px 12px",
                boxSizing: "border-box",
                lineHeight: "21px", // Exact line height from debug
                width: "100%",
                resize: "none",
              }}
              className="w-full leading-normal"
            />
          )}
          {!isEditing &&
            otherThingsBellow.map((each) => (
              <>
                <h1 className="text-sm font-semibold">{each.variantType}</h1>
                <p
                  ref={paragraphRef}
                  className="min-h-[1px] w-full resize-none p-0 text-sm leading-normal shadow-none"
                  style={{
                    position: isEditing ? "absolute" : "static",
                    visibility: isEditing ? "hidden" : "visible",
                    boxSizing: "border-box",
                    lineHeight: "21px",
                  }}
                >
                  {each.data.split("\n").map((line, index) => (
                    <React.Fragment key={index}>
                      {line}
                      <br />
                    </React.Fragment>
                  ))}
                </p>
              </>
            ))}
        </div>
      </div>
    </div>
  );
}

interface EditBaseCopyDialogProps {
  adSegmentId: string;
  setIsRefreshingCopy: Dispatch<SetStateAction<boolean>>;
  config:
    | {
        type: "socialPost";
        field: "body" | "title";
      }
    | {
        type: "conversation";
        field: "subject" | "message";
      };
}

export function EditBaseCopyDialog({
  adSegmentId,
  setIsRefreshingCopy,
  config,
}: EditBaseCopyDialogProps) {
  const socialPostBaseCopyQuery =
    api.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.useQuery({
      adSegmentId: adSegmentId,
    });

  const conversationBaseCopyQuery =
    api.v2.ads.conversationCopy.getBaseCopy.useQuery({
      adSegmentId: adSegmentId,
    });

  const apiUtils = api.useUtils();

  const updateBaseCopy =
    api.v2.ads.socialPostBaseCopy.updateBaseCopy.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.ads.invalidate();
        setIsRefreshingCopy(false);
        setIsSaving(false);
        setDialogOpen(false);
      },
    });

  const [editText, setEditText] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [feedback, setFeedback] = useState("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [isFeedbackOpen, setIsFeedbackOpen] = useState(false);

  useEffect(() => {
    if (config.type == "socialPost") {
      setEditText(socialPostBaseCopyQuery.data?.baseCopy || "");
    }
    if (config.type == "conversation") {
      setEditText(conversationBaseCopyQuery.data?.baseCopy || "");
    }
  }, [socialPostBaseCopyQuery.data, conversationBaseCopyQuery.data]);

  function saveBaseCopy() {
    setIsSaving(true);
    setIsRefreshingCopy(true);
    updateBaseCopy.mutate({
      adSegmentId: adSegmentId,
      baseCopy: editText,
    });
  }

  async function submitFeedback() {
    setEditText("");
    const response = await fetch(`${getBaseUrl()}/stream-refined`, {
      method: "POST",
      headers: {
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        baseCopy: editText,
        feedback: feedback,
        config: {
          type: "socialPost",
          field: "body",
        },
      }),
    });
    if (!response.ok || !response.body) {
      throw response.statusText;
    }

    // Here we start prepping for the streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    const loopRunner = true;

    while (loopRunner) {
      // Here we start reading the stream, until its done.
      const { value, done } = await reader.read();
      if (done) {
        break;
      }
      const decodedChunk = decoder.decode(value, { stream: true });
      for (const each of decodedChunk) {
        try {
          setEditText((prev) => prev + each);
        } catch (e) {}
      }
    }
  }

  useEffect(() => {
    if (!dialogOpen) {
      if (socialPostBaseCopyQuery.data) {
        setEditText(socialPostBaseCopyQuery.data.baseCopy);
      }
    }
  }, [dialogOpen]);

  useEffect(() => {
    setFeedback("");
  }, [isFeedbackOpen]);

  return (
    <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
      <DialogTrigger asChild>
        <Button>Edit Base Copy</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Base Copy</DialogTitle>
        </DialogHeader>
        <div className="flex w-full justify-between">
          <Popover open={isFeedbackOpen} onOpenChange={setIsFeedbackOpen}>
            <PopoverTrigger asChild>
              <Button className="border border-blue-500 bg-blue-200 text-black">
                <WandIcon width="16" height="16" className="mr-1" />
                Ask Blue to Rewrite
              </Button>
            </PopoverTrigger>
            <PopoverContent className="flex flex-col items-start justify-start space-y-2">
              <Textarea
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
              />
              <div className="flex w-full items-center justify-end gap-2">
                <Button onClick={submitFeedback}>Submit</Button>
              </div>
            </PopoverContent>
          </Popover>
          <div className="flex w-full items-center justify-end gap-2">
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={saveBaseCopy} disabled={isSaving}>
              Save
            </Button>
          </div>
        </div>

        <span className="text-sm font-semibold">Base Copy</span>
        <Textarea
          value={editText}
          onChange={(e) => setEditText(e.target.value)}
          style={{
            height: `310px`,
            minHeight: "100px",
            padding: "8px 12px",
            boxSizing: "border-box",
            lineHeight: "21px",
            width: "100%",
            resize: "none",
          }}
        />
      </DialogContent>
    </Dialog>
  );
}
