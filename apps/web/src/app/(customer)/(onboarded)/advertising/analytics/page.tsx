"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";
import { ArrowDownIcon, ArrowUpIcon, Pencil, PlusCircle } from "lucide-react";

import { Button } from "@kalos/ui/button";

import {
  CampaignGroupSegmentDetail,
  EditSegmentCampaignModal,
} from "./editCampaignGroupSegmentDialog";

const MetricCard = ({
  title,
  value,
  change,
  isPositive,
}: {
  title: string;
  value: string | number | React.ReactNode;
  change: string;
  isPositive?: boolean;
}) => (
  <div className="flex flex-col rounded-lg border border-gray-200 p-4">
    <div className="mb-1 text-sm text-gray-600">{title}</div>
    <div>
      <span className="text-2xl font-semibold">{value}</span>
      <span
        className={`ml-2 text-sm ${
          isPositive
            ? "text-green-600"
            : isPositive === false
              ? "text-red-600"
              : "text-gray-600"
        }`}
      >
        {isPositive !== undefined &&
          (isPositive ? <ArrowUpIcon /> : <ArrowDownIcon />)}{" "}
        {change}
      </span>
    </div>
  </div>
);

type AdData = {
  adAccountLinkedInUrn: string;
  adFormat: string;
  adTopic: string;
  budget: number;
  campaignGroupId: string;
  campaignGroupObjectiveType: string;
  campaignGroupSegmentId: string;
  campaignGroupSegmentStage: string;
  campaignGroupTitle: string;
  campaignId: string;
  campaignLinkedInStatus?: string;
  campaignLinkedInUrn: string;
  clicks: number;
  oneClickLeads: number;
  costInLocalCurrency: number;
  costInUsd: number;
  creativeLinkedInUrnWithPrefix: string;
  endDate: string;
  impressions: number;
  jobFunctionDisplayName: string;
  numberOfEmployeesHighBound: number | null;
  numberOfEmployeesLowBound: number | null;
  segmentId: string;
  segmentName: string;
  startDate: string;
  targetGroupLinkedInFacetUrn: string;
  targetGroupLinkedInFacetUrnAlt: string | null;
  vertical: string;
  campaignGroupSegmentDerivedStatus: string;
}[];

const AnalyticsTable = ({
  adData,
  filterStatus,
  updateCampaignGroupSegment,
  campaignGroupSegmentInfo,
  setCampaignGroupSegmentInfo,
  tableData,
  setTableData,
}: {
  adData: AdData | null;
  filterStatus: "ACTIVE" | "COMPLETED";
  updateCampaignGroupSegment: (
    campaignGroupSegmentId: string,
    stage: string,
    endDateEpochMs: number,
    budget: number,
  ) => void;
  campaignGroupSegmentInfo?: CampaignGroupSegmentDetail | null;
  setCampaignGroupSegmentInfo: (data: CampaignGroupSegmentDetail) => void;
  tableData: any[];
  setTableData: (data: any[]) => void;
}) => {
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Debug print
  useEffect(() => {
    console.log("AnalyticsTable::tableData", tableData);
  }, [tableData]);
  useEffect(() => {
    console.log("AnalyticsTable::adData", adData);
  }, [adData]);
  useEffect(() => {
    console.log(
      "AnalyticsTable::campaignGroupSegmentInfo",
      campaignGroupSegmentInfo,
    );
  }, [campaignGroupSegmentInfo]);
  useEffect(() => {
    console.log("AnalyticsTable::filterStatus", filterStatus);
  }, [filterStatus]);

  // Trigger the AdData to TableData conversion when the AdData changes
  useEffect(() => {
    if (adData) {
      mapDataToTable(adData);
    }
  }, [adData, filterStatus]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const mapDataToTable = (data: AdData) => {
    if (!data) return;
    console.log("fetching data for segments analytics page", data);
    // TODO: Currently DRAFT includes the deleted as well.
    data = data.filter((ad) => ad.campaignGroupSegmentStage !== "DRAFT");
    console.log("filtered data", data);
    //Filter data based on filterStatus
    data = data.filter((ad) =>
      filterStatus === "COMPLETED"
        ? ad.campaignGroupSegmentDerivedStatus === "COMPLETED"
        : ["ACTIVE"].includes(ad.campaignGroupSegmentDerivedStatus),
    );
    console.log("filtered data by status", data);

    // Get unique segments and campaign groups first
    const uniqueSegments = [
      ...new Set(data.map((row: { segmentId: string }) => row.segmentId)),
    ];
    console.log("uniqueSegments", uniqueSegments);

    const uniqueCampaignGroups = [
      ...new Set(
        data.map((row: { campaignGroupId: string }) => row.campaignGroupId),
      ),
    ];
    console.log("uniqueCampaignGroups", uniqueCampaignGroups);

    type AnalyticsRow = {
      segmentId: string;
      vertical: string;
      numberOfEmployeesLowBound: number | null;
      numberOfEmployeesHighBound: number | null;
      jobFunctionDisplayName: string;
      campaignGroupId: string;
      campaignGroupTitle: string;
      campaignLinkedInStatus?: string | null;
      adFormat: string;
      startDate: string;
      endDate: string;
      budget: number;
      campaignId: string;
      adTopic: string; // supposed to be the value prop
      targetGroupLinkedInFacetUrnAlt: string | null;
      targetGroupLinkedInFacetUrn: string | null;
      creativeLinkedInUrnWithPrefix: string | null;
      campaignGroupSegmentId: string;
    };

    const processedData = uniqueSegments.flatMap((segmentId) => {
      return uniqueCampaignGroups
        .map((campaignGroupId) => {
          // Find all rows for this segment + campaign group combination
          const matchingRows = data.filter(
            (r: AnalyticsRow) =>
              r.segmentId === segmentId &&
              r.campaignGroupId === campaignGroupId,
          );
          if (matchingRows.length === 0) return null;

          // Find row with highest clicks for learnings
          const rowWithLowestCostPerKeyResult = matchingRows.reduce(
            (prev, curr) => {
              const prevCost =
                prev.clicks === 0 ? Infinity : prev.costInUsd / prev.clicks;
              const currCost =
                curr.clicks === 0 ? Infinity : curr.costInUsd / curr.clicks;
              return prevCost > currCost ? curr : prev;
            },
          );

          const valuePropsDisplay =
            rowWithLowestCostPerKeyResult.adTopic || "N/A";

          // Sum up metrics across all matching rows
          const totalSpent = matchingRows.reduce(
            (sum, row) => sum + (row.costInUsd ?? 0),
            0,
          );
          const totalKeyResults = matchingRows.reduce(
            (sum, row) =>
              sum +
              (row.campaignGroupObjectiveType === "LEAD_GENERATION"
                ? row.oneClickLeads
                : row.clicks),
            0,
          );
          // Use first row for timeline and other static data
          const firstRow = matchingRows[0];

          return {
            segment:
              firstRow?.segmentName ||
              `${firstRow?.vertical}${
                firstRow?.numberOfEmployeesLowBound ||
                firstRow?.numberOfEmployeesHighBound
                  ? " • " +
                    (firstRow?.numberOfEmployeesLowBound &&
                    firstRow?.numberOfEmployeesHighBound
                      ? `${firstRow?.numberOfEmployeesLowBound}-${firstRow?.numberOfEmployeesHighBound} Employees`
                      : `${firstRow?.numberOfEmployeesLowBound}+ Employees`)
                  : ""
              }${firstRow?.jobFunctionDisplayName ? ` • ${firstRow?.jobFunctionDisplayName}` : ""}`,
            campaign: firstRow?.campaignGroupTitle,
            type: firstRow?.adFormat
              .split("_")
              .map(
                (word: string) =>
                  word.charAt(0).toUpperCase() + word.slice(1).toLowerCase(),
              )
              .join(" "),
            learnings: {
              audience: (() => {
                const facetUrn =
                  rowWithLowestCostPerKeyResult.targetGroupLinkedInFacetUrn;
                const facetUrnAlt =
                  rowWithLowestCostPerKeyResult.targetGroupLinkedInFacetUrnAlt;

                if (!facetUrn && !facetUrnAlt) {
                  return "Not specified";
                }

                const facets = (facetUrn || facetUrnAlt)?.split(",") ?? [];
                for (const facet of facets) {
                  const parts = facet?.split(":");
                  if (
                    ["titles", "jobFunctions"].includes(
                      parts?.[parts.length - 1] ?? "",
                    )
                  ) {
                    return parts?.[parts.length - 1]
                      ?.split(/(?=[A-Z])/)
                      .join(" ")
                      .split(" ")
                      .map(
                        (word: string) =>
                          word.charAt(0).toUpperCase() + word.slice(1),
                      )
                      .join(" ");
                  }
                }

                return "Not specified";
              })(),
              valueProp: valuePropsDisplay,
            },
            timeline: {
              days: firstRow
                ? `${Math.ceil(
                    (new Date(firstRow.endDate).getTime() -
                      new Date(firstRow.startDate).getTime() +
                      1) /
                      (1000 * 60 * 60 * 24),
                  )}d`
                : "",
              ends: firstRow?.endDate ? formatDate(firstRow.endDate) : "",
              endsEpochMs: firstRow?.endDate
                ? new Date(firstRow.endDate).getTime()
                : 0,
            },
            keyResult: totalKeyResults,
            spent: totalSpent,
            budget: firstRow?.budget ?? 0,
            stage: firstRow?.campaignGroupSegmentStage ?? "",
            campaignGroupSegmentId: firstRow?.campaignGroupSegmentId ?? "",
          };
        })
        .filter((item): item is NonNullable<typeof item> => item !== null);
    });
    console.log("processedData", processedData);

    // Process campaigns to determine merged cells
    const processedDataInTable = processedData.reduce(
      (acc: any[], curr: any, idx: number) => {
        if (idx === 0 || curr?.segment !== processedData[idx - 1]?.segment) {
          // Count how many consecutive rows have the same segment
          let rowSpan = 1;
          for (let i = idx + 1; i < processedData.length; i++) {
            if (processedData[i]?.segment === curr?.segment) {
              rowSpan++;
            } else {
              break;
            }
          }
          acc.push({ ...curr, showSegment: true, rowSpan });
        } else {
          acc.push({ ...curr, showSegment: false, rowSpan: 1 });
        }
        return acc;
      },
      [],
    );
    setTableData(processedDataInTable);
  };

  return (
    <div className="mt-6">
      {isModalOpen && (
        <EditSegmentCampaignModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onConfirm={(data: {
            campaignGroupSegmentId: string;
            stage: string;
            endDateEpochMs: number;
            budget: number;
          }) => {
            // Handle form submission here
            setIsModalOpen(false);
            console.log("confirming data", data);
            updateCampaignGroupSegment(
              data.campaignGroupSegmentId,
              data.stage,
              data.endDateEpochMs,
              data.budget,
            );
          }}
          campaignGroupSegmentInfo={campaignGroupSegmentInfo!}
        />
      )}

      <table className="w-full">
        <thead>
          <tr className="border-b border-gray-200">
            <th className="pb-3 text-left text-xs font-medium uppercase text-gray-500">
              Segment
            </th>
            <th className="pb-3 text-left text-xs font-medium uppercase text-gray-500">
              Campaign Group
            </th>
            <th className="pb-3 text-left text-xs font-medium uppercase text-gray-500">
              Key Learnings
            </th>
            <th className="pb-3 text-left text-xs font-medium uppercase text-gray-500">
              Spent/Budget
            </th>
            <th className="pb-3 text-left text-xs font-medium uppercase text-gray-500">
              Timeline
            </th>
            <th className="pb-3 text-left text-xs font-medium uppercase text-gray-500">
              Key Result
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-100">
          {tableData.map(
            (
              campaign: {
                campaignGroupSegmentId: string;
                showSegment: boolean;
                rowSpan: number;
                segment: string;
                campaign: string;
                type: string;
                learnings: {
                  audience: string;
                  valueProp: string;
                };
                spent: number;
                budget: number;
                timeline: {
                  days: string;
                  ends: string;
                  starts: string;
                  endsEpochMs: number;
                };
                keyResult: number;
                stage: string;
              },
              idx: number,
            ) => (
              <tr key={idx} className="group">
                {campaign.showSegment && (
                  <td
                    rowSpan={campaign.rowSpan}
                    className="border-b border-gray-100 py-4 align-top text-sm"
                  >
                    {campaign.segment}
                  </td>
                )}
                <td className="cursor-pointer py-4 group-hover:bg-blue-50">
                  <div>
                    <div className="text-sm">{campaign.campaign}</div>
                    <div className="text-xs text-gray-500">{campaign.type}</div>
                  </div>
                </td>
                <td className="cursor-pointer py-4 group-hover:bg-blue-50">
                  <div className="text-sm">
                    Most resonant Audience:{" "}
                    <strong>{campaign.learnings.audience}</strong>
                  </div>
                  <div className="text-sm">
                    Value prop: <strong>{campaign.learnings.valueProp}</strong>
                  </div>
                </td>
                <td className="cursor-pointer py-4 text-sm group-hover:bg-blue-50">
                  ${campaign.spent.toFixed(2)}/$
                  {campaign.budget.toLocaleString()}
                </td>
                <td className="cursor-pointer py-4 group-hover:bg-blue-50">
                  <div className="text-sm">{campaign.timeline.days}</div>
                  <div className="text-xs text-gray-500">
                    Ends {campaign.timeline.ends}
                  </div>
                </td>
                <td className="cursor-pointer py-4 group-hover:bg-blue-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm">{campaign.keyResult}</div>
                      <div className="text-xs text-gray-500">
                        {campaign.keyResult === 0
                          ? "N/A"
                          : `$${(campaign.spent / campaign.keyResult).toFixed(2)}`}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={(e) => {
                        e.stopPropagation();
                        setCampaignGroupSegmentInfo({
                          campaignGroupSegmentId:
                            campaign.campaignGroupSegmentId,
                          segmentDisplayName: campaign.segment,
                          campaignGroupTitle: campaign.campaign,
                          spent: campaign.spent,
                          budget: campaign.budget,
                          endDateEpochMs: campaign.timeline.endsEpochMs,
                          stage: campaign.stage,
                        });
                        setIsModalOpen(true);
                      }}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                  </div>
                </td>
              </tr>
            ),
          )}
        </tbody>
      </table>
    </div>
  );
};

export default function AnalyticsPage() {
  const [campaignGroupData, setCampaignGroupData] = useState<any[]>([]); // TODO: type this properly
  const [filterStatus, setFilterStatus] = useState<"ACTIVE" | "COMPLETED">(
    "ACTIVE",
  );
  const [campaignGroupSegmentInfo, setCampaignGroupSegmentInfo] =
    useState<CampaignGroupSegmentDetail | null>(null);
  const [isFetchingSegmentAnalyticsData, setIsFetchingSegmentAnalyticsData] =
    useState(false);
  const [adData, setAdData] = useState<AdData | null>(null);
  const [tableData, setTableData] = useState<any[]>([]);
  const router = useRouter();
  const organizationQuery =
    api.user.organizationUser.get.organization.useQuery();

  const q = api.v2.ads.analytics.all.useQuery();
  useEffect(() => {
    if (organizationQuery.data?.organizationId) {
      setIsFetchingSegmentAnalyticsData(true);
      if (q.data) {
        setAdData(q.data as AdData);
        setIsFetchingSegmentAnalyticsData(false);
      }
    }
  }, [organizationQuery.data?.organizationId, q.data]);

  const fetchSegmentAnalyticsData = async (organizationId: number) => {
    const res = await fetch("/api/v1/analytics/campaign-group-segments", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        organizationId,
      }),
    });
    if (res.ok) {
      const data = (await res.json()) as AdData;
      return data;
    } else {
      console.error("Error fetching segment analytics data", res);
      return null;
    }
  };

  const updateCampaignGroupSegment = async (
    campaignGroupSegmentId: string,
    stage: string,
    endDateEpochMs: number,
    budget: number,
  ) => {
    const organizationId = organizationQuery.data?.organizationId;
    if (!organizationId) {
      console.error("Organization ID not found");
      return;
    }
    const mappedStage =
      campaignGroupSegmentInfo?.stage?.toUpperCase() === "COMPLETED"
        ? "COMPLETED"
        : "ACTIVE";
    const requestBody = {
      organizationId,
      campaignGroupSegmentId,
      ...(stage.toUpperCase() !== mappedStage && {
        stage: stage.toUpperCase(),
      }),
      ...(campaignGroupSegmentInfo?.endDateEpochMs !== endDateEpochMs && {
        endDateEpochMs,
      }),
      ...(campaignGroupSegmentInfo?.budget !== budget && {
        budget,
      }),
    };
    console.log("requestBody", requestBody);

    const res = await fetch(
      "/api/v1/analytics/campaign-group-segments/update",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      },
    );

    if (res.ok) {
      console.log("Campaign group segment updated");
      setIsFetchingSegmentAnalyticsData(true);
      await fetchSegmentAnalyticsData(organizationId).then((data) => {
        setIsFetchingSegmentAnalyticsData(false);
        setAdData(data);
      });
    } else {
      console.error("Failed to update campaign group segment");
    }
  };

  return (
    <div className="px-8 py-6">
      <div className="mb-8 flex items-center justify-between">
        <h1 className="text-2xl font-semibold">All Campaigns</h1>
      </div>

      <div className="mb-8 grid grid-cols-4 gap-8">
        <MetricCard
          title="Key Results"
          value={
            isFetchingSegmentAnalyticsData ? (
              <div className="h-6 w-16 animate-pulse rounded bg-gray-200" />
            ) : tableData && tableData.length > 0 ? (
              tableData.reduce((sum, ad) => sum + ad.keyResult, 0)
            ) : (
              "N/A"
            )
          }
          change=""
        />
      </div>

      <div className="mb-6 flex items-center justify-between">
        <div className="space-x-2">
          <button
            className={`rounded-md px-4 py-2 ${
              filterStatus === "ACTIVE"
                ? "bg-blue-50 text-blue-600"
                : "text-gray-600"
            }`}
            onClick={() => setFilterStatus("ACTIVE")}
          >
            Running
          </button>
          <button
            className={`px-4 py-2 ${
              filterStatus === "COMPLETED"
                ? "bg-blue-50 text-blue-600"
                : "text-gray-600"
            }`}
            onClick={() => setFilterStatus("COMPLETED")}
          >
            Completed
          </button>
        </div>
      </div>
      <AnalyticsTable
        adData={adData}
        filterStatus={filterStatus}
        updateCampaignGroupSegment={updateCampaignGroupSegment}
        campaignGroupSegmentInfo={campaignGroupSegmentInfo}
        setCampaignGroupSegmentInfo={setCampaignGroupSegmentInfo}
        tableData={tableData}
        setTableData={setTableData}
      />
    </div>
  );
}
