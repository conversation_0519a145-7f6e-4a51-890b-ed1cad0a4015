import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { Pause } from "lucide-react";

import { But<PERSON> } from "@kalos/ui/button";

interface ActionDropdownMenuProps {
  onSelect: () => void;
  label: string;
}

export function ActionDropdownMenu({
  onSelect,
  label,
}: ActionDropdownMenuProps) {
  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <div className="flex h-4 w-4 flex-col justify-between">
            <div className="h-1 w-1 rounded-full bg-current"></div>
            <div className="h-1 w-1 rounded-full bg-current"></div>
            <div className="h-1 w-1 rounded-full bg-current"></div>
          </div>
        </Button>
      </DropdownMenu.Trigger>

      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className="min-w-[8rem] rounded-md bg-white p-1 shadow-md"
          sideOffset={5}
        >
          <DropdownMenu.Item
            className="flex cursor-pointer items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-gray-100"
            onSelect={onSelect}
          >
            {label}
          </DropdownMenu.Item>
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}
