import React, { useState } from "react";

import { Badge } from "@kalos/ui/badge";
import { Button } from "@kalos/ui/button";
import { Input } from "@kalos/ui/input";
import { Textarea } from "@kalos/ui/textarea";

interface AdInfo {
  title: string;
  adCopyId: string;
  body: string;
  description: string;
  audiences: string[];
  creativeUrl?: string;
}

interface EditAdModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: AdInfo) => void;
  onDelete?: () => void;
  adInfo: AdInfo;
}

const EditAdModal: React.FC<EditAdModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  onDelete,
  adInfo,
}) => {
  const [formData, setFormData] = useState<AdInfo>({
    adCopyId: adInfo?.adCopyId || "",
    title: adInfo?.title || "",
    body: adInfo?.body || "",
    description: adInfo?.description || "",
    audiences: adInfo?.audiences || [],
    creativeUrl: adInfo?.creativeUrl,
  });

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onConfirm(formData);
    console.log("formData", formData);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="w-[480px] rounded-lg bg-white p-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold">Edit: {adInfo.title}</h2>
        </div>

        <form onSubmit={handleSubmit} className="mt-6 space-y-6">
          <div>
            <label className="mb-2 block font-medium">Title</label>
            <Input
              value={formData.title}
              onChange={(e) => {
                setFormData({ ...formData, title: e.target.value });
              }}
              className="w-full"
            />
          </div>

          <div>
            <label className="mb-2 block font-medium">Description</label>
            <Textarea
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              className="h-18 w-full"
            />
          </div>

          <div>
            <label className="mb-2 block font-medium">Body</label>
            <Textarea
              value={formData.body}
              onChange={(e) =>
                setFormData({ ...formData, body: e.target.value })
              }
              className="h-36 w-full"
            />
          </div>

          <div>
            <label className="mb-2 block font-medium">Audience</label>
            <div className="flex flex-wrap gap-2">
              {formData.audiences.map((audience, index) => (
                <Badge key={index} variant="secondary">
                  {audience}
                </Badge>
              ))}
            </div>
          </div>

          {formData.creativeUrl && (
            <div>
              <label className="mb-2 block font-medium">Creative</label>
              <div className="h-16 w-16 rounded-lg bg-gray-100">
                <img
                  src="/api/placeholder/64/64"
                  alt="Ad creative"
                  className="h-full w-full rounded-lg object-cover"
                />
              </div>
            </div>
          )}

          <div className="flex justify-between pt-4">
            <div className="flex gap-3">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                Confirm
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditAdModal;
