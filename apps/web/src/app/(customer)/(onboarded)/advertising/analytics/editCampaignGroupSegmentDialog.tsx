"use client";

import React, { useState } from "react";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";

import { Button } from "@kalos/ui/button";
import { Calendar } from "@kalos/ui/calender";
import { cn } from "@kalos/ui/index"; // Utility for conditional class names
import { Popover, PopoverContent, PopoverTrigger } from "@kalos/ui/popover";

export interface CampaignGroupSegmentDetail {
  campaignGroupTitle: string;
  segmentDisplayName: string;
  campaignGroupSegmentId: string;
  endDateEpochMs: number;
  budget: number;
  spent?: number;
  stage?: string;
}

interface FormDataType {
  campaignGroupSegmentId: string;
  stage: string;
  endDateEpochMs: number;
  budget: number;
}

interface EditSegmentCampaignModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: FormDataType) => void;
  campaignGroupSegmentInfo: CampaignGroupSegmentDetail;
}

export const EditSegmentCampaignModal: React.FC<
  EditSegmentCampaignModalProps
> = ({ isOpen, onClose, onConfirm, campaignGroupSegmentInfo }) => {
  const [formData, setFormData] = useState<FormDataType>({
    campaignGroupSegmentId: campaignGroupSegmentInfo?.campaignGroupSegmentId!,
    stage:
      campaignGroupSegmentInfo?.stage &&
      ["RUNNING_WINNER", "AUDIENCE_TEST"].includes(
        campaignGroupSegmentInfo.stage,
      )
        ? "Running"
        : "Completed",
    endDateEpochMs: campaignGroupSegmentInfo.endDateEpochMs,
    budget: campaignGroupSegmentInfo?.budget || 0,
  });

  const [popoverOpen, setPopoverOpen] = useState(false);

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onConfirm(formData);
    console.log(formData);
  };

  const todayDate = new Date();
  todayDate.setHours(0, 0, 0, 0);

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
      onClick={onClose}
    >
      <div
        className="w-[480px] rounded-lg bg-white p-6"
        onClick={(e) => e.stopPropagation()}
      >
        <h2 className="mb-4 text-lg font-semibold">
          {campaignGroupSegmentInfo?.campaignGroupTitle} |{" "}
          {campaignGroupSegmentInfo?.segmentDisplayName}: Editing
        </h2>
        <p className="mb-6 text-sm text-gray-600">
          Updates will be in effect immediately
        </p>
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="mb-2 block text-sm font-medium">Status</label>
            <div className="flex items-center gap-2">
              <div
                className={`relative h-6 w-11 rounded-full transition-colors ${
                  formData.stage === "Running" ? "bg-blue-600" : "bg-gray-200"
                }`}
                onClick={() =>
                  setFormData({
                    ...formData,
                    stage:
                      formData.stage === "Running" ? "Completed" : "Running",
                  })
                }
              >
                <div
                  className={`absolute top-1 h-4 w-4 rounded-full bg-white transition-transform ${
                    formData.stage === "Running" ? "left-6" : "left-1"
                  }`}
                />
              </div>
              <span className="text-sm">{formData.stage}</span>
            </div>
          </div>

          <div className="mb-4">
            <label className="mb-2 block text-sm font-medium">End Date</label>
            <div className="flex gap-4">
              <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full pl-3 text-left font-normal",
                      !formData.endDateEpochMs && "text-muted-foreground",
                    )}
                  >
                    {formData.endDateEpochMs > 0 ? (
                      format(new Date(formData.endDateEpochMs), "PPP")
                    ) : (
                      <span>End Date</span>
                    )}
                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={new Date(formData.endDateEpochMs)}
                    onSelect={(date) => {
                      if (date) {
                        setFormData({
                          ...formData,
                          endDateEpochMs: new Date(date).getTime(),
                        });
                        setPopoverOpen(false);
                      }
                    }}
                    disabled={(date) => date < todayDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="mb-6">
            <label className="mb-2 block text-sm font-medium">Budget</label>
            <div className="relative">
              <span className="absolute left-2 top-2 text-gray-500">$</span>
              <input
                type="number"
                value={formData.budget}
                onChange={(e) =>
                  setFormData({ ...formData, budget: Number(e.target.value) })
                }
                className="w-full rounded-md border border-gray-200 p-2 pl-6"
              />
            </div>
            <p className="mt-2 text-sm text-gray-600">
              ${campaignGroupSegmentInfo?.spent ?? 0} already spent for this
              Campaign. Any updates will only impact future campaigns.
            </p>
          </div>

          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onClose} className="px-6">
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-blue-600 px-6 hover:bg-blue-700"
            >
              Confirm
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditSegmentCampaignModal;
