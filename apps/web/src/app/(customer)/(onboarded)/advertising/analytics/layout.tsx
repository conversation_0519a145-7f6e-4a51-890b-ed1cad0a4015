"use client";

import { useRouter } from "next/navigation";
import { PlusCircle } from "lucide-react";

export default function AnalyticsLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();
  return (
    <div className="flex h-full w-full flex-col justify-start">
      <div className="sticky inset-x-0 top-0 flex h-16 w-full items-center justify-between border-b bg-white px-6">
        <h1 className="text-xl font-medium">Performance</h1>
        <button
          onClick={() => router.push("/advertising/new")}
          className="flex items-center rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          New Campaign
        </button>
      </div>
      <div className="h-full w-full overflow-auto bg-muted">{children}</div>
    </div>
  );
}
