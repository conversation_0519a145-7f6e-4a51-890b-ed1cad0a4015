"use client";

import type { ColumnDef } from "@tanstack/react-table";
import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { api } from "@/trpc/client";
import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { Rocket } from "lucide-react";

import { Button } from "@kalos/ui/button";
import { Card } from "@kalos/ui/card";
import { DataTable } from "@kalos/ui/table/datatable";

import { ActionDropdownMenu } from "./actionDropdownMenu";
import EditAdModal from "./editAdCopyDialog";

type CampaignGroupSegmentInfo = {
  title: string; // Same as campaign group, its parent
  segmentName: string;
  stage: string;
  startDate: string; // Same as campaign group?
  endDate: string; // Same as campaign group?
  budget: number;
  spend: number;
  clicks: number;
  oneClickLeads: number;
  impressions: number;
  verticals: Array<{
    id: string;
    name: string;
    segmentId: string;
  }>;
  jobFunction: string;
  objectiveType: string;
};

type AdInfo = {
  title: string;
  adCopyId: string;
  body: string;
  description: string;
  audiences: string[];
  creativeUrl: string;
};

interface AdData {
  title: string;
  adCopyId: string;
  valueProp: string;
  body: string;
  description: string;
  costPerKeyResult: number | string;
  keyResult: number;
  timeline: string;
  timelineDate?: string;
  totalSpent: string;
  clicks: number;
  oneClickLeads: number;
  ctr: string;
  costPerClick: number | string;
  impressions: number;
  format: string;
  costPerImpression: number | string;
  audience: string;
  stage: "CURRENT" | "UPCOMING" | "PAST";
  creativeUrl: string;
  campaignLinkedInUrn: string;
  adCreativeLinkedInUrnWithPrefix: string;
  adAccountUrn: string;
}

interface UpcomingAd {
  order: number;
  title: string;
  date: string;
  image?: string;
}

export default function SegmentAdsDetailPage() {
  const campaignGroupSegmentId = useParams()[
    "campaign-group-segment-id"
  ] as string;

  const q = api.v2.ads.analytics.ads.useQuery({
    campaignGroupSegmentId,
  });
  const [campaignGroupSegmentInfo, setCampaignGroupSegmentInfo] =
    useState<CampaignGroupSegmentInfo>({} as CampaignGroupSegmentInfo);
  const [currentAds, setCurrentAds] = useState<AdData[]>([]);
  const [upcomingAds, setUpcomingAds] = useState<AdData[]>([]);
  const [pastAds, setPastAds] = useState<AdData[]>([]);
  const [activeTab, setActiveTab] = useState<"Active Ads" | "Past Ads">(
    "Active Ads",
  );
  const [isEditAdModalOpen, setIsEditAdModalOpen] = useState(false);
  const [selectedAdInfo, setSelectedAdInfo] = useState<AdData | null>(null);
  const [adInfo, setAdInfo] = useState<AdInfo | null>(null);
  const [isFetchingAdData, setIsFetchingAdData] = useState(false);

  const overrideABTestMutation =
    api.advertising.campaignGroupSegment.overrideABTest.useMutation();

  //Debug print
  useEffect(() => {
    console.log("campaignGroupSegmentInfo", campaignGroupSegmentInfo);
  }, [campaignGroupSegmentInfo]);
  useEffect(() => {
    console.log("currentAds", currentAds);
  }, [currentAds]);
  useEffect(() => {
    console.log("upcomingAds", upcomingAds);
  }, [upcomingAds]);
  useEffect(() => {
    console.log("pastAds", pastAds);
  }, [pastAds]);

  const organizationQuery =
    api.user.organizationUser.get.organization.useQuery();

  const fetchAdData = async (organizationId: number) => {
    setIsFetchingAdData(true);
    if (!q.data) {
      return;
    }

    setIsFetchingAdData(false);
    console.log("Ads in this campaign group/segment", q.data);
    if (q.data && q.data.campaignGroup && q.data.ads) {
      const derivedCampaignGroupSegmentStage = q.data.ads.some(
        (ad) => ad.campaignLinkedInStatus === "ACTIVE",
      )
        ? "ACTIVE"
        : "COMPLETED";
      setCampaignGroupSegmentInfo({
        title: q.data.campaignGroup.title,
        startDate: q.data.campaignGroup.startDate ?? "",
        endDate: q.data.campaignGroup.endDate ?? "",
        budget:
          q.data.campaignGroupSegmentDetails.campaign_group_segment.budget,
        spend: q.data.ads.reduce(
          (acc: number, ad: any) => acc + ad.costInUsd,
          0,
        ),
        clicks: q.data.ads.reduce((acc: number, ad: any) => acc + ad.clicks, 0),
        oneClickLeads: q.data.ads.reduce(
          (acc: number, ad: any) => acc + ad.oneClickLeads,
          0,
        ),
        impressions: q.data.ads.reduce(
          (acc: number, ad: any) => acc + ad.impressions,
          0,
        ),
        stage: derivedCampaignGroupSegmentStage,
        segmentName: q.data.campaignGroupSegmentDetails.segment.name ?? "",
        verticals: q.data.campaignGroupSegmentDetails.verticals.map((v) => ({
          id: "",
          name: v,
          segmentId: q.data.campaignGroupSegmentDetails.segment.id,
        })),
        jobFunction: q.data.campaignGroupSegmentDetails.job_function ?? "",
        objectiveType: q.data.campaignGroup.objectiveType,
      });

      console.log("data.ads", q.data.ads);
      const currentTime = new Date().getTime();
      console.log("currentTime", currentTime);
      console.log("campaignGroupSegmentInfo", campaignGroupSegmentInfo);
      const mappedAds: AdData[] = q.data.ads.map((ad) => ({
        title: ad.adTitle,
        adCopyId: ad.adCopyId,
        valueProp: ad.valueProp,
        body: ad.adBody,
        description: "",
        costPerKeyResult:
          q.data.campaignGroup.objectiveType === "LEAD_GENERATION"
            ? ad.oneClickLeads > 0
              ? (ad.costInUsd / ad.oneClickLeads).toFixed(2)
              : "N/A"
            : ad.clicks > 0
              ? (ad.costInUsd / ad.clicks).toFixed(2)
              : "N/A",
        keyResult:
          q.data.campaignGroup.objectiveType === "LEAD_GENERATION"
            ? ad.oneClickLeads
            : ad.clicks,
        timeline: "d",
        timelineDate: `${new Date(ad.startDate).toLocaleDateString()} to ${new Date(ad.endDate).toLocaleDateString()}`,
        totalSpent: ad.costInUsd.toFixed(2),
        clicks: ad.clicks,
        oneClickLeads: ad.oneClickLeads,
        costPerLead:
          ad.oneClickLeads > 0
            ? (ad.costInUsd / ad.oneClickLeads).toFixed(2)
            : "N/A",
        ctr:
          ad.impressions && ad.clicks
            ? ((ad.clicks / ad.impressions) * 100).toFixed(1) + "%"
            : "N/A",
        costPerClick:
          ad.clicks > 0 ? (ad.costInUsd / ad.clicks).toFixed(2) : "N/A",
        impressions: ad.impressions,
        format:
          ad.adFormat
            ?.split("_")
            .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" ") ?? "N/A",
        costPerImpression:
          ad.impressions > 0
            ? `$${(ad.costInUsd / ad.impressions).toFixed(2)}`
            : "N/A",
        audience: (() => {
          const facetUrn = ad.targetGroupLinkedInFacetUrn as string;
          const facetUrnAlt = ad.targetGroupLinkedInFacetUrnAlt as string;
          return facetUrn.toLowerCase().includes("titles") ||
            facetUrnAlt.toLowerCase().includes("titles")
            ? "Titles"
            : facetUrn.toLowerCase().includes("function") ||
                facetUrnAlt.toLowerCase().includes("function")
              ? "Function"
              : "Retargeting";
        })(),
        stage:
          derivedCampaignGroupSegmentStage === "COMPLETED"
            ? "PAST"
            : ad.adCreativeLinkedInStatus === "ACTIVE" &&
                ad.campaignLinkedInStatus === "ACTIVE" &&
                !ad.adCreativeHoldStatus
              ? "CURRENT"
              : ad.adDisqualified || ad.campaignDisqualified
                ? "PAST"
                : "UPCOMING",
        campaignLinkedInUrn: ad.campaignLinkedInUrn,
        adCreativeLinkedInUrnWithPrefix: ad.creativeLinkedInUrnWithPrefix,
        adAccountUrn: q.data.adAccountUrn,
        creativeUrl: "",
      }));
      mappedAds.sort((a, b) => {
        const costA =
          a.costPerKeyResult === "N/A"
            ? Infinity
            : parseFloat(a.costPerKeyResult as string);
        const costB =
          b.costPerKeyResult === "N/A"
            ? Infinity
            : parseFloat(b.costPerKeyResult as string);
        return costA - costB;
      });
      console.log("mappedAds", mappedAds);
      setCurrentAds(mappedAds.filter((ad) => ad.stage === "CURRENT"));
      setPastAds(mappedAds.filter((ad) => ad.stage === "PAST"));
      setUpcomingAds(mappedAds.filter((ad) => ad.stage === "UPCOMING"));
    }
  };

  useEffect(() => {
    if (
      organizationQuery.data &&
      organizationQuery.data.organizationId &&
      q.data
    ) {
      fetchAdData(organizationQuery.data!.organizationId!);
    }
  }, [organizationQuery.data, q.data]);

  const currentAdsColumns: ColumnDef<AdData>[] = [
    {
      accessorKey: "title",
      header: "Ad Title",
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <div>
            <div className="font-medium">
              {row.original.valueProp} • {row.original.audience}
            </div>
            <div className="text-sm text-gray-500">{row.original.format}</div>
          </div>
          {row.index === 0 && (
            <Rocket className="m-1 min-h-[2rem] min-w-[2rem] rounded bg-gray-100 p-1.5" />
          )}
        </div>
      ),
    },
    {
      accessorKey: "costPerKeyResult",
      header: "Cost/Key Result",
      cell: ({ row }) => {
        const value = row.getValue("costPerKeyResult");
        return <div>{value === "N/A" ? value : `$${value}`}</div>;
      },
    },
    {
      accessorKey: "keyResult",
      header: "Key Result",
    },
    // {
    //   accessorKey: "timeline",
    //   header: "Timeline",
    //   cell: ({ row }) => (
    //     <div>
    //       <div>{row.getValue("timeline")}</div>
    //       <div className="text-sm text-gray-500">
    //         {row.original.timelineDate}
    //       </div>
    //     </div>
    //   ),
    // },
    {
      accessorKey: "totalSpent",
      header: "Total Spent",
      cell: ({ row }) => <div>${row.getValue("totalSpent")}</div>,
    },
    ...(campaignGroupSegmentInfo.objectiveType === "LEAD_GENERATION"
      ? [
          {
            accessorKey: "oneClickLeads",
            header: "#Leads",
          },
          { accessorKey: "costPerLead", header: "Cost/Lead" },
        ]
      : []),
    {
      accessorKey: "clicks",
      header: "#Clicks",
    },
    {
      accessorKey: "ctr",
      header: "CTR",
    },
    {
      accessorKey: "costPerClick",
      header: "Cost/Click",
      cell: ({ row }) => {
        const value = row.getValue("costPerClick");
        return <div>{value === "N/A" ? value : `$${value}`}</div>;
      },
    },
    {
      accessorKey: "impressions",
      header: "#Impressions",
    },
    {
      accessorKey: "costPerImpression",
      header: "Cost/Impression",
    },
    {
      id: "edit",
      header: "",
      cell: ({ row }) => (
        <div className="flex gap-1">
          <Button
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              window.open(
                `https://www.linkedin.com/campaignmanager/accounts/${row.original.adAccountUrn}/campaigns/${row.original.campaignLinkedInUrn}/creatives/${row.original.adCreativeLinkedInUrnWithPrefix.split(":").pop()}`,
                "_blank",
              );
            }}
            title="Edit & Preview on LinkedIn"
          >
            Edit & Preview
          </Button>

          <ActionDropdownMenu
            onSelect={() => {
              console.log("Selecting ad for winner:", row.original);
              overrideABTestMutation.mutate({
                campaignGroupSegmentId,
                adLinkedInUrn:
                  row.original.adCreativeLinkedInUrnWithPrefix
                    .split(":")
                    .pop() ?? "",
              });
              new Promise((resolve) => setTimeout(resolve, 8000)).then(() => {
                fetchAdData(organizationQuery.data!.organizationId!).then(
                  (data) => {
                    setIsFetchingAdData(false);
                  },
                );
              });
            }}
            label="Select as Winner"
          />
        </div>
      ),
    },
  ];
  const pastAdsColumns: ColumnDef<AdData>[] = [
    {
      accessorKey: "title",
      header: "Ad Title",
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <div>
            <div className="font-medium">
              {row.original.valueProp} • {row.original.audience}
            </div>
            <div className="text-sm text-gray-500">{row.original.format}</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: "costPerKeyResult",
      header: "Cost/Key Result",
      cell: ({ row }) => {
        const value = row.getValue("costPerKeyResult");
        return <div>{value === "N/A" ? value : `$${value}`}</div>;
      },
    },
    {
      accessorKey: "keyResult",
      header: "Key Result",
    },
    // {
    //   accessorKey: "timeline",
    //   header: "Timeline",
    //   cell: ({ row }) => (
    //     <div>
    //       <div>{row.getValue("timeline")}</div>
    //       <div className="text-sm text-gray-500">
    //         {row.original.timelineDate}
    //       </div>
    //     </div>
    //   ),
    // },
    {
      accessorKey: "totalSpent",
      header: "Total Spent",
      cell: ({ row }) => <div>${row.getValue("totalSpent")}</div>,
    },
    ...(campaignGroupSegmentInfo.objectiveType === "LEAD_GENERATION"
      ? [
          {
            accessorKey: "oneClickLeads",
            header: "#Leads",
          },
          { accessorKey: "costPerLead", header: "Cost/Lead" },
        ]
      : []),
    {
      accessorKey: "clicks",
      header: "#Clicks",
    },
    {
      accessorKey: "ctr",
      header: "CTR",
    },
    {
      accessorKey: "costPerClick",
      header: "Cost/Click",
      cell: ({ row }) => {
        const value = row.getValue("costPerClick");
        return <div>{value === "N/A" ? value : `$${value}`}</div>;
      },
    },
    {
      accessorKey: "impressions",
      header: "#Impressions",
    },
    {
      accessorKey: "costPerImpression",
      header: "Cost/Impression",
    },
    {
      id: "edit",
      header: "",
      cell: ({ row }) => (
        <div className="flex gap-1">
          <Button
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              window.open(
                `https://www.linkedin.com/campaignmanager/accounts/${row.original.adAccountUrn}/campaigns/${row.original.campaignLinkedInUrn}/creatives/${row.original.adCreativeLinkedInUrnWithPrefix.split(":").pop()}`,
                "_blank",
              );
            }}
            title="Edit & Preview on LinkedIn"
          >
            Edit & Preview
          </Button>
        </div>
      ),
    },
  ];

  const upcomingAdsColumns: ColumnDef<AdData>[] = [
    {
      accessorKey: "title",
      header: "Ad Title",
      cell: ({ row }) => (
        <div>
          <div className="font-medium">
            {row.original.valueProp} • {row.original.audience}
          </div>
          <div className="text-sm text-gray-500">{row.original.format}</div>
        </div>
      ),
    },
    // {
    //   accessorKey: "timeline",
    //   header: "Timeline",
    //   cell: ({ row }) => (
    //     <div>
    //       <div>{row.getValue("timeline")}</div>
    //       <div className="text-sm text-gray-500">{row.original.date}</div>
    //     </div>
    //   ),
    // },
    {
      id: "actions",
      cell: ({ row }) => (
        <div className="flex justify-end gap-1">
          <Button
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              window.open(
                `https://www.linkedin.com/campaignmanager/accounts/${row.original.adAccountUrn}/campaigns/${row.original.campaignLinkedInUrn}/creatives/${row.original.adCreativeLinkedInUrnWithPrefix.split(":").pop()}`,
                "_blank",
              );
            }}
            title="Edit & Preview on LinkedIn"
          >
            Edit & Preview
          </Button>
        </div>
      ),
    },
  ];

  const currentAdsTable = useReactTable({
    data: currentAds,
    columns: currentAdsColumns,
    getCoreRowModel: getCoreRowModel(),
  });

  const upcomingAdsTable = useReactTable({
    data: upcomingAds,
    columns: upcomingAdsColumns,
    getCoreRowModel: getCoreRowModel(),
  });

  const pastAdsTable = useReactTable({
    data: pastAds,
    columns: pastAdsColumns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            {isFetchingAdData ? (
              <div className="h-8 w-96 animate-pulse rounded bg-gray-200" />
            ) : (
              <h1 className="text-2xl font-semibold">
                {campaignGroupSegmentInfo.title} |{" "}
                {campaignGroupSegmentInfo.segmentName ||
                  campaignGroupSegmentInfo.verticals
                    ?.map((v) => v.name)
                    .join(", ")}
              </h1>
            )}
            <div className="mt-2 flex items-center space-x-4 text-sm">
              {isFetchingAdData ? (
                <div className="flex w-full animate-pulse space-x-4">
                  <div className="h-4 w-20 rounded bg-gray-200" />
                  <div className="h-4 w-16 rounded bg-gray-200" />
                  <div className="h-4 w-32 rounded bg-gray-200" />
                  <div className="h-4 w-48 rounded bg-gray-200" />
                  <div className="h-4 w-24 rounded bg-gray-200" />
                </div>
              ) : (
                <>
                  <span className="flex items-center space-x-1">
                    <span
                      className={
                        campaignGroupSegmentInfo.stage === "ACTIVE"
                          ? "h-2 w-2 rounded-full bg-blue-600"
                          : "h-2 w-2 rounded-full bg-black"
                      }
                    ></span>
                    <span
                      className={
                        campaignGroupSegmentInfo.stage === "ACTIVE"
                          ? "text-blue-600"
                          : "text-gray-600"
                      }
                    >
                      {campaignGroupSegmentInfo.stage === "COMPLETED"
                        ? "Completed"
                        : campaignGroupSegmentInfo.stage === "DRAFT"
                          ? "Draft"
                          : "Running"}
                    </span>
                  </span>
                  <span className="text-gray-600">
                    {campaignGroupSegmentInfo.startDate &&
                    campaignGroupSegmentInfo.endDate
                      ? Math.ceil(
                          (new Date(
                            campaignGroupSegmentInfo.endDate,
                          ).getTime() -
                            new Date(
                              campaignGroupSegmentInfo.startDate,
                            ).getTime() +
                            1) /
                            (1000 * 60 * 60 * 24),
                        )
                      : ""}{" "}
                    Days
                  </span>
                  <span className="text-gray-600"></span>
                  <span className="text-gray-600">
                    Budget ${campaignGroupSegmentInfo.budget?.toFixed(2)}
                  </span>
                  <span className="text-gray-600">
                    {`${campaignGroupSegmentInfo.startDate} to ${campaignGroupSegmentInfo.endDate}`}
                  </span>
                  <span className="text-gray-600">
                    ${campaignGroupSegmentInfo.spend?.toFixed(2)} Spent
                  </span>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <span>⭐</span>
                <span className="text-sm text-gray-500">Key Result</span>
              </div>
              <div className="flex items-baseline space-x-2">
                <span className="text-2xl font-semibold">
                  {isFetchingAdData ? (
                    <div className="h-8 w-24 animate-pulse rounded bg-gray-200" />
                  ) : campaignGroupSegmentInfo.objectiveType ===
                    "LEAD_GENERATION" ? (
                    campaignGroupSegmentInfo.oneClickLeads
                  ) : (
                    campaignGroupSegmentInfo.clicks
                  )}
                </span>
                <span className="text-sm text-gray-500">
                  {campaignGroupSegmentInfo.objectiveType === "LEAD_GENERATION"
                    ? "Leads"
                    : "Site Visits"}
                </span>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="space-y-1">
              <div className="text-sm text-gray-500">Cost per Key Result</div>
              <div className="text-2xl font-semibold">
                {isFetchingAdData ? (
                  <div className="h-8 w-24 animate-pulse rounded bg-gray-200" />
                ) : (
                  <>
                    {campaignGroupSegmentInfo.spend &&
                    campaignGroupSegmentInfo.objectiveType === "LEAD_GENERATION"
                      ? campaignGroupSegmentInfo.oneClickLeads > 0
                        ? `$${(
                            campaignGroupSegmentInfo.spend /
                            campaignGroupSegmentInfo.oneClickLeads
                          ).toFixed(2)}`
                        : "N/A"
                      : campaignGroupSegmentInfo.clicks &&
                          campaignGroupSegmentInfo.clicks > 0
                        ? `$${(
                            campaignGroupSegmentInfo.spend /
                            campaignGroupSegmentInfo.clicks
                          ).toFixed(2)}`
                        : "N/A"}
                  </>
                )}
              </div>
            </div>
          </Card>
        </div>

        {/* Tabs */}
        <div className="border-b">
          <div className="flex space-x-8">
            <button
              className={`px-4 py-2 ${
                activeTab === "Active Ads"
                  ? "border-b-2 border-blue-600 text-blue-600"
                  : "text-gray-500"
              }`}
              onClick={() => setActiveTab("Active Ads")}
            >
              Active Ads
            </button>
            <button
              className={`px-4 py-2 ${
                activeTab === "Past Ads"
                  ? "border-b-2 border-blue-600 text-blue-600"
                  : "text-gray-500"
              }`}
              onClick={() => setActiveTab("Past Ads")}
            >
              Past Ads
            </button>
          </div>
        </div>

        {/* Conditionally render tables based on the active tab */}
        {activeTab === "Active Ads" && (
          <div>
            {/* Current Experiments Table */}
            <div>
              <div className="mb-4 flex items-center justify-between">
                <h2 className="text-lg font-semibold">Current Experiment</h2>
              </div>
              <div className="rounded-lg border bg-white">
                <DataTable
                  columns={currentAdsColumns}
                  table={currentAdsTable}
                  noHover={true}
                />
              </div>
            </div>

            {/* Upcoming Ads Table */}
            <div className="mt-8">
              <div className="mb-4 flex items-center justify-between">
                <h2 className="text-lg font-semibold">Upcoming</h2>
              </div>
              <div className="rounded-lg border bg-white">
                <DataTable
                  columns={upcomingAdsColumns}
                  table={upcomingAdsTable}
                  noHover={true}
                />
              </div>
            </div>
          </div>
        )}

        {activeTab === "Past Ads" && (
          <div>
            {/* Past Ads Table */}
            <div>
              <div className="mb-4 flex items-center justify-between">
                <h2 className="text-lg font-semibold">Past Ads</h2>
              </div>
              <div className="rounded-lg border bg-white">
                <DataTable
                  columns={currentAdsColumns}
                  table={pastAdsTable}
                  noHover={true}
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Render the EditAdModal */}
    </div>
  );
}
