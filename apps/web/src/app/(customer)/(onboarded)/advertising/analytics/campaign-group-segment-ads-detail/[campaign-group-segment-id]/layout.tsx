"use client";

export default function SegmentAdsDetailLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="flex h-full w-full flex-col justify-start">
      {/* <div className="sticky inset-x-0 top-0 flex h-16 w-full items-center justify-between border-b bg-white px-6">
        <h1 className="text-xl font-medium">Segment Ads Detail</h1>
      </div> */}
      <div className="h-full w-full overflow-auto bg-muted">{children}</div>
    </div>
  );
}
