"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { api } from "@/trpc/client";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@radix-ui/react-dropdown-menu";
import { format, formatDistance, set } from "date-fns";
import { CalendarIcon, ExternalLinkIcon, XIcon } from "lucide-react";

import { Button } from "@kalos/ui/button";
import { Calendar } from "@kalos/ui/calender";
import { Card } from "@kalos/ui/card";
import { Popover, PopoverContent, PopoverTrigger } from "@kalos/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@kalos/ui/select";
import { Skeleton } from "@kalos/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@kalos/ui/table/table";

import MetricsCards, {
  MetricsCardsArray,
  MetricsCardsProps,
} from "../performance/_components/MetricsCards";

// Simple utility function to join class names conditionally
const cn = (...classes: (string | boolean | undefined)[]) => {
  return classes.filter(Boolean).join(" ");
};

export default function PipelinePage() {
  const [selectedAdAccount, setSelectedAdAccount] = useState<string | null>(
    null,
  );
  const [stats, setStats] = useState<MetricsCardsProps[]>([]);
  const [date, setDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [organization, setOrganization] = useState<number>(0);
  // Fetch all ad accounts
  const adAccountsQuery = api.v2.ads.adAccounts.getForOrganization.useQuery();

  // Set the first ad account as selected when data loads
  useEffect(() => {
    if (adAccountsQuery.data?.length && !selectedAdAccount) {
      setSelectedAdAccount(adAccountsQuery.data[0]?.id ?? null);
      setOrganization(adAccountsQuery.data[0]?.organizationId ?? 0);
    }
  }, [adAccountsQuery.data, selectedAdAccount]);

  // Fetch leads and opportunities when needed
  // const leadsAndOpportunitiesQuery =
  //   api.v2.ads.linkedInLeadFormLead.getAllLeadsAndOpportunitiesByAccountId.useQuery(
  //     {
  //       linkedInAdAccountId: selectedAdAccount || "",
  //       startDateFilter: date,
  //       endDateFilter: endDate,
  //     },
  //     {
  //       enabled: !!selectedAdAccount,
  //     },
  //   );

  const getLeadsQuery = api.v2.ads.leads.getAllLeads.useQuery(
    {
      organizationId: organization,
      startDate: date ? toUTCDate(date) : undefined,
      endDate: endDate ? toUTCDate(endDate, true) : undefined,
    },
    {
      enabled: !!organization,
    },
  );

  useEffect(() => {}, [getLeadsQuery.data]);

  function toUTCDate(localDate: Date, addDay: boolean = false): Date {
    return new Date(
      Date.UTC(
        localDate.getFullYear(),
        localDate.getMonth(),
        addDay ? localDate.getDate() + 1 : localDate.getDate(),
        0,
        0,
        0,
        0,
      ),
    );
  }

  const allLeadsMetricsQuery = api.v2.ads.leads.getAllLeadsMetrics.useQuery(
    {
      organizationId: organization || 0,
      startDate: date ? toUTCDate(date) : undefined,
      endDate: endDate ? toUTCDate(endDate, true) : undefined,
    },
    {
      enabled: !!organization,
      refetchInterval: 60000,
    },
  );

  // Update stats when data loads
  useEffect(() => {
    if (allLeadsMetricsQuery.data) {
      const leadsData = allLeadsMetricsQuery.data;

      const stats: MetricsCardsArray = [
        {
          value: leadsData.totalCrmLeads + leadsData.totalLinkedLeads,
          label: "Leads",
          change: 0,
          trend: "neutral",
          currency: false,
        },
        {
          value: leadsData.totalMeetings,
          label: "Meetings Scheduled",
          change: 0,
          trend: "neutral",
          currency: false,
        },
        // {
        //   value: pipelineData.totalPipelineValue,
        //   label: "Pipeline Created",
        //   change: 0,
        //   trend: "neutral",
        //   currency: true,
        // },
        // {
        //   value: pipelineData.totalPipelineInfluence,
        //   label: "Pipeline Influenced",
        //   change: 0,
        //   trend: "neutral",
        //   currency: true,
        // },
      ];

      setStats(stats);
    }
  }, [allLeadsMetricsQuery.data]);

  // Format date range for display
  const formatDateRange = () => {
    if (date && endDate) {
      return `${format(date, "PPP")} - ${format(endDate, "PPP")}`;
    }
    if (date) {
      return format(date, "PPP");
    }
    return "Select date range";
  };

  return (
    <div className="flex h-screen w-full flex-col space-y-6 p-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <h2 className="mb-4 text-lg font-medium">Impact</h2>

        <div className="flex items-center gap-4">
          <div className="flex gap-2">
            <Popover
              open={isPopoverOpen}
              onOpenChange={(open) => {
                setIsPopoverOpen(open);
              }}
            >
              <PopoverTrigger asChild>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    className={cn(
                      "w-[300px] justify-start text-left font-normal",
                      !date && "text-muted-foreground",
                    )}
                    // onClick={() => setIsPopoverOpen(true)}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formatDateRange()}
                  </Button>
                </div>
              </PopoverTrigger>
              <PopoverContent
                className="w-auto justify-items-center pt-2"
                align="center"
              >
                <div className="mb-0 mt-1 flex gap-2">
                  <Button
                    onClick={() => {
                      setDate(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000));
                      setEndDate(new Date());
                      setIsPopoverOpen(false);
                    }}
                    className="bg-primary text-white "
                    variant="outline"
                    size="sm"
                  >
                    Last 1 Week
                  </Button>

                  <Button
                    onClick={() => {
                      setDate(new Date(Date.now() - 31 * 24 * 60 * 60 * 1000));
                      setEndDate(new Date());
                      setIsPopoverOpen(false);
                    }}
                    className="bg-primary text-white "
                    variant="outline"
                    size="sm"
                  >
                    Last 1 Month
                  </Button>

                  <Button
                    onClick={() => {
                      setDate(new Date(Date.now() - 90 * 24 * 60 * 60 * 1000));
                      setEndDate(new Date());
                      setIsPopoverOpen(false);
                    }}
                    className="bg-primary text-white "
                    variant="outline"
                    size="sm"
                  >
                    Last 3 Months
                  </Button>
                </div>
                <Calendar
                  className="!mt-0"
                  mode="range"
                  selected={{
                    from: date,
                    to: endDate,
                  }}
                  onSelect={(range: { from?: Date; to?: Date } | undefined) => {
                    setDate(range?.from);
                    setEndDate(range?.to);
                    if (range?.from && range?.to) {
                      setIsPopoverOpen(false);
                    }
                  }}
                  initialFocus
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
            <Button
              onClick={() => {
                setDate(undefined);
                setEndDate(undefined);
              }}
              variant="outline"
              size="sm"
              className="ml-0 p-1"
            >
              <XIcon size={2} className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>

      {/* Metrics Cards */}
      <MetricsCards stats={stats} />

      {/* Leads and Opportunities Table */}
      <Card className="p-6">
        <h2 className="mb-4 text-lg font-medium">Leads Created</h2>

        {getLeadsQuery.isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-8 w-full bg-gray-300" />
            <Skeleton className="h-8 w-full bg-gray-300" />
            <Skeleton className="h-8 w-full bg-gray-300" />
          </div>
        ) : getLeadsQuery.isError ? (
          <div className="rounded-md bg-red-50 p-4 text-red-700">
            Error loading leads: {getLeadsQuery.error.message}
          </div>
        ) : !getLeadsQuery.data || getLeadsQuery.data.length === 0 ? (
          <div className="py-8 text-center text-gray-500">No leads found</div>
        ) : (
          <div className="max-h-[60vh] overflow-y-auto  rounded-md border">
            <Table className="w-full table-auto">
              <TableHeader className="sticky top-0 z-20 bg-white shadow-sm ">
                <TableRow className="">
                  <TableHead className=" ">Company</TableHead>
                  <TableHead className=" ">Job Title</TableHead>
                  <TableHead className=" ">Lead Name</TableHead>
                  <TableHead className=" ">Linkedin</TableHead>
                  <TableHead className=" ">Email</TableHead>
                  <TableHead className=" ">Lead Created</TableHead>
                  <TableHead className=" ">Lead Source</TableHead>
                  <TableHead className=" ">Meeting Scheduled</TableHead>
                  {/* <TableHead>Opportunity</TableHead>
                  <TableHead className="text-right">Amount</TableHead> */}
                  {/* <TableHead>Opportunity Created</TableHead> */}
                </TableRow>
              </TableHeader>
              <TableBody className="">
                {getLeadsQuery.data.map((item, index) => (
                  <TableRow
                    className="transition-colors hover:bg-secondary data-[even=true]:bg-white data-[odd=true]:bg-gray-400"
                    key={index}
                  >
                    <TableCell>{item.companyName || "—"}</TableCell>
                    <TableCell>{item.jobTitle || "—"}</TableCell>

                    <TableCell className="font-medium">
                      {item.firstName} {item.lastName}
                    </TableCell>
                    <TableCell>
                      {item.linkedinProfileLink ? (
                        <Link
                          target="_blank"
                          rel="noreferrer"
                          href={item.linkedinProfileLink}
                        >
                          <ExternalLinkIcon className="mr-2 h-4 w-4" />
                        </Link>
                      ) : (
                        "—"
                      )}
                    </TableCell>
                    <TableCell>{item.workEmail || "—"}</TableCell>

                    <TableCell>
                      {item.leadCreatedAt
                        ? new Date(item.leadCreatedAt).toLocaleDateString(
                            "en-US",
                            {
                              timeZone: "UTC",
                            },
                          )
                        : ""}
                    </TableCell>
                    <TableCell>
                      {" "}
                      {item.source === "hubspot"
                        ? "Hubspot"
                        : item.source === "salesforce"
                          ? "Salesforce"
                          : item.source || ""}
                    </TableCell>
                    <TableCell>{item.engagement ? "Yes" : "No"}</TableCell>
                    {/* <TableCell>{item.opportunity.name || "—"}</TableCell>
                    <TableCell className="text-right">
                      {item.opportunity.amount || item.opportunity.amount === 0
                        ? item.opportunity.amount.toLocaleString("en-US", {
                            style: "currency",
                            currency: "USD",
                            maximumFractionDigits: 0,
                          })
                        : "—"}
                    </TableCell> */}
                    {/* <TableCell>
                      {item.opportunity.crmCreatedDate?.toLocaleDateString()}
                    </TableCell> */}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </Card>
    </div>
  );
}
