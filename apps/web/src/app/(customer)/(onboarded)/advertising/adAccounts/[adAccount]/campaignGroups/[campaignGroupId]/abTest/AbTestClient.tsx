"use client";

import type { Dispatch, SetStateAction } from "react";
import { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { snakeCaseToWords } from "@/app/utils/snakeCaseToWords";
import { useCampaignCreationStore } from "@/stores/campaignCreationStore";
import { api } from "@/trpc/client";
import { ReloadIcon } from "@radix-ui/react-icons";
import { ArrowLeft, ArrowRight, PlusIcon } from "lucide-react";

import { Button } from "@kalos/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@kalos/ui/card";
import { Checkbox } from "@kalos/ui/checkbox";
import { cn } from "@kalos/ui/index";
import { Input } from "@kalos/ui/input";
import { LottieLoading } from "@kalos/ui/lottie";

// Types
interface SelectedAd {
  campaignGroupSegmentId: string;
  adTopic: string;
}

// AbTestPage Component
export default function AbTestPage({
  params,
}: {
  params: {
    adAccount: string;
    campaignGroupId: string;
  };
}) {
  return <AbTestClient params={params} />;
}

// Main Client Component
function AbTestClient({
  params,
}: {
  params: {
    adAccount: string;
    campaignGroupId: string;
  };
}) {
  const router = useRouter();
  const { setAdAccount, setCampaignGroupId } = useCampaignCreationStore();

  // Set route params in store
  useEffect(() => {
    setAdAccount(params.adAccount);
    setCampaignGroupId(params.campaignGroupId);
  }, [params, setAdAccount, setCampaignGroupId]);

  const {
    isLoading,
    isSubmitting,
    loadingAds,
    selectedAds,
    segmentData,
    customTopics,
    adProgramFormat,
    setSelectedAds,
    onSubmit,
  } = useAbTestData();

  return (
    <>
      {isLoading && (
        <LottieLoading
          messages={[
            "Performing research",
            "Searching CRM and sales transcripts",
            "Reviewing your Closed-Won deals",
            'Analyzing thousands of "aha" moments',
            "Selecting top reasons per segment",
            "Finding matches",
            "Preparing to display...",
          ]}
          animations={{
            primary: {
              src: "/animations/Blue_Research_Numbers.lottie",
              autoplay: true,
              loop: false,
            },
            secondary: {
              src: "/animations/Blue_Research_Waves.lottie",
              autoplay: true,
              loop: false,
            },
          }}
        />
      )}

      <div
        className={cn(
          "flex h-full w-full flex-col items-start justify-between space-y-4",
          isLoading
            ? "opacity-0"
            : "opacity-100 transition-opacity duration-300 ease-in-out",
        )}
      >
        {!isLoading && segmentData && (
          <div className="flex h-full w-full flex-col items-start justify-start space-y-4 py-4">
            <div className="flex flex-wrap items-start justify-start gap-x-4 gap-y-4">
              {segmentData.map((segment) => (
                <ValueProps
                  key={segment.id}
                  segment={segment}
                  selectedAds={selectedAds}
                  setSelectedAds={setSelectedAds}
                  disabled={isSubmitting || loadingAds}
                  customTopics={customTopics[segment.id] || []}
                  isSubmitting={isSubmitting}
                  adProgramFormat={
                    adProgramFormat as "SPONSORED_INMAIL" | "SPONSORED_POST"
                  }
                />
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="fixed bottom-0 left-0 right-0 z-50 flex h-16 w-full items-center justify-between border-t bg-background p-4 shadow-md">
        <Button
          onClick={() =>
            router.push(
              `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/audience`,
            )
          }
          className="border-1 border border-blue-200 bg-blue-100 text-primary hover:bg-blue-200"
        >
          <ArrowLeft width="16" className="mr-2" /> Previous
        </Button>
        <Button
          onClick={onSubmit}
          disabled={selectedAds.length === 0 || isSubmitting}
        >
          {isSubmitting && <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />}
          Next
          <ArrowRight width="16" className="ml-2" />
        </Button>
      </div>
    </>
  );
}

// Hook for AB Test Data Logic
function useAbTestData() {
  const router = useRouter();
  const { adAccount, campaignGroupId } = useCampaignCreationStore();

  // Queries
  const adsQuery =
    api.v2.ads.adSegmentValueProp.getAdSegmentValuePropsForAdProgram.useQuery(
      {
        adProgramId: campaignGroupId,
        status: "DRAFT",
      },
      {
        enabled: !!campaignGroupId,
      },
    );

  const adProgramQuery = api.v2.ads.linkedInAdProgram.getOne.useQuery(
    {
      id: campaignGroupId,
    },
    {
      enabled: !!campaignGroupId,
    },
  );

  const segmentQuery =
    api.v2.ads.adSegment.getAllAdSegmentsForAdProgram.useQuery(
      {
        adProgramId: campaignGroupId,
      },
      {
        enabled: !!campaignGroupId,
      },
    );

  // Get segment details
  const segmentsDetailsQuery =
    api.v2.core.segment.getSegmentsForOrganization.useQuery();

  // State
  const [loadingAds, setLoadingAds] = useState(true);
  const [selectedAds, setSelectedAds] = useState<SelectedAd[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Utils
  const apiUtils = api.useUtils();

  // Set initial selected ads when data is loaded
  useEffect(() => {
    if (adsQuery.data && loadingAds) {
      setSelectedAds(
        adsQuery.data.map((ad) => ({
          campaignGroupSegmentId: ad.linkedInAdSegmentId,
          adTopic: ad.valueProp,
        })),
      );
      setLoadingAds(false);
    }
  }, [adsQuery.data, loadingAds]);

  // Mutation
  const adMutation =
    api.v2.ads.adSegmentValueProp.setAdSegmentValuePropsForManyAdSegments.useMutation(
      {
        onSuccess: async () => {
          await apiUtils.v2.ads.invalidate();
          if (adProgramQuery.data?.adFormat.type === "SPONSORED_INMAIL") {
            router.push(
              `/advertising/adAccounts/${adAccount}/campaignGroups/${campaignGroupId}/convo-subject`,
            );
          } else {
            router.push(
              `/advertising/adAccounts/${adAccount}/campaignGroups/${campaignGroupId}/ad`,
            );
          }
          setIsSubmitting(false);
        },
      },
    );

  // Handle form submission
  function onSubmit() {
    setIsSubmitting(true);

    // Group ads by segment
    const campaignsWithAds: {
      campaignGroupSegmentId: string;
      adTopics: string[];
    }[] = [];

    for (const selectedAd of selectedAds) {
      const campaign = campaignsWithAds.find(
        (campaign) =>
          campaign.campaignGroupSegmentId === selectedAd.campaignGroupSegmentId,
      );

      if (campaign) {
        campaign.adTopics.push(selectedAd.adTopic);
      } else {
        campaignsWithAds.push({
          campaignGroupSegmentId: selectedAd.campaignGroupSegmentId,
          adTopics: [selectedAd.adTopic],
        });
      }
    }

    // Submit data
    adMutation.mutate({
      adProgramId: campaignGroupId,
      adSegmentIds: campaignsWithAds
        .map((each) => ({
          adSegmentId: each.campaignGroupSegmentId,
          valuePropIds: each.adTopics,
        }))
        .filter((each) =>
          segmentQuery.data?.some((segment) => segment.id === each.adSegmentId),
        ),
    });
  }

  // Process segments and map custom topics
  const segmentData = segmentQuery.data?.map((segment) => {
    const segmentDetails = segmentsDetailsQuery.data?.find(
      (s) => s.id === segment.segmentId,
    );
    return {
      ...segment,
      details: segmentDetails || {
        id: segment.segmentId,
        name: "",
        verticals: [],
        annualRevenueLowBound: null,
        annualRevenueHighBound: null,
        numberOfEmployeesLowBound: null,
        numberOfEmployeesHighBound: null,
        annualContractValueLowBound: null,
        annualContractValueHighBound: null,
        jobFunction: null,
        jobSeniority: null,
      },
    };
  });

  // Map custom topics by segment ID
  const customTopics =
    segmentQuery.data?.reduce<Record<string, string[]>>((result, segment) => {
      result[segment.id] =
        adsQuery.data
          ?.filter((each) => each.linkedInAdSegmentId === segment.id)
          .map((ad) => ad.valueProp) || [];

      return result;
    }, {}) || {};

  return {
    isLoading:
      segmentQuery.isLoading ||
      adProgramQuery.isLoading ||
      segmentsDetailsQuery.isLoading,
    isSubmitting,
    loadingAds,
    selectedAds,
    segmentData,
    adProgramFormat: adProgramQuery.data?.adFormat.type,
    customTopics,
    setSelectedAds,
    onSubmit,
  };
}

// ValueProps Component
function ValueProps({
  segment,
  selectedAds,
  setSelectedAds,
  disabled,
  customTopics,
  isSubmitting,
  adProgramFormat,
}: {
  segment: {
    id: string;
    adProgramId: string;
    segmentId: string;
    details: {
      id: string;
      name?: string | null;
      verticals: string[];
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
      annualContractValueLowBound?: number | null;
      annualContractValueHighBound?: number | null;
      jobFunction?: string | null;
      jobSeniority?: string | null;
    };
  };
  selectedAds: SelectedAd[];
  setSelectedAds: Dispatch<SetStateAction<SelectedAd[]>>;
  customTopics: string[];
  disabled: boolean;
  isSubmitting: boolean;
  adProgramFormat: "SPONSORED_INMAIL" | "SPONSORED_POST";
}) {
  // Query for available value props in segment
  const valuePropsQuery =
    api.v2.core.segment.getSegmentValuePropForSegment.useQuery({
      segmentId: segment.segmentId,
    });

  const [userAddedTopics, setUserAddedTopics] = useState<string[]>([]);

  useEffect(() => {
    if (isSubmitting) {
      setUserAddedTopics([]);
    }
  }, [isSubmitting]);

  return (
    <Card className="h-[280] w-[424px] px-4 py-4">
      <CardHeader>
        <CardTitle>
          <SegmentDetails
            row={{
              original: segment.details,
            }}
          />
        </CardTitle>
      </CardHeader>
      <CardContent className="flex h-[175px] flex-col items-start justify-start space-y-2 overflow-auto text-sm">
        {valuePropsQuery.data?.map((each) => (
          <div
            key={each.name}
            className="flex items-center justify-start space-x-2"
          >
            <Checkbox
              disabled={disabled}
              checked={
                selectedAds.find(
                  (selectedAd) =>
                    selectedAd.adTopic === each.name &&
                    selectedAd.campaignGroupSegmentId === segment.id,
                ) !== undefined
              }
              onCheckedChange={(checked) => {
                if (adProgramFormat === "SPONSORED_INMAIL") {
                  if (checked) {
                    setSelectedAds([
                      ...selectedAds.filter(
                        (ad) => ad.campaignGroupSegmentId !== segment.id,
                      ),
                      {
                        campaignGroupSegmentId: segment.id,
                        adTopic: each.name,
                      },
                    ]);
                  } else {
                    setSelectedAds(
                      selectedAds.filter(
                        (ad) =>
                          ad.adTopic !== each.name ||
                          ad.campaignGroupSegmentId !== segment.id,
                      ),
                    );
                  }
                } else {
                  if (checked) {
                    setSelectedAds([
                      ...selectedAds,
                      {
                        campaignGroupSegmentId: segment.id,
                        adTopic: each.name,
                      },
                    ]);
                  } else {
                    setSelectedAds(
                      selectedAds.filter(
                        (selectedAd) =>
                          selectedAd.adTopic !== each.name ||
                          selectedAd.campaignGroupSegmentId !== segment.id,
                      ),
                    );
                  }
                }
              }}
            />
            <p>{each.name}</p>
          </div>
        ))}

        {valuePropsQuery.data &&
          customTopics
            .filter(
              (each) => !valuePropsQuery.data.find((v) => v.name === each),
            )
            .map((each) => (
              <div
                key={each}
                className="flex items-center justify-start space-x-2"
              >
                <Checkbox
                  disabled={disabled}
                  checked={
                    selectedAds.find(
                      (selectedAd) =>
                        selectedAd.adTopic === each &&
                        selectedAd.campaignGroupSegmentId === segment.id,
                    ) !== undefined
                  }
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedAds([
                        ...selectedAds,
                        {
                          campaignGroupSegmentId: segment.id,
                          adTopic: each,
                        },
                      ]);
                    } else {
                      setSelectedAds(
                        selectedAds.filter(
                          (selectedAd) =>
                            selectedAd.adTopic !== each ||
                            selectedAd.campaignGroupSegmentId !== segment.id,
                        ),
                      );
                    }
                  }}
                />
                <p>{each}</p>
              </div>
            ))}

        {userAddedTopics.map((each, index) => (
          <div
            key={index}
            className="flex items-center justify-start space-x-2"
          >
            <Checkbox
              checked={
                selectedAds.find(
                  (selectedAd) =>
                    selectedAd.adTopic === each &&
                    selectedAd.campaignGroupSegmentId === segment.id,
                ) !== undefined
              }
              disabled={each === "" || disabled}
              onCheckedChange={(checked) => {
                if (checked) {
                  setSelectedAds([
                    ...selectedAds,
                    {
                      campaignGroupSegmentId: segment.id,
                      adTopic: each,
                    },
                  ]);
                } else {
                  setSelectedAds(
                    selectedAds.filter(
                      (selectedAd) =>
                        selectedAd.adTopic !== each ||
                        selectedAd.campaignGroupSegmentId !== segment.id,
                    ),
                  );
                }
              }}
            />
            <Input
              onChange={(event) => {
                const newTopics = [...userAddedTopics];
                newTopics[index] = event.target.value;
                setUserAddedTopics(newTopics);
              }}
              className="h-7 w-full"
              value={userAddedTopics[index]}
            />
          </div>
        ))}

        <Button
          variant="ghost"
          disabled={disabled}
          className="hover:bg-transpatent p-0 text-xs text-primary hover:text-blue-300"
          onClick={() => {
            setUserAddedTopics([...userAddedTopics, ""]);
          }}
        >
          <PlusIcon className="m-0 mr-2 p-0" height="16" width="16" /> Add more
        </Button>
      </CardContent>
    </Card>
  );
}

// SegmentDetails Component
function SegmentDetails({
  row,
}: {
  row: {
    original: {
      id: string;
      name?: string | null;
      verticals: string[];
      annualRevenueLowBound?: number | null;
      annualRevenueHighBound?: number | null;
      numberOfEmployeesLowBound?: number | null;
      numberOfEmployeesHighBound?: number | null;
      annualContractValueLowBound?: number | null;
      annualContractValueHighBound?: number | null;
      jobFunction?: string | null;
      jobSeniority?: string | null;
    };
  };
}) {
  if (!row.original.name) {
    const nameArray: string[] = [];
    if (row.original.jobFunction) {
      nameArray.push(snakeCaseToWords(row.original.jobFunction));
    }
    if (row.original.jobSeniority) {
      nameArray.push(snakeCaseToWords(row.original.jobSeniority));
    }

    if (row.original.verticals) {
      nameArray.push(row.original.verticals.join(", "));
    }

    if (
      row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualContractValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }
    if (
      row.original.annualContractValueLowBound &&
      !row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ ACV`,
      );
    }
    if (
      !row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualContractValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }

    if (
      row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }
    if (
      row.original.annualRevenueLowBound &&
      !row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ prospect revenue`,
      );
    }
    if (
      !row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }

    if (
      row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-${row.original.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }
    if (
      row.original.numberOfEmployeesLowBound &&
      !row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ employees`,
      );
    }
    if (
      !row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `<${row.original.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }

    let acvQueryParam = "";
    if (
      row.original.annualContractValueHighBound === 20000 &&
      row.original.annualContractValueLowBound === undefined
    ) {
      acvQueryParam = "1";
    } else if (
      row.original.annualContractValueHighBound === 50000 &&
      row.original.annualContractValueLowBound === 20000
    ) {
      acvQueryParam = "2";
    } else if (
      row.original.annualContractValueHighBound === 100000 &&
      row.original.annualContractValueLowBound === 50000
    ) {
      acvQueryParam = "3";
    } else if (
      row.original.annualContractValueHighBound === undefined &&
      row.original.annualContractValueLowBound === 100000
    ) {
      acvQueryParam = "4";
    }

    let empoyeesQueryParam = "";
    if (
      row.original.numberOfEmployeesHighBound === 100 &&
      row.original.numberOfEmployeesLowBound === undefined
    ) {
      empoyeesQueryParam = "1";
    } else if (
      row.original.numberOfEmployeesHighBound === 500 &&
      row.original.numberOfEmployeesLowBound === 100
    ) {
      empoyeesQueryParam = "2";
    } else if (
      row.original.numberOfEmployeesHighBound === undefined &&
      row.original.numberOfEmployeesLowBound === 500
    ) {
      empoyeesQueryParam = "3";
    }

    let prospectRevenue = "";
    if (
      row.original.annualRevenueLowBound === 1000000 &&
      row.original.annualRevenueHighBound === 10000000
    ) {
      prospectRevenue = "1";
    } else if (
      row.original.annualRevenueLowBound === 10000000 &&
      row.original.annualRevenueHighBound === 200000000
    ) {
      prospectRevenue = "2";
    } else if (
      row.original.annualRevenueLowBound === 200000000 &&
      row.original.annualRevenueHighBound === undefined
    ) {
      prospectRevenue = "3";
    }

    // Search params
    const params = new URLSearchParams({
      acv: acvQueryParam,
      vertical: row.original.verticals.join(",") ?? "",
      employees: empoyeesQueryParam,
      prospectRevenue: prospectRevenue,
    });

    if (nameArray.length === 0) {
      return (
        <div className="space-y-1">
          <Link href={`/}`} className="w-[80px]">
            All
          </Link>
        </div>
      );
    }
    return (
      <div className="w-[380px] space-y-1 text-wrap">
        <span className="50 w-[380px] text-wrap">{nameArray.join(" • ")}</span>
      </div>
    );
  }
  return (
    <div className="space-y-1">
      <span className="w-[80px]">{row.original.name}</span>
    </div>
  );
}
