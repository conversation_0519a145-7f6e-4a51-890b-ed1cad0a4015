"use client";

import { SelectSegments } from "@/app/(customer)/(onboarded)/advertising/adAccounts/[adAccount]/campaignGroups/[campaignGroupId]/segments/_components/select-segment-form";
import { api } from "@/trpc/client";

export default function SelectAdSegmentPage({
  params,
}: {
  params: { adAccount: string; campaignGroupId: string };
}) {
  const res = api.v2.core.segment.getSegmentsForOrganization.useQuery();
  return (
    <SelectSegments
      data={res.data ?? []}
      adAccount={params.adAccount}
      campaignGroupId={params.campaignGroupId}
    />
  );
}
