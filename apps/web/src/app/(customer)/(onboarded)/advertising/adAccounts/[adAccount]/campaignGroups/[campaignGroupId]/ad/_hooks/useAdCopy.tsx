import type { Dispatch, SetStateAction } from "react";
import { createContext, useContext, useMemo, useState } from "react";

type AdCopyItem = {
  adId: string;
  headline: string;
  introductoryText: string;
  description: string;
};

type AdCopyContextType = {
  adCopy: AdCopyItem[];
  setAdCopy: Dispatch<SetStateAction<AdCopyItem[]>>;
};

const AdCopyContext = createContext<AdCopyContextType>({
  adCopy: [],
  setAdCopy: () => {},
});

/**
 * Hook for accessing the ad copy context
 * @returns The ad copy context
 */
export const useAdCopyContext = () => {
  return useContext(AdCopyContext);
};

/**
 * Provider component for ad copy context
 * @param children - Child components
 */
export function AdCopyContextProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [adCopy, setAdCopy] = useState<AdCopyItem[]>([]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      adCopy,
      setAdCopy,
    }),
    [adCopy],
  );

  return (
    <AdCopyContext.Provider value={contextValue}>
      {children}
    </AdCopyContext.Provider>
  );
}
