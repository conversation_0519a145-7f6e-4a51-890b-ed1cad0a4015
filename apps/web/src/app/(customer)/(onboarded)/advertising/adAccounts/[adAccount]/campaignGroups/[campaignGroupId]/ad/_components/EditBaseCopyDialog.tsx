import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import { api } from "@/trpc/client";
import { getBaseUrl } from "@/trpc/provider";
import { WandIcon } from "lucide-react";

import { But<PERSON> } from "@kalos/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";
import { Popover, PopoverContent, PopoverTrigger } from "@kalos/ui/popover";
import { Textarea } from "@kalos/ui/textarea";

interface EditBaseCopyDialogProps {
  adSegmentId: string;
  setIsRefreshingCopy: Dispatch<SetStateAction<boolean>>;
}

export function EditBaseCopyDialog({
  adSegmentId,
  setIsRefreshingCopy,
}: EditBaseCopyDialogProps) {
  const baseCopyQuery =
    api.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.useQuery({
      adSegmentId: adSegmentId,
    });

  const apiUtils = api.useUtils();

  const updateBaseCopy =
    api.v2.ads.socialPostBaseCopy.updateBaseCopy.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.ads.invalidate();
        setIsRefreshingCopy(false);
        setIsSaving(false);
        setDialogOpen(false);
      },
    });

  const [editText, setEditText] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [feedback, setFeedback] = useState("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [isFeedbackOpen, setIsFeedbackOpen] = useState(false);

  useEffect(() => {
    setEditText(baseCopyQuery.data?.baseCopy || "");
  }, [baseCopyQuery.data]);

  function saveBaseCopy() {
    setIsSaving(true);
    setIsRefreshingCopy(true);
    updateBaseCopy.mutate({
      adSegmentId: adSegmentId,
      baseCopy: editText,
    });
  }

  async function submitFeedback() {
    setEditText("");
    const response = await fetch(`${getBaseUrl()}/stream-refined`, {
      method: "POST",
      headers: {
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        baseCopy: editText,
        feedback: feedback,
        config: {
          type: "socialPost",
          field: "body",
        },
      }),
    });
    if (!response.ok || !response.body) {
      throw response.statusText;
    }

    // Here we start prepping for the streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    const loopRunner = true;

    while (loopRunner) {
      // Here we start reading the stream, until its done.
      const { value, done } = await reader.read();
      if (done) {
        break;
      }
      const decodedChunk = decoder.decode(value, { stream: true });
      for (const each of decodedChunk) {
        try {
          setEditText((prev) => prev + each);
        } catch (e) {}
      }
    }
  }

  useEffect(() => {
    if (!dialogOpen) {
      if (baseCopyQuery.data) {
        setEditText(baseCopyQuery.data.baseCopy);
      }
    }
  }, [dialogOpen]);

  useEffect(() => {
    setFeedback("");
  }, [isFeedbackOpen]);

  return (
    <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
      <DialogTrigger asChild>
        <Button>Edit Base Copy</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Base Copy</DialogTitle>
        </DialogHeader>
        <div className="flex w-full justify-between">
          <Popover open={isFeedbackOpen} onOpenChange={setIsFeedbackOpen}>
            <PopoverTrigger asChild>
              <Button className="border border-blue-500 bg-blue-200 text-black">
                <WandIcon width="16" height="16" className="mr-1" />
                Ask Blue to Rewrite
              </Button>
            </PopoverTrigger>
            <PopoverContent className="flex flex-col items-start justify-start space-y-2">
              <Textarea
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
              />
              <div className="flex w-full items-center justify-end gap-2">
                <Button onClick={submitFeedback}>Submit</Button>
              </div>
            </PopoverContent>
          </Popover>
          <div className="flex w-full items-center justify-end gap-2">
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={saveBaseCopy} disabled={isSaving}>
              Save
            </Button>
          </div>
        </div>

        <span className="text-sm font-semibold">Base Copy</span>
        <Textarea
          value={editText}
          onChange={(e) => setEditText(e.target.value)}
          style={{
            height: `310px`,
            minHeight: "100px",
            padding: "8px 12px",
            boxSizing: "border-box",
            lineHeight: "21px",
            width: "100%",
            resize: "none",
          }}
        />
      </DialogContent>
    </Dialog>
  );
}
