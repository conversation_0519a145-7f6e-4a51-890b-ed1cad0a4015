"use client";

import { useState } from "react";
import { ChevronDown } from "lucide-react";

import { Card, CardHeader } from "@kalos/ui/card";
import { cn } from "@kalos/ui/index";

import type { CustomTopic, SegmentData, SelectedAd } from "../types";
import { SegmentDetailsDisplay } from "./SegmentDetailsDisplay";
import { ValuePropsSelector } from "./ValuePropsSelector";

interface SegmentsListProps {
  segments: SegmentData[];
  selectedAds: SelectedAd[];
  setSelectedAds: React.Dispatch<React.SetStateAction<SelectedAd[]>>;
  customTopics: CustomTopic[];
  isSubmitting: boolean;
  loadingAds: boolean;
}

export function SegmentsList({
  segments,
  selectedAds,
  setSelectedAds,
  customTopics,
  isSubmitting,
  loadingAds,
}: SegmentsListProps): JSX.Element {
  const [collapsedSegments, setCollapsedSegments] = useState<string[]>([]);

  // Function to toggle segment collapse
  const toggleSegmentCollapse = (segmentId: string) => {
    setCollapsedSegments((prevCollapsed) => {
      if (!Array.isArray(prevCollapsed)) return [segmentId]; // Safety check
      return prevCollapsed.includes(segmentId)
        ? prevCollapsed.filter((id) => id !== segmentId)
        : [...prevCollapsed, segmentId];
    });
  };

  // Ensure collapsedSegments is always an array
  const safeCollapsedSegments = Array.isArray(collapsedSegments)
    ? collapsedSegments
    : [];

  return (
    <div className="flex w-full flex-col items-start justify-start gap-y-4">
      {segments.map((segment, index) => (
        <Card key={segment.id} className="w-full">
          <CardHeader
            className={cn("flex cursor-pointer items-center bg-gray-50 py-3")}
            onClick={() => toggleSegmentCollapse(segment.id)}
          >
            <div className="flex w-full items-center justify-between">
              <div>
                <div className="mb-1 text-xs text-muted-foreground">
                  Segment {index + 1}
                </div>
                <SegmentDetailsDisplay segmentId={segment.segmentId} />
              </div>
              <ChevronDown
                className={cn(
                  "h-5 w-5 text-gray-500 transition-transform duration-200",
                  safeCollapsedSegments.includes(segment.id)
                    ? "rotate-180"
                    : "",
                )}
              />
            </div>
          </CardHeader>

          <div
            className={cn(
              "transition-all duration-300 ease-in-out",
              safeCollapsedSegments.includes(segment.id)
                ? "max-h-0 opacity-0"
                : "max-h-[2000px] p-4 opacity-100",
            )}
          >
            <div className="p-0">
              <div className="flex flex-col items-start justify-start space-y-3">
                <div className="mb-2 w-full text-base">
                  Here are the top 5 reasons your customers are buying from you:
                </div>
                <ValuePropsSelector
                  segment={segment}
                  selectedAds={selectedAds}
                  setSelectedAds={setSelectedAds}
                  disabled={isSubmitting || loadingAds}
                  customTopics={customTopics
                    .filter((topic) => topic.segmentId === segment.id)
                    .map((topic) => topic.valueProp)}
                  isSubmitting={isSubmitting}
                />
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}
