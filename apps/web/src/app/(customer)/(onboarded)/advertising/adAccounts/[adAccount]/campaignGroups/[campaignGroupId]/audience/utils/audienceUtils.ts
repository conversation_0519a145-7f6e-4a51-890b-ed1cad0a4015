export function getFriendlyFacetName(facetName: string): string {
    const facetNameMap: Record<string, string> = {
        'Locations': 'Countries',
    };

    return facetNameMap[facetName] || facetName;
}


export const LINKEDIN_LOCATIONS_FACET_URN = 'urn:li:adTargetingFacet:locations';


export const unitedStatesLocationCriteria = {
    include: {
        and: [
            {
                or: [
                    {
                        facetUrn: LINKEDIN_LOCATIONS_FACET_URN,
                        facetName: 'Locations',
                        facetEntites: [
                            {
                                facetUrn: LINKEDIN_LOCATIONS_FACET_URN,
                                entityUrn: "urn:li:geo:*********",
                                entityName: "United States"
                            }
                        ]
                    }
                ]
            }
        ]
    },
};

export const defaultLocations = [
    {
        name: 'United States',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'Canada',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'United Kingdom',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'Germany',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'Australia',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'Japan',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'France',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'North America',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'Latin America',
        urn: 'urn:li:geo:91000011'
    },
    {
        name: 'Europe',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'Asia',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'Africa',
        urn: 'urn:li:geo:*********'
    },
    {
        name: 'Middle East',
        urn: 'urn:li:geo:91000001'
    }
];