import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useCampaignCreationStore } from "@/stores/campaignCreationStore";
import { api } from "@/trpc/client";

import type { SelectedAd } from "../types";

export function useAbTestData() {
  const router = useRouter();
  const {
    adAccount,
    campaignGroupId,
    abTestState,
    setSelectedAds,
    setAbTestSubmitting,
    setLoadingAds,
  } = useCampaignCreationStore();

  const { selectedAds, isSubmitting, loadingAds } = abTestState;

  // Local state for loading animation
  const [isLoading, setIsLoading] = useState(true);
  const [loadStartTime] = useState<number>(Date.now());

  const adsQuery =
    api.v2.ads.adSegmentValueProp.getAdSegmentValuePropsForAdProgram.useQuery({
      adProgramId: campaignGroupId,
      status: "DRAFT",
    });

  const adProgramQuery = api.v2.ads.linkedInAdProgram.getOne.useQuery({
    id: campaignGroupId,
  });

  const segmentQuery =
    api.v2.ads.adSegment.getAllAdSegmentsForAdProgram.useQuery({
      adProgramId: campaignGroupId,
    });

  const apiUtils = api.useUtils();

  const adMutation =
    api.v2.ads.adSegmentValueProp.setAdSegmentValuePropsForManyAdSegments.useMutation(
      {
        onSuccess: async () => {
          await apiUtils.v2.ads.invalidate();
          if (adProgramQuery.data?.adFormat.type === "SPONSORED_INMAIL") {
            router.push(
              `/advertising/adAccounts/${adAccount}/campaignGroups/${campaignGroupId}/conversation-ad`,
            );
          } else {
            router.push(
              `/advertising/adAccounts/${adAccount}/campaignGroups/${campaignGroupId}/ad`,
            );
          }
          setAbTestSubmitting(false);
        },
      },
    );

  // Hardcoded delay for loading animation
  useEffect(() => {
    setTimeout(() => {
      setIsLoading(false);
    }, 800);
  }, []);

  // Load ad data
  useEffect(() => {
    if (adsQuery.data && loadingAds) {
      setSelectedAds(
        adsQuery.data.map((ad) => ({
          campaignGroupSegmentId: ad.linkedInAdSegmentId,
          adTopic: ad.valueProp,
        })),
      );
      setLoadingAds(false);

      // Calculate and log the loading time
      const loadEndTime = Date.now();
      const loadTimeInSeconds = (loadEndTime - loadStartTime) / 1000;
      console.log(
        `Ad data loading time: ${loadTimeInSeconds.toFixed(2)} seconds`,
      );
    }
  }, [adsQuery.data, loadingAds, loadStartTime, setSelectedAds, setLoadingAds]);

  function onSubmit() {
    setAbTestSubmitting(true);
    const campaignsWithAds: {
      campaignGroupSegmentId: string;
      adTopics: string[];
    }[] = [];

    for (const selectedAd of selectedAds) {
      const campaign = campaignsWithAds.find(
        (campaign) =>
          campaign.campaignGroupSegmentId === selectedAd.campaignGroupSegmentId,
      );

      if (campaign) {
        campaign.adTopics.push(selectedAd.adTopic);
      } else {
        campaignsWithAds.push({
          campaignGroupSegmentId: selectedAd.campaignGroupSegmentId,
          adTopics: [selectedAd.adTopic],
        });
      }
    }

    adMutation.mutate({
      adProgramId: campaignGroupId,
      adSegmentIds: campaignsWithAds.map((each) => ({
        adSegmentId: each.campaignGroupSegmentId,
        valuePropIds: each.adTopics,
      })),
    });
  }

  return {
    isLoading,
    isSubmitting,
    loadingAds,
    selectedAds,
    segmentData: segmentQuery.data,
    customTopics: adsQuery.data?.map((ad) => ({
      segmentId: ad.linkedInAdSegmentId,
      valueProp: ad.valueProp,
    })),
    setSelectedAds,
    onSubmit,
  };
}
