"use client";

import { CreateCampaignGroupForm } from "@/features/advertising/campaignGroup/components/create-campaign-group-form";
import { useCampaignDataPrefetch } from "@/features/advertising/campaignGroup/hooks/useCampaignDataPrefetch";

export default function CreateAdvertisingPage({
  params,
}: {
  params: { adAccount: string };
}) {
  // Trigger prefetching of all required data in parallel
  useCampaignDataPrefetch(params.adAccount);

  return (
    <div className="flex h-full w-full flex-col items-start justify-start space-y-4">
      <CreateCampaignGroupForm adAccount={params.adAccount} />
    </div>
  );
}
