export interface SelectedAd {
  campaignGroupSegmentId: string;
  adTopic: string;
}

export interface SegmentData {
  id: string;
  adProgramId: string;
  segmentId: string;
}

export interface SegmentDetails {
  id: string;
  name?: string | null;
  verticals: string[];
  annualRevenueLowBound?: number | null;
  annualRevenueHighBound?: number | null;
  numberOfEmployeesLowBound?: number | null;
  numberOfEmployeesHighBound?: number | null;
  annualContractValueLowBound?: number | null;
  annualContractValueHighBound?: number | null;
  jobFunction?: string | null;
  jobSeniority?: string | null;
}

export interface CustomTopic {
  segmentId: string;
  valueProp: string;
}
