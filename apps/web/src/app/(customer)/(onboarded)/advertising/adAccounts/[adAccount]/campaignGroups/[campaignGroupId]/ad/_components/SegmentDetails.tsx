import React from "react";
import { snakeCaseToWords } from "@/app/utils/snakeCaseToWords";

type SegmentData = {
  name?: string | null;
  jobFunction?: string | null;
  jobSeniority?: string | null;
  verticals?: string[] | null;
  annualContractValueLowBound?: number | null;
  annualContractValueHighBound?: number | null;
  annualRevenueLowBound?: number | null;
  annualRevenueHighBound?: number | null;
  numberOfEmployeesLowBound?: number | null;
  numberOfEmployeesHighBound?: number | null;
};

interface SegmentDetailsProps {
  row: {
    original: SegmentData;
  };
}

export function SegmentDetails({ row }: SegmentDetailsProps) {
  if (!row.original.name) {
    const nameArray: string[] = [];

    if (row.original.jobFunction) {
      nameArray.push(snakeCaseToWords(row.original.jobFunction));
    }
    if (row.original.jobSeniority) {
      nameArray.push(snakeCaseToWords(row.original.jobSeniority));
    }

    if (row.original.verticals) {
      nameArray.push(...row.original.verticals);
    }

    if (
      row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualContractValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }
    // ... [rest of the conditional formatting logic]
    if (
      row.original.annualContractValueLowBound &&
      !row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ ACV`,
      );
    }
    if (
      !row.original.annualContractValueLowBound &&
      row.original.annualContractValueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualContractValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }

    if (
      row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }
    if (
      row.original.annualRevenueLowBound &&
      !row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ prospect revenue`,
      );
    }
    if (
      !row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }

    if (
      row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-${row.original.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }
    if (
      row.original.numberOfEmployeesLowBound &&
      !row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ employees`,
      );
    }
    if (
      !row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `<${row.original.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }

    if (nameArray.length === 0) {
      return (
        <div className="space-y-1">
          <span className="w-[80px]">All</span>
        </div>
      );
    }
    return (
      <div className="w-full space-y-1 text-wrap py-1">
        <span className="">{nameArray.join(" • ")}</span>
      </div>
    );
  }
  return (
    <div className="space-y-1">
      <span className="w-[80px]">{row.original.name}</span>
    </div>
  );
}
