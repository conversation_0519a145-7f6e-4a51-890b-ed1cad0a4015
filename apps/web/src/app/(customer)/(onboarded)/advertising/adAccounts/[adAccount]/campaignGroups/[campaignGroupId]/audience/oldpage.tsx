"use client";

// ADDING THIS COMMENT TO TRIGGER DEPLOY IN VERCEL
import type { DragEndEvent } from "@dnd-kit/core";
import type { CellContext, ColumnDef } from "@tanstack/react-table";
import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useCampaignGroupContext } from "@/features/advertising/context";
import { api } from "@/trpc/client";
import { DndContext, useDraggable, useDroppable } from "@dnd-kit/core";
import { ReloadIcon } from "@radix-ui/react-icons";
import {
  flexRender,
  getCoreRowModel,
  getGroupedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { SfDate } from "jsforce";
import { ArrowLeft, ArrowRight, PlusIcon, XIcon } from "lucide-react";

import type { CampaignSegment } from "@kalos/advertising";
import type { SelectAccountCustomStringFieldFiltersForSegment } from "@kalos/database/validation/accountCustomStringFieldFiltersForSegment";
import type { SelectAccountCustomStringListFieldFiltersForSegment } from "@kalos/database/validation/accountCustomStringListFieldFiltersForSegment.";
import type { SelectSegmentSchema } from "@kalos/database/validation/Segment";
import { Button } from "@kalos/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@kalos/ui/card";
import { Checkbox } from "@kalos/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";
import { cn } from "@kalos/ui/index";
import { Input } from "@kalos/ui/input";
import { Skeleton } from "@kalos/ui/skeleton";

interface Segment extends SelectSegmentSchema {
  verticals: string[];
  jobFunction?: string;
  jobSeniority?: string;
  accountCustomStringFieldFilters: SelectAccountCustomStringFieldFiltersForSegment[];
  accountCustomStringListFieldFilters: SelectAccountCustomStringListFieldFiltersForSegment[];
}
export default function ({
  params,
}: {
  params: { adAccount: string; campaignGroupId: string };
}) {
  return (
    <Audience
      campaignGroupId={params.campaignGroupId}
      adAccount={params.adAccount}
    />
  );
}

function Audience({
  campaignGroupId,
  adAccount,
}: {
  campaignGroupId: string;
  adAccount: string;
}) {
  const apiUtils = api.useUtils();
  const campaignSegmentsInCampaignGroup =
    api.v2.ads.adSegment.getAllAdSegmentsForAdProgram.useQuery({
      adProgramId: campaignGroupId,
    });
  const createCampaignMutation =
    api.advertising.campaign.createOneWithEmptyAudience.useMutation({
      onSuccess: async () => {
        await apiUtils.advertising.campaign.invalidate();
      },
    });
  const campaignGroup = api.v2.ads.linkedInAdProgram.getOne.useQuery({
    id: campaignGroupId,
  });

  useEffect(() => {
    if (campaignGroup.data) {
      console.log("campaignGroup.data", campaignGroup.data);
    }
    setCampaignGroupObjectiveType(campaignGroup.data?.objectiveType as any);
  }, [campaignGroup.data]);

  const [segments, setSegments] = useState<string[]>([
    ...new Set(
      campaignSegmentsInCampaignGroup.data?.map((segment) => segment.id),
    ),
  ]);

  const getLinkedInRetargetingAudiences =
    api.advertising.linkedIn.getMatchedAudiences.useQuery();

  const [campaignGroupObjectiveType, setCampaignGroupObjectiveType] = useState<
    "LEAD_GENERATION" | "BRAND_AWARENESS" | "CONVERSION" | "VIDEO_ENGAGEMENT"
  >(campaignGroup.data?.objectiveType as any);

  // debug prints
  useEffect(() => {
    console.log(
      "getLinkedInMatchedAudiences.data",
      getLinkedInRetargetingAudiences.data,
    );
  }, [getLinkedInRetargetingAudiences.data]);

  useEffect(() => {
    console.log(
      "campaignSegmentsInCampaignGroup.data",
      campaignSegmentsInCampaignGroup.data,
    );
    if (campaignSegmentsInCampaignGroup.data) {
      setSegments([
        ...new Set(
          campaignSegmentsInCampaignGroup.data.map((segment) => segment.id),
        ),
      ]);
    }
  }, [campaignSegmentsInCampaignGroup.data]);

  if (!campaignSegmentsInCampaignGroup.data) {
    return <h1>Loading.....</h1>;
  }

  const router = useRouter();
  return (
    <div className="flex w-full flex-col items-start justify-between">
      <div className="flex w-full flex-col items-start justify-start space-y-4 p-4">
        <div className="flex w-full flex-col items-start justify-start">
          <h1 className="text-xl font-medium">Audience Testing</h1>
          <h2 className="text-base ">
            Test several audiences for each segment. Each audience informed by
            your Closed-Won deals and Kalos best practices.
          </h2>
        </div>

        <div className="flex w-full flex-col items-start justify-start gap-y-4">
          {segments.map((segment) => {
            const row = campaignSegmentsInCampaignGroup.data.filter(
              (each) => each.id == segment,
            );
            const s = campaignSegmentsInCampaignGroup.data.find(
              (each) => each.id == segment,
            );
            if (row.length == 0 || !row[0]?.id) {
              return null;
            }
            const campaignGroupSegmentId = row[0]?.id;
            return (
              <Card className="w-full">
                <CardHeader>
                  <CardTitle>
                    <SegmentDetails
                      row={{ original: s as unknown as Segment }}
                    />
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex w-full flex-col items-start justify-start gap-y-6">
                  {row.map((campaign, index) => {
                    return (
                      <CampaignThing
                        campaignId={campaign.id}
                        index={index + 1}
                      />
                    );
                  })}

                  <Button
                    onClick={() => {
                      createCampaignMutation.mutate({
                        campaignGroupSegmentId: campaignGroupSegmentId!,
                        description: "Retargeting",
                      });
                    }}
                    className="mt-4 bg-blue-100 text-blue-500 hover:bg-blue-200"
                  >
                    <PlusIcon className="mr-2 h-4 w-4" />
                    Add Audience Targeting
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
      <div className="flex h-16 w-full items-center justify-between border-t bg-background p-4">
        <Button
          onClick={() =>
            router.push(
              `/advertising/adAccounts/${adAccount}/campaignGroups/${campaignGroupId}/segments`,
            )
          }
          className="border-1 border border-blue-200 bg-blue-100 text-primary hover:bg-blue-200"
        >
          <ArrowLeft width="16" className="mr-2" /> Previous
        </Button>
        <Button
          onClick={() => {
            router.push(
              `/advertising/adAccounts/${adAccount}/campaignGroups/${campaignGroupId}/abTest`,
            );
          }}
        >
          Next: Campaign Setup
          <ArrowRight width="16" className="ml-2" />
        </Button>
      </div>
    </div>
  );
}

function CampaignThing({
  campaignId,
  index,
}: {
  campaignId: string;
  index: number;
}) {
  return <AudienceTargetThing campaignId={campaignId} index={index} />;
}

function AudienceTargetThing({
  campaignId,
  index,
}: {
  campaignId: string;
  index: number;
}) {
  const [refetchInterval, setRefetchInterval] = useState<number | undefined>(
    2500,
  );

  const audienceTargetQuery = api.v2.ads.adAudience.getAllForAdSegment.useQuery(
    {
      adSegmentId: campaignId,
    },
    {
      refetchInterval: refetchInterval,
    },
  );

  const apiUtils = api.useUtils();

  const deleteAudienceTargetMutation =
    api.advertising.campaign.deleteAudienceTarget.useMutation({
      onSuccess: async () => {
        await apiUtils.advertising.campaign.invalidate();
      },
    });

  useEffect(() => {
    if (audienceTargetQuery.data?. == true) {
      setRefetchInterval(undefined);
    }
  }, [audienceTargetQuery.data]);

  function handleDelete(data: { groupId: string; audienceTargetId: string }) {
    deleteAudienceTargetMutation.mutate({
      ...data,
      campaignId: campaignId,
    });
  }
  const [facetModalOpen, setFacetModalOpen] = useState(false);

  const createAudienceTargetMutation =
    api.advertising.campaign.createAudienceTarget.useMutation({
      onSuccess: async () => {
        await apiUtils.advertising.campaign.invalidate();
      },
    });
  const groupMutation = api.advertising.campaign.createTargetGroup.useMutation({
    onSuccess: async () => {
      await apiUtils.advertising.invalidate();
    },
  });
  function handleCreateFacetSubmit(data: {
    facetUrn: string;
    facetName: string;
  }) {
    groupMutation.mutate({
      campaignId: campaignId,
      facetUrn: data.facetUrn,
      facetName: data.facetName,
    });
    setFacetModalOpen(false);
  }

  console.log("audienceTargetQuery.data", audienceTargetQuery.data);
  const [isOpen, setIsOpen] = useState(false);

  function handleSubmit(
    data:
      | {
          entityUrn: string;
          name: string;
          facetUrn: string;
          isSegment: false;
        }
      | {
          entityUrn: string;
          name: string;
          facetUrn: string;
          isSegment: true;
          segmentType: string;
        },
  ) {
    let groupId = undefined;
    for (const each of audienceTargetQuery.data?.groups ?? []) {
      if (each.facetUrn === data.facetUrn) {
        groupId = each.targetGroupId;
        break;
      }
      for (const target of each.targets) {
        if (target.facetUrn === data.facetUrn) {
          groupId = each.targetGroupId;
          break;
        }
      }
    }
    createAudienceTargetMutation.mutate({
      campaignId: campaignId,
      groupId: groupId,
      entityUrn: data.entityUrn,
      facetUrn: data.facetUrn,
      name: camelCaseToWords(data.name),
      segmentType: data.isSegment ? data.segmentType : undefined,
    });

    setIsOpen(false);
  }

  const [facetNames, setFacetNames] = useState<string[]>([]);

  useEffect(() => {
    const facetArr: string[] = [];
    if (
      audienceTargetQuery.data !== undefined &&
      audienceTargetQuery.data !== null
    ) {
      for (const group of audienceTargetQuery.data.groups) {
        if (
          group.facetName !== "" &&
          group.facetName !== null &&
          group.facetName !== undefined &&
          group.facetUrn !== "urn:li:adTargetingFacet:segments"
        ) {
          facetArr.push(camelCaseToWords(group.facetName));
        } else if (group.facetUrn == "urn:li:adTargetingFacet:segments") {
          const segmentType = Array.from(
            new Set(
              group.targets
                .filter(
                  (target) => target.isSegment && target.segmentType !== null,
                )
                .map((each) => {
                  if (each.segmentType === "BULK") {
                    return "List Upload";
                  } else if (each.segmentType === "MARKET_AUTOMATION") {
                    return "Third Party";
                  } else if (each.segmentType === "RETARGETING") {
                    return "Retargeting";
                  }
                }),
            ),
          ) as string[];
          facetArr.push(...segmentType);
        } else {
          facetArr.push(
            camelCaseToWords(group.targets[0]?.facetUrn.split(":").pop() ?? ""),
          );
        }
      }
    }
    setFacetNames(facetArr);
  }, [audienceTargetQuery.data]);

  const updateCampaignEnabledOrDisabledMutation =
    api.advertising.campaign.updateCampaignEnabledOrDisabled.useMutation({
      onSuccess: async () => {
        await apiUtils.advertising.invalidate();
      },
    });

  return (
    <>
      <div className="flex h-full w-full flex-col items-start justify-start gap-y-2">
        <div className="flex w-full items-center justify-between">
          <h1 className="text-sm">
            {audienceTargetQuery.data !== undefined &&
              audienceTargetQuery.data !== null && (
                <Checkbox
                  checked={campaignQuery.data?.enabled ?? false}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      updateCampaignEnabledOrDisabledMutation.mutate({
                        campaignId: campaignId,
                        enabled: true,
                      });
                    } else {
                      updateCampaignEnabledOrDisabledMutation.mutate({
                        campaignId: campaignId,
                        enabled: false,
                      });
                    }
                  }}
                />
              )}
            Audience Experiment {index}:{" "}
            {audienceTargetQuery.data !== undefined &&
            audienceTargetQuery.data !== null &&
            audienceTargetQuery.data.audienceMatchingSegments?.length > 0 ? (
              <>
                {audienceTargetQuery.data.audienceMatchingSegments[0]?.name ??
                  "Custom Audience Targeting"}
              </>
            ) : audienceTargetQuery.data !== undefined &&
              audienceTargetQuery.data !== null &&
              audienceTargetQuery.data.groups.length > 0 ? (
              <>
                {facetNames.map((facetName, idx) => {
                  return (
                    <>
                      {facetName}
                      {idx < facetNames.length - 1 && <b>{" AND "}</b>}
                    </>
                  );
                })}
              </>
            ) : (
              <>{audienceTargetQuery.data?.description}</>
            )}
          </h1>
          {audienceTargetQuery.data !== undefined &&
            audienceTargetQuery.data !== null &&
            audienceTargetQuery.data?.audienceMatchingSegments?.length ===
              0 && (
              <AddAudienceTargetDialog
                isOpen={isOpen}
                setIsOpen={setIsOpen}
                onSubmit={handleSubmit}
              />
            )}
        </div>
        {audienceTargetQuery.data && (
          <div className="flex w-full flex-wrap items-start justify-start gap-4">
            {audienceTargetQuery.data.groups.map((each) => {
              return (
                <div className="flex flex-col items-start justify-start gap-y-2 rounded-md bg-secondary p-2">
                  <div className="flex items-center justify-between">
                    <h1 className="text-sm font-medium">
                      {each.facetName == "" || !each.facetName
                        ? camelCaseToWords(
                            each.targets[0]?.facetUrn.split(":").pop() ?? "",
                          )
                        : camelCaseToWords(each.facetName)}
                    </h1>
                  </div>
                  <div className="flex max-h-[280px] max-w-full flex-col flex-wrap items-start justify-start gap-1 overflow-auto">
                    {each.targets.map((entity) => {
                      return (
                        <div className="flex items-center justify-start rounded-md border bg-white p-1">
                          <span className="text-xs">{entity.entityName}</span>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 p-0"
                            onClick={() => {
                              handleDelete({
                                audienceTargetId: entity.linkedInTargetId,
                                groupId: each.targetGroupId,
                              });
                            }}
                          >
                            <XIcon width={16} height={16} />
                          </Button>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
      {!campaignQuery.data?.audiencePopulated && (
        <div className="flex w-full flex-col items-start justify-start gap-y-2">
          <div className="flex flex-wrap items-start justify-start gap-2">
            {[1, 2, 3].map((each) => {
              return <Skeleton className="h-8 w-[100px] bg-gray-200" />;
            })}
          </div>
        </div>
      )}
    </>
  );
}

function camelCaseToWords(camelCase: string) {
  return camelCase
    .replace(/([a-z])([A-Z])/g, "$1 $2") // Add a space before each uppercase letter.
    .replace(/^./, (str) => str.toUpperCase()) // Capitalize the first letter of the string.
    .replace(/ ([a-z])/g, (match) => match.toUpperCase()); // Capitalize any letter after a space.
}

function AddAudienceTargetDialog({
  onSubmit,
  isOpen,
  setIsOpen,
}: {
  onSubmit: (
    data:
      | {
          entityUrn: string;
          name: string;
          facetUrn: string;
          isSegment: false;
        }
      | {
          entityUrn: string;
          name: string;
          facetUrn: string;
          isSegment: true;
          segmentType: string;
        },
  ) => void;
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
}) {
  const [selectedFacet, setSelectedFacet] = useState<
    | {
        urn: string;
        availableEntityFinders: (
          | "TYPEAHEAD"
          | "AD_TARGETING_FACET"
          | "SEGMENT_LIST_UPLOAD"
          | "SEGMENT_RETARGETING"
          | "SEGMENT_THIRD_PARTY"
        )[];
      }
    | undefined
  >(undefined);
  const [selectedEntity, setSelectedEntity] = useState<
    | {
        entityUrn: string;
        name: string;
        facetUrn: string;
        isSegment: false;
      }
    | {
        entityUrn: string;
        name: string;
        facetUrn: string;
        isSegment: true;
        segmentType: string;
      }
    | undefined
  >(undefined);
  const facetsQuery = api.advertising.linkedIn.getAdTargetingFacets.useQuery();

  useEffect(() => {
    setSelectedEntity(undefined);
  }, [selectedFacet]);

  useEffect(() => {
    setSelectedFacet(undefined);
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className="h-6 bg-blue-100 text-xs text-blue-500">
          <PlusIcon width={16} height={16} />
          Add Criteria
        </Button>
      </DialogTrigger>
      {isOpen && (
        <DialogContent className="">
          <DialogHeader>
            <DialogTitle>Add Audience Target</DialogTitle>
          </DialogHeader>
          <div className="flex h-full w-full items-start justify-start">
            {!selectedFacet && (
              <div className="flex h-[400px] w-full flex-col items-start justify-start overflow-y-auto rounded-md border">
                {facetsQuery.data?.map((each) => {
                  return (
                    <Button
                      variant="ghost"
                      className={cn(
                        "w-full border-b p-2  text-xs hover:bg-secondary",
                      )}
                      onClick={() => {
                        setSelectedEntity(undefined);
                        setSelectedFacet({
                          urn: each.adTargetingFacetUrn,
                          availableEntityFinders: each.availableEntityFinders,
                        });
                      }}
                    >
                      <h1 className="w-full text-start">
                        {camelCaseToWords(each.facetName)}
                      </h1>
                    </Button>
                  );
                })}
                <Button
                  variant="ghost"
                  className={cn(
                    "w-full border-b p-2  text-xs hover:bg-secondary",
                  )}
                  onClick={() => {
                    setSelectedEntity(undefined);
                    setSelectedFacet({
                      urn: "urn:li:adTargetingFacet:segments",
                      availableEntityFinders: ["SEGMENT_LIST_UPLOAD"],
                    });
                  }}
                >
                  <h1 className="w-full text-start">List Upload</h1>
                </Button>
                <Button
                  variant="ghost"
                  className={cn(
                    "w-full border-b p-2  text-xs hover:bg-secondary",
                  )}
                  onClick={() => {
                    setSelectedEntity(undefined);
                    setSelectedFacet({
                      urn: "urn:li:adTargetingFacet:segments",
                      availableEntityFinders: ["SEGMENT_THIRD_PARTY"],
                    });
                  }}
                >
                  <h1 className="w-full text-start">Third Party</h1>
                </Button>
                <Button
                  variant="ghost"
                  className={cn(
                    "w-full border-b p-2  text-xs hover:bg-secondary",
                  )}
                  onClick={() => {
                    setSelectedEntity(undefined);
                    setSelectedFacet({
                      urn: "urn:li:adTargetingFacet:segments",
                      availableEntityFinders: ["SEGMENT_RETARGETING"],
                    });
                  }}
                >
                  <h1 className="w-full text-start">Retargeting</h1>
                </Button>
              </div>
            )}
            {selectedFacet !== undefined &&
              selectedFacet.availableEntityFinders.includes("TYPEAHEAD") && (
                <TypeaheadThing
                  selectedEntity={selectedEntity}
                  setSelectedEntity={setSelectedEntity}
                  facetUrn={selectedFacet.urn}
                  setSelectedFacet={setSelectedFacet}
                />
              )}
            {selectedFacet !== undefined &&
              selectedFacet.availableEntityFinders.includes(
                "AD_TARGETING_FACET",
              ) &&
              !selectedFacet.availableEntityFinders.includes("TYPEAHEAD") && (
                <AdTargetingFacetThing
                  selectedEntity={selectedEntity}
                  setSelectedEntity={setSelectedEntity}
                  facetUrn={selectedFacet.urn}
                />
              )}
            {selectedFacet !== undefined &&
              selectedFacet.availableEntityFinders.includes(
                "SEGMENT_LIST_UPLOAD",
              ) && (
                <AdTargetingSegment
                  segmentType="BULK"
                  selectedEntity={selectedEntity}
                  setSelectedEntity={setSelectedEntity}
                />
              )}
            {selectedFacet !== undefined &&
              selectedFacet.availableEntityFinders.includes(
                "SEGMENT_RETARGETING",
              ) && (
                <AdTargetingSegment
                  segmentType="RETARGETING"
                  selectedEntity={selectedEntity}
                  setSelectedEntity={setSelectedEntity}
                />
              )}
            {selectedFacet !== undefined &&
              selectedFacet.availableEntityFinders.includes(
                "SEGMENT_THIRD_PARTY",
              ) && (
                <AdTargetingSegment
                  segmentType="MARKET_AUTOMATION"
                  selectedEntity={selectedEntity}
                  setSelectedEntity={setSelectedEntity}
                />
              )}
          </div>
          {selectedFacet !== undefined && (
            <Button
              disabled={selectedEntity === undefined}
              onClick={() => {
                if (selectedEntity !== undefined) {
                  onSubmit(selectedEntity);
                }
              }}
            >
              Submit
            </Button>
          )}
        </DialogContent>
      )}
    </Dialog>
  );
}

function TypeaheadThing({
  selectedEntity,
  setSelectedEntity,
  facetUrn,
  setSelectedFacet,
}: {
  selectedEntity:
    | {
        entityUrn: string;
        name: string;
        facetUrn: string;
        isSegment: false;
      }
    | {
        entityUrn: string;
        name: string;
        facetUrn: string;
        isSegment: true;
        segmentType: string;
      }
    | undefined;
  setSelectedEntity: Dispatch<
    SetStateAction<
      | {
          entityUrn: string;
          name: string;
          facetUrn: string;
          isSegment: false;
        }
      | {
          entityUrn: string;
          name: string;
          facetUrn: string;
          isSegment: true;
          segmentType: string;
        }
      | undefined
    >
  >;
  facetUrn: string;
  setSelectedFacet: Dispatch<
    SetStateAction<
      | {
          urn: string;
          availableEntityFinders: (
            | "TYPEAHEAD"
            | "AD_TARGETING_FACET"
            | "SEGMENT_LIST_UPLOAD"
            | "SEGMENT_RETARGETING"
            | "SEGMENT_THIRD_PARTY"
          )[];
        }
      | undefined
    >
  >;
}) {
  const [query, setSearch] = useState("");
  const searchQuery = api.advertising.linkedIn.searchTypeahead.useQuery({
    facetUrn: facetUrn,
    query: query,
  });

  useEffect(() => {
    setSearch("");
  }, [facetUrn]);

  return (
    <div className="flex w-full flex-col items-start justify-start space-y-2 rounded-md ">
      <Button
        variant="ghost"
        size="icon"
        onClick={() => setSelectedFacet(undefined)}
      >
        <ArrowLeft width={16} height={16} />
      </Button>
      <Input
        placeholder="Search..."
        onChange={(e) => {
          if (e.target.value != "") {
            setSearch(e.target.value);
          }
        }}
        value={query}
      />
      <div className="flex w-full flex-col items-start justify-start pt-2">
        {query.length > 0 &&
          searchQuery.data?.map((each) => {
            return (
              <Button
                variant="ghost"
                className={cn(
                  selectedEntity?.facetUrn == each.urn
                    ? "bg-blue-100"
                    : "bg-white",
                  "w-full border-b p-2  text-xs hover:bg-secondary",
                )}
                onClick={() => {
                  setSelectedEntity({
                    entityUrn: each.urn,
                    name: each.name,
                    facetUrn: facetUrn,
                    isSegment: false,
                  });
                }}
              >
                <h1 className="w-full">{camelCaseToWords(each.name)}</h1>
              </Button>
            );
          })}
        {(query.length == 0 || searchQuery.data?.length == 0) && (
          <h1>No results...</h1>
        )}
      </div>
    </div>
  );
}

function AdTargetingFacetThing({
  facetUrn,
  selectedEntity,
  setSelectedEntity,
}: {
  facetUrn: string;
  selectedEntity:
    | {
        entityUrn: string;
        name: string;
        facetUrn: string;
        isSegment: false;
      }
    | {
        entityUrn: string;
        name: string;
        facetUrn: string;
        isSegment: true;
        segmentType: string;
      }
    | undefined;
  setSelectedEntity: Dispatch<
    SetStateAction<
      | {
          entityUrn: string;
          name: string;
          facetUrn: string;
          isSegment: false;
        }
      | {
          entityUrn: string;
          name: string;
          facetUrn: string;
          isSegment: true;
          segmentType: string;
        }
      | undefined
    >
  >;
}) {
  const searchQuery = api.advertising.linkedIn.searchEntity.useQuery({
    urn: facetUrn,
  });

  return (
    <div className="flex w-full flex-col items-start justify-start rounded-md border">
      <div className="flex w-full flex-col items-start justify-start">
        {searchQuery.data?.map((each) => {
          return (
            <Button
              variant="ghost"
              className={cn(
                selectedEntity?.entityUrn == each.urn
                  ? "bg-blue-100"
                  : "bg-white",
                "w-full border-b p-2  text-xs hover:bg-secondary",
              )}
              onClick={() => {
                setSelectedEntity({
                  entityUrn: each.urn,
                  name: each.name,
                  facetUrn: facetUrn,
                  isSegment: false,
                });
              }}
            >
              <h1 className="w-full">{camelCaseToWords(each.name)}</h1>
            </Button>
          );
        })}
      </div>
    </div>
  );
}

function AdTargetingSegment({
  segmentType,
  selectedEntity,
  setSelectedEntity,
}: {
  segmentType: "BULK" | "RETARGETING" | "MARKET_AUTOMATION";
  selectedEntity:
    | {
        entityUrn: string;
        name: string;
        facetUrn: string;
        isSegment: false;
      }
    | {
        entityUrn: string;
        name: string;
        facetUrn: string;
        isSegment: true;
        segmentType: string;
      }
    | undefined;
  setSelectedEntity: Dispatch<
    SetStateAction<
      | {
          entityUrn: string;
          name: string;
          facetUrn: string;
          isSegment: false;
        }
      | {
          entityUrn: string;
          name: string;
          facetUrn: string;
          isSegment: true;
          segmentType: string;
        }
      | undefined
    >
  >;
}) {
  const searchQuery = api.advertising.linkedIn.getAdSegments.useQuery({
    types: segmentType,
  });

  return (
    <div className="flex w-full flex-col items-start justify-start rounded-md border">
      <div className="flex h-[400px] w-full flex-col items-start justify-start overflow-y-auto overflow-x-hidden">
        {searchQuery.data?.elements
          .filter((each) => each.status == "READY")
          .map((each) => {
            return (
              <Button
                variant="ghost"
                className={cn(
                  selectedEntity?.entityUrn == `urn:li:adSegment:${each.id}`
                    ? "bg-blue-100"
                    : "bg-white",
                  "w-full border-b p-2  text-xs hover:bg-secondary",
                )}
                onClick={() => {
                  setSelectedEntity({
                    entityUrn: `urn:li:adSegment:${each.id}`,
                    name: each.name,
                    facetUrn: "urn:li:adTargetingFacet:segments",
                    isSegment: true,
                    segmentType: segmentType,
                  });
                }}
              >
                <h1 className="w-full">{camelCaseToWords(each.name)}</h1>
              </Button>
            );
          })}
      </div>
    </div>
  );
}

function SegmentDetails({ row }: { row: { original: Segment } }) {
  console.log(row.original);
  if (!row.original.name) {
    const nameArray: string[] = [];

    if (row.original.jobFunction) {
      nameArray.push(row.original.jobFunction);
    }
    if (row.original.jobSeniority) {
      nameArray.push(row.original.jobSeniority);
    }

    if (row.original.verticals) {
      nameArray.push(...row.original.verticals);
    }

    if (row.original.subVertical) {
      nameArray.push(row.original.subVertical);
    }

    if (
      row.original.annualContractValueLowBound &&
      row.original.annualContactValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualContactValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }
    if (
      row.original.annualContractValueLowBound &&
      !row.original.annualContactValueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualContractValueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ ACV`,
      );
    }
    if (
      !row.original.annualContractValueLowBound &&
      row.original.annualContactValueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualContactValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }

    if (
      row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${row.original.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }
    if (
      row.original.annualRevenueLowBound &&
      !row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${row.original.annualRevenueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ prospect revenue`,
      );
    }
    if (
      !row.original.annualRevenueLowBound &&
      row.original.annualRevenueHighBound
    ) {
      nameArray.push(
        `<$${row.original.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }

    if (
      row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-${row.original.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }
    if (
      row.original.numberOfEmployeesLowBound &&
      !row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${row.original.numberOfEmployeesLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ employees`,
      );
    }
    if (
      !row.original.numberOfEmployeesLowBound &&
      row.original.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `<${row.original.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }

    let acvQueryParam = "";
    if (
      row.original.annualContactValueHighBound == 20000 &&
      row.original.annualContractValueLowBound == undefined
    ) {
      acvQueryParam = "1";
    } else if (
      row.original.annualContactValueHighBound == 50000 &&
      row.original.annualContractValueLowBound == 20000
    ) {
      acvQueryParam = "2";
    } else if (
      row.original.annualContactValueHighBound == 100000 &&
      row.original.annualContractValueLowBound == 50000
    ) {
      acvQueryParam = "3";
    } else if (
      row.original.annualContactValueHighBound == undefined &&
      row.original.annualContractValueLowBound == 100000
    ) {
      acvQueryParam = "4";
    }

    let empoyeesQueryParam = "";
    if (
      row.original.numberOfEmployeesHighBound == 100 &&
      row.original.numberOfEmployeesLowBound == undefined
    ) {
      empoyeesQueryParam = "1";
    } else if (
      row.original.numberOfEmployeesHighBound == 500 &&
      row.original.numberOfEmployeesLowBound == 100
    ) {
      empoyeesQueryParam = "2";
    } else if (
      row.original.numberOfEmployeesHighBound == undefined &&
      row.original.numberOfEmployeesLowBound == 500
    ) {
      empoyeesQueryParam = "3";
    }

    let prospectRevenue = "";
    if (
      row.original.annualRevenueLowBound == 1000000 &&
      row.original.annualRevenueHighBound == 10000000
    ) {
      prospectRevenue = "1";
    } else if (
      row.original.annualRevenueLowBound == 10000000 &&
      row.original.annualRevenueHighBound == 200000000
    ) {
      prospectRevenue = "2";
    } else if (
      row.original.annualRevenueLowBound == 200000000 &&
      row.original.annualRevenueHighBound == undefined
    ) {
      prospectRevenue = "3";
    }

    if (nameArray.length == 0) {
      return (
        <div className="space-y-1">
          <span className="w-[80px]">All</span>
        </div>
      );
    }
    return (
      <div className="w-full space-y-1 text-wrap py-1">
        <span className="">{nameArray.join(" • ")}</span>
      </div>
    );
  }
  return (
    <div className="space-y-1">
      <span className="w-[80px]">{row.original.name}</span>
    </div>
  );
}
