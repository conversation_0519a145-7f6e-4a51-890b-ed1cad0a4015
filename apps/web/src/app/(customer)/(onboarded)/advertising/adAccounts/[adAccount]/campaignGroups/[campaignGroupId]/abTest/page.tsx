"use client";

import { Suspense } from "react";
import { api } from "@/trpc/client";

import { Skeleton } from "@kalos/ui/skeleton";

import AbTestClient from "./AbTestClient";

export default function AbTestPage({
  params,
}: {
  params: {
    adAccount: string;
    campaignGroupId: string;
  };
}) {
  const adProgram = api.v2.ads.linkedInAdProgram.getOne.useQuery({
    id: params.campaignGroupId,
  });
  return (
    <div className="flex h-full w-full flex-col items-start justify-between">
      <div className="flex w-full flex-1 flex-col items-start justify-start space-y-4 overflow-y-scroll px-4 py-6 pb-24">
        {adProgram.data && (
          <div className="flex w-full flex-col items-start justify-start">
            <h1 className="text-xl font-medium">Value Proposition Testing</h1>
            <h2 className="text-base">
              {adProgram.data.adFormat.type == "SPONSORED_IMAIL"
                ? `Select the first value proposition to use in our campaign. We’ll come back and test many more later too.`
                : `Select the value props to test. View ad copy for each on the next screen.`}
            </h2>
          </div>
        )}
        {/* Note: Mar 3, 2025 merge with staging, changes to this component moved to AbTestClient.tsx */}

        <Suspense fallback={<Skeleton />}>
          <AbTestClient params={params} />
        </Suspense>
      </div>
    </div>
  );
}
