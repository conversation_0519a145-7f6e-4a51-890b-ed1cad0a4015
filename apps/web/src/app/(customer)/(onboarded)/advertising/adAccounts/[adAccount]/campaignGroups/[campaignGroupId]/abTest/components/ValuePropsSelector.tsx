"use client";

import { useEffect, useState } from "react";
import { api } from "@/trpc/client";
import { PlusIcon } from "lucide-react";

import { Button } from "@kalos/ui/button";
import { Checkbox } from "@kalos/ui/checkbox";
import { Input } from "@kalos/ui/input";

import type { SegmentData, SelectedAd } from "../types";

interface ValuePropsSelectorProps {
  segment: SegmentData;
  selectedAds: SelectedAd[];
  setSelectedAds: React.Dispatch<React.SetStateAction<SelectedAd[]>>;
  disabled: boolean;
  customTopics: string[];
  isSubmitting: boolean;
}

export function ValuePropsSelector({
  segment,
  selectedAds,
  setSelectedAds,
  disabled,
  customTopics,
  isSubmitting,
}: ValuePropsSelectorProps): JSX.Element {
  const valuePropsQuery =
    api.v2.core.segment.getSegmentValuePropForSegment.useQuery({
      segmentId: segment.segmentId,
    });

  const [userAddedTopics, setUserAddedTopics] = useState<string[]>([]);

  useEffect(() => {
    if (isSubmitting) {
      setUserAddedTopics([]);
    }
  }, [isSubmitting]);

  const handleCheckboxChange = (
    checked: boolean | "indeterminate",
    topicName: string,
  ) => {
    if (checked === true) {
      setSelectedAds([
        ...selectedAds,
        {
          campaignGroupSegmentId: segment.id,
          adTopic: topicName,
        },
      ]);
    } else if (checked === false) {
      setSelectedAds(
        selectedAds.filter(
          (selectedAd) =>
            selectedAd.adTopic !== topicName ||
            selectedAd.campaignGroupSegmentId !== segment.id,
        ),
      );
    }
  };

  const isTopicSelected = (topicName: string): boolean => {
    return selectedAds.some(
      (ad) =>
        ad.adTopic === topicName && ad.campaignGroupSegmentId === segment.id,
    );
  };

  return (
    <div className="w-full">
      <div className="flex flex-col items-start justify-start space-y-2">
        {/* Standard value props */}
        {valuePropsQuery.data?.map((valueProp) => (
          <div
            key={valueProp.name}
            className="flex items-center justify-start space-x-2"
          >
            <Checkbox
              disabled={disabled}
              checked={isTopicSelected(valueProp.name)}
              onCheckedChange={(checked) =>
                handleCheckboxChange(checked, valueProp.name)
              }
            />
            <p className="font-md text-sm">{valueProp.name}</p>
          </div>
        ))}

        {/* Custom topics from API */}
        {valuePropsQuery.data &&
          customTopics
            .filter(
              (topic) => !valuePropsQuery.data.find((v) => v.name === topic),
            )
            .map((topic) => (
              <div
                key={topic}
                className="flex items-center justify-start space-x-2"
              >
                <Checkbox
                  disabled={disabled}
                  checked={isTopicSelected(topic)}
                  onCheckedChange={(checked) =>
                    handleCheckboxChange(checked, topic)
                  }
                />
                <p>{topic}</p>
              </div>
            ))}

        {/* User added topics */}
        {userAddedTopics.map((topic, index) => (
          <div
            key={index}
            className="flex items-center justify-start space-x-2"
          >
            <Checkbox
              checked={isTopicSelected(topic)}
              disabled={topic === "" || disabled}
              onCheckedChange={(checked) =>
                handleCheckboxChange(checked, topic)
              }
            />
            <Input
              onChange={(event) => {
                const newTopics = [...userAddedTopics];
                newTopics[index] = event.target.value;
                setUserAddedTopics(newTopics);
              }}
              className="h-7 w-full"
              value={topic}
            />
          </div>
        ))}

        {/* Add more button */}
        <Button
          variant="ghost"
          disabled={disabled}
          className="p-0 text-xs text-primary hover:bg-transparent hover:text-blue-300"
          onClick={() => {
            setUserAddedTopics([...userAddedTopics, ""]);
          }}
        >
          <PlusIcon className="m-0 mr-2 p-0" height="16" width="16" /> Add more
        </Button>
      </div>
    </div>
  );
}
