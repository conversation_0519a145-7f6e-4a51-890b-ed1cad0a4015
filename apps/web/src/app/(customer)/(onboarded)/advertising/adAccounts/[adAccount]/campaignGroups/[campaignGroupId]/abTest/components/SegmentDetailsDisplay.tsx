"use client";

import { snakeCaseToWords } from "@/app/utils/snakeCaseToWords";
import { api } from "@/trpc/client";

import type { SegmentDetails } from "../types";

interface SegmentDetailsDisplayProps {
  segmentId: string;
}

export function SegmentDetailsDisplay({
  segmentId,
}: SegmentDetailsDisplayProps): JSX.Element {
  const segmentsQuery =
    api.v2.core.segment.getSegmentsForOrganization.useQuery();

  const segmentData = segmentsQuery.data?.find((s) => s.id === segmentId) ?? {
    id: segmentId,
    name: "",
    verticals: [],
    annualRevenueLowBound: null,
    annualRevenueHighBound: null,
    numberOfEmployeesLowBound: null,
    numberOfEmployeesHighBound: null,
    annualContractValueLowBound: null,
    annualContractValueHighBound: null,
    jobFunction: null,
    jobSeniority: null,
  };

  return <SegmentDetailsFormatter segment={segmentData} />;
}

function SegmentDetailsFormatter({
  segment,
}: {
  segment: SegmentDetails;
}): JSX.Element {
  if (!segment.name) {
    const nameArray: string[] = [];

    if (segment.jobFunction) {
      nameArray.push(snakeCaseToWords(segment.jobFunction));
    }

    if (segment.jobSeniority) {
      nameArray.push(snakeCaseToWords(segment.jobSeniority));
    }

    if (segment.verticals && segment.verticals.length > 0) {
      nameArray.push(segment.verticals.join(", "));
    }

    if (
      segment.annualContractValueLowBound &&
      segment.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${segment.annualContractValueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${segment.annualContractValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    } else if (
      segment.annualContractValueLowBound &&
      !segment.annualContractValueHighBound
    ) {
      nameArray.push(
        `$${segment.annualContractValueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ ACV`,
      );
    } else if (
      !segment.annualContractValueLowBound &&
      segment.annualContractValueHighBound
    ) {
      nameArray.push(
        `<$${segment.annualContractValueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} ACV`,
      );
    }

    if (segment.annualRevenueLowBound && segment.annualRevenueHighBound) {
      nameArray.push(
        `$${segment.annualRevenueLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-$${segment.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    } else if (
      segment.annualRevenueLowBound &&
      !segment.annualRevenueHighBound
    ) {
      nameArray.push(
        `$${segment.annualRevenueLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ prospect revenue`,
      );
    } else if (
      !segment.annualRevenueLowBound &&
      segment.annualRevenueHighBound
    ) {
      nameArray.push(
        `<$${segment.annualRevenueHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} prospect revenue`,
      );
    }

    if (
      segment.numberOfEmployeesLowBound &&
      segment.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${segment.numberOfEmployeesLowBound
          .toString()
          .replace(
            /\B(?=(\d{3})+(?!\d))/g,
            ",",
          )}-${segment.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    } else if (
      segment.numberOfEmployeesLowBound &&
      !segment.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `${segment.numberOfEmployeesLowBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}+ employees`,
      );
    } else if (
      !segment.numberOfEmployeesLowBound &&
      segment.numberOfEmployeesHighBound
    ) {
      nameArray.push(
        `<${segment.numberOfEmployeesHighBound
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")} employees`,
      );
    }

    if (nameArray.length === 0) {
      return <div className="text-base font-medium">All</div>;
    }

    return <div className="text-base font-medium">{nameArray.join(" • ")}</div>;
  }

  return <div className="text-base font-medium">{segment.name}</div>;
}
