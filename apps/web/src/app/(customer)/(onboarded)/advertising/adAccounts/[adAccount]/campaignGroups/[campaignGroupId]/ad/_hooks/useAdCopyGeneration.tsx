import { useEffect, useState } from "react";
import { api } from "@/trpc/client";
import { getBaseUrl } from "@/trpc/provider";

/**
 * Type definition for the ad copy state
 */
type AdCopyState = {
  title: string;
  body: string;
  done: boolean;
  leadGenForm: string | null;
};

/**
 * Custom hook for managing ad copy generation, editing, and saving
 *
 * @param valuePropId - The ID of the value proposition
 * @param adSegmentId - The ID of the ad segment
 * @param adAccountId - The ID of the ad account
 * @returns An object containing state and functions for managing ad copy
 */
export function useAdCopyGeneration(
  valuePropId: string,
  adSegmentId: string,
  adAccountId: string,
) {
  const organizationUser = api.v2.core.user.getUser.useQuery();
  const [generating, setGenerating] = useState(true);
  const [adCopy, setAdCopy] = useState<AdCopyState>({
    title: "",
    body: "",
    done: false,
    leadGenForm: null,
  });
  const [baseCopyRefetchInterval, setBaseCopyRefetchInterval] = useState<
    number | undefined
  >(undefined);
  const [readyToGetAdCopy, setReadyToGetAdCopy] = useState(false);
  const [getCopyRan, setGetCopyRan] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [feedbackMode, setFeedbackMode] = useState<"body" | "title">("body");

  const baseCopyQuery =
    api.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.useQuery(
      {
        adSegmentId: adSegmentId,
      },
      {
        refetchInterval: baseCopyRefetchInterval,
      },
    );

  const apiUtils = api.useUtils();

  const adProgramQuery = api.v2.ads.linkedInAdProgram.getOne.useQuery({
    id: adSegmentId.split(":")[0] ?? "",
  });

  /**
   * Mutation for updating the lead generation form URN
   */
  const updateLeadGenFormUrn =
    api.v2.ads.socialPostBaseCopy.updateLeadGenFormUrn.useMutation({
      onMutate: async ({ linkedInAdSegmentValuePropId, leadGenFormUrn }) => {
        await apiUtils.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.cancel(
          { adSegmentId },
        );

        const previousData =
          apiUtils.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.getData(
            { adSegmentId },
          );

        return { previousData };
      },

      onError: (_err, _newData, context) => {
        if (context?.previousData) {
          apiUtils.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.setData(
            { adSegmentId },
            context.previousData,
          );
        }

        setAdCopy((prev) => ({
          ...prev,
          leadGenForm: null,
        }));
      },

      onSettled: () => {
        apiUtils.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.invalidate(
          { adSegmentId },
        );
      },
    });

  /**
   * Mutation for updating the ad copy (title and body)
   */
  const updateAdCopy =
    api.v2.ads.socialPostBaseCopy.updateSocialPostCopy.useMutation({
      onMutate: async ({ linkedInAdSegmentValuePropId, body, title }) => {
        await apiUtils.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.cancel(
          { adSegmentId },
        );

        const previousData =
          apiUtils.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.getData(
            { adSegmentId },
          );

        apiUtils.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.setData(
          { adSegmentId },
          (old) => {
            if (!old) return old;
            return {
              ...old,
            };
          },
        );

        return { previousData, previousAdCopy: { ...adCopy } };
      },

      onError: (_err, _newData, context) => {
        if (context?.previousData) {
          apiUtils.v2.ads.socialPostBaseCopy.getAdSegmentSocialPostBaseCopy.setData(
            { adSegmentId },
            context.previousData,
          );
        }

        if (context?.previousAdCopy) {
          setAdCopy(context.previousAdCopy);
        }

        setIsSaving(false);
      },

      onSuccess: () => {
        setIsEditing(false);
        setIsSaving(false);
      },

      onSettled: () => {},
    });

  /**
   * Fetches and processes ad copy from the streaming API
   */
  async function getAdCopy() {
    setAdCopy({
      title: "",
      body: "",
      done: false,
      leadGenForm: null,
    });
    setGenerating(true);
    try {
      const response = await fetch(`${getBaseUrl()}/stream`, {
        method: "POST",
        headers: {
          Accept: "application/json, text/plain, */*",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          valuePropId: valuePropId,
          organizationId: organizationUser.data?.organizationId,
        }),
      });

      if (!response.ok || !response.body) {
        throw response.statusText;
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      const loopRunner = true;

      while (loopRunner) {
        const { value, done } = await reader.read();
        if (done) {
          break;
        }
        const decodedChunk = decoder
          .decode(value, { stream: true })
          .split("!JSON_LINE_END!");
        for (const each of decodedChunk) {
          try {
            const json = JSON.parse(each);
            setAdCopy((prev) => ({
              title: prev.title + json.title,
              body: prev.body + json.body,
              done: prev.done + json.done,
              leadGenForm: json.leadGenFormUrn || prev.leadGenForm,
            }));
            if (json.leadGenFormUrn) {
              console.log("LEAD GEN FORM", json.leadGenFormUrn);
            }
          } catch (e) {}
        }
      }
    } catch (error) {
      console.error("Error generating ad copy:", error);
    } finally {
      setGenerating(false);
    }
  }

  /**
   * Submits feedback to refine the ad copy
   *
   * @param feedback - The feedback to apply to the current copy
   */
  async function submitFeedback(feedback: string) {
    const baseBody = feedbackMode === "body" ? adCopy.body : adCopy.title;

    if (feedbackMode === "body") {
      setAdCopy((prev) => ({ ...prev, body: "" }));
    } else {
      setAdCopy((prev) => ({ ...prev, title: "" }));
    }

    try {
      const response = await fetch(`${getBaseUrl()}/stream-refined`, {
        method: "POST",
        headers: {
          Accept: "application/json, text/plain, */*",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          baseCopy: baseBody,
          feedback: feedback,
          config: {
            type: "socialPost",
            field: feedbackMode,
          },
        }),
      });

      if (!response.ok || !response.body) {
        throw response.statusText;
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      const loopRunner = true;

      while (loopRunner) {
        const { value, done } = await reader.read();
        if (done) {
          break;
        }
        const decodedChunk = decoder.decode(value, { stream: true });
        for (const each of decodedChunk) {
          try {
            if (feedbackMode === "body") {
              setAdCopy((prev) => ({
                ...prev,
                body: prev.body + each,
              }));
            } else {
              setAdCopy((prev) => ({
                ...prev,
                title: prev.title + each,
              }));
            }
          } catch (e) {}
        }
      }
    } catch (error) {
      console.error("Error submitting feedback:", error);
    }
  }

  /**
   * Effect to set up ad copy generation when data is available
   */
  useEffect(() => {
    if (baseCopyQuery.data && organizationUser.data?.organizationId) {
      setBaseCopyRefetchInterval(undefined);
      setReadyToGetAdCopy(true);
    }
  }, [baseCopyQuery.data, organizationUser.data]);

  /**
   * Effect to trigger ad copy generation when ready
   */
  useEffect(() => {
    if (readyToGetAdCopy && !getCopyRan) {
      getAdCopy();
      setGetCopyRan(true);
    }
  }, [readyToGetAdCopy, getCopyRan]);

  /**
   * Saves the current ad copy
   */
  const saveAdCopy = () => {
    setIsSaving(true);
    updateAdCopy.mutate({
      linkedInAdSegmentValuePropId: valuePropId,
      copyType: "standard",
      body: adCopy.body,
      title: adCopy.title,
      callToActionType: "Standard",
    });
  };

  /**
   * Cancels editing mode without saving changes
   */
  const cancelEditing = () => {
    setIsEditing(false);
  };

  /**
   * Handles saving a lead generation form
   *
   * @param leadGenFormUrn - The URN of the lead generation form
   */
  function handleSaveLeadGenForm(leadGenFormUrn: string) {
    setAdCopy((prev) => ({
      ...prev,
      leadGenForm: leadGenFormUrn,
    }));

    updateLeadGenFormUrn.mutate(
      {
        linkedInAdSegmentValuePropId: valuePropId,
        leadGenFormUrn: leadGenFormUrn,
      },
      {
        onSuccess: () => {},
        onSettled: () => {},
      },
    );
  }

  return {
    adCopy,
    setAdCopy,
    generating,
    isEditing,
    setIsEditing,
    isSaving,
    feedbackMode,
    setFeedbackMode,
    getAdCopy,
    submitFeedback,
    saveAdCopy,
    cancelEditing,
    adProgramQuery,
    handleSaveLeadGenForm,
  };
}
