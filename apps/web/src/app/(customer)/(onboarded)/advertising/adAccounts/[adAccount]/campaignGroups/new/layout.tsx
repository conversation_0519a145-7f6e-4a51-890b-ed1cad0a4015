"use client";

import { useEffect, useState } from "react";
import { CampaignGroupContextProvider } from "@/features/advertising/context";
import { api } from "@/trpc/client";

export default function CampaignGroupLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: { adAccount: string };
}>) {
  // Prefetch all required data in parallel
  const apiUtils = api.useUtils();

  // Prefetch ad accounts
  const adAccountQuery = api.v2.ads.adAccounts.getForOrganization.useQuery(
    undefined,
    {
      // Enable suspense to prevent layout shift
      suspense: false,
      // Increase stale time to reduce refetches
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  );

  // Prefetch lead gen forms in parallel - this will be needed later in the flow
  useEffect(() => {
    if (params.adAccount) {
      // Prefetch lead gen forms data
      apiUtils.v2.ads.linkedInApi.leadGenForms.getForAccount.prefetch({
        adAccountId: params.adAccount,
      });
    }
  }, [params.adAccount, apiUtils]);

  const [adAccount, setAdAccount] = useState<string | undefined>(undefined);

  useEffect(() => {
    // Use params.adAccount if available, otherwise fall back to the first account from query
    if (params.adAccount) {
      setAdAccount(params.adAccount);
    } else if (adAccountQuery.data?.[0]?.id) {
      setAdAccount(adAccountQuery.data[0].id);
    }
  }, [params.adAccount, adAccountQuery.data]);

  // Show optimized loading state
  if (adAccountQuery.isLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="space-y-4">
          <div className="h-8 w-48 animate-pulse rounded-md bg-gray-200"></div>
          <div className="h-64 w-96 animate-pulse rounded-md bg-gray-100"></div>
        </div>
      </div>
    );
  }

  return (
    <>
      {adAccount && (
        <CampaignGroupContextProvider>{children}</CampaignGroupContextProvider>
      )}
      {!adAccount && (
        <div className="flex h-full w-full items-center justify-center">
          <div>No ad account available. Please create an ad account first.</div>
        </div>
      )}
    </>
  );
}
