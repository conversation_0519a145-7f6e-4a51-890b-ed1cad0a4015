import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import {
  ArrowR<PERSON>,
  BarChart2,
  Bell,
  CheckCircle,
  Link as LinkIcon,
} from "lucide-react";

import { Button } from "@kalos/ui/button";
import { Card, CardContent } from "@kalos/ui/card";
import { Separator } from "@kalos/ui/seperator";

interface SuccessScreenProps {
  adAccount: string;
  campaignGroupId: string;
  campaignName?: string;
  startDate?: string;
  endDate?: string;
}

export function SuccessScreen({
  adAccount,
  campaignGroupId,
  campaignName = "LinkedIn Ad Program",
  startDate,
  endDate,
}: SuccessScreenProps) {
  const router = useRouter();
  const [animateIn, setAnimateIn] = useState(false);

  // Trigger animation after component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimateIn(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  const formattedStartDate = startDate
    ? format(new Date(startDate), "MMMM do, yyyy")
    : "";

  return (
    <>
      <div
        className={`flex h-full w-full flex-col items-center justify-start overflow-y-auto px-4 py-6 pb-24 transition-opacity duration-500 ${animateIn ? "opacity-100" : "opacity-0"}`}
      >
        {/* Success Header */}
        <div className="flex w-full flex-col items-center text-center">
          <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
            <CheckCircle size={32} className="text-green-600" />
          </div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">
            All Set!
          </h1>
          <p className="mt-3 max-w-xl text-lg text-gray-600">
            Blue is setting up your campaign now, and it will go live as soon as
            Linkedin approves.
          </p>
          <p className="mt-2 text-lg text-gray-600">
            The campaign will run starting on{" "}
            <strong>{formattedStartDate}</strong>.
          </p>
        </div>

        {/* Cards Section */}
        <div className="mt-12 w-full max-w-3xl space-y-6">
          {/* What's Next Card */}
          <Card className="border border-gray-200 shadow-sm">
            <CardContent className="p-6">
              <h2 className="mb-4 text-xl font-semibold text-gray-800">
                What happens next
              </h2>
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-blue-100">
                    <BarChart2 size={20} className="text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">
                      Ad optimization begins
                    </h3>
                    <p className="mt-1 text-gray-600">
                      Blue will start testing ad variations to identify the best
                      performers.
                    </p>
                  </div>
                </div>

                <Separator className="h-px bg-gray-200" />

                <div className="flex items-start gap-4">
                  <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-blue-100">
                    <Bell size={20} className="text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">
                      You'll get notified when new ads are ready
                    </h3>
                    <p className="mt-1 text-gray-600">
                      You'll be notified after all experiments are completed -
                      and based on our learnings - get a new batch of
                      recommended experiments to continually drive better
                      performance.
                    </p>
                  </div>
                </div>

                <Separator className="h-px bg-gray-200" />

                <div className="flex items-start gap-4">
                  <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-blue-100">
                    <LinkIcon size={20} className="text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">
                      You can review results in Linkedin
                    </h3>
                    <p className="mt-1 text-gray-600">
                      See conversions and more directly in Linkedin.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Action buttons */}
      <div className="sticky bottom-0 left-0 right-0 z-10 flex h-16 w-full items-center justify-between border-t bg-background px-4 py-3 shadow-sm">
        <Button
          variant="outline"
          onClick={() => router.push(`/`)}
          className="border-gray-300 text-gray-700 hover:bg-gray-100"
        >
          Exit
        </Button>
      </div>
    </>
  );
}
