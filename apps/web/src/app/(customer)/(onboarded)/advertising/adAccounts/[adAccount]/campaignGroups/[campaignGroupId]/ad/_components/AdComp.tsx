import React, { useCallback } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";
import { ArrowLeft, ArrowRight } from "lucide-react";

import { Button } from "@kalos/ui/button";

import { SegmentComponent } from "./SegmentComponent";

interface AdCompProps {
  params: {
    adAccount: string;
    campaignGroupId: string;
  };
}

export function AdComp({ params }: AdCompProps) {
  // Use a stable reference for the query
  const campaignsQuery =
    api.v2.ads.adSegment.getAllAdSegmentsForAdProgram.useQuery(
      {
        adProgramId: params.campaignGroupId,
      },
      {
        refetchOnWindowFocus: false,
        staleTime: 60 * 1000,
      },
    );

  const router = useRouter();

  // Use callbacks for event handlers to avoid recreating functions
  const handlePrevious = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      router.push(
        `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/abTest`,
      );
    },
    [router, params.adAccount, params.campaignGroupId],
  );

  const handleNext = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      router.push(
        `/advertising/adAccounts/${params.adAccount}/campaignGroups/${params.campaignGroupId}/assets`,
      );
    },
    [router, params.adAccount, params.campaignGroupId],
  );

  return (
    <div className="w-full flex-col items-start justify-between">
      <div className="flex h-[calc(100vh-64px)] w-full flex-col items-start justify-start space-y-4 overflow-auto px-4 pb-20 pt-4">
        {campaignsQuery.data?.map((segment, index) => (
          <SegmentComponent
            key={segment.id}
            segment={segment}
            campaignGroupId={params.campaignGroupId}
            adAccountId={params.adAccount}
            index={index}
          />
        ))}
      </div>
      <div className="sticky bottom-0 left-0 right-0 z-10 flex h-16 w-full items-center justify-between border-t bg-background p-4">
        <Button
          onClick={handlePrevious}
          className="border-1 border border-blue-200 bg-blue-100 text-primary hover:bg-blue-200"
        >
          <ArrowLeft width="16" className="mr-2" /> Previous
        </Button>
        <Button onClick={handleNext}>
          Next
          <ArrowRight width="16" className="ml-2" />
        </Button>
      </div>
    </div>
  );
}
