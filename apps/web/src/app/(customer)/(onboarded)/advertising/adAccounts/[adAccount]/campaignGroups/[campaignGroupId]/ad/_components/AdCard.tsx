import React from "react";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@kalos/ui/card";

import { AdText } from "./AdText";

interface AdCardProps {
  valuePropId: string;
  valueProp: string;
  adSegmentId: string;
  adAccountId: string;
}

export function AdCard({ valuePropId, valueProp, adSegmentId, adAccountId }: AdCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{valueProp}</CardTitle>
      </CardHeader>
      <CardContent className="h-full">
        <AdText valuePropId={valuePropId} adSegmentId={adSegmentId} adAccountId={adAccountId} />
      </CardContent>
    </Card>
  );
}
