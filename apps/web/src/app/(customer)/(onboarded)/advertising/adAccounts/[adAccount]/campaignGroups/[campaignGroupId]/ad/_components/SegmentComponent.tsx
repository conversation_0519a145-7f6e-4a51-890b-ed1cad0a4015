import React, { useState } from "react";
import { api } from "@/trpc/client";

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@kalos/ui/card";

import { AdCard } from "./AdCard";
import { EditBaseCopyDialog } from "./EditBaseCopyDialog";
import { SegmentDetails } from "./SegmentDetails";

interface SegmentComponentProps {
  segment: {
    id: string;
    adProgramId: string;
    segmentId: string;
  };
  campaignGroupId: string;
  adAccountId: string;
  index?: number; // Optional index for numbering
}

export function SegmentComponent({
  segment,
  campaignGroupId,
  adAccountId,
  index,
}: SegmentComponentProps) {
  const adsQuery =
    api.v2.ads.adSegmentValueProp.getAdSegmentValuePropsForAdSegment.useQuery({
      adSegmentId: segment.id,
      status: "DRAFT",
    });

  const segmentQuery = api.v2.core.segment.getSegment.useQuery({
    segmentId: segment.segmentId,
  });

  const [isRefreshingCopy, setIsRefreshingCopy] = useState(false);

  return (
    <Card className="w-full ">
      <CardHeader className="w-full">
        <CardTitle className="flex w-full items-center justify-between">
          <div className="flex flex-col">
            {index !== undefined && (
              <div className="mb-1 text-xs text-muted-foreground">
                Segment {index + 1}
              </div>
            )}
            {segmentQuery.data && (
              <SegmentDetails row={{ original: segmentQuery.data }} />
            )}
          </div>
          <EditBaseCopyDialog
            adSegmentId={segment.id}
            setIsRefreshingCopy={setIsRefreshingCopy}
          />
        </CardTitle>
      </CardHeader>
      <CardContent
        data-content-segment-container
        className="flex w-full flex-wrap items-start justify-start gap-4 "
      >
        {!isRefreshingCopy &&
          adsQuery.data?.map((ad) => (
            <AdCard
              key={ad.id}
              valuePropId={ad.id}
              valueProp={ad.valueProp}
              adSegmentId={segment.id}
              adAccountId={adAccountId}
            />
          ))}
      </CardContent>
    </Card>
  );
}
