export interface AudienceEntity {
  facetUrn: string;
  entityUrn: string;
  entityName: string;
}

export interface AudienceFacet {
  facetUrn: string;
  facetName: string;
  facetEntites: AudienceEntity[];
}

export interface AudienceOrGroup {
  or: AudienceFacet[];
}

export interface AudienceTargetingCriteria {
  include: {
    and: AudienceOrGroup[];
  };
  exclude?: {
    or: AudienceFacet[];
  };
}

export interface AddCriteriaData {
  facetUrn: string;
  entites: {
    entityUrn: string;
    entityName: string;
  }[];
  facetName: string;
}

export interface AudienceMutation {
  mutate: (params: {
    id: string;
    targetCriteria: AudienceTargetingCriteria;
    mutationId?: string;
  }) => void;
}
