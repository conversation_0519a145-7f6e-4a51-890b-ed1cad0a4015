"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/client";
import { useAuth } from "@clerk/nextjs";

export default function CreateAdvertisingPage() {
  const router = useRouter();
  const { userId } = useAuth();
  if (!userId) {
    return null;
  }

  const handleSignIn = () => {
    const LINKEDIN_URL = getURLWithQueryParams(
      "https://www.linkedin.com/oauth/v2/authorization",
      {
        response_type: "code",
        client_id: "86yfe63sb37g1s",
        state: `${userId}`,
        redirect_uri: `${getBackendUrl()}/api/oauth2/linkedin/callback`,
        scope:
          "rw_ads w_organization_social r_organization_social w_member_social r_ads r_ads_reporting r_marketing_leadgen_automation",
      },
    );
    window.location.href = LINKEDIN_URL;
  };
  const oauthQuery = api.v2.ads.linkedInUser.getForUser.useQuery();
  // const orgLinkedInUserQuery =
  //   api.organization.integrations.doesOrganizationHaveLinkedinConnection.useQuery();

  useEffect(() => {
    console.log("orgLinkedUserData", oauthQuery);

    if (oauthQuery.data !== undefined) {
      // First call is undefined, not sure why, maybe app needs to init? This fixes issue though
      if (!oauthQuery.data) {
        handleSignIn();
      } else {
        if (oauthQuery.data.linkedInAdAccountIds.length === 0) {
          router.push("/advertising/selectAdAccount");
        } else {
          router.push(
            `/advertising/adAccounts/${oauthQuery.data.linkedInAdAccountIds[0]}/campaignGroups/new`,
          );
        }
      }
    }
  }, [oauthQuery.data]);

  return <div>Loading...</div>;
}

function getBackendUrl() {
  // "https://url"
  const baseUrl = process.env.NEXT_PUBLIC_BACKEND_URL;

  return baseUrl;
}

const getURLWithQueryParams = (base: string, params: Object) => {
  const query = Object.entries(params)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join("&");

  return `${base}?${query}`;
};
