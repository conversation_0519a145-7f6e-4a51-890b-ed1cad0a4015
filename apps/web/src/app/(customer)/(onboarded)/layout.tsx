import Link from "next/link";
import { OrganizationIsOnboardedBoundry } from "@/features/organization/components/organization-is-onboarded-boundry";
import { api } from "@/trpc/server";
import {
  BarChartIcon,
  FilePlusIcon,
  GlobeIcon,
  Pencil1Icon,
  PieChartIcon,
  RocketIcon,
} from "@radix-ui/react-icons";

import WinLossIcon from "@kalos/ui/icons/win-loss-icon";
import { NavBar } from "@kalos/ui/navbar";

import { FeatureFlag } from "../../utils/feature-flag";

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const account = await api.user.organizationUser.get.organization();
  return (
    <OrganizationIsOnboardedBoundry onboardStatus="onboarded">
      <div className="flex h-screen w-screen justify-start bg-muted">
        <NavBar.Shell className="w-[240px] min-w-[240px] flex-shrink-0">
          <NavBar.ClientInfoSection clientName="Middesk" />
          <NavBar.Items>
            {[2].includes(account.organizationId) && (
              <NavBar.ItemsGroup>
                <NavBar.ItemsGroupHeader>
                  <WinLossIcon color="blue" />
                  <NavBar.ItemsGroupHeaderTitle title="ICP Engine" />
                </NavBar.ItemsGroupHeader>
                <Link
                  href="/segments"
                  className="flex w-full items-center justify-start"
                >
                  <NavBar.Item>
                    <BarChartIcon />
                    <NavBar.ItemTitle title="Segments" />
                  </NavBar.Item>
                </Link>
                <Link
                  href="/insights"
                  className="flex w-full items-center justify-start"
                >
                  <NavBar.Item>
                    <GlobeIcon />
                    <NavBar.ItemTitle title="Insights" />
                  </NavBar.Item>
                </Link>
              </NavBar.ItemsGroup>
            )}
            <NavBar.ItemsGroup>
              <NavBar.ItemsGroupHeader>
                <WinLossIcon color="blue" />
                <NavBar.ItemsGroupHeaderTitle title="Ads" />
              </NavBar.ItemsGroupHeader>
              <Link
                href="/advertising/new"
                className="flex w-full items-center justify-start"
              >
                <NavBar.Item>
                  <BarChartIcon />
                  <NavBar.ItemTitle title="Create" />
                </NavBar.Item>
              </Link>
              <Link
                href="/advertising/drafts"
                className="flex w-full items-center justify-start"
              >
                <NavBar.Item>
                  <Pencil1Icon />
                  <NavBar.ItemTitle title="Drafts" />
                </NavBar.Item>
              </Link>
              <Link
                href="/advertising/assets"
                className="flex w-full items-center justify-start"
              >
                <NavBar.Item>
                  <FilePlusIcon />
                  <NavBar.ItemTitle title="Files & Assets" />
                </NavBar.Item>
              </Link>
              <FeatureFlag flag="advertising-performance">
                <Link
                  href="/advertising/performance"
                  className="flex w-full items-center justify-start"
                >
                  <NavBar.Item>
                    <RocketIcon />
                    <NavBar.ItemTitle title="Performance" />
                  </NavBar.Item>
                </Link>
              </FeatureFlag>
            </NavBar.ItemsGroup>
          </NavBar.Items>
        </NavBar.Shell>
        {children}
      </div>
    </OrganizationIsOnboardedBoundry>
  );
}
