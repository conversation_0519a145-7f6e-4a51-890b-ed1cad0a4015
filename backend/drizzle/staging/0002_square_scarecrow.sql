CREATE TABLE "advertising"."ad_segment_best_audience" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ad_segment_id" varchar(36) NOT NULL,
	"variant_id" varchar(36) NOT NULL,
	CONSTRAINT "$asb_audience_unqiue_ad_segment_id" UNIQUE("ad_segment_id")
);
--> statement-breakpoint
CREATE TABLE "advertising"."ad_segment_best_conversationCallToActionCopy" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ad_segment_id" varchar(36) NOT NULL,
	"variant_id" varchar(36) NOT NULL,
	CONSTRAINT "$asb_conversationCallToActionCopy_unqiue_ad_segment_id" UNIQUE("ad_segment_id")
);
--> statement-breakpoint
CREATE TABLE "advertising"."ad_segment_best_conversationMessageCopy" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ad_segment_id" varchar(36) NOT NULL,
	"variant_id" varchar(36) NOT NULL,
	CONSTRAINT "$asb_conversationMessageCopy_unqiue_ad_segment_id" UNIQUE("ad_segment_id")
);
--> statement-breakpoint
CREATE TABLE "advertising"."ad_segment_best_conversationSubject" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ad_segment_id" varchar(36) NOT NULL,
	"variant_id" varchar(36) NOT NULL,
	CONSTRAINT "$asb_conversationSubject_unqiue_ad_segment_id" UNIQUE("ad_segment_id")
);
--> statement-breakpoint
CREATE TABLE "advertising"."ad_segment_best_creative" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ad_segment_id" varchar(36) NOT NULL,
	"variant_id" varchar(36) NOT NULL,
	CONSTRAINT "$asb_creative_unqiue_ad_segment_id" UNIQUE("ad_segment_id")
);
--> statement-breakpoint
CREATE TABLE "advertising"."ad_segment_best_socialPostBodyCopy" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ad_segment_id" varchar(36) NOT NULL,
	"variant_id" varchar(36) NOT NULL,
	CONSTRAINT "$asb_socialPostBodyCopy_unqiue_ad_segment_id" UNIQUE("ad_segment_id")
);
--> statement-breakpoint
CREATE TABLE "advertising"."ad_segment_best_socialPostCallToActionCopy" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ad_segment_id" varchar(36) NOT NULL,
	"variant_id" varchar(36) NOT NULL,
	CONSTRAINT "$asb_socialPostCallToActionCopy_unqiue_ad_segment_id" UNIQUE("ad_segment_id")
);
--> statement-breakpoint
CREATE TABLE "advertising"."ad_segment_best_valueProp" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ad_segment_id" varchar(36) NOT NULL,
	"variant_id" varchar(36) NOT NULL,
	CONSTRAINT "$asb_valueProp_unqiue_ad_segment_id" UNIQUE("ad_segment_id")
);
--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_best_audience" ADD CONSTRAINT "ad_segment_best_audience_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_best_audience" ADD CONSTRAINT "ad_segment_best_audience_variant_id_linked_in_audience_id_fk" FOREIGN KEY ("variant_id") REFERENCES "advertising"."linked_in_audience"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_best_conversationCallToActionCopy" ADD CONSTRAINT "ad_segment_best_conversationCallToActionCopy_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_best_conversationCallToActionCopy" ADD CONSTRAINT "ad_segment_best_conversationCallToActionCopy_variant_id_conversation_call_to_action_copy_id_fk" FOREIGN KEY ("variant_id") REFERENCES "advertising"."conversation_call_to_action_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_best_conversationMessageCopy" ADD CONSTRAINT "ad_segment_best_conversationMessageCopy_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_best_conversationMessageCopy" ADD CONSTRAINT "ad_segment_best_conversationMessageCopy_variant_id_conversation_message_copy_id_fk" FOREIGN KEY ("variant_id") REFERENCES "advertising"."conversation_message_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_best_conversationSubject" ADD CONSTRAINT "ad_segment_best_conversationSubject_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_best_conversationSubject" ADD CONSTRAINT "ad_segment_best_conversationSubject_variant_id_conversation_subject_copy_id_fk" FOREIGN KEY ("variant_id") REFERENCES "advertising"."conversation_subject_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_best_creative" ADD CONSTRAINT "ad_segment_best_creative_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_best_creative" ADD CONSTRAINT "ad_segment_best_creative_variant_id_linkedin_ad_program_creative_id_fk" FOREIGN KEY ("variant_id") REFERENCES "advertising"."linkedin_ad_program_creative"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_best_socialPostBodyCopy" ADD CONSTRAINT "ad_segment_best_socialPostBodyCopy_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_best_socialPostBodyCopy" ADD CONSTRAINT "ad_segment_best_socialPostBodyCopy_variant_id_social_post_copy_id_fk" FOREIGN KEY ("variant_id") REFERENCES "advertising"."social_post_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_best_socialPostCallToActionCopy" ADD CONSTRAINT "ad_segment_best_socialPostCallToActionCopy_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_best_socialPostCallToActionCopy" ADD CONSTRAINT "ad_segment_best_socialPostCallToActionCopy_variant_id_social_post_call_to_action_copy_id_fk" FOREIGN KEY ("variant_id") REFERENCES "advertising"."social_post_call_to_action_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_best_valueProp" ADD CONSTRAINT "ad_segment_best_valueProp_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_best_valueProp" ADD CONSTRAINT "ad_segment_best_valueProp_variant_id_linkedin_ad_segment_value_prop_id_fk" FOREIGN KEY ("variant_id") REFERENCES "advertising"."linkedin_ad_segment_value_prop"("id") ON DELETE no action ON UPDATE no action;