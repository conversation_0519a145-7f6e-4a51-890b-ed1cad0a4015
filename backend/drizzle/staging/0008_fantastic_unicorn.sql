CREATE SCHEMA "shared";
--> statement-breakpoint
CREATE TABLE "shared"."error_log" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"organization_id" bigint,
	"error_type" varchar(50) NOT NULL,
	"title" varchar(500) NOT NULL,
	"description" text NOT NULL,
	"endpoint" varchar(200),
	"method" varchar(10),
	"status_code" varchar(10),
	"metadata" json,
	"severity" varchar(20) DEFAULT 'MEDIUM',
	"is_processed" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "core"."organization" ADD COLUMN "slack_lead_notifications_enabled" boolean DEFAULT true;