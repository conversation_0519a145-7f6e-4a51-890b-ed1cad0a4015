ALTER TABLE "advertising"."linkedin_deployment_config_campaign" ADD COLUMN "leads" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_sponsored_creative" ADD COLUMN "leads" integer;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "original_bid" numeric(12, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "time_elapsed" numeric(12, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "budget_used" numeric(12, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "budget" numeric(12, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "daily_or_total_budget" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "min_bid" numeric(12, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "max_bid" numeric(12, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "min_suggested_bid" numeric(12, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "max_suggested_bid" numeric(12, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "suggested_bid" numeric(12, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "decision" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "bid_type" varchar(255) NOT NULL;