CREATE SCHEMA "advertising";
--> statement-breakpoint
CREATE SCHEMA "core";
--> statement-breakpoint
CREATE SCHEMA "crm";
--> statement-breakpoint
CREATE TYPE "public"."ab_test_round_winner" AS ENUM('CURRENT_BEST', 'CONTENDER');--> statement-breakpoint
CREATE TYPE "public"."ab_test_status" AS ENUM('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELLED');--> statement-breakpoint
CREATE TYPE "public"."ad_creative_file_type" AS ENUM('IMAGE', 'VIDEO', 'DOCUMENT');--> statement-breakpoint
CREATE TYPE "public"."audience_test_round_status" AS ENUM('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED');--> statement-breakpoint
CREATE TYPE "public"."audience_test_round_winner_audience" AS ENUM('CURRENT_BEST', 'CONTESTER');--> statement-breakpoint
CREATE TYPE "public"."status" AS ENUM('DRAFT', 'ACTIVE', 'ARCHIVED_DRAFT', 'ARCHIVED_ACTIVE', 'COMPLETED');--> statement-breakpoint
CREATE TYPE "public"."type" AS ENUM('EVERGREEN', 'EVENT_DRIVEN');--> statement-breakpoint
CREATE TYPE "public"."objective_type" AS ENUM('BRAND_AWARENESS', 'CREATIVE_ENGAGEMENT', 'ENGAGEMENT', 'JOB_APPLICANT', 'LEAD_GENERATION', 'TALENT_LEAD', 'VIDEO_VIEW', 'WEBSITE_CONVERSION', 'WEBSITE_TRAFFIC', 'WEBSITE_VISIT');--> statement-breakpoint
CREATE TYPE "public"."linkedin_campaign_status_enum" AS ENUM('ACTIVE', 'PAUSED', 'ARCHIVED', 'CANCELLED', 'DRAFT', 'PENDING_DELETION', 'REMOVED');--> statement-breakpoint
CREATE TYPE "public"."linkedin_campaign_group_status_enum" AS ENUM('ACTIVE', 'PAUSED', 'ARCHIVED', 'CANCELLED', 'DRAFT', 'PENDING_DELETION', 'REMOVED');--> statement-breakpoint
CREATE TYPE "public"."linkedin_deployment_config_status_enum" AS ENUM('NOT_STARTED', 'RUNNING', 'CANCELLED', 'INTERRUPTED', 'FAILED', 'COMPLETED');--> statement-breakpoint
CREATE TYPE "public"."linkedin_post_type_enum" AS ENUM('SINGLE_IMAGE', 'MULTI_IMAGE', 'SINGLE_VIDEO', 'DOCUMENT');--> statement-breakpoint
CREATE TYPE "public"."linkedin_sponsored_creative_status_enum" AS ENUM('ACTIVE', 'PAUSED', 'ARCHIVED', 'CANCELLED', 'DRAFT', 'PENDING_DELETION', 'REMOVED');--> statement-breakpoint
CREATE TYPE "public"."linkedin_sponsored_creative_type_enum" AS ENUM('POST', 'CONVERSATION', 'INMAIL');--> statement-breakpoint
CREATE TYPE "public"."stage_status_enum" AS ENUM('NOT_STATED', 'PROVISIONING', 'RUNNING', 'INTERRUPTED', 'FATAL_PROBLEM', 'FINISHED');--> statement-breakpoint
CREATE TYPE "public"."stage_step_on_end_output_type_enum" AS ENUM('CALL_STEP', 'CALL_END_WORKFLOW', 'CALL_END_STEP', 'CALL_CONFIG');--> statement-breakpoint
CREATE TYPE "public"."stage_step_on_start_output_type_enum" AS ENUM('CALL_STEP', 'CALL_END_WORKFLOW', 'CALL_END_STEP', 'CALL_CONFIG');--> statement-breakpoint
CREATE TYPE "public"."stage_step_status_enum" AS ENUM('NOT_STATED', 'PROVISIONING', 'RUNNING', 'INTERRUPTED', 'FATAL_PROBLEM', 'FINISHING', 'FINISHED');--> statement-breakpoint
CREATE TYPE "public"."value_prop_test_round_status" AS ENUM('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED');--> statement-breakpoint
CREATE TYPE "public"."value_prop_test_round_winner" AS ENUM('CURRENT_BEST', 'CONTENDER');--> statement-breakpoint
CREATE TYPE "public"."segment_status" AS ENUM('GENERATING', 'ACTIVE', 'ARCHIVED');--> statement-breakpoint
CREATE TABLE "advertising"."audience_ab_test_round_day" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ab_test_round_id" varchar(36) NOT NULL,
	"day_index" smallint NOT NULL,
	"winner" "ab_test_round_winner",
	"status" "ab_test_status" NOT NULL,
	"current_best_result" smallint,
	"contender_result" smallint,
	"deployment_config_id" varchar(36) NOT NULL,
	CONSTRAINT "ab_test_round_day_status_constraint_audience" CHECK (("advertising"."audience_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."audience_ab_test_round_day"."winner" IS NOT NULL)),
	CONSTRAINT "ab_test_round_day_status_constraint_cb_audience" CHECK (("advertising"."audience_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."audience_ab_test_round_day"."current_best_result" IS NOT NULL)),
	CONSTRAINT "ab_test_round_day_status_constraint_contender_audience" CHECK (("advertising"."audience_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."audience_ab_test_round_day"."contender_result" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."audience_ab_test_round" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ab_test_id" varchar(36) NOT NULL,
	"status" "ab_test_status" NOT NULL,
	"round_index" smallint NOT NULL,
	"current_best_id" varchar(36) NOT NULL,
	"contender_id" varchar(36) NOT NULL,
	"winner" "ab_test_round_winner",
	CONSTRAINT "ab_test_round_status_constraint_audience" CHECK (("advertising"."audience_ab_test_round"."status" != 'COMPLETED') OR ("advertising"."audience_ab_test_round"."winner" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."audience_ab_test" (
	"stage_id" varchar(36) PRIMARY KEY NOT NULL,
	"status" "ab_test_status" NOT NULL,
	"winner_id" varchar(36),
	CONSTRAINT "ab_test_status_constraint_audience" CHECK (("advertising"."audience_ab_test"."status" != 'COMPLETED') OR ("advertising"."audience_ab_test"."winner_id" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."conversationSubject_ab_test_round_day" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ab_test_round_id" varchar(36) NOT NULL,
	"day_index" smallint NOT NULL,
	"winner" "ab_test_round_winner",
	"status" "ab_test_status" NOT NULL,
	"current_best_result" smallint,
	"contender_result" smallint,
	"deployment_config_id" varchar(36) NOT NULL,
	CONSTRAINT "ab_test_round_day_status_constraint_conversationSubject" CHECK (("advertising"."conversationSubject_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."conversationSubject_ab_test_round_day"."winner" IS NOT NULL)),
	CONSTRAINT "ab_test_round_day_status_constraint_cb_conversationSubject" CHECK (("advertising"."conversationSubject_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."conversationSubject_ab_test_round_day"."current_best_result" IS NOT NULL)),
	CONSTRAINT "ab_test_round_day_status_constraint_contender_conversationSubject" CHECK (("advertising"."conversationSubject_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."conversationSubject_ab_test_round_day"."contender_result" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."conversationSubject_ab_test_round" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ab_test_id" varchar(36) NOT NULL,
	"status" "ab_test_status" NOT NULL,
	"round_index" smallint NOT NULL,
	"current_best_id" varchar(36) NOT NULL,
	"contender_id" varchar(36) NOT NULL,
	"winner" "ab_test_round_winner",
	CONSTRAINT "ab_test_round_status_constraint_conversationSubject" CHECK (("advertising"."conversationSubject_ab_test_round"."status" != 'COMPLETED') OR ("advertising"."conversationSubject_ab_test_round"."winner" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."conversationSubject_ab_test" (
	"stage_id" varchar(36) PRIMARY KEY NOT NULL,
	"status" "ab_test_status" NOT NULL,
	"winner_id" varchar(36),
	CONSTRAINT "ab_test_status_constraint_conversationSubject" CHECK (("advertising"."conversationSubject_ab_test"."status" != 'COMPLETED') OR ("advertising"."conversationSubject_ab_test"."winner_id" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."creative_ab_test_round_day" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ab_test_round_id" varchar(36) NOT NULL,
	"day_index" smallint NOT NULL,
	"winner" "ab_test_round_winner",
	"status" "ab_test_status" NOT NULL,
	"current_best_result" smallint,
	"contender_result" smallint,
	"deployment_config_id" varchar(36) NOT NULL,
	CONSTRAINT "ab_test_round_day_status_constraint_creative" CHECK (("advertising"."creative_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."creative_ab_test_round_day"."winner" IS NOT NULL)),
	CONSTRAINT "ab_test_round_day_status_constraint_cb_creative" CHECK (("advertising"."creative_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."creative_ab_test_round_day"."current_best_result" IS NOT NULL)),
	CONSTRAINT "ab_test_round_day_status_constraint_contender_creative" CHECK (("advertising"."creative_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."creative_ab_test_round_day"."contender_result" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."creative_ab_test_round" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ab_test_id" varchar(36) NOT NULL,
	"status" "ab_test_status" NOT NULL,
	"round_index" smallint NOT NULL,
	"current_best_id" varchar(36) NOT NULL,
	"contender_id" varchar(36) NOT NULL,
	"winner" "ab_test_round_winner",
	CONSTRAINT "ab_test_round_status_constraint_creative" CHECK (("advertising"."creative_ab_test_round"."status" != 'COMPLETED') OR ("advertising"."creative_ab_test_round"."winner" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."creative_ab_test" (
	"stage_id" varchar(36) PRIMARY KEY NOT NULL,
	"status" "ab_test_status" NOT NULL,
	"winner_id" varchar(36),
	CONSTRAINT "ab_test_status_constraint_creative" CHECK (("advertising"."creative_ab_test"."status" != 'COMPLETED') OR ("advertising"."creative_ab_test"."winner_id" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."valueProp_ab_test_round_day" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ab_test_round_id" varchar(36) NOT NULL,
	"day_index" smallint NOT NULL,
	"winner" "ab_test_round_winner",
	"status" "ab_test_status" NOT NULL,
	"current_best_result" smallint,
	"contender_result" smallint,
	"deployment_config_id" varchar(36) NOT NULL,
	CONSTRAINT "ab_test_round_day_status_constraint_valueProp" CHECK (("advertising"."valueProp_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."valueProp_ab_test_round_day"."winner" IS NOT NULL)),
	CONSTRAINT "ab_test_round_day_status_constraint_cb_valueProp" CHECK (("advertising"."valueProp_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."valueProp_ab_test_round_day"."current_best_result" IS NOT NULL)),
	CONSTRAINT "ab_test_round_day_status_constraint_contender_valueProp" CHECK (("advertising"."valueProp_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."valueProp_ab_test_round_day"."contender_result" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."valueProp_ab_test_round" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ab_test_id" varchar(36) NOT NULL,
	"status" "ab_test_status" NOT NULL,
	"round_index" smallint NOT NULL,
	"current_best_id" varchar(36) NOT NULL,
	"contender_id" varchar(36) NOT NULL,
	"winner" "ab_test_round_winner",
	CONSTRAINT "ab_test_round_status_constraint_valueProp" CHECK (("advertising"."valueProp_ab_test_round"."status" != 'COMPLETED') OR ("advertising"."valueProp_ab_test_round"."winner" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."valueProp_ab_test" (
	"stage_id" varchar(36) PRIMARY KEY NOT NULL,
	"status" "ab_test_status" NOT NULL,
	"winner_id" varchar(36),
	CONSTRAINT "ab_test_status_constraint_valueProp" CHECK (("advertising"."valueProp_ab_test"."status" != 'COMPLETED') OR ("advertising"."valueProp_ab_test"."winner_id" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."adCreative" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"organization_id" bigint NOT NULL,
	"file_name" varchar(255) NOT NULL,
	"s3_bucket_key" varchar(255) NOT NULL,
	"file_type" "ad_creative_file_type" NOT NULL,
	CONSTRAINT "adCreative_s3_bucket_key_unique" UNIQUE("s3_bucket_key")
);
--> statement-breakpoint
CREATE TABLE "advertising"."ad_segment_selected_conversation_subject_type" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ad_segment_id" varchar(36) NOT NULL,
	"type" varchar(255) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."ad_segment_value_prop_creative" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ad_segment_value_prop_id" varchar(36) NOT NULL,
	"ad_program_creative_id" varchar(36) NOT NULL,
	CONSTRAINT "unique_ad_segment_value_prop_and_creative" UNIQUE("ad_segment_value_prop_id","ad_program_creative_id")
);
--> statement-breakpoint
CREATE TABLE "advertising"."audience_test_round" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"stage_id" varchar(36) NOT NULL,
	"current_audience_id" varchar(36) NOT NULL,
	"contender_audience_id" varchar(36) NOT NULL,
	"status" "audience_test_round_status" NOT NULL,
	"winner_audience" "audience_test_round_winner_audience" NOT NULL,
	"index" smallint NOT NULL,
	"step_id" varchar(36),
	"retries" smallint DEFAULT 0 NOT NULL,
	"max_retries" smallint DEFAULT 5 NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."case_study" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"organization_id" bigint NOT NULL,
	"content" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."conversation_call_to_action_copy" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"content" text NOT NULL,
	"conversation_message_copy_id" varchar(36) NOT NULL,
	"type" varchar(255) NOT NULL,
	CONSTRAINT "unique_copy_type_and_message_copy_id" UNIQUE("conversation_message_copy_id","type")
);
--> statement-breakpoint
CREATE TABLE "advertising"."conversation_call_to_action_copy_type" (
	"name" varchar(255) PRIMARY KEY NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."deprecated_conversation_copy" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"title" text NOT NULL,
	"body" text NOT NULL,
	"linkedin_ad_segment_value_prop_id" varchar(36) NOT NULL,
	"conversation_copy_type" varchar(255) NOT NULL,
	"call_to_action" varchar(255) NOT NULL,
	CONSTRAINT "unique_copy_type_and_value_prop_convo" UNIQUE("linkedin_ad_segment_value_prop_id","conversation_copy_type")
);
--> statement-breakpoint
CREATE TABLE "advertising"."conversation_copy_type" (
	"name" varchar(255) PRIMARY KEY NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."conversation_message_copy" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"content" text NOT NULL,
	"conversation_subject_copy_id" varchar(36) NOT NULL,
	"type" varchar(255) NOT NULL,
	CONSTRAINT "unique_copy_type_and_subject_copy_id" UNIQUE("conversation_subject_copy_id","type")
);
--> statement-breakpoint
CREATE TABLE "advertising"."conversation_subject_copy" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ad_segment_value_prop_id" varchar(36) NOT NULL,
	"content" text NOT NULL,
	"type" varchar(255) NOT NULL,
	"lead_gen_form_urn" varchar(255),
	CONSTRAINT "unique_subject_copy_per_ad_segment_value_prop" UNIQUE("ad_segment_value_prop_id","type")
);
--> statement-breakpoint
CREATE TABLE "advertising"."conversation_subject_copy_type" (
	"name" varchar(255) PRIMARY KEY NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."example_social_post" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"organization_id" bigint NOT NULL,
	"content" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedin_ad_account" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"organization_id" bigint NOT NULL,
	"name" varchar(255) NOT NULL,
	"linkedin_ad_account_urn" varchar(255) NOT NULL,
	"linkedin_organization_urn" varchar(255) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "advertising"."linked_in_ad_format" (
	"id" serial PRIMARY KEY NOT NULL,
	"type" varchar(256) NOT NULL,
	"format" varchar(256) NOT NULL,
	CONSTRAINT "unique_type_and_format" UNIQUE("type","format")
);
--> statement-breakpoint
CREATE TABLE "advertising"."linked_in_ad_program" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"title" varchar(256) NOT NULL,
	"linked_in_ad_account_id" varchar(36) NOT NULL,
	"start_datetime" timestamp NOT NULL,
	"end_datetime" timestamp,
	"type" "type" DEFAULT 'EVENT_DRIVEN',
	"total_budget" integer,
	"monthly_budget" integer,
	"ad_format_id" integer NOT NULL,
	"objective_type" "objective_type" NOT NULL,
	"lead_gen_form_urn" varchar(255),
	"created_at" timestamp DEFAULT now(),
	"status" "status" DEFAULT 'DRAFT',
	CONSTRAINT "lead_gen_constraint" CHECK (("advertising"."linked_in_ad_program"."objective_type" != 'LEAD_GENERATION') OR ("advertising"."linked_in_ad_program"."lead_gen_form_urn" IS NOT NULL)),
	CONSTRAINT "total_budget_constraint" CHECK (("advertising"."linked_in_ad_program"."type" = 'evergreen') OR ("advertising"."linked_in_ad_program"."total_budget" IS NOT NULL)),
	CONSTRAINT "monthly_budget_constraint" CHECK (("advertising"."linked_in_ad_program"."type" != 'evergreen') OR ("advertising"."linked_in_ad_program"."monthly_budget" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedin_ad_program_creative" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"linkedin_ad_program_id" varchar(36) NOT NULL,
	"linkedin_ad_creative_id" varchar(36) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."linked_in_ad_segment" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"linked_in_ad_program_id" varchar(36) NOT NULL,
	"segment_id" varchar(36) NOT NULL,
	"is_ready" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedin_ad_segment_conversation_base_copy" (
	"ad_segment_id" varchar(36) PRIMARY KEY NOT NULL,
	"base_copy" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedin_ad_segment_social_post_base_copy" (
	"ad_segment_id" varchar(36) PRIMARY KEY NOT NULL,
	"base_copy" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedin_ad_segment_value_prop" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"linkedin_ad_segment_id" varchar(36) NOT NULL,
	"value_prop" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."linked_in_audience" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"linkedin_ad_segment_id" varchar(36) NOT NULL,
	"description" text,
	"audience_populated" boolean DEFAULT false,
	"to_use" boolean DEFAULT true NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedin_audience_exclude_entity" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"linkedin_audience_id" varchar(36) NOT NULL,
	"linkedin_audience_entity_urn" varchar(255) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."linked_in_audience_or_group" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"linked_in_audience_id" varchar(36) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."linked_in_audience_or_group_entity" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"linked_in_audience_or_group_id" varchar(36) NOT NULL,
	"linked_in_audience_entity_urn" varchar(255) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."linked_in_audience_entity" (
	"entity_urn" varchar(255) PRIMARY KEY NOT NULL,
	"entity_name" varchar(255) NOT NULL,
	"facet_urn" varchar(255) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."linked_in_facet" (
	"facet_urn" varchar(255) PRIMARY KEY NOT NULL,
	"facet_name" varchar(255) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."segment_linked_in_audience_or_group_entity" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"segment_linked_in_audience_or_group_id" varchar(36) NOT NULL,
	"linked_in_audience_entity_urn" varchar(255) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."segment_linkedin_audience_exclude_entity" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"segment_linked_in_audience_prefab_id" varchar(36) NOT NULL,
	"linkedin_audience_entity_urn" varchar(255) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."segment_linked_in_audience_and_group" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"segment_linked_in_audience_prefab_id" varchar(36) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedin_campaign" (
	"linkedin_audience_id" varchar(36) PRIMARY KEY NOT NULL,
	"linkedin_campaign_urn" varchar(255) NOT NULL,
	"total_budget" integer NOT NULL,
	"status" "linkedin_campaign_status_enum" NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedin_campaign_group" (
	"linkedin_ad_segment_id" varchar(36) PRIMARY KEY NOT NULL,
	"total_budget" integer NOT NULL,
	"linkedin_campaign_group_urn" varchar(255) NOT NULL,
	"name" varchar NOT NULL,
	"status" "linkedin_campaign_group_status_enum" NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedin_deployment_config" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"linked_in_ad_segment_id" varchar(36) NOT NULL,
	"status" "linkedin_deployment_config_status_enum" NOT NULL,
	"start_datetime" timestamp,
	"end_datetime" timestamp,
	CONSTRAINT "deployment_config_start_date_time_constraint" CHECK (("advertising"."linkedin_deployment_config"."status" NOT IN ('RUNNING', 'COMPLETED')) OR ("advertising"."linkedin_deployment_config"."start_datetime" IS NOT NULL)),
	CONSTRAINT "deployment_config_end_date_time_constraint" CHECK (("advertising"."linkedin_deployment_config"."status" NOT IN ('COMPLETED')) OR ("advertising"."linkedin_deployment_config"."end_datetime" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedin_deployment_config_campaign" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"linkedin_deployment_config_id" varchar(36) NOT NULL,
	"campaign_id" varchar(36) NOT NULL,
	"impressions" integer,
	"clicks" integer,
	"conversions" integer,
	"cost" numeric(12, 2),
	"video_views" integer,
	"sends" integer,
	"opens" integer
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedin_deployment_config_sponsored_creative" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"linkedin_deployment_config_id" varchar(36) NOT NULL,
	"sponsored_creative_id" varchar(36) NOT NULL,
	"impressions" integer,
	"clicks" integer,
	"conversions" integer,
	"cost" numeric(12, 2),
	"video_views" integer,
	"sends" integer,
	"opens" integer
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedInPost" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"single_image_post_id" varchar(36),
	"single_video_post_id" varchar(36),
	"single_document_post_id" varchar(36),
	"linkedin_post_type" "linkedin_post_type_enum" NOT NULL,
	CONSTRAINT "linkedin_post_type_constraint" CHECK (("advertising"."linkedInPost"."linkedin_post_type" != 'SINGLE_IMAGE') OR ("advertising"."linkedInPost"."single_image_post_id" IS NOT NULL)),
	CONSTRAINT "linkedin_post_type_constraint_single_video" CHECK (("advertising"."linkedInPost"."linkedin_post_type" != 'SINGLE_VIDEO') OR ("advertising"."linkedInPost"."single_video_post_id" IS NOT NULL)),
	CONSTRAINT "linkedin_post_type_constraint_document" CHECK (("advertising"."linkedInPost"."linkedin_post_type" != 'DOCUMENT') OR ("advertising"."linkedInPost"."single_document_post_id" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedin_single_document_post" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"linkedin_ad_segment_value_prop" varchar(36) NOT NULL,
	"social_post_copy_type" varchar(255) NOT NULL,
	"call_to_action_copy_type" varchar(255),
	"ad_program_creative_id" varchar(36) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedInSingleImagePost" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"linkedin_ad_segment_value_prop" varchar(36) NOT NULL,
	"social_post_copy_type" varchar(255) NOT NULL,
	"call_to_action_copy_type" varchar(255),
	"ad_program_creative_id" varchar(36) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedin_single_video_post" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"linkedin_ad_segment_value_prop" varchar(36) NOT NULL,
	"social_post_copy_type" varchar(255) NOT NULL,
	"call_to_action_copy_type" varchar(255),
	"ad_program_creative_id" varchar(36) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedInSponsoredCreative" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"linkedin_campaign_id" varchar(36) NOT NULL,
	"linkedin_sponsored_creative_urn" varchar(255) NOT NULL,
	"status" "linkedin_sponsored_creative_status_enum" NOT NULL,
	"type" "linkedin_sponsored_creative_type_enum" NOT NULL,
	"linkedin_post_id" varchar(36),
	"deprecated_conversation_copy_id" varchar(36),
	"conversation_call_to_action_id" varchar(36),
	CONSTRAINT "sponsored_creative_type_constraint" CHECK (("advertising"."linkedInSponsoredCreative"."type" != 'POST') OR ("advertising"."linkedInSponsoredCreative"."linkedin_post_id" IS NOT NULL)),
	CONSTRAINT "deprecated_conversation_copy_constraint" CHECK (("advertising"."linkedInSponsoredCreative"."type" != 'CONVERSATION') OR ("advertising"."linkedInSponsoredCreative"."deprecated_conversation_copy_id" IS NOT NULL)),
	CONSTRAINT "inmail_constraint" CHECK (("advertising"."linkedInSponsoredCreative"."type" != 'INMAIL') OR ("advertising"."linkedInSponsoredCreative"."conversation_call_to_action_id" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedin_user" (
	"organization_user_id" varchar(33) PRIMARY KEY NOT NULL,
	"refresh_token" varchar(1000) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedin_user_to_ad_account" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"linkedin_ad_account_id" varchar(36) NOT NULL,
	"linkedin_user_id" varchar(36) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."manual_bidding_event" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"campaign_id" varchar(36) NOT NULL,
	"cost" numeric(12, 2) NOT NULL,
	"budget_used_percentage" numeric(12, 2) NOT NULL,
	"time_elapsed_percentage" numeric(12, 2) NOT NULL,
	"timestamp" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."organizationConversion" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"organization_id" bigint NOT NULL,
	"conversion_urn" varchar(255) NOT NULL,
	"name" varchar(255) NOT NULL,
	CONSTRAINT "organizationConversion_conversion_urn_unique" UNIQUE("conversion_urn")
);
--> statement-breakpoint
CREATE TABLE "advertising"."positioning" (
	"organization_id" bigint PRIMARY KEY NOT NULL,
	"content" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."segment_linked_in_audience_prefab" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"segment_id" varchar(36) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."social_post_call_to_action_copy" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"type" varchar(255) NOT NULL,
	"call_to_action" varchar(255) NOT NULL,
	"ad_segment_value_prop_id" varchar(36) NOT NULL,
	CONSTRAINT "unique_call_to_action_and_type" UNIQUE("ad_segment_value_prop_id","type")
);
--> statement-breakpoint
CREATE TABLE "advertising"."social_post_copy" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"title" text NOT NULL,
	"body" text NOT NULL,
	"linkedin_ad_segment_value_prop_id" varchar(36) NOT NULL,
	"social_post_copy_type" varchar(255) NOT NULL,
	"lead_gen_form_urn" varchar(255),
	CONSTRAINT "unique_copy_type_and_value_prop" UNIQUE("linkedin_ad_segment_value_prop_id","social_post_copy_type")
);
--> statement-breakpoint
CREATE TABLE "advertising"."social_post_copy_type" (
	"name" varchar(255) PRIMARY KEY NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."social_post_style_guide" (
	"organization_id" bigint PRIMARY KEY NOT NULL,
	"content" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."stage" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ad_segment_id" varchar(36) NOT NULL,
	"index" integer NOT NULL,
	"stage_type" varchar(255) NOT NULL,
	"stage_status" "stage_status_enum" NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."stage_step" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"stage_id" varchar(36) NOT NULL,
	"step_name" varchar(255) NOT NULL,
	"status" "stage_step_status_enum" NOT NULL,
	"on_start_output_type" "stage_step_on_start_output_type_enum",
	"on_end_output_type" "stage_step_on_end_output_type_enum"
);
--> statement-breakpoint
CREATE TABLE "advertising"."value_prop_test_round" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"stage_id" varchar(36) NOT NULL,
	"current_value_prop_id" varchar(36) NOT NULL,
	"contender_value_prop_id" varchar(36) NOT NULL,
	"status" "value_prop_test_round_status" NOT NULL,
	"winner_value_prop" "value_prop_test_round_winner" NOT NULL,
	"index" smallint NOT NULL,
	"step_id" varchar(36),
	"retries" smallint DEFAULT 0 NOT NULL,
	"max_retries" smallint DEFAULT 5 NOT NULL
);
--> statement-breakpoint
CREATE TABLE "core"."job_function" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(256) NOT NULL,
	"description" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "core"."job_seniority" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(256) NOT NULL,
	"description" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "core"."organization" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"stripe_customer_id" varchar(255)
);
--> statement-breakpoint
CREATE TABLE "core"."organization_user" (
	"user_id" varchar(33) PRIMARY KEY NOT NULL,
	"organization_id" bigint NOT NULL,
	CONSTRAINT "unique_user_and_organization" UNIQUE("user_id","organization_id")
);
--> statement-breakpoint
CREATE TABLE "core"."segment" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"organization_id" bigint NOT NULL,
	"name" varchar(255),
	"annual_revenue_low_bound" bigint,
	"annual_revenue_high_bound" bigint,
	"number_of_employees_low_bound" integer,
	"number_of_employees_high_bound" integer,
	"annual_contact_value_low_bound" bigint,
	"annual_contract_value_high_bound" bigint,
	"job_function_id" integer,
	"job_seniority_id" integer,
	"status" "segment_status" DEFAULT 'GENERATING' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "core"."segment_value_prop" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"segment_id" varchar(36) NOT NULL,
	"value_prop" varchar(256) NOT NULL,
	"description" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "core"."segment_vertical" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"segment_id" varchar(36) NOT NULL,
	"name" varchar(255) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "core"."user" (
	"id" varchar(33) PRIMARY KEY NOT NULL,
	"email" varchar(255) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "crm"."account" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"organization_id" bigint NOT NULL,
	"crm_id" varchar(255) NOT NULL,
	"name" varchar(255) NOT NULL,
	"vertical" varchar(255),
	"sub_vertical" varchar(255),
	"number_of_employees" integer,
	"annual_revenue" bigint,
	"description" text,
	"website" varchar(255),
	"owner_id" varchar(36),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	CONSTRAINT "account_unique_crm_id" UNIQUE("crm_id","organization_id")
);
--> statement-breakpoint
CREATE TABLE "crm"."avoma_credentials" (
	"organization_id" bigint PRIMARY KEY NOT NULL,
	"client_key" varchar(1000) NOT NULL,
	"client_secret" varchar(1000) NOT NULL,
	"api_key" varchar(1000) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "crm"."contact" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"organization_id" bigint NOT NULL,
	"crm_id" varchar(255) NOT NULL,
	"account_id" varchar(36),
	"title" varchar(255),
	"first_name" varchar(255),
	"last_name" varchar(255),
	"email" varchar(255),
	"phone_number" varchar(255),
	"job_function_id" bigint,
	"job_seniority_id" bigint,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	CONSTRAINT "contact_unique_crm_id" UNIQUE("crm_id","organization_id")
);
--> statement-breakpoint
CREATE TABLE "crm"."crm_user" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"crm_id" varchar(256) NOT NULL,
	"organization_id" bigint NOT NULL,
	"first_name" varchar(256),
	"last_name" varchar(256),
	"email" varchar(256) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	CONSTRAINT "crm_user_unique_crm_id" UNIQUE("crm_id","organization_id")
);
--> statement-breakpoint
CREATE TABLE "crm"."hubspot_credentials" (
	"organization_id" bigint PRIMARY KEY NOT NULL,
	"organization_user_id" varchar(36) NOT NULL,
	"refresh_token" varchar(1000) NOT NULL,
	CONSTRAINT "hubspot_credentials_organization_user_id_unique" UNIQUE("organization_user_id")
);
--> statement-breakpoint
CREATE TABLE "crm"."opportunity_primary_contact" (
	"opportunity_id" varchar(36) PRIMARY KEY NOT NULL,
	"contact_id" varchar(36) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "crm"."opportunity" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"crm_id" varchar(255) NOT NULL,
	"organization_id" bigint NOT NULL,
	"name" varchar(255) NOT NULL,
	"annual_contract_value" bigint,
	"account_id" varchar(36),
	"is_closed" boolean DEFAULT false NOT NULL,
	"crm_created_date" date NOT NULL,
	"is_won" boolean,
	"closed_date" date,
	"owner_id" varchar(36),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	CONSTRAINT "opportunity_unique_crm_id" UNIQUE("crm_id","organization_id")
);
--> statement-breakpoint
ALTER TABLE "advertising"."audience_ab_test_round_day" ADD CONSTRAINT "audience_ab_test_round_day_ab_test_round_id_audience_ab_test_round_id_fk" FOREIGN KEY ("ab_test_round_id") REFERENCES "advertising"."audience_ab_test_round"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."audience_ab_test_round_day" ADD CONSTRAINT "audience_ab_test_round_day_deployment_config_id_linkedin_deployment_config_id_fk" FOREIGN KEY ("deployment_config_id") REFERENCES "advertising"."linkedin_deployment_config"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."audience_ab_test_round" ADD CONSTRAINT "audience_ab_test_round_ab_test_id_audience_ab_test_stage_id_fk" FOREIGN KEY ("ab_test_id") REFERENCES "advertising"."audience_ab_test"("stage_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."audience_ab_test_round" ADD CONSTRAINT "audience_ab_test_round_current_best_id_linkedin_campaign_linkedin_audience_id_fk" FOREIGN KEY ("current_best_id") REFERENCES "advertising"."linkedin_campaign"("linkedin_audience_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."audience_ab_test_round" ADD CONSTRAINT "audience_ab_test_round_contender_id_linkedin_campaign_linkedin_audience_id_fk" FOREIGN KEY ("contender_id") REFERENCES "advertising"."linkedin_campaign"("linkedin_audience_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."audience_ab_test" ADD CONSTRAINT "audience_ab_test_stage_id_stage_id_fk" FOREIGN KEY ("stage_id") REFERENCES "advertising"."stage"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."audience_ab_test" ADD CONSTRAINT "audience_ab_test_winner_id_linkedin_campaign_linkedin_audience_id_fk" FOREIGN KEY ("winner_id") REFERENCES "advertising"."linkedin_campaign"("linkedin_audience_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationSubject_ab_test_round_day" ADD CONSTRAINT "conversationSubject_ab_test_round_day_ab_test_round_id_conversationSubject_ab_test_round_id_fk" FOREIGN KEY ("ab_test_round_id") REFERENCES "advertising"."conversationSubject_ab_test_round"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationSubject_ab_test_round_day" ADD CONSTRAINT "conversationSubject_ab_test_round_day_deployment_config_id_linkedin_deployment_config_id_fk" FOREIGN KEY ("deployment_config_id") REFERENCES "advertising"."linkedin_deployment_config"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationSubject_ab_test_round" ADD CONSTRAINT "conversationSubject_ab_test_round_ab_test_id_conversationSubject_ab_test_stage_id_fk" FOREIGN KEY ("ab_test_id") REFERENCES "advertising"."conversationSubject_ab_test"("stage_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationSubject_ab_test_round" ADD CONSTRAINT "conversationSubject_ab_test_round_current_best_id_conversation_subject_copy_id_fk" FOREIGN KEY ("current_best_id") REFERENCES "advertising"."conversation_subject_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationSubject_ab_test_round" ADD CONSTRAINT "conversationSubject_ab_test_round_contender_id_conversation_subject_copy_id_fk" FOREIGN KEY ("contender_id") REFERENCES "advertising"."conversation_subject_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationSubject_ab_test" ADD CONSTRAINT "conversationSubject_ab_test_stage_id_stage_id_fk" FOREIGN KEY ("stage_id") REFERENCES "advertising"."stage"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationSubject_ab_test" ADD CONSTRAINT "conversationSubject_ab_test_winner_id_conversation_subject_copy_id_fk" FOREIGN KEY ("winner_id") REFERENCES "advertising"."conversation_subject_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."creative_ab_test_round_day" ADD CONSTRAINT "creative_ab_test_round_day_ab_test_round_id_creative_ab_test_round_id_fk" FOREIGN KEY ("ab_test_round_id") REFERENCES "advertising"."creative_ab_test_round"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."creative_ab_test_round_day" ADD CONSTRAINT "creative_ab_test_round_day_deployment_config_id_linkedin_deployment_config_id_fk" FOREIGN KEY ("deployment_config_id") REFERENCES "advertising"."linkedin_deployment_config"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."creative_ab_test_round" ADD CONSTRAINT "creative_ab_test_round_ab_test_id_creative_ab_test_stage_id_fk" FOREIGN KEY ("ab_test_id") REFERENCES "advertising"."creative_ab_test"("stage_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."creative_ab_test_round" ADD CONSTRAINT "creative_ab_test_round_current_best_id_linkedin_ad_program_creative_id_fk" FOREIGN KEY ("current_best_id") REFERENCES "advertising"."linkedin_ad_program_creative"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."creative_ab_test_round" ADD CONSTRAINT "creative_ab_test_round_contender_id_linkedin_ad_program_creative_id_fk" FOREIGN KEY ("contender_id") REFERENCES "advertising"."linkedin_ad_program_creative"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."creative_ab_test" ADD CONSTRAINT "creative_ab_test_stage_id_stage_id_fk" FOREIGN KEY ("stage_id") REFERENCES "advertising"."stage"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."creative_ab_test" ADD CONSTRAINT "creative_ab_test_winner_id_linkedin_ad_program_creative_id_fk" FOREIGN KEY ("winner_id") REFERENCES "advertising"."linkedin_ad_program_creative"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."valueProp_ab_test_round_day" ADD CONSTRAINT "valueProp_ab_test_round_day_ab_test_round_id_valueProp_ab_test_round_id_fk" FOREIGN KEY ("ab_test_round_id") REFERENCES "advertising"."valueProp_ab_test_round"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."valueProp_ab_test_round_day" ADD CONSTRAINT "valueProp_ab_test_round_day_deployment_config_id_linkedin_deployment_config_id_fk" FOREIGN KEY ("deployment_config_id") REFERENCES "advertising"."linkedin_deployment_config"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."valueProp_ab_test_round" ADD CONSTRAINT "valueProp_ab_test_round_ab_test_id_valueProp_ab_test_stage_id_fk" FOREIGN KEY ("ab_test_id") REFERENCES "advertising"."valueProp_ab_test"("stage_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."valueProp_ab_test_round" ADD CONSTRAINT "valueProp_ab_test_round_current_best_id_linkedin_ad_segment_value_prop_id_fk" FOREIGN KEY ("current_best_id") REFERENCES "advertising"."linkedin_ad_segment_value_prop"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."valueProp_ab_test_round" ADD CONSTRAINT "valueProp_ab_test_round_contender_id_linkedin_ad_segment_value_prop_id_fk" FOREIGN KEY ("contender_id") REFERENCES "advertising"."linkedin_ad_segment_value_prop"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."valueProp_ab_test" ADD CONSTRAINT "valueProp_ab_test_stage_id_stage_id_fk" FOREIGN KEY ("stage_id") REFERENCES "advertising"."stage"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."valueProp_ab_test" ADD CONSTRAINT "valueProp_ab_test_winner_id_linkedin_ad_segment_value_prop_id_fk" FOREIGN KEY ("winner_id") REFERENCES "advertising"."linkedin_ad_segment_value_prop"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."adCreative" ADD CONSTRAINT "adCreative_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_selected_conversation_subject_type" ADD CONSTRAINT "ad_segment_selected_conversation_subject_type_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_value_prop_creative" ADD CONSTRAINT "ad_segment_value_prop_creative_ad_segment_value_prop_id_linkedin_ad_segment_value_prop_id_fk" FOREIGN KEY ("ad_segment_value_prop_id") REFERENCES "advertising"."linkedin_ad_segment_value_prop"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_value_prop_creative" ADD CONSTRAINT "ad_segment_value_prop_creative_ad_program_creative_id_linkedin_ad_program_creative_id_fk" FOREIGN KEY ("ad_program_creative_id") REFERENCES "advertising"."linkedin_ad_program_creative"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."audience_test_round" ADD CONSTRAINT "audience_test_round_step_id_stage_step_id_fk" FOREIGN KEY ("step_id") REFERENCES "advertising"."stage_step"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."case_study" ADD CONSTRAINT "case_study_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversation_call_to_action_copy" ADD CONSTRAINT "conversation_call_to_action_copy_conversation_message_copy_id_conversation_message_copy_id_fk" FOREIGN KEY ("conversation_message_copy_id") REFERENCES "advertising"."conversation_message_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversation_call_to_action_copy" ADD CONSTRAINT "conversation_call_to_action_copy_type_conversation_call_to_action_copy_type_name_fk" FOREIGN KEY ("type") REFERENCES "advertising"."conversation_call_to_action_copy_type"("name") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."deprecated_conversation_copy" ADD CONSTRAINT "deprecated_conversation_copy_linkedin_ad_segment_value_prop_id_linkedin_ad_segment_value_prop_id_fk" FOREIGN KEY ("linkedin_ad_segment_value_prop_id") REFERENCES "advertising"."linkedin_ad_segment_value_prop"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."deprecated_conversation_copy" ADD CONSTRAINT "deprecated_conversation_copy_conversation_copy_type_conversation_copy_type_name_fk" FOREIGN KEY ("conversation_copy_type") REFERENCES "advertising"."conversation_copy_type"("name") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversation_message_copy" ADD CONSTRAINT "conversation_message_copy_conversation_subject_copy_id_conversation_subject_copy_id_fk" FOREIGN KEY ("conversation_subject_copy_id") REFERENCES "advertising"."conversation_subject_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversation_message_copy" ADD CONSTRAINT "conversation_message_copy_type_conversation_copy_type_name_fk" FOREIGN KEY ("type") REFERENCES "advertising"."conversation_copy_type"("name") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversation_subject_copy" ADD CONSTRAINT "conversation_subject_copy_ad_segment_value_prop_id_linkedin_ad_segment_value_prop_id_fk" FOREIGN KEY ("ad_segment_value_prop_id") REFERENCES "advertising"."linkedin_ad_segment_value_prop"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."example_social_post" ADD CONSTRAINT "example_social_post_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_ad_account" ADD CONSTRAINT "linkedin_ad_account_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linked_in_ad_program" ADD CONSTRAINT "linked_in_ad_program_linked_in_ad_account_id_linkedin_ad_account_id_fk" FOREIGN KEY ("linked_in_ad_account_id") REFERENCES "advertising"."linkedin_ad_account"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linked_in_ad_program" ADD CONSTRAINT "linked_in_ad_program_ad_format_id_linked_in_ad_format_id_fk" FOREIGN KEY ("ad_format_id") REFERENCES "advertising"."linked_in_ad_format"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_ad_program_creative" ADD CONSTRAINT "linkedin_ad_program_creative_linkedin_ad_program_id_linked_in_ad_program_id_fk" FOREIGN KEY ("linkedin_ad_program_id") REFERENCES "advertising"."linked_in_ad_program"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_ad_program_creative" ADD CONSTRAINT "linkedin_ad_program_creative_linkedin_ad_creative_id_adCreative_id_fk" FOREIGN KEY ("linkedin_ad_creative_id") REFERENCES "advertising"."adCreative"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linked_in_ad_segment" ADD CONSTRAINT "linked_in_ad_segment_linked_in_ad_program_id_linked_in_ad_program_id_fk" FOREIGN KEY ("linked_in_ad_program_id") REFERENCES "advertising"."linked_in_ad_program"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linked_in_ad_segment" ADD CONSTRAINT "linked_in_ad_segment_segment_id_segment_id_fk" FOREIGN KEY ("segment_id") REFERENCES "core"."segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_ad_segment_conversation_base_copy" ADD CONSTRAINT "linkedin_ad_segment_conversation_base_copy_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_ad_segment_social_post_base_copy" ADD CONSTRAINT "linkedin_ad_segment_social_post_base_copy_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_ad_segment_value_prop" ADD CONSTRAINT "linkedin_ad_segment_value_prop_linkedin_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("linkedin_ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linked_in_audience" ADD CONSTRAINT "linked_in_audience_linkedin_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("linkedin_ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_audience_exclude_entity" ADD CONSTRAINT "linkedin_audience_exclude_entity_linkedin_audience_id_linked_in_audience_id_fk" FOREIGN KEY ("linkedin_audience_id") REFERENCES "advertising"."linked_in_audience"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_audience_exclude_entity" ADD CONSTRAINT "linkedin_audience_exclude_entity_linkedin_audience_entity_urn_linked_in_audience_entity_entity_urn_fk" FOREIGN KEY ("linkedin_audience_entity_urn") REFERENCES "advertising"."linked_in_audience_entity"("entity_urn") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linked_in_audience_or_group" ADD CONSTRAINT "linked_in_audience_or_group_linked_in_audience_id_linked_in_audience_id_fk" FOREIGN KEY ("linked_in_audience_id") REFERENCES "advertising"."linked_in_audience"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linked_in_audience_or_group_entity" ADD CONSTRAINT "linked_in_audience_or_group_entity_linked_in_audience_or_group_id_linked_in_audience_or_group_id_fk" FOREIGN KEY ("linked_in_audience_or_group_id") REFERENCES "advertising"."linked_in_audience_or_group"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linked_in_audience_or_group_entity" ADD CONSTRAINT "linked_in_audience_or_group_entity_linked_in_audience_entity_urn_linked_in_audience_entity_entity_urn_fk" FOREIGN KEY ("linked_in_audience_entity_urn") REFERENCES "advertising"."linked_in_audience_entity"("entity_urn") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linked_in_audience_entity" ADD CONSTRAINT "linked_in_audience_entity_facet_urn_linked_in_facet_facet_urn_fk" FOREIGN KEY ("facet_urn") REFERENCES "advertising"."linked_in_facet"("facet_urn") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."segment_linked_in_audience_or_group_entity" ADD CONSTRAINT "segment_linked_in_audience_or_group_entity_segment_linked_in_audience_or_group_id_segment_linked_in_audience_and_group_id_fk" FOREIGN KEY ("segment_linked_in_audience_or_group_id") REFERENCES "advertising"."segment_linked_in_audience_and_group"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."segment_linked_in_audience_or_group_entity" ADD CONSTRAINT "segment_linked_in_audience_or_group_entity_linked_in_audience_entity_urn_linked_in_audience_entity_entity_urn_fk" FOREIGN KEY ("linked_in_audience_entity_urn") REFERENCES "advertising"."linked_in_audience_entity"("entity_urn") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."segment_linkedin_audience_exclude_entity" ADD CONSTRAINT "segment_linkedin_audience_exclude_entity_segment_linked_in_audience_prefab_id_segment_linked_in_audience_prefab_id_fk" FOREIGN KEY ("segment_linked_in_audience_prefab_id") REFERENCES "advertising"."segment_linked_in_audience_prefab"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."segment_linkedin_audience_exclude_entity" ADD CONSTRAINT "segment_linkedin_audience_exclude_entity_linkedin_audience_entity_urn_linked_in_audience_entity_entity_urn_fk" FOREIGN KEY ("linkedin_audience_entity_urn") REFERENCES "advertising"."linked_in_audience_entity"("entity_urn") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."segment_linked_in_audience_and_group" ADD CONSTRAINT "segment_linked_in_audience_and_group_segment_linked_in_audience_prefab_id_segment_linked_in_audience_prefab_id_fk" FOREIGN KEY ("segment_linked_in_audience_prefab_id") REFERENCES "advertising"."segment_linked_in_audience_prefab"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_campaign" ADD CONSTRAINT "linkedin_campaign_linkedin_audience_id_linked_in_audience_id_fk" FOREIGN KEY ("linkedin_audience_id") REFERENCES "advertising"."linked_in_audience"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_campaign_group" ADD CONSTRAINT "linkedin_campaign_group_linkedin_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("linkedin_ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config" ADD CONSTRAINT "linkedin_deployment_config_linked_in_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("linked_in_ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_campaign" ADD CONSTRAINT "linkedin_deployment_config_campaign_linkedin_deployment_config_id_linkedin_deployment_config_id_fk" FOREIGN KEY ("linkedin_deployment_config_id") REFERENCES "advertising"."linkedin_deployment_config"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_campaign" ADD CONSTRAINT "linkedin_deployment_config_campaign_campaign_id_linkedin_campaign_linkedin_audience_id_fk" FOREIGN KEY ("campaign_id") REFERENCES "advertising"."linkedin_campaign"("linkedin_audience_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_sponsored_creative" ADD CONSTRAINT "linkedin_deployment_config_sponsored_creative_linkedin_deployment_config_id_linkedin_deployment_config_id_fk" FOREIGN KEY ("linkedin_deployment_config_id") REFERENCES "advertising"."linkedin_deployment_config"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_sponsored_creative" ADD CONSTRAINT "linkedin_deployment_config_sponsored_creative_sponsored_creative_id_linkedInSponsoredCreative_id_fk" FOREIGN KEY ("sponsored_creative_id") REFERENCES "advertising"."linkedInSponsoredCreative"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedInPost" ADD CONSTRAINT "linkedInPost_single_image_post_id_linkedInSingleImagePost_id_fk" FOREIGN KEY ("single_image_post_id") REFERENCES "advertising"."linkedInSingleImagePost"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedInPost" ADD CONSTRAINT "linkedInPost_single_video_post_id_linkedin_single_video_post_id_fk" FOREIGN KEY ("single_video_post_id") REFERENCES "advertising"."linkedin_single_video_post"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedInPost" ADD CONSTRAINT "linkedInPost_single_document_post_id_linkedin_single_document_post_id_fk" FOREIGN KEY ("single_document_post_id") REFERENCES "advertising"."linkedin_single_document_post"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_single_document_post" ADD CONSTRAINT "linkedin_single_document_post_ad_program_creative_id_linkedin_ad_program_creative_id_fk" FOREIGN KEY ("ad_program_creative_id") REFERENCES "advertising"."linkedin_ad_program_creative"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_single_document_post" ADD CONSTRAINT "singleDocumentPostvaluePropAndCopyTypeFk" FOREIGN KEY ("linkedin_ad_segment_value_prop","social_post_copy_type") REFERENCES "advertising"."social_post_copy"("linkedin_ad_segment_value_prop_id","social_post_copy_type") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_single_document_post" ADD CONSTRAINT "singleDocumentPostvaluePropAndCtaTypeFk" FOREIGN KEY ("linkedin_ad_segment_value_prop","call_to_action_copy_type") REFERENCES "advertising"."social_post_call_to_action_copy"("ad_segment_value_prop_id","type") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedInSingleImagePost" ADD CONSTRAINT "linkedInSingleImagePost_ad_program_creative_id_linkedin_ad_program_creative_id_fk" FOREIGN KEY ("ad_program_creative_id") REFERENCES "advertising"."linkedin_ad_program_creative"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedInSingleImagePost" ADD CONSTRAINT "singleImagePostvaluePropAndCopyTypeFk" FOREIGN KEY ("linkedin_ad_segment_value_prop","social_post_copy_type") REFERENCES "advertising"."social_post_copy"("linkedin_ad_segment_value_prop_id","social_post_copy_type") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedInSingleImagePost" ADD CONSTRAINT "singleImagePostvaluePropAndCtaTypeFk" FOREIGN KEY ("linkedin_ad_segment_value_prop","call_to_action_copy_type") REFERENCES "advertising"."social_post_call_to_action_copy"("ad_segment_value_prop_id","type") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_single_video_post" ADD CONSTRAINT "linkedin_single_video_post_ad_program_creative_id_linkedin_ad_program_creative_id_fk" FOREIGN KEY ("ad_program_creative_id") REFERENCES "advertising"."linkedin_ad_program_creative"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_single_video_post" ADD CONSTRAINT "singleImagePostvaluePropAndCopyTypeFk" FOREIGN KEY ("linkedin_ad_segment_value_prop","social_post_copy_type") REFERENCES "advertising"."social_post_copy"("linkedin_ad_segment_value_prop_id","social_post_copy_type") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_single_video_post" ADD CONSTRAINT "linkedin_single_video_post_linkedin_ad_segment_value_prop_call_to_action_copy_type_social_post_call_to_action_copy_ad_segment_value_prop_id_type_fk" FOREIGN KEY ("linkedin_ad_segment_value_prop","call_to_action_copy_type") REFERENCES "advertising"."social_post_call_to_action_copy"("ad_segment_value_prop_id","type") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedInSponsoredCreative" ADD CONSTRAINT "linkedInSponsoredCreative_linkedin_campaign_id_linkedin_campaign_linkedin_audience_id_fk" FOREIGN KEY ("linkedin_campaign_id") REFERENCES "advertising"."linkedin_campaign"("linkedin_audience_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedInSponsoredCreative" ADD CONSTRAINT "linkedInSponsoredCreative_linkedin_post_id_linkedInPost_id_fk" FOREIGN KEY ("linkedin_post_id") REFERENCES "advertising"."linkedInPost"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedInSponsoredCreative" ADD CONSTRAINT "linkedInSponsoredCreative_deprecated_conversation_copy_id_deprecated_conversation_copy_id_fk" FOREIGN KEY ("deprecated_conversation_copy_id") REFERENCES "advertising"."deprecated_conversation_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedInSponsoredCreative" ADD CONSTRAINT "linkedInSponsoredCreative_conversation_call_to_action_id_conversation_call_to_action_copy_id_fk" FOREIGN KEY ("conversation_call_to_action_id") REFERENCES "advertising"."conversation_call_to_action_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_user" ADD CONSTRAINT "linkedin_user_organization_user_id_organization_user_user_id_fk" FOREIGN KEY ("organization_user_id") REFERENCES "core"."organization_user"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_user_to_ad_account" ADD CONSTRAINT "linkedin_user_to_ad_account_linkedin_ad_account_id_linkedin_ad_account_id_fk" FOREIGN KEY ("linkedin_ad_account_id") REFERENCES "advertising"."linkedin_ad_account"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_user_to_ad_account" ADD CONSTRAINT "linkedin_user_to_ad_account_linkedin_user_id_linkedin_user_organization_user_id_fk" FOREIGN KEY ("linkedin_user_id") REFERENCES "advertising"."linkedin_user"("organization_user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD CONSTRAINT "manual_bidding_event_campaign_id_linkedin_campaign_linkedin_audience_id_fk" FOREIGN KEY ("campaign_id") REFERENCES "advertising"."linkedin_campaign"("linkedin_audience_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."organizationConversion" ADD CONSTRAINT "organizationConversion_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."positioning" ADD CONSTRAINT "positioning_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."segment_linked_in_audience_prefab" ADD CONSTRAINT "segment_linked_in_audience_prefab_segment_id_segment_id_fk" FOREIGN KEY ("segment_id") REFERENCES "core"."segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."social_post_call_to_action_copy" ADD CONSTRAINT "social_post_call_to_action_copy_ad_segment_value_prop_id_linkedin_ad_segment_value_prop_id_fk" FOREIGN KEY ("ad_segment_value_prop_id") REFERENCES "advertising"."linkedin_ad_segment_value_prop"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."social_post_call_to_action_copy" ADD CONSTRAINT "social_post_call_to_action_copy_ad_segment_value_prop_id_linked" FOREIGN KEY ("ad_segment_value_prop_id") REFERENCES "advertising"."linkedin_ad_segment_value_prop"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."social_post_copy" ADD CONSTRAINT "social_post_copy_linkedin_ad_segment_value_prop_id_linkedin_ad_segment_value_prop_id_fk" FOREIGN KEY ("linkedin_ad_segment_value_prop_id") REFERENCES "advertising"."linkedin_ad_segment_value_prop"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."social_post_copy" ADD CONSTRAINT "social_post_copy_social_post_copy_type_social_post_copy_type_name_fk" FOREIGN KEY ("social_post_copy_type") REFERENCES "advertising"."social_post_copy_type"("name") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."social_post_style_guide" ADD CONSTRAINT "social_post_style_guide_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."stage" ADD CONSTRAINT "stage_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."stage_step" ADD CONSTRAINT "stage_step_stage_id_stage_id_fk" FOREIGN KEY ("stage_id") REFERENCES "advertising"."stage"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."value_prop_test_round" ADD CONSTRAINT "value_prop_test_round_step_id_stage_step_id_fk" FOREIGN KEY ("step_id") REFERENCES "advertising"."stage_step"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core"."organization_user" ADD CONSTRAINT "organization_user_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "core"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core"."organization_user" ADD CONSTRAINT "organization_user_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core"."segment" ADD CONSTRAINT "segment_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core"."segment" ADD CONSTRAINT "segment_job_function_id_job_function_id_fk" FOREIGN KEY ("job_function_id") REFERENCES "core"."job_function"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core"."segment" ADD CONSTRAINT "segment_job_seniority_id_job_seniority_id_fk" FOREIGN KEY ("job_seniority_id") REFERENCES "core"."job_seniority"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core"."segment_value_prop" ADD CONSTRAINT "segment_value_prop_segment_id_segment_id_fk" FOREIGN KEY ("segment_id") REFERENCES "core"."segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core"."segment_vertical" ADD CONSTRAINT "segment_vertical_segment_id_segment_id_fk" FOREIGN KEY ("segment_id") REFERENCES "core"."segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."account" ADD CONSTRAINT "account_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."account" ADD CONSTRAINT "account_owner_id_crm_user_id_fk" FOREIGN KEY ("owner_id") REFERENCES "crm"."crm_user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."avoma_credentials" ADD CONSTRAINT "avoma_credentials_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."contact" ADD CONSTRAINT "contact_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."contact" ADD CONSTRAINT "contact_account_id_account_id_fk" FOREIGN KEY ("account_id") REFERENCES "crm"."account"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."contact" ADD CONSTRAINT "contact_job_function_id_job_function_id_fk" FOREIGN KEY ("job_function_id") REFERENCES "core"."job_function"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."contact" ADD CONSTRAINT "contact_job_seniority_id_job_seniority_id_fk" FOREIGN KEY ("job_seniority_id") REFERENCES "core"."job_seniority"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."crm_user" ADD CONSTRAINT "crm_user_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."hubspot_credentials" ADD CONSTRAINT "hubspot_credentials_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."hubspot_credentials" ADD CONSTRAINT "hubspot_credentials_organization_user_id_organization_user_user_id_fk" FOREIGN KEY ("organization_user_id") REFERENCES "core"."organization_user"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."opportunity_primary_contact" ADD CONSTRAINT "opportunity_primary_contact_opportunity_id_opportunity_id_fk" FOREIGN KEY ("opportunity_id") REFERENCES "crm"."opportunity"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."opportunity_primary_contact" ADD CONSTRAINT "opportunity_primary_contact_contact_id_contact_id_fk" FOREIGN KEY ("contact_id") REFERENCES "crm"."contact"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."opportunity" ADD CONSTRAINT "opportunity_account_id_account_id_fk" FOREIGN KEY ("account_id") REFERENCES "crm"."account"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."opportunity" ADD CONSTRAINT "opportunity_owner_id_crm_user_id_fk" FOREIGN KEY ("owner_id") REFERENCES "crm"."crm_user"("id") ON DELETE no action ON UPDATE no action;