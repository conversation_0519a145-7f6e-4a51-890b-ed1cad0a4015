ALTER TYPE "public"."ab_test_status" ADD VALUE 'AUTO_RESOLVED';--> statement-breakpoint
ALTER TYPE "public"."ab_test_status" ADD VALUE 'USER_RESOLVED';--> statement-breakpoint
CREATE TABLE "advertising"."audience_ab_test_round_day_metrics" (
	"ab_test_round_day_id" varchar(36) PRIMARY KEY NOT NULL,
	"current_best_primary_metric_for_day" real NOT NULL,
	"current_best_secondary_metric_for_day" real NOT NULL,
	"contender_primary_metric_count_for_day" real NOT NULL,
	"contender_secondary_metric_count_for_day" real NOT NULL,
	"current_best_primary_metric_mean" real NOT NULL,
	"current_best_secondary_metric_mean" real NOT NULL,
	"contender_primary_metric_mean" real NOT NULL,
	"contender_secondary_metric_mean" real NOT NULL,
	"current_best_primary_metric_sum" real NOT NULL,
	"current_best_secondary_metric_sum" real NOT NULL,
	"contender_primary_metric_sum" real NOT NULL,
	"contender_secondary_metric_sum" real NOT NULL,
	"primary_metric_t_test_result" real,
	"secondary_metric_t_test_result" real,
	"decision" varchar(255) NOT NULL,
	"decision_type" varchar(255) NOT NULL,
	"created_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."conversationCallToAction_ab_test_round_day_metrics" (
	"ab_test_round_day_id" varchar(36) PRIMARY KEY NOT NULL,
	"current_best_primary_metric_for_day" real NOT NULL,
	"current_best_secondary_metric_for_day" real NOT NULL,
	"contender_primary_metric_count_for_day" real NOT NULL,
	"contender_secondary_metric_count_for_day" real NOT NULL,
	"current_best_primary_metric_mean" real NOT NULL,
	"current_best_secondary_metric_mean" real NOT NULL,
	"contender_primary_metric_mean" real NOT NULL,
	"contender_secondary_metric_mean" real NOT NULL,
	"current_best_primary_metric_sum" real NOT NULL,
	"current_best_secondary_metric_sum" real NOT NULL,
	"contender_primary_metric_sum" real NOT NULL,
	"contender_secondary_metric_sum" real NOT NULL,
	"primary_metric_t_test_result" real,
	"secondary_metric_t_test_result" real,
	"decision" varchar(255) NOT NULL,
	"decision_type" varchar(255) NOT NULL,
	"created_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."conversationMessageCopy_ab_test_round_day_metrics" (
	"ab_test_round_day_id" varchar(36) PRIMARY KEY NOT NULL,
	"current_best_primary_metric_for_day" real NOT NULL,
	"current_best_secondary_metric_for_day" real NOT NULL,
	"contender_primary_metric_count_for_day" real NOT NULL,
	"contender_secondary_metric_count_for_day" real NOT NULL,
	"current_best_primary_metric_mean" real NOT NULL,
	"current_best_secondary_metric_mean" real NOT NULL,
	"contender_primary_metric_mean" real NOT NULL,
	"contender_secondary_metric_mean" real NOT NULL,
	"current_best_primary_metric_sum" real NOT NULL,
	"current_best_secondary_metric_sum" real NOT NULL,
	"contender_primary_metric_sum" real NOT NULL,
	"contender_secondary_metric_sum" real NOT NULL,
	"primary_metric_t_test_result" real,
	"secondary_metric_t_test_result" real,
	"decision" varchar(255) NOT NULL,
	"decision_type" varchar(255) NOT NULL,
	"created_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."conversationSubject_ab_test_round_day_metrics" (
	"ab_test_round_day_id" varchar(36) PRIMARY KEY NOT NULL,
	"current_best_primary_metric_for_day" real NOT NULL,
	"current_best_secondary_metric_for_day" real NOT NULL,
	"contender_primary_metric_count_for_day" real NOT NULL,
	"contender_secondary_metric_count_for_day" real NOT NULL,
	"current_best_primary_metric_mean" real NOT NULL,
	"current_best_secondary_metric_mean" real NOT NULL,
	"contender_primary_metric_mean" real NOT NULL,
	"contender_secondary_metric_mean" real NOT NULL,
	"current_best_primary_metric_sum" real NOT NULL,
	"current_best_secondary_metric_sum" real NOT NULL,
	"contender_primary_metric_sum" real NOT NULL,
	"contender_secondary_metric_sum" real NOT NULL,
	"primary_metric_t_test_result" real,
	"secondary_metric_t_test_result" real,
	"decision" varchar(255) NOT NULL,
	"decision_type" varchar(255) NOT NULL,
	"created_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."creative_ab_test_round_day_metrics" (
	"ab_test_round_day_id" varchar(36) PRIMARY KEY NOT NULL,
	"current_best_primary_metric_for_day" real NOT NULL,
	"current_best_secondary_metric_for_day" real NOT NULL,
	"contender_primary_metric_count_for_day" real NOT NULL,
	"contender_secondary_metric_count_for_day" real NOT NULL,
	"current_best_primary_metric_mean" real NOT NULL,
	"current_best_secondary_metric_mean" real NOT NULL,
	"contender_primary_metric_mean" real NOT NULL,
	"contender_secondary_metric_mean" real NOT NULL,
	"current_best_primary_metric_sum" real NOT NULL,
	"current_best_secondary_metric_sum" real NOT NULL,
	"contender_primary_metric_sum" real NOT NULL,
	"contender_secondary_metric_sum" real NOT NULL,
	"primary_metric_t_test_result" real,
	"secondary_metric_t_test_result" real,
	"decision" varchar(255) NOT NULL,
	"decision_type" varchar(255) NOT NULL,
	"created_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."socialPostBodyCopy_ab_test_round_day_metrics" (
	"ab_test_round_day_id" varchar(36) PRIMARY KEY NOT NULL,
	"current_best_primary_metric_for_day" real NOT NULL,
	"current_best_secondary_metric_for_day" real NOT NULL,
	"contender_primary_metric_count_for_day" real NOT NULL,
	"contender_secondary_metric_count_for_day" real NOT NULL,
	"current_best_primary_metric_mean" real NOT NULL,
	"current_best_secondary_metric_mean" real NOT NULL,
	"contender_primary_metric_mean" real NOT NULL,
	"contender_secondary_metric_mean" real NOT NULL,
	"current_best_primary_metric_sum" real NOT NULL,
	"current_best_secondary_metric_sum" real NOT NULL,
	"contender_primary_metric_sum" real NOT NULL,
	"contender_secondary_metric_sum" real NOT NULL,
	"primary_metric_t_test_result" real,
	"secondary_metric_t_test_result" real,
	"decision" varchar(255) NOT NULL,
	"decision_type" varchar(255) NOT NULL,
	"created_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."socialPostCallToAction_ab_test_round_day_metrics" (
	"ab_test_round_day_id" varchar(36) PRIMARY KEY NOT NULL,
	"current_best_primary_metric_for_day" real NOT NULL,
	"current_best_secondary_metric_for_day" real NOT NULL,
	"contender_primary_metric_count_for_day" real NOT NULL,
	"contender_secondary_metric_count_for_day" real NOT NULL,
	"current_best_primary_metric_mean" real NOT NULL,
	"current_best_secondary_metric_mean" real NOT NULL,
	"contender_primary_metric_mean" real NOT NULL,
	"contender_secondary_metric_mean" real NOT NULL,
	"current_best_primary_metric_sum" real NOT NULL,
	"current_best_secondary_metric_sum" real NOT NULL,
	"contender_primary_metric_sum" real NOT NULL,
	"contender_secondary_metric_sum" real NOT NULL,
	"primary_metric_t_test_result" real,
	"secondary_metric_t_test_result" real,
	"decision" varchar(255) NOT NULL,
	"decision_type" varchar(255) NOT NULL,
	"created_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."valueProp_ab_test_round_day_metrics" (
	"ab_test_round_day_id" varchar(36) PRIMARY KEY NOT NULL,
	"current_best_primary_metric_for_day" real NOT NULL,
	"current_best_secondary_metric_for_day" real NOT NULL,
	"contender_primary_metric_count_for_day" real NOT NULL,
	"contender_secondary_metric_count_for_day" real NOT NULL,
	"current_best_primary_metric_mean" real NOT NULL,
	"current_best_secondary_metric_mean" real NOT NULL,
	"contender_primary_metric_mean" real NOT NULL,
	"contender_secondary_metric_mean" real NOT NULL,
	"current_best_primary_metric_sum" real NOT NULL,
	"current_best_secondary_metric_sum" real NOT NULL,
	"contender_primary_metric_sum" real NOT NULL,
	"contender_secondary_metric_sum" real NOT NULL,
	"primary_metric_t_test_result" real,
	"secondary_metric_t_test_result" real,
	"decision" varchar(255) NOT NULL,
	"decision_type" varchar(255) NOT NULL,
	"created_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."notification_log" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"organization_id" bigint NOT NULL,
	"notification_type" varchar(50) NOT NULL,
	"week_start_date" date NOT NULL,
	"week_end_date" date NOT NULL,
	"sent_at" timestamp DEFAULT now() NOT NULL,
	"status" varchar(20) DEFAULT 'sent' NOT NULL,
	"slack_message_ts" varchar(50),
	"campaign_data" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
-- ALTER TABLE "advertising"."audience_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_audience";--> statement-breakpoint
-- ALTER TABLE "advertising"."audience_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_cb_audience";--> statement-breakpoint
-- ALTER TABLE "advertising"."audience_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_contender_audience";--> statement-breakpoint
-- ALTER TABLE "advertising"."audience_ab_test_round" DROP CONSTRAINT "ab_test_round_status_constraint_audience";--> statement-breakpoint
-- ALTER TABLE "advertising"."audience_ab_test" DROP CONSTRAINT "ab_test_status_constraint_audience";--> statement-breakpoint
ALTER TABLE "advertising"."conversationCallToAction_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_conversationCallToAction";--> statement-breakpoint
ALTER TABLE "advertising"."conversationCallToAction_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_cb_conversationCallToAction";--> statement-breakpoint
ALTER TABLE "advertising"."conversationCallToAction_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_contender_conversationCallToAction";--> statement-breakpoint
ALTER TABLE "advertising"."conversationCallToAction_ab_test_round" DROP CONSTRAINT "ab_test_round_status_constraint_conversationCallToAction";--> statement-breakpoint
ALTER TABLE "advertising"."conversationCallToAction_ab_test" DROP CONSTRAINT "ab_test_status_constraint_conversationCallToAction";--> statement-breakpoint
ALTER TABLE "advertising"."conversationMessageCopy_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_conversationMessageCopy";--> statement-breakpoint
ALTER TABLE "advertising"."conversationMessageCopy_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_cb_conversationMessageCopy";--> statement-breakpoint
ALTER TABLE "advertising"."conversationMessageCopy_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_contender_conversationMessageCopy";--> statement-breakpoint
ALTER TABLE "advertising"."conversationMessageCopy_ab_test_round" DROP CONSTRAINT "ab_test_round_status_constraint_conversationMessageCopy";--> statement-breakpoint
ALTER TABLE "advertising"."conversationMessageCopy_ab_test" DROP CONSTRAINT "ab_test_status_constraint_conversationMessageCopy";--> statement-breakpoint
-- ALTER TABLE "advertising"."conversationSubject_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_conversationSubject";--> statement-breakpoint
-- ALTER TABLE "advertising"."conversationSubject_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_cb_conversationSubject";--> statement-breakpoint
-- ALTER TABLE "advertising"."conversationSubject_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_contender_conversationSubject";--> statement-breakpoint
-- ALTER TABLE "advertising"."conversationSubject_ab_test_round" DROP CONSTRAINT "ab_test_round_status_constraint_conversationSubject";--> statement-breakpoint
-- ALTER TABLE "advertising"."conversationSubject_ab_test" DROP CONSTRAINT "ab_test_status_constraint_conversationSubject";--> statement-breakpoint
-- ALTER TABLE "advertising"."creative_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_creative";--> statement-breakpoint
-- ALTER TABLE "advertising"."creative_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_cb_creative";--> statement-breakpoint
-- ALTER TABLE "advertising"."creative_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_contender_creative";--> statement-breakpoint
-- ALTER TABLE "advertising"."creative_ab_test_round" DROP CONSTRAINT "ab_test_round_status_constraint_creative";--> statement-breakpoint
-- ALTER TABLE "advertising"."creative_ab_test" DROP CONSTRAINT "ab_test_status_constraint_creative";--> statement-breakpoint
ALTER TABLE "advertising"."socialPostBodyCopy_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_socialPostBodyCopy";--> statement-breakpoint
ALTER TABLE "advertising"."socialPostBodyCopy_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_cb_socialPostBodyCopy";--> statement-breakpoint
ALTER TABLE "advertising"."socialPostBodyCopy_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_contender_socialPostBodyCopy";--> statement-breakpoint
ALTER TABLE "advertising"."socialPostBodyCopy_ab_test_round" DROP CONSTRAINT "ab_test_round_status_constraint_socialPostBodyCopy";--> statement-breakpoint
ALTER TABLE "advertising"."socialPostBodyCopy_ab_test" DROP CONSTRAINT "ab_test_status_constraint_socialPostBodyCopy";--> statement-breakpoint
-- ALTER TABLE "advertising"."socialPostCallToAction_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_socialPostCallToAction";--> statement-breakpoint
ALTER TABLE "advertising"."socialPostCallToAction_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_cb_socialPostCallToAction";--> statement-breakpoint
ALTER TABLE "advertising"."socialPostCallToAction_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_contender_socialPostCallToAction";--> statement-breakpoint
ALTER TABLE "advertising"."socialPostCallToAction_ab_test_round" DROP CONSTRAINT "ab_test_round_status_constraint_socialPostCallToAction";--> statement-breakpoint
ALTER TABLE "advertising"."socialPostCallToAction_ab_test" DROP CONSTRAINT "ab_test_status_constraint_socialPostCallToAction";--> statement-breakpoint
-- ALTER TABLE "advertising"."valueProp_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_valueProp";--> statement-breakpoint
-- ALTER TABLE "advertising"."valueProp_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_cb_valueProp";--> statement-breakpoint
-- ALTER TABLE "advertising"."valueProp_ab_test_round_day" DROP CONSTRAINT "ab_test_round_day_status_constraint_contender_valueProp";--> statement-breakpoint
-- ALTER TABLE "advertising"."valueProp_ab_test_round" DROP CONSTRAINT "ab_test_round_status_constraint_valueProp";--> statement-breakpoint
-- ALTER TABLE "advertising"."valueProp_ab_test" DROP CONSTRAINT "ab_test_status_constraint_valueProp";--> statement-breakpoint
ALTER TABLE "advertising"."audience_ab_test_round_day" ALTER COLUMN "winner" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "advertising"."conversationCallToAction_ab_test_round_day" ALTER COLUMN "winner" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "advertising"."conversationMessageCopy_ab_test_round_day" ALTER COLUMN "winner" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "advertising"."conversationSubject_ab_test_round_day" ALTER COLUMN "winner" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "advertising"."creative_ab_test_round_day" ALTER COLUMN "winner" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "advertising"."socialPostBodyCopy_ab_test_round_day" ALTER COLUMN "winner" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "advertising"."socialPostCallToAction_ab_test_round_day" ALTER COLUMN "winner" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "advertising"."valueProp_ab_test_round_day" ALTER COLUMN "winner" SET DATA TYPE varchar(255);--> statement-breakpoint
-- ALTER TABLE "advertising"."conversation_subject_copy" ADD COLUMN "destination_url" varchar;--> statement-breakpoint
-- ALTER TABLE "advertising"."linked_in_ad_program" ADD COLUMN "destination_url" varchar;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_campaign" ADD COLUMN "action_clicks" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_campaign" ADD COLUMN "total_engagements" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_campaign" ADD COLUMN "one_click_lead_form_opens" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_campaign" ADD COLUMN "landing_page_clicks" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_campaign" ADD COLUMN "video_completions" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_campaign" ADD COLUMN "video_first_quartile_completions" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_campaign" ADD COLUMN "video_midpoint_completions" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_campaign" ADD COLUMN "video_third_quartile_completions" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_campaign" ADD COLUMN "video_starts" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_campaign" ADD COLUMN "external_website_conversions" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_sponsored_creative" ADD COLUMN "action_clicks" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_sponsored_creative" ADD COLUMN "total_engagements" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_sponsored_creative" ADD COLUMN "one_click_lead_form_opens" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_sponsored_creative" ADD COLUMN "landing_page_clicks" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_sponsored_creative" ADD COLUMN "video_completions" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_sponsored_creative" ADD COLUMN "video_first_quartile_completions" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_sponsored_creative" ADD COLUMN "video_midpoint_completions" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_sponsored_creative" ADD COLUMN "video_third_quartile_completions" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_sponsored_creative" ADD COLUMN "video_starts" integer;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_deployment_config_sponsored_creative" ADD COLUMN "external_website_conversions" integer;--> statement-breakpoint
ALTER TABLE "core"."organization" ADD COLUMN "slack_notification_webhook_url" varchar(500);--> statement-breakpoint
ALTER TABLE "core"."organization" ADD COLUMN "slack_notifications_enabled" boolean DEFAULT true;--> statement-breakpoint
ALTER TABLE "core"."organization" ADD COLUMN "slack_notification_day_of_week" integer DEFAULT 1;--> statement-breakpoint
ALTER TABLE "core"."organization" ADD COLUMN "slack_notification_frequency" varchar(20) DEFAULT 'weekly';--> statement-breakpoint
ALTER TABLE "core"."user" ADD COLUMN "is_deleted" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."audience_ab_test_round_day_metrics" ADD CONSTRAINT "audience_ab_test_round_day_metrics_ab_test_round_day_id_audience_ab_test_round_day_id_fk" FOREIGN KEY ("ab_test_round_day_id") REFERENCES "advertising"."audience_ab_test_round_day"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationCallToAction_ab_test_round_day_metrics" ADD CONSTRAINT "conversationCallToAction_ab_test_round_day_metrics_ab_test_round_day_id_conversationCallToAction_ab_test_round_day_id_fk" FOREIGN KEY ("ab_test_round_day_id") REFERENCES "advertising"."conversationCallToAction_ab_test_round_day"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationMessageCopy_ab_test_round_day_metrics" ADD CONSTRAINT "conversationMessageCopy_ab_test_round_day_metrics_ab_test_round_day_id_conversationMessageCopy_ab_test_round_day_id_fk" FOREIGN KEY ("ab_test_round_day_id") REFERENCES "advertising"."conversationMessageCopy_ab_test_round_day"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationSubject_ab_test_round_day_metrics" ADD CONSTRAINT "conversationSubject_ab_test_round_day_metrics_ab_test_round_day_id_conversationSubject_ab_test_round_day_id_fk" FOREIGN KEY ("ab_test_round_day_id") REFERENCES "advertising"."conversationSubject_ab_test_round_day"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."creative_ab_test_round_day_metrics" ADD CONSTRAINT "creative_ab_test_round_day_metrics_ab_test_round_day_id_creative_ab_test_round_day_id_fk" FOREIGN KEY ("ab_test_round_day_id") REFERENCES "advertising"."creative_ab_test_round_day"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."socialPostBodyCopy_ab_test_round_day_metrics" ADD CONSTRAINT "socialPostBodyCopy_ab_test_round_day_metrics_ab_test_round_day_id_socialPostBodyCopy_ab_test_round_day_id_fk" FOREIGN KEY ("ab_test_round_day_id") REFERENCES "advertising"."socialPostBodyCopy_ab_test_round_day"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."socialPostCallToAction_ab_test_round_day_metrics" ADD CONSTRAINT "socialPostCallToAction_ab_test_round_day_metrics_ab_test_round_day_id_socialPostCallToAction_ab_test_round_day_id_fk" FOREIGN KEY ("ab_test_round_day_id") REFERENCES "advertising"."socialPostCallToAction_ab_test_round_day"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."valueProp_ab_test_round_day_metrics" ADD CONSTRAINT "valueProp_ab_test_round_day_metrics_ab_test_round_day_id_valueProp_ab_test_round_day_id_fk" FOREIGN KEY ("ab_test_round_day_id") REFERENCES "advertising"."valueProp_ab_test_round_day"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."notification_log" ADD CONSTRAINT "notification_log_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;