CREATE SCHEMA "shared";
--> statement-breakpoint
CREATE TYPE "public"."lifecycle_stage" AS ENUM('subscriber', 'lead', 'marketingqualifiedlead', 'salesqualifiedlead', 'opportunity', 'customer', 'evangelist', 'other');--> statement-breakpoint
CREATE TYPE "public"."source" AS ENUM('hubspot', 'salesforce');--> statement-breakpoint
CREATE TYPE "public"."crm_type" AS ENUM('hubspot', 'salesforce');--> statement-breakpoint
CREATE TYPE "public"."engagement_status" AS ENUM('scheduled', 'completed', 'rescheduled', 'no_show', 'canceled');--> statement-breakpoint
CREATE TYPE "public"."engagement_type" AS ENUM('meeting');--> statement-breakpoint
CREATE TYPE "public"."mapping_type" AS ENUM('utm', 'first_meeting');--> statement-breakpoint
CREATE TABLE "crm"."crm_credential" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"organization_id" bigint NOT NULL,
	"organization_user_id" varchar(36) NOT NULL,
	"crm_type" "crm_type" NOT NULL,
	"access_token" varchar(1000),
	"refresh_token" varchar(1000),
	"access_token_expires_at" date,
	"refresh_token_expires_at" date,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "crm"."crm_mapping" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"organization_id" bigint NOT NULL,
	"crm_type" "crm_type",
	"mapping_definition_id" varchar(36) NOT NULL,
	"mappings" jsonb NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "crm"."engagement" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"organization_id" bigint NOT NULL,
	"contact_id" varchar(36) NOT NULL,
	"external_id" varchar(255) NOT NULL,
	"source" "source" NOT NULL,
	"type" "engagement_type" NOT NULL,
	"status" "engagement_status",
	"subject" varchar(512),
	"start_time" timestamp,
	"end_time" timestamp,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	CONSTRAINT "engagement_unique_external_id" UNIQUE("external_id","source","organization_id","contact_id")
);
--> statement-breakpoint
CREATE TABLE "crm"."mapping_definition" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"mapping_type" "mapping_type" NOT NULL,
	"fields" jsonb NOT NULL,
	"version" integer NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "crm"."salesforce_connection" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"crm_credential_id" varchar(36) NOT NULL,
	"salesforce_organization_id" text NOT NULL,
	"instance_url" text NOT NULL,
	"salesforce_user_id" text NOT NULL,
	"last_synced_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "shared"."error_log" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"organization_id" bigint,
	"error_type" varchar(50) NOT NULL,
	"title" varchar(500) NOT NULL,
	"description" text NOT NULL,
	"endpoint" varchar(200),
	"method" varchar(10),
	"status_code" varchar(10),
	"metadata" json,
	"severity" varchar(20) DEFAULT 'MEDIUM',
	"is_processed" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_lead_form_lead" ADD COLUMN "contact_id" varchar(36);--> statement-breakpoint
ALTER TABLE "core"."organization" ADD COLUMN "slack_lead_notifications_enabled" boolean DEFAULT true;--> statement-breakpoint
ALTER TABLE "crm"."contact" ADD COLUMN "linkedin_profile_link" text;--> statement-breakpoint
ALTER TABLE "crm"."contact" ADD COLUMN "utm_params" jsonb;--> statement-breakpoint
ALTER TABLE "crm"."contact" ADD COLUMN "page_tracking" jsonb;--> statement-breakpoint
ALTER TABLE "crm"."contact" ADD COLUMN "source" "source";--> statement-breakpoint
ALTER TABLE "crm"."contact" ADD COLUMN "company_name" varchar(255);--> statement-breakpoint
ALTER TABLE "crm"."contact" ADD COLUMN "lifecycle_stage" "lifecycle_stage";--> statement-breakpoint
ALTER TABLE "crm"."contact" ADD COLUMN "lead_created_at" timestamp;--> statement-breakpoint
ALTER TABLE "crm"."contact" ADD COLUMN "linkedin_campaign_group_urn" varchar(255);--> statement-breakpoint
ALTER TABLE "crm"."contact" ADD COLUMN "linkedin_sponsored_creative_urn" varchar(255);--> statement-breakpoint
ALTER TABLE "crm"."crm_credential" ADD CONSTRAINT "crm_credential_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."crm_credential" ADD CONSTRAINT "crm_credential_organization_user_id_organization_user_user_id_fk" FOREIGN KEY ("organization_user_id") REFERENCES "core"."organization_user"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."crm_mapping" ADD CONSTRAINT "crm_mapping_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."crm_mapping" ADD CONSTRAINT "crm_mapping_mapping_definition_id_mapping_definition_id_fk" FOREIGN KEY ("mapping_definition_id") REFERENCES "crm"."mapping_definition"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."crm_mapping" ADD CONSTRAINT "unique_org_crmtype_mappingdef" UNIQUE (organization_id, crm_type, mapping_definition_id);
ALTER TABLE "crm"."engagement" ADD CONSTRAINT "engagement_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "core"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."engagement" ADD CONSTRAINT "engagement_contact_id_contact_id_fk" FOREIGN KEY ("contact_id") REFERENCES "crm"."contact"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crm"."salesforce_connection" ADD CONSTRAINT "salesforce_connection_crm_credential_id_crm_credential_id_fk" FOREIGN KEY ("crm_credential_id") REFERENCES "crm"."crm_credential"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_lead_form_lead" ADD CONSTRAINT "linkedin_lead_form_lead_contact_id_contact_id_fk" FOREIGN KEY ("contact_id") REFERENCES "crm"."contact"("id") ON DELETE no action ON UPDATE no action;