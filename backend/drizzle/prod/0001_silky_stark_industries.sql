CREATE TYPE "public"."linkedin_lead_form_state" AS ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED');--> statement-breakpoint
CREATE TABLE "advertising"."linkedin_lead_form" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"linkedin_ad_account_id" varchar(36) NOT NULL,
	"lead_gen_form_urn" varchar(255) NOT NULL,
	"version" integer DEFAULT 1 NOT NULL,
	"name" text NOT NULL,
	"state" "linkedin_lead_form_state" DEFAULT 'DRAFT' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "advertising"."linkedin_lead_form_lead" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"linkedin_lead_form_response_id" varchar(255) NOT NULL,
	"linkedin_lead_form_id" varchar(36) NOT NULL,
	"linkedin_campaign_urn" varchar(255),
	"linkedin_ad_account_id" varchar(36) NOT NULL,
	"first_name" text,
	"last_name" text,
	"phone_number" text,
	"email" text,
	"city" text,
	"state" text,
	"country" text,
	"zip_code" text,
	"job_title" text,
	"job_function" text,
	"seniority" text,
	"company_name" text,
	"company_size" text,
	"industry" text,
	"degree" text,
	"field_of_study" text,
	"school" text,
	"start_date" date,
	"graduation_date" date,
	"gender" text,
	"work_email" text,
	"linkedin_profile_link" text,
	"work_phone_number" text,
	"lead_created_at" timestamp,
	"lead_type" text,
	"test_lead" boolean DEFAULT false,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);
--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_lead_form" ADD CONSTRAINT "linkedin_lead_form_linkedin_ad_account_id_linkedin_ad_account_id_fk" FOREIGN KEY ("linkedin_ad_account_id") REFERENCES "advertising"."linkedin_ad_account"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_lead_form_lead" ADD CONSTRAINT "linkedin_lead_form_lead_linkedin_lead_form_id_linkedin_lead_form_id_fk" FOREIGN KEY ("linkedin_lead_form_id") REFERENCES "advertising"."linkedin_lead_form"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_lead_form_lead" ADD CONSTRAINT "linkedin_lead_form_lead_linkedin_ad_account_id_linkedin_ad_account_id_fk" FOREIGN KEY ("linkedin_ad_account_id") REFERENCES "advertising"."linkedin_ad_account"("id") ON DELETE no action ON UPDATE no action;