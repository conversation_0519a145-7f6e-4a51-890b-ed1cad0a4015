CREATE TABLE "advertising"."conversationCallToAction_ab_test_round_day" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ab_test_round_id" varchar(36) NOT NULL,
	"day_index" smallint NOT NULL,
	"winner" "ab_test_round_winner",
	"status" "ab_test_status" NOT NULL,
	"current_best_result" smallint,
	"contender_result" smallint,
	"deployment_config_id" varchar(36) NOT NULL,
	CONSTRAINT "ab_test_round_day_status_constraint_conversationCallToAction" CHECK (("advertising"."conversationCallToAction_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."conversationCallToAction_ab_test_round_day"."winner" IS NOT NULL)),
	CONSTRAINT "ab_test_round_day_status_constraint_cb_conversationCallToAction" CHECK (("advertising"."conversationCallToAction_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."conversationCallToAction_ab_test_round_day"."current_best_result" IS NOT NULL)),
	CONSTRAINT "ab_test_round_day_status_constraint_contender_conversationCallToAction" CHECK (("advertising"."conversationCallToAction_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."conversationCallToAction_ab_test_round_day"."contender_result" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."conversationCallToAction_ab_test_round" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ab_test_id" varchar(36) NOT NULL,
	"status" "ab_test_status" NOT NULL,
	"round_index" smallint NOT NULL,
	"current_best_id" varchar(36) NOT NULL,
	"contender_id" varchar(36) NOT NULL,
	"winner" "ab_test_round_winner",
	CONSTRAINT "ab_test_round_status_constraint_conversationCallToAction" CHECK (("advertising"."conversationCallToAction_ab_test_round"."status" != 'COMPLETED') OR ("advertising"."conversationCallToAction_ab_test_round"."winner" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."conversationCallToAction_ab_test" (
	"stage_id" varchar(36) PRIMARY KEY NOT NULL,
	"status" "ab_test_status" NOT NULL,
	"winner_id" varchar(36),
	CONSTRAINT "ab_test_status_constraint_conversationCallToAction" CHECK (("advertising"."conversationCallToAction_ab_test"."status" != 'COMPLETED') OR ("advertising"."conversationCallToAction_ab_test"."winner_id" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."conversationMessageCopy_ab_test_round_day" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ab_test_round_id" varchar(36) NOT NULL,
	"day_index" smallint NOT NULL,
	"winner" "ab_test_round_winner",
	"status" "ab_test_status" NOT NULL,
	"current_best_result" smallint,
	"contender_result" smallint,
	"deployment_config_id" varchar(36) NOT NULL,
	CONSTRAINT "ab_test_round_day_status_constraint_conversationMessageCopy" CHECK (("advertising"."conversationMessageCopy_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."conversationMessageCopy_ab_test_round_day"."winner" IS NOT NULL)),
	CONSTRAINT "ab_test_round_day_status_constraint_cb_conversationMessageCopy" CHECK (("advertising"."conversationMessageCopy_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."conversationMessageCopy_ab_test_round_day"."current_best_result" IS NOT NULL)),
	CONSTRAINT "ab_test_round_day_status_constraint_contender_conversationMessageCopy" CHECK (("advertising"."conversationMessageCopy_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."conversationMessageCopy_ab_test_round_day"."contender_result" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."conversationMessageCopy_ab_test_round" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ab_test_id" varchar(36) NOT NULL,
	"status" "ab_test_status" NOT NULL,
	"round_index" smallint NOT NULL,
	"current_best_id" varchar(36) NOT NULL,
	"contender_id" varchar(36) NOT NULL,
	"winner" "ab_test_round_winner",
	CONSTRAINT "ab_test_round_status_constraint_conversationMessageCopy" CHECK (("advertising"."conversationMessageCopy_ab_test_round"."status" != 'COMPLETED') OR ("advertising"."conversationMessageCopy_ab_test_round"."winner" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."conversationMessageCopy_ab_test" (
	"stage_id" varchar(36) PRIMARY KEY NOT NULL,
	"status" "ab_test_status" NOT NULL,
	"winner_id" varchar(36),
	CONSTRAINT "ab_test_status_constraint_conversationMessageCopy" CHECK (("advertising"."conversationMessageCopy_ab_test"."status" != 'COMPLETED') OR ("advertising"."conversationMessageCopy_ab_test"."winner_id" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."socialPostBodyCopy_ab_test_round_day" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ab_test_round_id" varchar(36) NOT NULL,
	"day_index" smallint NOT NULL,
	"winner" "ab_test_round_winner",
	"status" "ab_test_status" NOT NULL,
	"current_best_result" smallint,
	"contender_result" smallint,
	"deployment_config_id" varchar(36) NOT NULL,
	CONSTRAINT "ab_test_round_day_status_constraint_socialPostBodyCopy" CHECK (("advertising"."socialPostBodyCopy_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."socialPostBodyCopy_ab_test_round_day"."winner" IS NOT NULL)),
	CONSTRAINT "ab_test_round_day_status_constraint_cb_socialPostBodyCopy" CHECK (("advertising"."socialPostBodyCopy_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."socialPostBodyCopy_ab_test_round_day"."current_best_result" IS NOT NULL)),
	CONSTRAINT "ab_test_round_day_status_constraint_contender_socialPostBodyCopy" CHECK (("advertising"."socialPostBodyCopy_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."socialPostBodyCopy_ab_test_round_day"."contender_result" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."socialPostBodyCopy_ab_test_round" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ab_test_id" varchar(36) NOT NULL,
	"status" "ab_test_status" NOT NULL,
	"round_index" smallint NOT NULL,
	"current_best_id" varchar(36) NOT NULL,
	"contender_id" varchar(36) NOT NULL,
	"winner" "ab_test_round_winner",
	CONSTRAINT "ab_test_round_status_constraint_socialPostBodyCopy" CHECK (("advertising"."socialPostBodyCopy_ab_test_round"."status" != 'COMPLETED') OR ("advertising"."socialPostBodyCopy_ab_test_round"."winner" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."socialPostBodyCopy_ab_test" (
	"stage_id" varchar(36) PRIMARY KEY NOT NULL,
	"status" "ab_test_status" NOT NULL,
	"winner_id" varchar(36),
	CONSTRAINT "ab_test_status_constraint_socialPostBodyCopy" CHECK (("advertising"."socialPostBodyCopy_ab_test"."status" != 'COMPLETED') OR ("advertising"."socialPostBodyCopy_ab_test"."winner_id" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."socialPostCallToAction_ab_test_round_day" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ab_test_round_id" varchar(36) NOT NULL,
	"day_index" smallint NOT NULL,
	"winner" "ab_test_round_winner",
	"status" "ab_test_status" NOT NULL,
	"current_best_result" smallint,
	"contender_result" smallint,
	"deployment_config_id" varchar(36) NOT NULL,
	CONSTRAINT "ab_test_round_day_status_constraint_socialPostCallToAction" CHECK (("advertising"."socialPostCallToAction_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."socialPostCallToAction_ab_test_round_day"."winner" IS NOT NULL)),
	CONSTRAINT "ab_test_round_day_status_constraint_cb_socialPostCallToAction" CHECK (("advertising"."socialPostCallToAction_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."socialPostCallToAction_ab_test_round_day"."current_best_result" IS NOT NULL)),
	CONSTRAINT "ab_test_round_day_status_constraint_contender_socialPostCallToAction" CHECK (("advertising"."socialPostCallToAction_ab_test_round_day"."status" != 'COMPLETED') OR ("advertising"."socialPostCallToAction_ab_test_round_day"."contender_result" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."socialPostCallToAction_ab_test_round" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ab_test_id" varchar(36) NOT NULL,
	"status" "ab_test_status" NOT NULL,
	"round_index" smallint NOT NULL,
	"current_best_id" varchar(36) NOT NULL,
	"contender_id" varchar(36) NOT NULL,
	"winner" "ab_test_round_winner",
	CONSTRAINT "ab_test_round_status_constraint_socialPostCallToAction" CHECK (("advertising"."socialPostCallToAction_ab_test_round"."status" != 'COMPLETED') OR ("advertising"."socialPostCallToAction_ab_test_round"."winner" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."socialPostCallToAction_ab_test" (
	"stage_id" varchar(36) PRIMARY KEY NOT NULL,
	"status" "ab_test_status" NOT NULL,
	"winner_id" varchar(36),
	CONSTRAINT "ab_test_status_constraint_socialPostCallToAction" CHECK (("advertising"."socialPostCallToAction_ab_test"."status" != 'COMPLETED') OR ("advertising"."socialPostCallToAction_ab_test"."winner_id" IS NOT NULL))
);
--> statement-breakpoint
CREATE TABLE "advertising"."ad_segment_selected_conversation_call_to_action_type" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ad_segment_id" varchar(36) NOT NULL,
	"type" varchar(255) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."ad_segment_selected_conversation_message_type" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ad_segment_id" varchar(36) NOT NULL,
	"type" varchar(255) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."ad_segment_selected_social_post_body_type" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ad_segment_id" varchar(36) NOT NULL,
	"type" varchar(255) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."ad_segment_selected_social_post_call_to_action_type" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ad_segment_id" varchar(36) NOT NULL,
	"type" varchar(255) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "advertising"."create_new_conversation_value_prop_state" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"ad_segment" varchar(36) NOT NULL,
	"value_prop" varchar(255) NOT NULL,
	"conversation_subject_copy_type" varchar(255) NOT NULL,
	"conversation_subject_copy" varchar(255) NOT NULL,
	"conversation_message_copy_type" varchar(255) NOT NULL,
	"conversation_message_copy" varchar(255) NOT NULL,
	"conversation_call_to_action_copy_type" varchar(255) NOT NULL,
	"conversation_call_to_action_copy" varchar(255) NOT NULL,
	"to_be_used" boolean DEFAULT true NOT NULL,
	CONSTRAINT "cncvps_unique_adSegment_vp" UNIQUE("ad_segment","value_prop")
);
--> statement-breakpoint
CREATE TABLE "advertising"."mid_campaign_notification" (
	"ad_segment_id" varchar(36) PRIMARY KEY NOT NULL,
	"variant_type" varchar(255) NOT NULL
);
--> statement-breakpoint
ALTER TABLE "advertising"."conversation_call_to_action_copy" DROP CONSTRAINT "conversation_call_to_action_copy_type_conversation_call_to_action_copy_type_name_fk";
--> statement-breakpoint
ALTER TABLE "advertising"."conversation_message_copy" DROP CONSTRAINT "conversation_message_copy_type_conversation_copy_type_name_fk";
--> statement-breakpoint
ALTER TABLE "advertising"."social_post_copy" DROP CONSTRAINT "social_post_copy_social_post_copy_type_social_post_copy_type_name_fk";
--> statement-breakpoint
ALTER TABLE "advertising"."conversation_call_to_action_copy" ADD COLUMN "status" varchar(255) DEFAULT 'DRAFT' NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."conversation_message_copy" ADD COLUMN "status" varchar(255) DEFAULT 'DRAFT' NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."conversation_subject_copy" ADD COLUMN "status" varchar(255) DEFAULT 'DRAFT' NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."linked_in_ad_segment" ADD COLUMN "sender" varchar(256);--> statement-breakpoint
ALTER TABLE "advertising"."linked_in_ad_segment" ADD COLUMN "destination_url" varchar(256);--> statement-breakpoint
ALTER TABLE "advertising"."linkedin_ad_segment_value_prop" ADD COLUMN "status" varchar(255) DEFAULT 'DRAFT' NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."social_post_call_to_action_copy" ADD COLUMN "status" varchar(255) DEFAULT 'DRAFT' NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."social_post_copy" ADD COLUMN "status" varchar(255) DEFAULT 'DRAFT' NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."conversationCallToAction_ab_test_round_day" ADD CONSTRAINT "conversationCallToAction_ab_test_round_day_ab_test_round_id_conversationCallToAction_ab_test_round_id_fk" FOREIGN KEY ("ab_test_round_id") REFERENCES "advertising"."conversationCallToAction_ab_test_round"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationCallToAction_ab_test_round_day" ADD CONSTRAINT "conversationCallToAction_ab_test_round_day_deployment_config_id_linkedin_deployment_config_id_fk" FOREIGN KEY ("deployment_config_id") REFERENCES "advertising"."linkedin_deployment_config"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationCallToAction_ab_test_round" ADD CONSTRAINT "conversationCallToAction_ab_test_round_ab_test_id_conversationCallToAction_ab_test_stage_id_fk" FOREIGN KEY ("ab_test_id") REFERENCES "advertising"."conversationCallToAction_ab_test"("stage_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationCallToAction_ab_test_round" ADD CONSTRAINT "conversationCallToAction_ab_test_round_current_best_id_conversation_call_to_action_copy_id_fk" FOREIGN KEY ("current_best_id") REFERENCES "advertising"."conversation_call_to_action_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationCallToAction_ab_test_round" ADD CONSTRAINT "conversationCallToAction_ab_test_round_contender_id_conversation_call_to_action_copy_id_fk" FOREIGN KEY ("contender_id") REFERENCES "advertising"."conversation_call_to_action_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationCallToAction_ab_test" ADD CONSTRAINT "conversationCallToAction_ab_test_stage_id_stage_id_fk" FOREIGN KEY ("stage_id") REFERENCES "advertising"."stage"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationCallToAction_ab_test" ADD CONSTRAINT "conversationCallToAction_ab_test_winner_id_conversation_call_to_action_copy_id_fk" FOREIGN KEY ("winner_id") REFERENCES "advertising"."conversation_call_to_action_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationMessageCopy_ab_test_round_day" ADD CONSTRAINT "conversationMessageCopy_ab_test_round_day_ab_test_round_id_conversationMessageCopy_ab_test_round_id_fk" FOREIGN KEY ("ab_test_round_id") REFERENCES "advertising"."conversationMessageCopy_ab_test_round"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationMessageCopy_ab_test_round_day" ADD CONSTRAINT "conversationMessageCopy_ab_test_round_day_deployment_config_id_linkedin_deployment_config_id_fk" FOREIGN KEY ("deployment_config_id") REFERENCES "advertising"."linkedin_deployment_config"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationMessageCopy_ab_test_round" ADD CONSTRAINT "conversationMessageCopy_ab_test_round_ab_test_id_conversationMessageCopy_ab_test_stage_id_fk" FOREIGN KEY ("ab_test_id") REFERENCES "advertising"."conversationMessageCopy_ab_test"("stage_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationMessageCopy_ab_test_round" ADD CONSTRAINT "conversationMessageCopy_ab_test_round_current_best_id_conversation_message_copy_id_fk" FOREIGN KEY ("current_best_id") REFERENCES "advertising"."conversation_message_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationMessageCopy_ab_test_round" ADD CONSTRAINT "conversationMessageCopy_ab_test_round_contender_id_conversation_message_copy_id_fk" FOREIGN KEY ("contender_id") REFERENCES "advertising"."conversation_message_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationMessageCopy_ab_test" ADD CONSTRAINT "conversationMessageCopy_ab_test_stage_id_stage_id_fk" FOREIGN KEY ("stage_id") REFERENCES "advertising"."stage"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."conversationMessageCopy_ab_test" ADD CONSTRAINT "conversationMessageCopy_ab_test_winner_id_conversation_message_copy_id_fk" FOREIGN KEY ("winner_id") REFERENCES "advertising"."conversation_message_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."socialPostBodyCopy_ab_test_round_day" ADD CONSTRAINT "socialPostBodyCopy_ab_test_round_day_ab_test_round_id_socialPostBodyCopy_ab_test_round_id_fk" FOREIGN KEY ("ab_test_round_id") REFERENCES "advertising"."socialPostBodyCopy_ab_test_round"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."socialPostBodyCopy_ab_test_round_day" ADD CONSTRAINT "socialPostBodyCopy_ab_test_round_day_deployment_config_id_linkedin_deployment_config_id_fk" FOREIGN KEY ("deployment_config_id") REFERENCES "advertising"."linkedin_deployment_config"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."socialPostBodyCopy_ab_test_round" ADD CONSTRAINT "socialPostBodyCopy_ab_test_round_ab_test_id_socialPostBodyCopy_ab_test_stage_id_fk" FOREIGN KEY ("ab_test_id") REFERENCES "advertising"."socialPostBodyCopy_ab_test"("stage_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."socialPostBodyCopy_ab_test_round" ADD CONSTRAINT "socialPostBodyCopy_ab_test_round_current_best_id_social_post_copy_id_fk" FOREIGN KEY ("current_best_id") REFERENCES "advertising"."social_post_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."socialPostBodyCopy_ab_test_round" ADD CONSTRAINT "socialPostBodyCopy_ab_test_round_contender_id_social_post_copy_id_fk" FOREIGN KEY ("contender_id") REFERENCES "advertising"."social_post_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."socialPostBodyCopy_ab_test" ADD CONSTRAINT "socialPostBodyCopy_ab_test_stage_id_stage_id_fk" FOREIGN KEY ("stage_id") REFERENCES "advertising"."stage"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."socialPostBodyCopy_ab_test" ADD CONSTRAINT "socialPostBodyCopy_ab_test_winner_id_social_post_copy_id_fk" FOREIGN KEY ("winner_id") REFERENCES "advertising"."social_post_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."socialPostCallToAction_ab_test_round_day" ADD CONSTRAINT "socialPostCallToAction_ab_test_round_day_ab_test_round_id_socialPostCallToAction_ab_test_round_id_fk" FOREIGN KEY ("ab_test_round_id") REFERENCES "advertising"."socialPostCallToAction_ab_test_round"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."socialPostCallToAction_ab_test_round_day" ADD CONSTRAINT "socialPostCallToAction_ab_test_round_day_deployment_config_id_linkedin_deployment_config_id_fk" FOREIGN KEY ("deployment_config_id") REFERENCES "advertising"."linkedin_deployment_config"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."socialPostCallToAction_ab_test_round" ADD CONSTRAINT "socialPostCallToAction_ab_test_round_ab_test_id_socialPostCallToAction_ab_test_stage_id_fk" FOREIGN KEY ("ab_test_id") REFERENCES "advertising"."socialPostCallToAction_ab_test"("stage_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."socialPostCallToAction_ab_test_round" ADD CONSTRAINT "socialPostCallToAction_ab_test_round_current_best_id_social_post_call_to_action_copy_id_fk" FOREIGN KEY ("current_best_id") REFERENCES "advertising"."social_post_call_to_action_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."socialPostCallToAction_ab_test_round" ADD CONSTRAINT "socialPostCallToAction_ab_test_round_contender_id_social_post_call_to_action_copy_id_fk" FOREIGN KEY ("contender_id") REFERENCES "advertising"."social_post_call_to_action_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."socialPostCallToAction_ab_test" ADD CONSTRAINT "socialPostCallToAction_ab_test_stage_id_stage_id_fk" FOREIGN KEY ("stage_id") REFERENCES "advertising"."stage"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."socialPostCallToAction_ab_test" ADD CONSTRAINT "socialPostCallToAction_ab_test_winner_id_social_post_call_to_action_copy_id_fk" FOREIGN KEY ("winner_id") REFERENCES "advertising"."social_post_call_to_action_copy"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_selected_conversation_call_to_action_type" ADD CONSTRAINT "ad_segment_selected_conversation_call_to_action_type_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_selected_conversation_message_type" ADD CONSTRAINT "ad_segment_selected_conversation_message_type_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_selected_social_post_body_type" ADD CONSTRAINT "ad_segment_selected_social_post_body_type_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."ad_segment_selected_social_post_call_to_action_type" ADD CONSTRAINT "ad_segment_selected_social_post_call_to_action_type_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "advertising"."mid_campaign_notification" ADD CONSTRAINT "mid_campaign_notification_ad_segment_id_linked_in_ad_segment_id_fk" FOREIGN KEY ("ad_segment_id") REFERENCES "advertising"."linked_in_ad_segment"("id") ON DELETE no action ON UPDATE no action;