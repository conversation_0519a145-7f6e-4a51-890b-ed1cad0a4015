ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "min_bid" numeric(12, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "max_bid" numeric(12, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "min_suggested_bid" numeric(12, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "max_suggested_bid" numeric(12, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "suggested_bid" numeric(12, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "decision" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "advertising"."manual_bidding_event" ADD COLUMN "bid_type" varchar(255) NOT NULL;