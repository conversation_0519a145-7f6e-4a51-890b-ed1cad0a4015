{"id": "9fd7fdf3-f399-4f6a-b2cc-72c4ee615337", "prevId": "119c7a35-f7b7-4902-9cfa-8ff056e8dc48", "version": "7", "dialect": "postgresql", "tables": {"advertising.audience_ab_test_round_day": {"name": "audience_ab_test_round_day", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ab_test_round_id": {"name": "ab_test_round_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "day_index": {"name": "day_index", "type": "smallint", "primaryKey": false, "notNull": true}, "winner": {"name": "winner", "type": "ab_test_round_winner", "typeSchema": "public", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "ab_test_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "current_best_result": {"name": "current_best_result", "type": "smallint", "primaryKey": false, "notNull": false}, "contender_result": {"name": "contender_result", "type": "smallint", "primaryKey": false, "notNull": false}, "deployment_config_id": {"name": "deployment_config_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"audience_ab_test_round_day_ab_test_round_id_audience_ab_test_round_id_fk": {"name": "audience_ab_test_round_day_ab_test_round_id_audience_ab_test_round_id_fk", "tableFrom": "audience_ab_test_round_day", "tableTo": "audience_ab_test_round", "schemaTo": "advertising", "columnsFrom": ["ab_test_round_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "audience_ab_test_round_day_deployment_config_id_linkedin_deployment_config_id_fk": {"name": "audience_ab_test_round_day_deployment_config_id_linkedin_deployment_config_id_fk", "tableFrom": "audience_ab_test_round_day", "tableTo": "linkedin_deployment_config", "schemaTo": "advertising", "columnsFrom": ["deployment_config_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"ab_test_round_day_status_constraint_audience": {"name": "ab_test_round_day_status_constraint_audience", "value": "(\"advertising\".\"audience_ab_test_round_day\".\"status\" != 'COMPLETED') OR (\"advertising\".\"audience_ab_test_round_day\".\"winner\" IS NOT NULL)"}, "ab_test_round_day_status_constraint_cb_audience": {"name": "ab_test_round_day_status_constraint_cb_audience", "value": "(\"advertising\".\"audience_ab_test_round_day\".\"status\" != 'COMPLETED') OR (\"advertising\".\"audience_ab_test_round_day\".\"current_best_result\" IS NOT NULL)"}, "ab_test_round_day_status_constraint_contender_audience": {"name": "ab_test_round_day_status_constraint_contender_audience", "value": "(\"advertising\".\"audience_ab_test_round_day\".\"status\" != 'COMPLETED') OR (\"advertising\".\"audience_ab_test_round_day\".\"contender_result\" IS NOT NULL)"}}, "isRLSEnabled": false}, "advertising.audience_ab_test_round": {"name": "audience_ab_test_round", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ab_test_id": {"name": "ab_test_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "ab_test_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "round_index": {"name": "round_index", "type": "smallint", "primaryKey": false, "notNull": true}, "current_best_id": {"name": "current_best_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "contender_id": {"name": "contender_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "winner": {"name": "winner", "type": "ab_test_round_winner", "typeSchema": "public", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"audience_ab_test_round_ab_test_id_audience_ab_test_stage_id_fk": {"name": "audience_ab_test_round_ab_test_id_audience_ab_test_stage_id_fk", "tableFrom": "audience_ab_test_round", "tableTo": "audience_ab_test", "schemaTo": "advertising", "columnsFrom": ["ab_test_id"], "columnsTo": ["stage_id"], "onDelete": "no action", "onUpdate": "no action"}, "audience_ab_test_round_current_best_id_linkedin_campaign_linkedin_audience_id_fk": {"name": "audience_ab_test_round_current_best_id_linkedin_campaign_linkedin_audience_id_fk", "tableFrom": "audience_ab_test_round", "tableTo": "linkedin_campaign", "schemaTo": "advertising", "columnsFrom": ["current_best_id"], "columnsTo": ["linkedin_audience_id"], "onDelete": "no action", "onUpdate": "no action"}, "audience_ab_test_round_contender_id_linkedin_campaign_linkedin_audience_id_fk": {"name": "audience_ab_test_round_contender_id_linkedin_campaign_linkedin_audience_id_fk", "tableFrom": "audience_ab_test_round", "tableTo": "linkedin_campaign", "schemaTo": "advertising", "columnsFrom": ["contender_id"], "columnsTo": ["linkedin_audience_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"ab_test_round_status_constraint_audience": {"name": "ab_test_round_status_constraint_audience", "value": "(\"advertising\".\"audience_ab_test_round\".\"status\" != 'COMPLETED') OR (\"advertising\".\"audience_ab_test_round\".\"winner\" IS NOT NULL)"}}, "isRLSEnabled": false}, "advertising.audience_ab_test": {"name": "audience_ab_test", "schema": "advertising", "columns": {"stage_id": {"name": "stage_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "status": {"name": "status", "type": "ab_test_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "winner_id": {"name": "winner_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"audience_ab_test_stage_id_stage_id_fk": {"name": "audience_ab_test_stage_id_stage_id_fk", "tableFrom": "audience_ab_test", "tableTo": "stage", "schemaTo": "advertising", "columnsFrom": ["stage_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "audience_ab_test_winner_id_linkedin_campaign_linkedin_audience_id_fk": {"name": "audience_ab_test_winner_id_linkedin_campaign_linkedin_audience_id_fk", "tableFrom": "audience_ab_test", "tableTo": "linkedin_campaign", "schemaTo": "advertising", "columnsFrom": ["winner_id"], "columnsTo": ["linkedin_audience_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"ab_test_status_constraint_audience": {"name": "ab_test_status_constraint_audience", "value": "(\"advertising\".\"audience_ab_test\".\"status\" != 'COMPLETED') OR (\"advertising\".\"audience_ab_test\".\"winner_id\" IS NOT NULL)"}}, "isRLSEnabled": false}, "advertising.conversationSubject_ab_test_round_day": {"name": "conversationSubject_ab_test_round_day", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ab_test_round_id": {"name": "ab_test_round_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "day_index": {"name": "day_index", "type": "smallint", "primaryKey": false, "notNull": true}, "winner": {"name": "winner", "type": "ab_test_round_winner", "typeSchema": "public", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "ab_test_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "current_best_result": {"name": "current_best_result", "type": "smallint", "primaryKey": false, "notNull": false}, "contender_result": {"name": "contender_result", "type": "smallint", "primaryKey": false, "notNull": false}, "deployment_config_id": {"name": "deployment_config_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"conversationSubject_ab_test_round_day_ab_test_round_id_conversationSubject_ab_test_round_id_fk": {"name": "conversationSubject_ab_test_round_day_ab_test_round_id_conversationSubject_ab_test_round_id_fk", "tableFrom": "conversationSubject_ab_test_round_day", "tableTo": "conversationSubject_ab_test_round", "schemaTo": "advertising", "columnsFrom": ["ab_test_round_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "conversationSubject_ab_test_round_day_deployment_config_id_linkedin_deployment_config_id_fk": {"name": "conversationSubject_ab_test_round_day_deployment_config_id_linkedin_deployment_config_id_fk", "tableFrom": "conversationSubject_ab_test_round_day", "tableTo": "linkedin_deployment_config", "schemaTo": "advertising", "columnsFrom": ["deployment_config_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"ab_test_round_day_status_constraint_conversationSubject": {"name": "ab_test_round_day_status_constraint_conversationSubject", "value": "(\"advertising\".\"conversationSubject_ab_test_round_day\".\"status\" != 'COMPLETED') OR (\"advertising\".\"conversationSubject_ab_test_round_day\".\"winner\" IS NOT NULL)"}, "ab_test_round_day_status_constraint_cb_conversationSubject": {"name": "ab_test_round_day_status_constraint_cb_conversationSubject", "value": "(\"advertising\".\"conversationSubject_ab_test_round_day\".\"status\" != 'COMPLETED') OR (\"advertising\".\"conversationSubject_ab_test_round_day\".\"current_best_result\" IS NOT NULL)"}, "ab_test_round_day_status_constraint_contender_conversationSubject": {"name": "ab_test_round_day_status_constraint_contender_conversationSubject", "value": "(\"advertising\".\"conversationSubject_ab_test_round_day\".\"status\" != 'COMPLETED') OR (\"advertising\".\"conversationSubject_ab_test_round_day\".\"contender_result\" IS NOT NULL)"}}, "isRLSEnabled": false}, "advertising.conversationSubject_ab_test_round": {"name": "conversationSubject_ab_test_round", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ab_test_id": {"name": "ab_test_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "ab_test_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "round_index": {"name": "round_index", "type": "smallint", "primaryKey": false, "notNull": true}, "current_best_id": {"name": "current_best_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "contender_id": {"name": "contender_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "winner": {"name": "winner", "type": "ab_test_round_winner", "typeSchema": "public", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"conversationSubject_ab_test_round_ab_test_id_conversationSubject_ab_test_stage_id_fk": {"name": "conversationSubject_ab_test_round_ab_test_id_conversationSubject_ab_test_stage_id_fk", "tableFrom": "conversationSubject_ab_test_round", "tableTo": "conversationSubject_ab_test", "schemaTo": "advertising", "columnsFrom": ["ab_test_id"], "columnsTo": ["stage_id"], "onDelete": "no action", "onUpdate": "no action"}, "conversationSubject_ab_test_round_current_best_id_conversation_subject_copy_id_fk": {"name": "conversationSubject_ab_test_round_current_best_id_conversation_subject_copy_id_fk", "tableFrom": "conversationSubject_ab_test_round", "tableTo": "conversation_subject_copy", "schemaTo": "advertising", "columnsFrom": ["current_best_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "conversationSubject_ab_test_round_contender_id_conversation_subject_copy_id_fk": {"name": "conversationSubject_ab_test_round_contender_id_conversation_subject_copy_id_fk", "tableFrom": "conversationSubject_ab_test_round", "tableTo": "conversation_subject_copy", "schemaTo": "advertising", "columnsFrom": ["contender_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"ab_test_round_status_constraint_conversationSubject": {"name": "ab_test_round_status_constraint_conversationSubject", "value": "(\"advertising\".\"conversationSubject_ab_test_round\".\"status\" != 'COMPLETED') OR (\"advertising\".\"conversationSubject_ab_test_round\".\"winner\" IS NOT NULL)"}}, "isRLSEnabled": false}, "advertising.conversationSubject_ab_test": {"name": "conversationSubject_ab_test", "schema": "advertising", "columns": {"stage_id": {"name": "stage_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "status": {"name": "status", "type": "ab_test_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "winner_id": {"name": "winner_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"conversationSubject_ab_test_stage_id_stage_id_fk": {"name": "conversationSubject_ab_test_stage_id_stage_id_fk", "tableFrom": "conversationSubject_ab_test", "tableTo": "stage", "schemaTo": "advertising", "columnsFrom": ["stage_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "conversationSubject_ab_test_winner_id_conversation_subject_copy_id_fk": {"name": "conversationSubject_ab_test_winner_id_conversation_subject_copy_id_fk", "tableFrom": "conversationSubject_ab_test", "tableTo": "conversation_subject_copy", "schemaTo": "advertising", "columnsFrom": ["winner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"ab_test_status_constraint_conversationSubject": {"name": "ab_test_status_constraint_conversationSubject", "value": "(\"advertising\".\"conversationSubject_ab_test\".\"status\" != 'COMPLETED') OR (\"advertising\".\"conversationSubject_ab_test\".\"winner_id\" IS NOT NULL)"}}, "isRLSEnabled": false}, "advertising.creative_ab_test_round_day": {"name": "creative_ab_test_round_day", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ab_test_round_id": {"name": "ab_test_round_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "day_index": {"name": "day_index", "type": "smallint", "primaryKey": false, "notNull": true}, "winner": {"name": "winner", "type": "ab_test_round_winner", "typeSchema": "public", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "ab_test_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "current_best_result": {"name": "current_best_result", "type": "smallint", "primaryKey": false, "notNull": false}, "contender_result": {"name": "contender_result", "type": "smallint", "primaryKey": false, "notNull": false}, "deployment_config_id": {"name": "deployment_config_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"creative_ab_test_round_day_ab_test_round_id_creative_ab_test_round_id_fk": {"name": "creative_ab_test_round_day_ab_test_round_id_creative_ab_test_round_id_fk", "tableFrom": "creative_ab_test_round_day", "tableTo": "creative_ab_test_round", "schemaTo": "advertising", "columnsFrom": ["ab_test_round_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "creative_ab_test_round_day_deployment_config_id_linkedin_deployment_config_id_fk": {"name": "creative_ab_test_round_day_deployment_config_id_linkedin_deployment_config_id_fk", "tableFrom": "creative_ab_test_round_day", "tableTo": "linkedin_deployment_config", "schemaTo": "advertising", "columnsFrom": ["deployment_config_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"ab_test_round_day_status_constraint_creative": {"name": "ab_test_round_day_status_constraint_creative", "value": "(\"advertising\".\"creative_ab_test_round_day\".\"status\" != 'COMPLETED') OR (\"advertising\".\"creative_ab_test_round_day\".\"winner\" IS NOT NULL)"}, "ab_test_round_day_status_constraint_cb_creative": {"name": "ab_test_round_day_status_constraint_cb_creative", "value": "(\"advertising\".\"creative_ab_test_round_day\".\"status\" != 'COMPLETED') OR (\"advertising\".\"creative_ab_test_round_day\".\"current_best_result\" IS NOT NULL)"}, "ab_test_round_day_status_constraint_contender_creative": {"name": "ab_test_round_day_status_constraint_contender_creative", "value": "(\"advertising\".\"creative_ab_test_round_day\".\"status\" != 'COMPLETED') OR (\"advertising\".\"creative_ab_test_round_day\".\"contender_result\" IS NOT NULL)"}}, "isRLSEnabled": false}, "advertising.creative_ab_test_round": {"name": "creative_ab_test_round", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ab_test_id": {"name": "ab_test_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "ab_test_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "round_index": {"name": "round_index", "type": "smallint", "primaryKey": false, "notNull": true}, "current_best_id": {"name": "current_best_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "contender_id": {"name": "contender_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "winner": {"name": "winner", "type": "ab_test_round_winner", "typeSchema": "public", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"creative_ab_test_round_ab_test_id_creative_ab_test_stage_id_fk": {"name": "creative_ab_test_round_ab_test_id_creative_ab_test_stage_id_fk", "tableFrom": "creative_ab_test_round", "tableTo": "creative_ab_test", "schemaTo": "advertising", "columnsFrom": ["ab_test_id"], "columnsTo": ["stage_id"], "onDelete": "no action", "onUpdate": "no action"}, "creative_ab_test_round_current_best_id_linkedin_ad_program_creative_id_fk": {"name": "creative_ab_test_round_current_best_id_linkedin_ad_program_creative_id_fk", "tableFrom": "creative_ab_test_round", "tableTo": "linkedin_ad_program_creative", "schemaTo": "advertising", "columnsFrom": ["current_best_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "creative_ab_test_round_contender_id_linkedin_ad_program_creative_id_fk": {"name": "creative_ab_test_round_contender_id_linkedin_ad_program_creative_id_fk", "tableFrom": "creative_ab_test_round", "tableTo": "linkedin_ad_program_creative", "schemaTo": "advertising", "columnsFrom": ["contender_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"ab_test_round_status_constraint_creative": {"name": "ab_test_round_status_constraint_creative", "value": "(\"advertising\".\"creative_ab_test_round\".\"status\" != 'COMPLETED') OR (\"advertising\".\"creative_ab_test_round\".\"winner\" IS NOT NULL)"}}, "isRLSEnabled": false}, "advertising.creative_ab_test": {"name": "creative_ab_test", "schema": "advertising", "columns": {"stage_id": {"name": "stage_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "status": {"name": "status", "type": "ab_test_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "winner_id": {"name": "winner_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"creative_ab_test_stage_id_stage_id_fk": {"name": "creative_ab_test_stage_id_stage_id_fk", "tableFrom": "creative_ab_test", "tableTo": "stage", "schemaTo": "advertising", "columnsFrom": ["stage_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "creative_ab_test_winner_id_linkedin_ad_program_creative_id_fk": {"name": "creative_ab_test_winner_id_linkedin_ad_program_creative_id_fk", "tableFrom": "creative_ab_test", "tableTo": "linkedin_ad_program_creative", "schemaTo": "advertising", "columnsFrom": ["winner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"ab_test_status_constraint_creative": {"name": "ab_test_status_constraint_creative", "value": "(\"advertising\".\"creative_ab_test\".\"status\" != 'COMPLETED') OR (\"advertising\".\"creative_ab_test\".\"winner_id\" IS NOT NULL)"}}, "isRLSEnabled": false}, "advertising.valueProp_ab_test_round_day": {"name": "valueProp_ab_test_round_day", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ab_test_round_id": {"name": "ab_test_round_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "day_index": {"name": "day_index", "type": "smallint", "primaryKey": false, "notNull": true}, "winner": {"name": "winner", "type": "ab_test_round_winner", "typeSchema": "public", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "ab_test_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "current_best_result": {"name": "current_best_result", "type": "smallint", "primaryKey": false, "notNull": false}, "contender_result": {"name": "contender_result", "type": "smallint", "primaryKey": false, "notNull": false}, "deployment_config_id": {"name": "deployment_config_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"valueProp_ab_test_round_day_ab_test_round_id_valueProp_ab_test_round_id_fk": {"name": "valueProp_ab_test_round_day_ab_test_round_id_valueProp_ab_test_round_id_fk", "tableFrom": "valueProp_ab_test_round_day", "tableTo": "valueProp_ab_test_round", "schemaTo": "advertising", "columnsFrom": ["ab_test_round_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "valueProp_ab_test_round_day_deployment_config_id_linkedin_deployment_config_id_fk": {"name": "valueProp_ab_test_round_day_deployment_config_id_linkedin_deployment_config_id_fk", "tableFrom": "valueProp_ab_test_round_day", "tableTo": "linkedin_deployment_config", "schemaTo": "advertising", "columnsFrom": ["deployment_config_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"ab_test_round_day_status_constraint_valueProp": {"name": "ab_test_round_day_status_constraint_valueProp", "value": "(\"advertising\".\"valueProp_ab_test_round_day\".\"status\" != 'COMPLETED') OR (\"advertising\".\"valueProp_ab_test_round_day\".\"winner\" IS NOT NULL)"}, "ab_test_round_day_status_constraint_cb_valueProp": {"name": "ab_test_round_day_status_constraint_cb_valueProp", "value": "(\"advertising\".\"valueProp_ab_test_round_day\".\"status\" != 'COMPLETED') OR (\"advertising\".\"valueProp_ab_test_round_day\".\"current_best_result\" IS NOT NULL)"}, "ab_test_round_day_status_constraint_contender_valueProp": {"name": "ab_test_round_day_status_constraint_contender_valueProp", "value": "(\"advertising\".\"valueProp_ab_test_round_day\".\"status\" != 'COMPLETED') OR (\"advertising\".\"valueProp_ab_test_round_day\".\"contender_result\" IS NOT NULL)"}}, "isRLSEnabled": false}, "advertising.valueProp_ab_test_round": {"name": "valueProp_ab_test_round", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ab_test_id": {"name": "ab_test_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "ab_test_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "round_index": {"name": "round_index", "type": "smallint", "primaryKey": false, "notNull": true}, "current_best_id": {"name": "current_best_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "contender_id": {"name": "contender_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "winner": {"name": "winner", "type": "ab_test_round_winner", "typeSchema": "public", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"valueProp_ab_test_round_ab_test_id_valueProp_ab_test_stage_id_fk": {"name": "valueProp_ab_test_round_ab_test_id_valueProp_ab_test_stage_id_fk", "tableFrom": "valueProp_ab_test_round", "tableTo": "valueProp_ab_test", "schemaTo": "advertising", "columnsFrom": ["ab_test_id"], "columnsTo": ["stage_id"], "onDelete": "no action", "onUpdate": "no action"}, "valueProp_ab_test_round_current_best_id_linkedin_ad_segment_value_prop_id_fk": {"name": "valueProp_ab_test_round_current_best_id_linkedin_ad_segment_value_prop_id_fk", "tableFrom": "valueProp_ab_test_round", "tableTo": "linkedin_ad_segment_value_prop", "schemaTo": "advertising", "columnsFrom": ["current_best_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "valueProp_ab_test_round_contender_id_linkedin_ad_segment_value_prop_id_fk": {"name": "valueProp_ab_test_round_contender_id_linkedin_ad_segment_value_prop_id_fk", "tableFrom": "valueProp_ab_test_round", "tableTo": "linkedin_ad_segment_value_prop", "schemaTo": "advertising", "columnsFrom": ["contender_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"ab_test_round_status_constraint_valueProp": {"name": "ab_test_round_status_constraint_valueProp", "value": "(\"advertising\".\"valueProp_ab_test_round\".\"status\" != 'COMPLETED') OR (\"advertising\".\"valueProp_ab_test_round\".\"winner\" IS NOT NULL)"}}, "isRLSEnabled": false}, "advertising.valueProp_ab_test": {"name": "valueProp_ab_test", "schema": "advertising", "columns": {"stage_id": {"name": "stage_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "status": {"name": "status", "type": "ab_test_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "winner_id": {"name": "winner_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"valueProp_ab_test_stage_id_stage_id_fk": {"name": "valueProp_ab_test_stage_id_stage_id_fk", "tableFrom": "valueProp_ab_test", "tableTo": "stage", "schemaTo": "advertising", "columnsFrom": ["stage_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "valueProp_ab_test_winner_id_linkedin_ad_segment_value_prop_id_fk": {"name": "valueProp_ab_test_winner_id_linkedin_ad_segment_value_prop_id_fk", "tableFrom": "valueProp_ab_test", "tableTo": "linkedin_ad_segment_value_prop", "schemaTo": "advertising", "columnsFrom": ["winner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"ab_test_status_constraint_valueProp": {"name": "ab_test_status_constraint_valueProp", "value": "(\"advertising\".\"valueProp_ab_test\".\"status\" != 'COMPLETED') OR (\"advertising\".\"valueProp_ab_test\".\"winner_id\" IS NOT NULL)"}}, "isRLSEnabled": false}, "advertising.ad_segment_best_audience": {"name": "ad_segment_best_audience", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ad_segment_id": {"name": "ad_segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "variant_id": {"name": "variant_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"ad_segment_best_audience_ad_segment_id_linked_in_ad_segment_id_fk": {"name": "ad_segment_best_audience_ad_segment_id_linked_in_ad_segment_id_fk", "tableFrom": "ad_segment_best_audience", "tableTo": "linked_in_ad_segment", "schemaTo": "advertising", "columnsFrom": ["ad_segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ad_segment_best_audience_variant_id_linked_in_audience_id_fk": {"name": "ad_segment_best_audience_variant_id_linked_in_audience_id_fk", "tableFrom": "ad_segment_best_audience", "tableTo": "linked_in_audience", "schemaTo": "advertising", "columnsFrom": ["variant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"$asb_audience_unqiue_ad_segment_id": {"name": "$asb_audience_unqiue_ad_segment_id", "nullsNotDistinct": false, "columns": ["ad_segment_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.ad_segment_best_conversationCallToActionCopy": {"name": "ad_segment_best_conversationCallToActionCopy", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ad_segment_id": {"name": "ad_segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "variant_id": {"name": "variant_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"ad_segment_best_conversationCallToActionCopy_ad_segment_id_linked_in_ad_segment_id_fk": {"name": "ad_segment_best_conversationCallToActionCopy_ad_segment_id_linked_in_ad_segment_id_fk", "tableFrom": "ad_segment_best_conversationCallToActionCopy", "tableTo": "linked_in_ad_segment", "schemaTo": "advertising", "columnsFrom": ["ad_segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ad_segment_best_conversationCallToActionCopy_variant_id_conversation_call_to_action_copy_id_fk": {"name": "ad_segment_best_conversationCallToActionCopy_variant_id_conversation_call_to_action_copy_id_fk", "tableFrom": "ad_segment_best_conversationCallToActionCopy", "tableTo": "conversation_call_to_action_copy", "schemaTo": "advertising", "columnsFrom": ["variant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"$asb_conversationCallToActionCopy_unqiue_ad_segment_id": {"name": "$asb_conversationCallToActionCopy_unqiue_ad_segment_id", "nullsNotDistinct": false, "columns": ["ad_segment_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.ad_segment_best_conversationMessageCopy": {"name": "ad_segment_best_conversationMessageCopy", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ad_segment_id": {"name": "ad_segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "variant_id": {"name": "variant_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"ad_segment_best_conversationMessageCopy_ad_segment_id_linked_in_ad_segment_id_fk": {"name": "ad_segment_best_conversationMessageCopy_ad_segment_id_linked_in_ad_segment_id_fk", "tableFrom": "ad_segment_best_conversationMessageCopy", "tableTo": "linked_in_ad_segment", "schemaTo": "advertising", "columnsFrom": ["ad_segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ad_segment_best_conversationMessageCopy_variant_id_conversation_message_copy_id_fk": {"name": "ad_segment_best_conversationMessageCopy_variant_id_conversation_message_copy_id_fk", "tableFrom": "ad_segment_best_conversationMessageCopy", "tableTo": "conversation_message_copy", "schemaTo": "advertising", "columnsFrom": ["variant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"$asb_conversationMessageCopy_unqiue_ad_segment_id": {"name": "$asb_conversationMessageCopy_unqiue_ad_segment_id", "nullsNotDistinct": false, "columns": ["ad_segment_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.ad_segment_best_conversationSubject": {"name": "ad_segment_best_conversationSubject", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ad_segment_id": {"name": "ad_segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "variant_id": {"name": "variant_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"ad_segment_best_conversationSubject_ad_segment_id_linked_in_ad_segment_id_fk": {"name": "ad_segment_best_conversationSubject_ad_segment_id_linked_in_ad_segment_id_fk", "tableFrom": "ad_segment_best_conversationSubject", "tableTo": "linked_in_ad_segment", "schemaTo": "advertising", "columnsFrom": ["ad_segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ad_segment_best_conversationSubject_variant_id_conversation_subject_copy_id_fk": {"name": "ad_segment_best_conversationSubject_variant_id_conversation_subject_copy_id_fk", "tableFrom": "ad_segment_best_conversationSubject", "tableTo": "conversation_subject_copy", "schemaTo": "advertising", "columnsFrom": ["variant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"$asb_conversationSubject_unqiue_ad_segment_id": {"name": "$asb_conversationSubject_unqiue_ad_segment_id", "nullsNotDistinct": false, "columns": ["ad_segment_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.ad_segment_best_creative": {"name": "ad_segment_best_creative", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ad_segment_id": {"name": "ad_segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "variant_id": {"name": "variant_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"ad_segment_best_creative_ad_segment_id_linked_in_ad_segment_id_fk": {"name": "ad_segment_best_creative_ad_segment_id_linked_in_ad_segment_id_fk", "tableFrom": "ad_segment_best_creative", "tableTo": "linked_in_ad_segment", "schemaTo": "advertising", "columnsFrom": ["ad_segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ad_segment_best_creative_variant_id_linkedin_ad_program_creative_id_fk": {"name": "ad_segment_best_creative_variant_id_linkedin_ad_program_creative_id_fk", "tableFrom": "ad_segment_best_creative", "tableTo": "linkedin_ad_program_creative", "schemaTo": "advertising", "columnsFrom": ["variant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"$asb_creative_unqiue_ad_segment_id": {"name": "$asb_creative_unqiue_ad_segment_id", "nullsNotDistinct": false, "columns": ["ad_segment_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.ad_segment_best_socialPostBodyCopy": {"name": "ad_segment_best_socialPostBodyCopy", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ad_segment_id": {"name": "ad_segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "variant_id": {"name": "variant_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"ad_segment_best_socialPostBodyCopy_ad_segment_id_linked_in_ad_segment_id_fk": {"name": "ad_segment_best_socialPostBodyCopy_ad_segment_id_linked_in_ad_segment_id_fk", "tableFrom": "ad_segment_best_socialPostBodyCopy", "tableTo": "linked_in_ad_segment", "schemaTo": "advertising", "columnsFrom": ["ad_segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ad_segment_best_socialPostBodyCopy_variant_id_social_post_copy_id_fk": {"name": "ad_segment_best_socialPostBodyCopy_variant_id_social_post_copy_id_fk", "tableFrom": "ad_segment_best_socialPostBodyCopy", "tableTo": "social_post_copy", "schemaTo": "advertising", "columnsFrom": ["variant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"$asb_socialPostBodyCopy_unqiue_ad_segment_id": {"name": "$asb_socialPostBodyCopy_unqiue_ad_segment_id", "nullsNotDistinct": false, "columns": ["ad_segment_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.ad_segment_best_socialPostCallToActionCopy": {"name": "ad_segment_best_socialPostCallToActionCopy", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ad_segment_id": {"name": "ad_segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "variant_id": {"name": "variant_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"ad_segment_best_socialPostCallToActionCopy_ad_segment_id_linked_in_ad_segment_id_fk": {"name": "ad_segment_best_socialPostCallToActionCopy_ad_segment_id_linked_in_ad_segment_id_fk", "tableFrom": "ad_segment_best_socialPostCallToActionCopy", "tableTo": "linked_in_ad_segment", "schemaTo": "advertising", "columnsFrom": ["ad_segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ad_segment_best_socialPostCallToActionCopy_variant_id_social_post_call_to_action_copy_id_fk": {"name": "ad_segment_best_socialPostCallToActionCopy_variant_id_social_post_call_to_action_copy_id_fk", "tableFrom": "ad_segment_best_socialPostCallToActionCopy", "tableTo": "social_post_call_to_action_copy", "schemaTo": "advertising", "columnsFrom": ["variant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"$asb_socialPostCallToActionCopy_unqiue_ad_segment_id": {"name": "$asb_socialPostCallToActionCopy_unqiue_ad_segment_id", "nullsNotDistinct": false, "columns": ["ad_segment_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.ad_segment_best_valueProp": {"name": "ad_segment_best_valueProp", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ad_segment_id": {"name": "ad_segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "variant_id": {"name": "variant_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"ad_segment_best_valueProp_ad_segment_id_linked_in_ad_segment_id_fk": {"name": "ad_segment_best_valueProp_ad_segment_id_linked_in_ad_segment_id_fk", "tableFrom": "ad_segment_best_valueProp", "tableTo": "linked_in_ad_segment", "schemaTo": "advertising", "columnsFrom": ["ad_segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ad_segment_best_valueProp_variant_id_linkedin_ad_segment_value_prop_id_fk": {"name": "ad_segment_best_valueProp_variant_id_linkedin_ad_segment_value_prop_id_fk", "tableFrom": "ad_segment_best_valueProp", "tableTo": "linkedin_ad_segment_value_prop", "schemaTo": "advertising", "columnsFrom": ["variant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"$asb_valueProp_unqiue_ad_segment_id": {"name": "$asb_valueProp_unqiue_ad_segment_id", "nullsNotDistinct": false, "columns": ["ad_segment_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.adCreative": {"name": "adCreative", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "bigint", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "s3_bucket_key": {"name": "s3_bucket_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "file_type": {"name": "file_type", "type": "ad_creative_file_type", "typeSchema": "public", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"adCreative_organization_id_organization_id_fk": {"name": "adCreative_organization_id_organization_id_fk", "tableFrom": "adCreative", "tableTo": "organization", "schemaTo": "core", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"adCreative_s3_bucket_key_unique": {"name": "adCreative_s3_bucket_key_unique", "nullsNotDistinct": false, "columns": ["s3_bucket_key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.ad_segment_selected_conversation_subject_type": {"name": "ad_segment_selected_conversation_subject_type", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ad_segment_id": {"name": "ad_segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"ad_segment_selected_conversation_subject_type_ad_segment_id_linked_in_ad_segment_id_fk": {"name": "ad_segment_selected_conversation_subject_type_ad_segment_id_linked_in_ad_segment_id_fk", "tableFrom": "ad_segment_selected_conversation_subject_type", "tableTo": "linked_in_ad_segment", "schemaTo": "advertising", "columnsFrom": ["ad_segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.ad_segment_value_prop_creative": {"name": "ad_segment_value_prop_creative", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ad_segment_value_prop_id": {"name": "ad_segment_value_prop_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "ad_program_creative_id": {"name": "ad_program_creative_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"ad_segment_value_prop_creative_ad_segment_value_prop_id_linkedin_ad_segment_value_prop_id_fk": {"name": "ad_segment_value_prop_creative_ad_segment_value_prop_id_linkedin_ad_segment_value_prop_id_fk", "tableFrom": "ad_segment_value_prop_creative", "tableTo": "linkedin_ad_segment_value_prop", "schemaTo": "advertising", "columnsFrom": ["ad_segment_value_prop_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ad_segment_value_prop_creative_ad_program_creative_id_linkedin_ad_program_creative_id_fk": {"name": "ad_segment_value_prop_creative_ad_program_creative_id_linkedin_ad_program_creative_id_fk", "tableFrom": "ad_segment_value_prop_creative", "tableTo": "linkedin_ad_program_creative", "schemaTo": "advertising", "columnsFrom": ["ad_program_creative_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_ad_segment_value_prop_and_creative": {"name": "unique_ad_segment_value_prop_and_creative", "nullsNotDistinct": false, "columns": ["ad_segment_value_prop_id", "ad_program_creative_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.audience_test_round": {"name": "audience_test_round", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "stage_id": {"name": "stage_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "current_audience_id": {"name": "current_audience_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "contender_audience_id": {"name": "contender_audience_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "audience_test_round_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "winner_audience": {"name": "winner_audience", "type": "audience_test_round_winner_audience", "typeSchema": "public", "primaryKey": false, "notNull": true}, "index": {"name": "index", "type": "smallint", "primaryKey": false, "notNull": true}, "step_id": {"name": "step_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "retries": {"name": "retries", "type": "smallint", "primaryKey": false, "notNull": true, "default": 0}, "max_retries": {"name": "max_retries", "type": "smallint", "primaryKey": false, "notNull": true, "default": 5}}, "indexes": {}, "foreignKeys": {"audience_test_round_step_id_stage_step_id_fk": {"name": "audience_test_round_step_id_stage_step_id_fk", "tableFrom": "audience_test_round", "tableTo": "stage_step", "schemaTo": "advertising", "columnsFrom": ["step_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.case_study": {"name": "case_study", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "bigint", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"case_study_organization_id_organization_id_fk": {"name": "case_study_organization_id_organization_id_fk", "tableFrom": "case_study", "tableTo": "organization", "schemaTo": "core", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.conversation_call_to_action_copy": {"name": "conversation_call_to_action_copy", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "conversation_message_copy_id": {"name": "conversation_message_copy_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"conversation_call_to_action_copy_conversation_message_copy_id_conversation_message_copy_id_fk": {"name": "conversation_call_to_action_copy_conversation_message_copy_id_conversation_message_copy_id_fk", "tableFrom": "conversation_call_to_action_copy", "tableTo": "conversation_message_copy", "schemaTo": "advertising", "columnsFrom": ["conversation_message_copy_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "conversation_call_to_action_copy_type_conversation_call_to_action_copy_type_name_fk": {"name": "conversation_call_to_action_copy_type_conversation_call_to_action_copy_type_name_fk", "tableFrom": "conversation_call_to_action_copy", "tableTo": "conversation_call_to_action_copy_type", "schemaTo": "advertising", "columnsFrom": ["type"], "columnsTo": ["name"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_copy_type_and_message_copy_id": {"name": "unique_copy_type_and_message_copy_id", "nullsNotDistinct": false, "columns": ["conversation_message_copy_id", "type"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.conversation_call_to_action_copy_type": {"name": "conversation_call_to_action_copy_type", "schema": "advertising", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.deprecated_conversation_copy": {"name": "deprecated_conversation_copy", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "linkedin_ad_segment_value_prop_id": {"name": "linkedin_ad_segment_value_prop_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "conversation_copy_type": {"name": "conversation_copy_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "call_to_action": {"name": "call_to_action", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"deprecated_conversation_copy_linkedin_ad_segment_value_prop_id_linkedin_ad_segment_value_prop_id_fk": {"name": "deprecated_conversation_copy_linkedin_ad_segment_value_prop_id_linkedin_ad_segment_value_prop_id_fk", "tableFrom": "deprecated_conversation_copy", "tableTo": "linkedin_ad_segment_value_prop", "schemaTo": "advertising", "columnsFrom": ["linkedin_ad_segment_value_prop_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "deprecated_conversation_copy_conversation_copy_type_conversation_copy_type_name_fk": {"name": "deprecated_conversation_copy_conversation_copy_type_conversation_copy_type_name_fk", "tableFrom": "deprecated_conversation_copy", "tableTo": "conversation_copy_type", "schemaTo": "advertising", "columnsFrom": ["conversation_copy_type"], "columnsTo": ["name"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_copy_type_and_value_prop_convo": {"name": "unique_copy_type_and_value_prop_convo", "nullsNotDistinct": false, "columns": ["linkedin_ad_segment_value_prop_id", "conversation_copy_type"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.conversation_copy_type": {"name": "conversation_copy_type", "schema": "advertising", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.conversation_message_copy": {"name": "conversation_message_copy", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "conversation_subject_copy_id": {"name": "conversation_subject_copy_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"conversation_message_copy_conversation_subject_copy_id_conversation_subject_copy_id_fk": {"name": "conversation_message_copy_conversation_subject_copy_id_conversation_subject_copy_id_fk", "tableFrom": "conversation_message_copy", "tableTo": "conversation_subject_copy", "schemaTo": "advertising", "columnsFrom": ["conversation_subject_copy_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "conversation_message_copy_type_conversation_copy_type_name_fk": {"name": "conversation_message_copy_type_conversation_copy_type_name_fk", "tableFrom": "conversation_message_copy", "tableTo": "conversation_copy_type", "schemaTo": "advertising", "columnsFrom": ["type"], "columnsTo": ["name"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_copy_type_and_subject_copy_id": {"name": "unique_copy_type_and_subject_copy_id", "nullsNotDistinct": false, "columns": ["conversation_subject_copy_id", "type"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.conversation_subject_copy": {"name": "conversation_subject_copy", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ad_segment_value_prop_id": {"name": "ad_segment_value_prop_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "lead_gen_form_urn": {"name": "lead_gen_form_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"conversation_subject_copy_ad_segment_value_prop_id_linkedin_ad_segment_value_prop_id_fk": {"name": "conversation_subject_copy_ad_segment_value_prop_id_linkedin_ad_segment_value_prop_id_fk", "tableFrom": "conversation_subject_copy", "tableTo": "linkedin_ad_segment_value_prop", "schemaTo": "advertising", "columnsFrom": ["ad_segment_value_prop_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_subject_copy_per_ad_segment_value_prop": {"name": "unique_subject_copy_per_ad_segment_value_prop", "nullsNotDistinct": false, "columns": ["ad_segment_value_prop_id", "type"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.conversation_subject_copy_type": {"name": "conversation_subject_copy_type", "schema": "advertising", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.example_social_post": {"name": "example_social_post", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "bigint", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"example_social_post_organization_id_organization_id_fk": {"name": "example_social_post_organization_id_organization_id_fk", "tableFrom": "example_social_post", "tableTo": "organization", "schemaTo": "core", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linkedin_ad_account": {"name": "linkedin_ad_account", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "bigint", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "linkedin_ad_account_urn": {"name": "linkedin_ad_account_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "linkedin_organization_urn": {"name": "linkedin_organization_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"linkedin_ad_account_organization_id_organization_id_fk": {"name": "linkedin_ad_account_organization_id_organization_id_fk", "tableFrom": "linkedin_ad_account", "tableTo": "organization", "schemaTo": "core", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linked_in_ad_format": {"name": "linked_in_ad_format", "schema": "advertising", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "format": {"name": "format", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_type_and_format": {"name": "unique_type_and_format", "nullsNotDistinct": false, "columns": ["type", "format"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linked_in_ad_program": {"name": "linked_in_ad_program", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "linked_in_ad_account_id": {"name": "linked_in_ad_account_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "start_datetime": {"name": "start_datetime", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_datetime": {"name": "end_datetime", "type": "timestamp", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'EVENT_DRIVEN'"}, "total_budget": {"name": "total_budget", "type": "integer", "primaryKey": false, "notNull": false}, "monthly_budget": {"name": "monthly_budget", "type": "integer", "primaryKey": false, "notNull": false}, "ad_format_id": {"name": "ad_format_id", "type": "integer", "primaryKey": false, "notNull": true}, "objective_type": {"name": "objective_type", "type": "objective_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "lead_gen_form_urn": {"name": "lead_gen_form_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'DRAFT'"}}, "indexes": {}, "foreignKeys": {"linked_in_ad_program_linked_in_ad_account_id_linkedin_ad_account_id_fk": {"name": "linked_in_ad_program_linked_in_ad_account_id_linkedin_ad_account_id_fk", "tableFrom": "linked_in_ad_program", "tableTo": "linkedin_ad_account", "schemaTo": "advertising", "columnsFrom": ["linked_in_ad_account_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "linked_in_ad_program_ad_format_id_linked_in_ad_format_id_fk": {"name": "linked_in_ad_program_ad_format_id_linked_in_ad_format_id_fk", "tableFrom": "linked_in_ad_program", "tableTo": "linked_in_ad_format", "schemaTo": "advertising", "columnsFrom": ["ad_format_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"lead_gen_constraint": {"name": "lead_gen_constraint", "value": "(\"advertising\".\"linked_in_ad_program\".\"objective_type\" != 'LEAD_GENERATION') OR (\"advertising\".\"linked_in_ad_program\".\"lead_gen_form_urn\" IS NOT NULL)"}, "total_budget_constraint": {"name": "total_budget_constraint", "value": "(\"advertising\".\"linked_in_ad_program\".\"type\" = 'evergreen') OR (\"advertising\".\"linked_in_ad_program\".\"total_budget\" IS NOT NULL)"}, "monthly_budget_constraint": {"name": "monthly_budget_constraint", "value": "(\"advertising\".\"linked_in_ad_program\".\"type\" != 'evergreen') OR (\"advertising\".\"linked_in_ad_program\".\"monthly_budget\" IS NOT NULL)"}}, "isRLSEnabled": false}, "advertising.linkedin_ad_program_creative": {"name": "linkedin_ad_program_creative", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linkedin_ad_program_id": {"name": "linkedin_ad_program_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "linkedin_ad_creative_id": {"name": "linkedin_ad_creative_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"linkedin_ad_program_creative_linkedin_ad_program_id_linked_in_ad_program_id_fk": {"name": "linkedin_ad_program_creative_linkedin_ad_program_id_linked_in_ad_program_id_fk", "tableFrom": "linkedin_ad_program_creative", "tableTo": "linked_in_ad_program", "schemaTo": "advertising", "columnsFrom": ["linkedin_ad_program_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "linkedin_ad_program_creative_linkedin_ad_creative_id_adCreative_id_fk": {"name": "linkedin_ad_program_creative_linkedin_ad_creative_id_adCreative_id_fk", "tableFrom": "linkedin_ad_program_creative", "tableTo": "adCreative", "schemaTo": "advertising", "columnsFrom": ["linkedin_ad_creative_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linked_in_ad_segment": {"name": "linked_in_ad_segment", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linked_in_ad_program_id": {"name": "linked_in_ad_program_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "segment_id": {"name": "segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "is_ready": {"name": "is_ready", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"linked_in_ad_segment_linked_in_ad_program_id_linked_in_ad_program_id_fk": {"name": "linked_in_ad_segment_linked_in_ad_program_id_linked_in_ad_program_id_fk", "tableFrom": "linked_in_ad_segment", "tableTo": "linked_in_ad_program", "schemaTo": "advertising", "columnsFrom": ["linked_in_ad_program_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "linked_in_ad_segment_segment_id_segment_id_fk": {"name": "linked_in_ad_segment_segment_id_segment_id_fk", "tableFrom": "linked_in_ad_segment", "tableTo": "segment", "schemaTo": "core", "columnsFrom": ["segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linkedin_ad_segment_conversation_base_copy": {"name": "linkedin_ad_segment_conversation_base_copy", "schema": "advertising", "columns": {"ad_segment_id": {"name": "ad_segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "base_copy": {"name": "base_copy", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"linkedin_ad_segment_conversation_base_copy_ad_segment_id_linked_in_ad_segment_id_fk": {"name": "linkedin_ad_segment_conversation_base_copy_ad_segment_id_linked_in_ad_segment_id_fk", "tableFrom": "linkedin_ad_segment_conversation_base_copy", "tableTo": "linked_in_ad_segment", "schemaTo": "advertising", "columnsFrom": ["ad_segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linkedin_ad_segment_social_post_base_copy": {"name": "linkedin_ad_segment_social_post_base_copy", "schema": "advertising", "columns": {"ad_segment_id": {"name": "ad_segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "base_copy": {"name": "base_copy", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"linkedin_ad_segment_social_post_base_copy_ad_segment_id_linked_in_ad_segment_id_fk": {"name": "linkedin_ad_segment_social_post_base_copy_ad_segment_id_linked_in_ad_segment_id_fk", "tableFrom": "linkedin_ad_segment_social_post_base_copy", "tableTo": "linked_in_ad_segment", "schemaTo": "advertising", "columnsFrom": ["ad_segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linkedin_ad_segment_value_prop": {"name": "linkedin_ad_segment_value_prop", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linkedin_ad_segment_id": {"name": "linkedin_ad_segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "value_prop": {"name": "value_prop", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"linkedin_ad_segment_value_prop_linkedin_ad_segment_id_linked_in_ad_segment_id_fk": {"name": "linkedin_ad_segment_value_prop_linkedin_ad_segment_id_linked_in_ad_segment_id_fk", "tableFrom": "linkedin_ad_segment_value_prop", "tableTo": "linked_in_ad_segment", "schemaTo": "advertising", "columnsFrom": ["linkedin_ad_segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linked_in_audience": {"name": "linked_in_audience", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linkedin_ad_segment_id": {"name": "linkedin_ad_segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "audience_populated": {"name": "audience_populated", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "to_use": {"name": "to_use", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {}, "foreignKeys": {"linked_in_audience_linkedin_ad_segment_id_linked_in_ad_segment_id_fk": {"name": "linked_in_audience_linkedin_ad_segment_id_linked_in_ad_segment_id_fk", "tableFrom": "linked_in_audience", "tableTo": "linked_in_ad_segment", "schemaTo": "advertising", "columnsFrom": ["linkedin_ad_segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linkedin_audience_exclude_entity": {"name": "linkedin_audience_exclude_entity", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linkedin_audience_id": {"name": "linkedin_audience_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "linkedin_audience_entity_urn": {"name": "linkedin_audience_entity_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"linkedin_audience_exclude_entity_linkedin_audience_id_linked_in_audience_id_fk": {"name": "linkedin_audience_exclude_entity_linkedin_audience_id_linked_in_audience_id_fk", "tableFrom": "linkedin_audience_exclude_entity", "tableTo": "linked_in_audience", "schemaTo": "advertising", "columnsFrom": ["linkedin_audience_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "linkedin_audience_exclude_entity_linkedin_audience_entity_urn_linked_in_audience_entity_entity_urn_fk": {"name": "linkedin_audience_exclude_entity_linkedin_audience_entity_urn_linked_in_audience_entity_entity_urn_fk", "tableFrom": "linkedin_audience_exclude_entity", "tableTo": "linked_in_audience_entity", "schemaTo": "advertising", "columnsFrom": ["linkedin_audience_entity_urn"], "columnsTo": ["entity_urn"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linked_in_audience_or_group": {"name": "linked_in_audience_or_group", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linked_in_audience_id": {"name": "linked_in_audience_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"linked_in_audience_or_group_linked_in_audience_id_linked_in_audience_id_fk": {"name": "linked_in_audience_or_group_linked_in_audience_id_linked_in_audience_id_fk", "tableFrom": "linked_in_audience_or_group", "tableTo": "linked_in_audience", "schemaTo": "advertising", "columnsFrom": ["linked_in_audience_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linked_in_audience_or_group_entity": {"name": "linked_in_audience_or_group_entity", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linked_in_audience_or_group_id": {"name": "linked_in_audience_or_group_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "linked_in_audience_entity_urn": {"name": "linked_in_audience_entity_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"linked_in_audience_or_group_entity_linked_in_audience_or_group_id_linked_in_audience_or_group_id_fk": {"name": "linked_in_audience_or_group_entity_linked_in_audience_or_group_id_linked_in_audience_or_group_id_fk", "tableFrom": "linked_in_audience_or_group_entity", "tableTo": "linked_in_audience_or_group", "schemaTo": "advertising", "columnsFrom": ["linked_in_audience_or_group_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "linked_in_audience_or_group_entity_linked_in_audience_entity_urn_linked_in_audience_entity_entity_urn_fk": {"name": "linked_in_audience_or_group_entity_linked_in_audience_entity_urn_linked_in_audience_entity_entity_urn_fk", "tableFrom": "linked_in_audience_or_group_entity", "tableTo": "linked_in_audience_entity", "schemaTo": "advertising", "columnsFrom": ["linked_in_audience_entity_urn"], "columnsTo": ["entity_urn"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linked_in_audience_entity": {"name": "linked_in_audience_entity", "schema": "advertising", "columns": {"entity_urn": {"name": "entity_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "entity_name": {"name": "entity_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "facet_urn": {"name": "facet_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"linked_in_audience_entity_facet_urn_linked_in_facet_facet_urn_fk": {"name": "linked_in_audience_entity_facet_urn_linked_in_facet_facet_urn_fk", "tableFrom": "linked_in_audience_entity", "tableTo": "linked_in_facet", "schemaTo": "advertising", "columnsFrom": ["facet_urn"], "columnsTo": ["facet_urn"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linked_in_facet": {"name": "linked_in_facet", "schema": "advertising", "columns": {"facet_urn": {"name": "facet_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "facet_name": {"name": "facet_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.segment_linked_in_audience_or_group_entity": {"name": "segment_linked_in_audience_or_group_entity", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "segment_linked_in_audience_or_group_id": {"name": "segment_linked_in_audience_or_group_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "linked_in_audience_entity_urn": {"name": "linked_in_audience_entity_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"segment_linked_in_audience_or_group_entity_segment_linked_in_audience_or_group_id_segment_linked_in_audience_and_group_id_fk": {"name": "segment_linked_in_audience_or_group_entity_segment_linked_in_audience_or_group_id_segment_linked_in_audience_and_group_id_fk", "tableFrom": "segment_linked_in_audience_or_group_entity", "tableTo": "segment_linked_in_audience_and_group", "schemaTo": "advertising", "columnsFrom": ["segment_linked_in_audience_or_group_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "segment_linked_in_audience_or_group_entity_linked_in_audience_entity_urn_linked_in_audience_entity_entity_urn_fk": {"name": "segment_linked_in_audience_or_group_entity_linked_in_audience_entity_urn_linked_in_audience_entity_entity_urn_fk", "tableFrom": "segment_linked_in_audience_or_group_entity", "tableTo": "linked_in_audience_entity", "schemaTo": "advertising", "columnsFrom": ["linked_in_audience_entity_urn"], "columnsTo": ["entity_urn"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.segment_linkedin_audience_exclude_entity": {"name": "segment_linkedin_audience_exclude_entity", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "segment_linked_in_audience_prefab_id": {"name": "segment_linked_in_audience_prefab_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "linkedin_audience_entity_urn": {"name": "linkedin_audience_entity_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"segment_linkedin_audience_exclude_entity_segment_linked_in_audience_prefab_id_segment_linked_in_audience_prefab_id_fk": {"name": "segment_linkedin_audience_exclude_entity_segment_linked_in_audience_prefab_id_segment_linked_in_audience_prefab_id_fk", "tableFrom": "segment_linkedin_audience_exclude_entity", "tableTo": "segment_linked_in_audience_prefab", "schemaTo": "advertising", "columnsFrom": ["segment_linked_in_audience_prefab_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "segment_linkedin_audience_exclude_entity_linkedin_audience_entity_urn_linked_in_audience_entity_entity_urn_fk": {"name": "segment_linkedin_audience_exclude_entity_linkedin_audience_entity_urn_linked_in_audience_entity_entity_urn_fk", "tableFrom": "segment_linkedin_audience_exclude_entity", "tableTo": "linked_in_audience_entity", "schemaTo": "advertising", "columnsFrom": ["linkedin_audience_entity_urn"], "columnsTo": ["entity_urn"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.segment_linked_in_audience_and_group": {"name": "segment_linked_in_audience_and_group", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "segment_linked_in_audience_prefab_id": {"name": "segment_linked_in_audience_prefab_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"segment_linked_in_audience_and_group_segment_linked_in_audience_prefab_id_segment_linked_in_audience_prefab_id_fk": {"name": "segment_linked_in_audience_and_group_segment_linked_in_audience_prefab_id_segment_linked_in_audience_prefab_id_fk", "tableFrom": "segment_linked_in_audience_and_group", "tableTo": "segment_linked_in_audience_prefab", "schemaTo": "advertising", "columnsFrom": ["segment_linked_in_audience_prefab_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linkedin_campaign": {"name": "linkedin_campaign", "schema": "advertising", "columns": {"linkedin_audience_id": {"name": "linkedin_audience_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linkedin_campaign_urn": {"name": "linkedin_campaign_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "total_budget": {"name": "total_budget", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "linkedin_campaign_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"linkedin_campaign_linkedin_audience_id_linked_in_audience_id_fk": {"name": "linkedin_campaign_linkedin_audience_id_linked_in_audience_id_fk", "tableFrom": "linkedin_campaign", "tableTo": "linked_in_audience", "schemaTo": "advertising", "columnsFrom": ["linkedin_audience_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linkedin_campaign_group": {"name": "linkedin_campaign_group", "schema": "advertising", "columns": {"linkedin_ad_segment_id": {"name": "linkedin_ad_segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "total_budget": {"name": "total_budget", "type": "integer", "primaryKey": false, "notNull": true}, "linkedin_campaign_group_urn": {"name": "linkedin_campaign_group_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "linkedin_campaign_group_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"linkedin_campaign_group_linkedin_ad_segment_id_linked_in_ad_segment_id_fk": {"name": "linkedin_campaign_group_linkedin_ad_segment_id_linked_in_ad_segment_id_fk", "tableFrom": "linkedin_campaign_group", "tableTo": "linked_in_ad_segment", "schemaTo": "advertising", "columnsFrom": ["linkedin_ad_segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linkedin_deployment_config": {"name": "linkedin_deployment_config", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linked_in_ad_segment_id": {"name": "linked_in_ad_segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "linkedin_deployment_config_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "start_datetime": {"name": "start_datetime", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_datetime": {"name": "end_datetime", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"linkedin_deployment_config_linked_in_ad_segment_id_linked_in_ad_segment_id_fk": {"name": "linkedin_deployment_config_linked_in_ad_segment_id_linked_in_ad_segment_id_fk", "tableFrom": "linkedin_deployment_config", "tableTo": "linked_in_ad_segment", "schemaTo": "advertising", "columnsFrom": ["linked_in_ad_segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"deployment_config_start_date_time_constraint": {"name": "deployment_config_start_date_time_constraint", "value": "(\"advertising\".\"linkedin_deployment_config\".\"status\" NOT IN ('RUNNING', 'COMPLETED')) OR (\"advertising\".\"linkedin_deployment_config\".\"start_datetime\" IS NOT NULL)"}, "deployment_config_end_date_time_constraint": {"name": "deployment_config_end_date_time_constraint", "value": "(\"advertising\".\"linkedin_deployment_config\".\"status\" NOT IN ('COMPLETED')) OR (\"advertising\".\"linkedin_deployment_config\".\"end_datetime\" IS NOT NULL)"}}, "isRLSEnabled": false}, "advertising.linkedin_deployment_config_campaign": {"name": "linkedin_deployment_config_campaign", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linkedin_deployment_config_id": {"name": "linkedin_deployment_config_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "campaign_id": {"name": "campaign_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "impressions": {"name": "impressions", "type": "integer", "primaryKey": false, "notNull": false}, "clicks": {"name": "clicks", "type": "integer", "primaryKey": false, "notNull": false}, "conversions": {"name": "conversions", "type": "integer", "primaryKey": false, "notNull": false}, "cost": {"name": "cost", "type": "numeric(12, 2)", "primaryKey": false, "notNull": false}, "video_views": {"name": "video_views", "type": "integer", "primaryKey": false, "notNull": false}, "sends": {"name": "sends", "type": "integer", "primaryKey": false, "notNull": false}, "opens": {"name": "opens", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"linkedin_deployment_config_campaign_linkedin_deployment_config_id_linkedin_deployment_config_id_fk": {"name": "linkedin_deployment_config_campaign_linkedin_deployment_config_id_linkedin_deployment_config_id_fk", "tableFrom": "linkedin_deployment_config_campaign", "tableTo": "linkedin_deployment_config", "schemaTo": "advertising", "columnsFrom": ["linkedin_deployment_config_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "linkedin_deployment_config_campaign_campaign_id_linkedin_campaign_linkedin_audience_id_fk": {"name": "linkedin_deployment_config_campaign_campaign_id_linkedin_campaign_linkedin_audience_id_fk", "tableFrom": "linkedin_deployment_config_campaign", "tableTo": "linkedin_campaign", "schemaTo": "advertising", "columnsFrom": ["campaign_id"], "columnsTo": ["linkedin_audience_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linkedin_deployment_config_sponsored_creative": {"name": "linkedin_deployment_config_sponsored_creative", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linkedin_deployment_config_id": {"name": "linkedin_deployment_config_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "sponsored_creative_id": {"name": "sponsored_creative_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "impressions": {"name": "impressions", "type": "integer", "primaryKey": false, "notNull": false}, "clicks": {"name": "clicks", "type": "integer", "primaryKey": false, "notNull": false}, "conversions": {"name": "conversions", "type": "integer", "primaryKey": false, "notNull": false}, "cost": {"name": "cost", "type": "numeric(12, 2)", "primaryKey": false, "notNull": false}, "video_views": {"name": "video_views", "type": "integer", "primaryKey": false, "notNull": false}, "sends": {"name": "sends", "type": "integer", "primaryKey": false, "notNull": false}, "opens": {"name": "opens", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"linkedin_deployment_config_sponsored_creative_linkedin_deployment_config_id_linkedin_deployment_config_id_fk": {"name": "linkedin_deployment_config_sponsored_creative_linkedin_deployment_config_id_linkedin_deployment_config_id_fk", "tableFrom": "linkedin_deployment_config_sponsored_creative", "tableTo": "linkedin_deployment_config", "schemaTo": "advertising", "columnsFrom": ["linkedin_deployment_config_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "linkedin_deployment_config_sponsored_creative_sponsored_creative_id_linkedInSponsoredCreative_id_fk": {"name": "linkedin_deployment_config_sponsored_creative_sponsored_creative_id_linkedInSponsoredCreative_id_fk", "tableFrom": "linkedin_deployment_config_sponsored_creative", "tableTo": "linkedInSponsoredCreative", "schemaTo": "advertising", "columnsFrom": ["sponsored_creative_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linkedin_lead_form": {"name": "linkedin_lead_form", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linkedin_ad_account_id": {"name": "linkedin_ad_account_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "lead_gen_form_urn": {"name": "lead_gen_form_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "linkedin_lead_form_state", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'DRAFT'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"linkedin_lead_form_linkedin_ad_account_id_linkedin_ad_account_id_fk": {"name": "linkedin_lead_form_linkedin_ad_account_id_linkedin_ad_account_id_fk", "tableFrom": "linkedin_lead_form", "tableTo": "linkedin_ad_account", "schemaTo": "advertising", "columnsFrom": ["linkedin_ad_account_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linkedin_lead_form_lead": {"name": "linkedin_lead_form_lead", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linkedin_lead_form_response_id": {"name": "linkedin_lead_form_response_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "linkedin_lead_form_id": {"name": "linkedin_lead_form_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "linkedin_campaign_urn": {"name": "linkedin_campaign_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "linkedin_ad_account_id": {"name": "linkedin_ad_account_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "phone_number": {"name": "phone_number", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "text", "primaryKey": false, "notNull": false}, "job_title": {"name": "job_title", "type": "text", "primaryKey": false, "notNull": false}, "job_function": {"name": "job_function", "type": "text", "primaryKey": false, "notNull": false}, "seniority": {"name": "seniority", "type": "text", "primaryKey": false, "notNull": false}, "company_name": {"name": "company_name", "type": "text", "primaryKey": false, "notNull": false}, "company_size": {"name": "company_size", "type": "text", "primaryKey": false, "notNull": false}, "industry": {"name": "industry", "type": "text", "primaryKey": false, "notNull": false}, "degree": {"name": "degree", "type": "text", "primaryKey": false, "notNull": false}, "field_of_study": {"name": "field_of_study", "type": "text", "primaryKey": false, "notNull": false}, "school": {"name": "school", "type": "text", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": false}, "graduation_date": {"name": "graduation_date", "type": "date", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false}, "work_email": {"name": "work_email", "type": "text", "primaryKey": false, "notNull": false}, "linkedin_profile_link": {"name": "linkedin_profile_link", "type": "text", "primaryKey": false, "notNull": false}, "work_phone_number": {"name": "work_phone_number", "type": "text", "primaryKey": false, "notNull": false}, "lead_created_at": {"name": "lead_created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "lead_type": {"name": "lead_type", "type": "text", "primaryKey": false, "notNull": false}, "test_lead": {"name": "test_lead", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"linkedin_lead_form_lead_linkedin_lead_form_id_linkedin_lead_form_id_fk": {"name": "linkedin_lead_form_lead_linkedin_lead_form_id_linkedin_lead_form_id_fk", "tableFrom": "linkedin_lead_form_lead", "tableTo": "linkedin_lead_form", "schemaTo": "advertising", "columnsFrom": ["linkedin_lead_form_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "linkedin_lead_form_lead_linkedin_ad_account_id_linkedin_ad_account_id_fk": {"name": "linkedin_lead_form_lead_linkedin_ad_account_id_linkedin_ad_account_id_fk", "tableFrom": "linkedin_lead_form_lead", "tableTo": "linkedin_ad_account", "schemaTo": "advertising", "columnsFrom": ["linkedin_ad_account_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linkedInPost": {"name": "linkedInPost", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "single_image_post_id": {"name": "single_image_post_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "single_video_post_id": {"name": "single_video_post_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "single_document_post_id": {"name": "single_document_post_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "linkedin_post_type": {"name": "linkedin_post_type", "type": "linkedin_post_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"linkedInPost_single_image_post_id_linkedInSingleImagePost_id_fk": {"name": "linkedInPost_single_image_post_id_linkedInSingleImagePost_id_fk", "tableFrom": "linkedInPost", "tableTo": "linkedInSingleImagePost", "schemaTo": "advertising", "columnsFrom": ["single_image_post_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "linkedInPost_single_video_post_id_linkedin_single_video_post_id_fk": {"name": "linkedInPost_single_video_post_id_linkedin_single_video_post_id_fk", "tableFrom": "linkedInPost", "tableTo": "linkedin_single_video_post", "schemaTo": "advertising", "columnsFrom": ["single_video_post_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "linkedInPost_single_document_post_id_linkedin_single_document_post_id_fk": {"name": "linkedInPost_single_document_post_id_linkedin_single_document_post_id_fk", "tableFrom": "linkedInPost", "tableTo": "linkedin_single_document_post", "schemaTo": "advertising", "columnsFrom": ["single_document_post_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"linkedin_post_type_constraint": {"name": "linkedin_post_type_constraint", "value": "(\"advertising\".\"linkedInPost\".\"linkedin_post_type\" != 'SINGLE_IMAGE') OR (\"advertising\".\"linkedInPost\".\"single_image_post_id\" IS NOT NULL)"}, "linkedin_post_type_constraint_single_video": {"name": "linkedin_post_type_constraint_single_video", "value": "(\"advertising\".\"linkedInPost\".\"linkedin_post_type\" != 'SINGLE_VIDEO') OR (\"advertising\".\"linkedInPost\".\"single_video_post_id\" IS NOT NULL)"}, "linkedin_post_type_constraint_document": {"name": "linkedin_post_type_constraint_document", "value": "(\"advertising\".\"linkedInPost\".\"linkedin_post_type\" != 'DOCUMENT') OR (\"advertising\".\"linkedInPost\".\"single_document_post_id\" IS NOT NULL)"}}, "isRLSEnabled": false}, "advertising.linkedin_single_document_post": {"name": "linkedin_single_document_post", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linkedin_ad_segment_value_prop": {"name": "linkedin_ad_segment_value_prop", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "social_post_copy_type": {"name": "social_post_copy_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "call_to_action_copy_type": {"name": "call_to_action_copy_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "ad_program_creative_id": {"name": "ad_program_creative_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"linkedin_single_document_post_ad_program_creative_id_linkedin_ad_program_creative_id_fk": {"name": "linkedin_single_document_post_ad_program_creative_id_linkedin_ad_program_creative_id_fk", "tableFrom": "linkedin_single_document_post", "tableTo": "linkedin_ad_program_creative", "schemaTo": "advertising", "columnsFrom": ["ad_program_creative_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "singleDocumentPostvaluePropAndCopyTypeFk": {"name": "singleDocumentPostvaluePropAndCopyTypeFk", "tableFrom": "linkedin_single_document_post", "tableTo": "social_post_copy", "schemaTo": "advertising", "columnsFrom": ["linkedin_ad_segment_value_prop", "social_post_copy_type"], "columnsTo": ["linkedin_ad_segment_value_prop_id", "social_post_copy_type"], "onDelete": "no action", "onUpdate": "no action"}, "singleDocumentPostvaluePropAndCtaTypeFk": {"name": "singleDocumentPostvaluePropAndCtaTypeFk", "tableFrom": "linkedin_single_document_post", "tableTo": "social_post_call_to_action_copy", "schemaTo": "advertising", "columnsFrom": ["linkedin_ad_segment_value_prop", "call_to_action_copy_type"], "columnsTo": ["ad_segment_value_prop_id", "type"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linkedInSingleImagePost": {"name": "linkedInSingleImagePost", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linkedin_ad_segment_value_prop": {"name": "linkedin_ad_segment_value_prop", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "social_post_copy_type": {"name": "social_post_copy_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "call_to_action_copy_type": {"name": "call_to_action_copy_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "ad_program_creative_id": {"name": "ad_program_creative_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"linkedInSingleImagePost_ad_program_creative_id_linkedin_ad_program_creative_id_fk": {"name": "linkedInSingleImagePost_ad_program_creative_id_linkedin_ad_program_creative_id_fk", "tableFrom": "linkedInSingleImagePost", "tableTo": "linkedin_ad_program_creative", "schemaTo": "advertising", "columnsFrom": ["ad_program_creative_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "singleImagePostvaluePropAndCopyTypeFk": {"name": "singleImagePostvaluePropAndCopyTypeFk", "tableFrom": "linkedInSingleImagePost", "tableTo": "social_post_copy", "schemaTo": "advertising", "columnsFrom": ["linkedin_ad_segment_value_prop", "social_post_copy_type"], "columnsTo": ["linkedin_ad_segment_value_prop_id", "social_post_copy_type"], "onDelete": "no action", "onUpdate": "no action"}, "singleImagePostvaluePropAndCtaTypeFk": {"name": "singleImagePostvaluePropAndCtaTypeFk", "tableFrom": "linkedInSingleImagePost", "tableTo": "social_post_call_to_action_copy", "schemaTo": "advertising", "columnsFrom": ["linkedin_ad_segment_value_prop", "call_to_action_copy_type"], "columnsTo": ["ad_segment_value_prop_id", "type"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linkedin_single_video_post": {"name": "linkedin_single_video_post", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linkedin_ad_segment_value_prop": {"name": "linkedin_ad_segment_value_prop", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "social_post_copy_type": {"name": "social_post_copy_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "call_to_action_copy_type": {"name": "call_to_action_copy_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "ad_program_creative_id": {"name": "ad_program_creative_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"linkedin_single_video_post_ad_program_creative_id_linkedin_ad_program_creative_id_fk": {"name": "linkedin_single_video_post_ad_program_creative_id_linkedin_ad_program_creative_id_fk", "tableFrom": "linkedin_single_video_post", "tableTo": "linkedin_ad_program_creative", "schemaTo": "advertising", "columnsFrom": ["ad_program_creative_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "singleImagePostvaluePropAndCopyTypeFk": {"name": "singleImagePostvaluePropAndCopyTypeFk", "tableFrom": "linkedin_single_video_post", "tableTo": "social_post_copy", "schemaTo": "advertising", "columnsFrom": ["linkedin_ad_segment_value_prop", "social_post_copy_type"], "columnsTo": ["linkedin_ad_segment_value_prop_id", "social_post_copy_type"], "onDelete": "no action", "onUpdate": "no action"}, "linkedin_single_video_post_linkedin_ad_segment_value_prop_call_to_action_copy_type_social_post_call_to_action_copy_ad_segment_value_prop_id_type_fk": {"name": "linkedin_single_video_post_linkedin_ad_segment_value_prop_call_to_action_copy_type_social_post_call_to_action_copy_ad_segment_value_prop_id_type_fk", "tableFrom": "linkedin_single_video_post", "tableTo": "social_post_call_to_action_copy", "schemaTo": "advertising", "columnsFrom": ["linkedin_ad_segment_value_prop", "call_to_action_copy_type"], "columnsTo": ["ad_segment_value_prop_id", "type"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linkedInSponsoredCreative": {"name": "linkedInSponsoredCreative", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linkedin_campaign_id": {"name": "linkedin_campaign_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "linkedin_sponsored_creative_urn": {"name": "linkedin_sponsored_creative_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "linkedin_sponsored_creative_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "linkedin_sponsored_creative_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "linkedin_post_id": {"name": "linkedin_post_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "deprecated_conversation_copy_id": {"name": "deprecated_conversation_copy_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "conversation_call_to_action_id": {"name": "conversation_call_to_action_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"linkedInSponsoredCreative_linkedin_campaign_id_linkedin_campaign_linkedin_audience_id_fk": {"name": "linkedInSponsoredCreative_linkedin_campaign_id_linkedin_campaign_linkedin_audience_id_fk", "tableFrom": "linkedInSponsoredCreative", "tableTo": "linkedin_campaign", "schemaTo": "advertising", "columnsFrom": ["linkedin_campaign_id"], "columnsTo": ["linkedin_audience_id"], "onDelete": "no action", "onUpdate": "no action"}, "linkedInSponsoredCreative_linkedin_post_id_linkedInPost_id_fk": {"name": "linkedInSponsoredCreative_linkedin_post_id_linkedInPost_id_fk", "tableFrom": "linkedInSponsoredCreative", "tableTo": "linkedInPost", "schemaTo": "advertising", "columnsFrom": ["linkedin_post_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "linkedInSponsoredCreative_deprecated_conversation_copy_id_deprecated_conversation_copy_id_fk": {"name": "linkedInSponsoredCreative_deprecated_conversation_copy_id_deprecated_conversation_copy_id_fk", "tableFrom": "linkedInSponsoredCreative", "tableTo": "deprecated_conversation_copy", "schemaTo": "advertising", "columnsFrom": ["deprecated_conversation_copy_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "linkedInSponsoredCreative_conversation_call_to_action_id_conversation_call_to_action_copy_id_fk": {"name": "linkedInSponsoredCreative_conversation_call_to_action_id_conversation_call_to_action_copy_id_fk", "tableFrom": "linkedInSponsoredCreative", "tableTo": "conversation_call_to_action_copy", "schemaTo": "advertising", "columnsFrom": ["conversation_call_to_action_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"sponsored_creative_type_constraint": {"name": "sponsored_creative_type_constraint", "value": "(\"advertising\".\"linkedInSponsoredCreative\".\"type\" != 'POST') OR (\"advertising\".\"linkedInSponsoredCreative\".\"linkedin_post_id\" IS NOT NULL)"}, "deprecated_conversation_copy_constraint": {"name": "deprecated_conversation_copy_constraint", "value": "(\"advertising\".\"linkedInSponsoredCreative\".\"type\" != 'CONVERSATION') OR (\"advertising\".\"linkedInSponsoredCreative\".\"deprecated_conversation_copy_id\" IS NOT NULL)"}, "inmail_constraint": {"name": "inmail_constraint", "value": "(\"advertising\".\"linkedInSponsoredCreative\".\"type\" != 'INMAIL') OR (\"advertising\".\"linkedInSponsoredCreative\".\"conversation_call_to_action_id\" IS NOT NULL)"}}, "isRLSEnabled": false}, "advertising.linkedin_user": {"name": "linkedin_user", "schema": "advertising", "columns": {"organization_user_id": {"name": "organization_user_id", "type": "<PERSON><PERSON><PERSON>(33)", "primaryKey": true, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"linkedin_user_organization_user_id_organization_user_user_id_fk": {"name": "linkedin_user_organization_user_id_organization_user_user_id_fk", "tableFrom": "linkedin_user", "tableTo": "organization_user", "schemaTo": "core", "columnsFrom": ["organization_user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.linkedin_user_to_ad_account": {"name": "linkedin_user_to_ad_account", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "linkedin_ad_account_id": {"name": "linkedin_ad_account_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "linkedin_user_id": {"name": "linkedin_user_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"linkedin_user_to_ad_account_linkedin_ad_account_id_linkedin_ad_account_id_fk": {"name": "linkedin_user_to_ad_account_linkedin_ad_account_id_linkedin_ad_account_id_fk", "tableFrom": "linkedin_user_to_ad_account", "tableTo": "linkedin_ad_account", "schemaTo": "advertising", "columnsFrom": ["linkedin_ad_account_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "linkedin_user_to_ad_account_linkedin_user_id_linkedin_user_organization_user_id_fk": {"name": "linkedin_user_to_ad_account_linkedin_user_id_linkedin_user_organization_user_id_fk", "tableFrom": "linkedin_user_to_ad_account", "tableTo": "linkedin_user", "schemaTo": "advertising", "columnsFrom": ["linkedin_user_id"], "columnsTo": ["organization_user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.manual_bidding_event": {"name": "manual_bidding_event", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "campaign_id": {"name": "campaign_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "cost": {"name": "cost", "type": "numeric(12, 2)", "primaryKey": false, "notNull": true}, "budget_used_percentage": {"name": "budget_used_percentage", "type": "numeric(12, 2)", "primaryKey": false, "notNull": true}, "time_elapsed_percentage": {"name": "time_elapsed_percentage", "type": "numeric(12, 2)", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true}, "original_bid": {"name": "original_bid", "type": "numeric(12, 2)", "primaryKey": false, "notNull": true}, "time_elapsed": {"name": "time_elapsed", "type": "numeric(12, 2)", "primaryKey": false, "notNull": true}, "budget_used": {"name": "budget_used", "type": "numeric(12, 2)", "primaryKey": false, "notNull": true}, "budget": {"name": "budget", "type": "numeric(12, 2)", "primaryKey": false, "notNull": true}, "daily_or_total_budget": {"name": "daily_or_total_budget", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"manual_bidding_event_campaign_id_linkedin_campaign_linkedin_audience_id_fk": {"name": "manual_bidding_event_campaign_id_linkedin_campaign_linkedin_audience_id_fk", "tableFrom": "manual_bidding_event", "tableTo": "linkedin_campaign", "schemaTo": "advertising", "columnsFrom": ["campaign_id"], "columnsTo": ["linkedin_audience_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.organizationConversion": {"name": "organizationConversion", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "bigint", "primaryKey": false, "notNull": true}, "conversion_urn": {"name": "conversion_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"organizationConversion_organization_id_organization_id_fk": {"name": "organizationConversion_organization_id_organization_id_fk", "tableFrom": "organizationConversion", "tableTo": "organization", "schemaTo": "core", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organizationConversion_conversion_urn_unique": {"name": "organizationConversion_conversion_urn_unique", "nullsNotDistinct": false, "columns": ["conversion_urn"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.positioning": {"name": "positioning", "schema": "advertising", "columns": {"organization_id": {"name": "organization_id", "type": "bigint", "primaryKey": true, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"positioning_organization_id_organization_id_fk": {"name": "positioning_organization_id_organization_id_fk", "tableFrom": "positioning", "tableTo": "organization", "schemaTo": "core", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.segment_linked_in_audience_prefab": {"name": "segment_linked_in_audience_prefab", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "segment_id": {"name": "segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"segment_linked_in_audience_prefab_segment_id_segment_id_fk": {"name": "segment_linked_in_audience_prefab_segment_id_segment_id_fk", "tableFrom": "segment_linked_in_audience_prefab", "tableTo": "segment", "schemaTo": "core", "columnsFrom": ["segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.social_post_call_to_action_copy": {"name": "social_post_call_to_action_copy", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "call_to_action": {"name": "call_to_action", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "ad_segment_value_prop_id": {"name": "ad_segment_value_prop_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"social_post_call_to_action_copy_ad_segment_value_prop_id_linkedin_ad_segment_value_prop_id_fk": {"name": "social_post_call_to_action_copy_ad_segment_value_prop_id_linkedin_ad_segment_value_prop_id_fk", "tableFrom": "social_post_call_to_action_copy", "tableTo": "linkedin_ad_segment_value_prop", "schemaTo": "advertising", "columnsFrom": ["ad_segment_value_prop_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "social_post_call_to_action_copy_ad_segment_value_prop_id_linked": {"name": "social_post_call_to_action_copy_ad_segment_value_prop_id_linked", "tableFrom": "social_post_call_to_action_copy", "tableTo": "linkedin_ad_segment_value_prop", "schemaTo": "advertising", "columnsFrom": ["ad_segment_value_prop_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_call_to_action_and_type": {"name": "unique_call_to_action_and_type", "nullsNotDistinct": false, "columns": ["ad_segment_value_prop_id", "type"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.social_post_copy": {"name": "social_post_copy", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "linkedin_ad_segment_value_prop_id": {"name": "linkedin_ad_segment_value_prop_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "social_post_copy_type": {"name": "social_post_copy_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "lead_gen_form_urn": {"name": "lead_gen_form_urn", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"social_post_copy_linkedin_ad_segment_value_prop_id_linkedin_ad_segment_value_prop_id_fk": {"name": "social_post_copy_linkedin_ad_segment_value_prop_id_linkedin_ad_segment_value_prop_id_fk", "tableFrom": "social_post_copy", "tableTo": "linkedin_ad_segment_value_prop", "schemaTo": "advertising", "columnsFrom": ["linkedin_ad_segment_value_prop_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "social_post_copy_social_post_copy_type_social_post_copy_type_name_fk": {"name": "social_post_copy_social_post_copy_type_social_post_copy_type_name_fk", "tableFrom": "social_post_copy", "tableTo": "social_post_copy_type", "schemaTo": "advertising", "columnsFrom": ["social_post_copy_type"], "columnsTo": ["name"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_copy_type_and_value_prop": {"name": "unique_copy_type_and_value_prop", "nullsNotDistinct": false, "columns": ["linkedin_ad_segment_value_prop_id", "social_post_copy_type"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.social_post_copy_type": {"name": "social_post_copy_type", "schema": "advertising", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.social_post_style_guide": {"name": "social_post_style_guide", "schema": "advertising", "columns": {"organization_id": {"name": "organization_id", "type": "bigint", "primaryKey": true, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"social_post_style_guide_organization_id_organization_id_fk": {"name": "social_post_style_guide_organization_id_organization_id_fk", "tableFrom": "social_post_style_guide", "tableTo": "organization", "schemaTo": "core", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.stage": {"name": "stage", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "ad_segment_id": {"name": "ad_segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "index": {"name": "index", "type": "integer", "primaryKey": false, "notNull": true}, "stage_type": {"name": "stage_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "stage_status": {"name": "stage_status", "type": "stage_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"stage_ad_segment_id_linked_in_ad_segment_id_fk": {"name": "stage_ad_segment_id_linked_in_ad_segment_id_fk", "tableFrom": "stage", "tableTo": "linked_in_ad_segment", "schemaTo": "advertising", "columnsFrom": ["ad_segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.stage_step": {"name": "stage_step", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "stage_id": {"name": "stage_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "step_name": {"name": "step_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "stage_step_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "on_start_output_type": {"name": "on_start_output_type", "type": "stage_step_on_start_output_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": false}, "on_end_output_type": {"name": "on_end_output_type", "type": "stage_step_on_end_output_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"stage_step_stage_id_stage_id_fk": {"name": "stage_step_stage_id_stage_id_fk", "tableFrom": "stage_step", "tableTo": "stage", "schemaTo": "advertising", "columnsFrom": ["stage_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "advertising.value_prop_test_round": {"name": "value_prop_test_round", "schema": "advertising", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "stage_id": {"name": "stage_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "current_value_prop_id": {"name": "current_value_prop_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "contender_value_prop_id": {"name": "contender_value_prop_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "value_prop_test_round_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "winner_value_prop": {"name": "winner_value_prop", "type": "value_prop_test_round_winner", "typeSchema": "public", "primaryKey": false, "notNull": true}, "index": {"name": "index", "type": "smallint", "primaryKey": false, "notNull": true}, "step_id": {"name": "step_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "retries": {"name": "retries", "type": "smallint", "primaryKey": false, "notNull": true, "default": 0}, "max_retries": {"name": "max_retries", "type": "smallint", "primaryKey": false, "notNull": true, "default": 5}}, "indexes": {}, "foreignKeys": {"value_prop_test_round_step_id_stage_step_id_fk": {"name": "value_prop_test_round_step_id_stage_step_id_fk", "tableFrom": "value_prop_test_round", "tableTo": "stage_step", "schemaTo": "advertising", "columnsFrom": ["step_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core.job_function": {"name": "job_function", "schema": "core", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core.job_seniority": {"name": "job_seniority", "schema": "core", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core.organization": {"name": "organization", "schema": "core", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core.organization_user": {"name": "organization_user", "schema": "core", "columns": {"user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(33)", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"organization_user_user_id_user_id_fk": {"name": "organization_user_user_id_user_id_fk", "tableFrom": "organization_user", "tableTo": "user", "schemaTo": "core", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "organization_user_organization_id_organization_id_fk": {"name": "organization_user_organization_id_organization_id_fk", "tableFrom": "organization_user", "tableTo": "organization", "schemaTo": "core", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_user_and_organization": {"name": "unique_user_and_organization", "nullsNotDistinct": false, "columns": ["user_id", "organization_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core.segment": {"name": "segment", "schema": "core", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "bigint", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "annual_revenue_low_bound": {"name": "annual_revenue_low_bound", "type": "bigint", "primaryKey": false, "notNull": false}, "annual_revenue_high_bound": {"name": "annual_revenue_high_bound", "type": "bigint", "primaryKey": false, "notNull": false}, "number_of_employees_low_bound": {"name": "number_of_employees_low_bound", "type": "integer", "primaryKey": false, "notNull": false}, "number_of_employees_high_bound": {"name": "number_of_employees_high_bound", "type": "integer", "primaryKey": false, "notNull": false}, "annual_contact_value_low_bound": {"name": "annual_contact_value_low_bound", "type": "bigint", "primaryKey": false, "notNull": false}, "annual_contract_value_high_bound": {"name": "annual_contract_value_high_bound", "type": "bigint", "primaryKey": false, "notNull": false}, "job_function_id": {"name": "job_function_id", "type": "integer", "primaryKey": false, "notNull": false}, "job_seniority_id": {"name": "job_seniority_id", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "segment_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'GENERATING'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"segment_organization_id_organization_id_fk": {"name": "segment_organization_id_organization_id_fk", "tableFrom": "segment", "tableTo": "organization", "schemaTo": "core", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "segment_job_function_id_job_function_id_fk": {"name": "segment_job_function_id_job_function_id_fk", "tableFrom": "segment", "tableTo": "job_function", "schemaTo": "core", "columnsFrom": ["job_function_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "segment_job_seniority_id_job_seniority_id_fk": {"name": "segment_job_seniority_id_job_seniority_id_fk", "tableFrom": "segment", "tableTo": "job_seniority", "schemaTo": "core", "columnsFrom": ["job_seniority_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core.segment_value_prop": {"name": "segment_value_prop", "schema": "core", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "segment_id": {"name": "segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "value_prop": {"name": "value_prop", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"segment_value_prop_segment_id_segment_id_fk": {"name": "segment_value_prop_segment_id_segment_id_fk", "tableFrom": "segment_value_prop", "tableTo": "segment", "schemaTo": "core", "columnsFrom": ["segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core.segment_vertical": {"name": "segment_vertical", "schema": "core", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "segment_id": {"name": "segment_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"segment_vertical_segment_id_segment_id_fk": {"name": "segment_vertical_segment_id_segment_id_fk", "tableFrom": "segment_vertical", "tableTo": "segment", "schemaTo": "core", "columnsFrom": ["segment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core.user": {"name": "user", "schema": "core", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(33)", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "crm.account": {"name": "account", "schema": "crm", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "bigint", "primaryKey": false, "notNull": true}, "crm_id": {"name": "crm_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "vertical": {"name": "vertical", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "sub_vertical": {"name": "sub_vertical", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "number_of_employees": {"name": "number_of_employees", "type": "integer", "primaryKey": false, "notNull": false}, "annual_revenue": {"name": "annual_revenue", "type": "bigint", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "owner_id": {"name": "owner_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"account_organization_id_organization_id_fk": {"name": "account_organization_id_organization_id_fk", "tableFrom": "account", "tableTo": "organization", "schemaTo": "core", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "account_owner_id_crm_user_id_fk": {"name": "account_owner_id_crm_user_id_fk", "tableFrom": "account", "tableTo": "crm_user", "schemaTo": "crm", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"account_unique_crm_id": {"name": "account_unique_crm_id", "nullsNotDistinct": false, "columns": ["crm_id", "organization_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "crm.avoma_credentials": {"name": "avoma_credentials", "schema": "crm", "columns": {"organization_id": {"name": "organization_id", "type": "bigint", "primaryKey": true, "notNull": true}, "client_key": {"name": "client_key", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": true}, "client_secret": {"name": "client_secret", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": true}, "api_key": {"name": "api_key", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"avoma_credentials_organization_id_organization_id_fk": {"name": "avoma_credentials_organization_id_organization_id_fk", "tableFrom": "avoma_credentials", "tableTo": "organization", "schemaTo": "core", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "crm.contact": {"name": "contact", "schema": "crm", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "bigint", "primaryKey": false, "notNull": true}, "crm_id": {"name": "crm_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "account_id": {"name": "account_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "job_function_id": {"name": "job_function_id", "type": "bigint", "primaryKey": false, "notNull": false}, "job_seniority_id": {"name": "job_seniority_id", "type": "bigint", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"contact_organization_id_organization_id_fk": {"name": "contact_organization_id_organization_id_fk", "tableFrom": "contact", "tableTo": "organization", "schemaTo": "core", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "contact_account_id_account_id_fk": {"name": "contact_account_id_account_id_fk", "tableFrom": "contact", "tableTo": "account", "schemaTo": "crm", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "contact_job_function_id_job_function_id_fk": {"name": "contact_job_function_id_job_function_id_fk", "tableFrom": "contact", "tableTo": "job_function", "schemaTo": "core", "columnsFrom": ["job_function_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "contact_job_seniority_id_job_seniority_id_fk": {"name": "contact_job_seniority_id_job_seniority_id_fk", "tableFrom": "contact", "tableTo": "job_seniority", "schemaTo": "core", "columnsFrom": ["job_seniority_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"contact_unique_crm_id": {"name": "contact_unique_crm_id", "nullsNotDistinct": false, "columns": ["crm_id", "organization_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "crm.crm_user": {"name": "crm_user", "schema": "crm", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "crm_id": {"name": "crm_id", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "bigint", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"crm_user_organization_id_organization_id_fk": {"name": "crm_user_organization_id_organization_id_fk", "tableFrom": "crm_user", "tableTo": "organization", "schemaTo": "core", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"crm_user_unique_crm_id": {"name": "crm_user_unique_crm_id", "nullsNotDistinct": false, "columns": ["crm_id", "organization_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "crm.hubspot_credentials": {"name": "hubspot_credentials", "schema": "crm", "columns": {"organization_id": {"name": "organization_id", "type": "bigint", "primaryKey": true, "notNull": true}, "organization_user_id": {"name": "organization_user_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"hubspot_credentials_organization_id_organization_id_fk": {"name": "hubspot_credentials_organization_id_organization_id_fk", "tableFrom": "hubspot_credentials", "tableTo": "organization", "schemaTo": "core", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "hubspot_credentials_organization_user_id_organization_user_user_id_fk": {"name": "hubspot_credentials_organization_user_id_organization_user_user_id_fk", "tableFrom": "hubspot_credentials", "tableTo": "organization_user", "schemaTo": "core", "columnsFrom": ["organization_user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"hubspot_credentials_organization_user_id_unique": {"name": "hubspot_credentials_organization_user_id_unique", "nullsNotDistinct": false, "columns": ["organization_user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "crm.opportunity_primary_contact": {"name": "opportunity_primary_contact", "schema": "crm", "columns": {"opportunity_id": {"name": "opportunity_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "contact_id": {"name": "contact_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"opportunity_primary_contact_opportunity_id_opportunity_id_fk": {"name": "opportunity_primary_contact_opportunity_id_opportunity_id_fk", "tableFrom": "opportunity_primary_contact", "tableTo": "opportunity", "schemaTo": "crm", "columnsFrom": ["opportunity_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "opportunity_primary_contact_contact_id_contact_id_fk": {"name": "opportunity_primary_contact_contact_id_contact_id_fk", "tableFrom": "opportunity_primary_contact", "tableTo": "contact", "schemaTo": "crm", "columnsFrom": ["contact_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "crm.opportunity": {"name": "opportunity", "schema": "crm", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "crm_id": {"name": "crm_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "bigint", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "annual_contract_value": {"name": "annual_contract_value", "type": "bigint", "primaryKey": false, "notNull": false}, "account_id": {"name": "account_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "is_closed": {"name": "is_closed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "crm_created_date": {"name": "crm_created_date", "type": "date", "primaryKey": false, "notNull": true}, "is_won": {"name": "is_won", "type": "boolean", "primaryKey": false, "notNull": false}, "closed_date": {"name": "closed_date", "type": "date", "primaryKey": false, "notNull": false}, "owner_id": {"name": "owner_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"opportunity_account_id_account_id_fk": {"name": "opportunity_account_id_account_id_fk", "tableFrom": "opportunity", "tableTo": "account", "schemaTo": "crm", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "opportunity_owner_id_crm_user_id_fk": {"name": "opportunity_owner_id_crm_user_id_fk", "tableFrom": "opportunity", "tableTo": "crm_user", "schemaTo": "crm", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"opportunity_unique_crm_id": {"name": "opportunity_unique_crm_id", "nullsNotDistinct": false, "columns": ["crm_id", "organization_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.ab_test_round_winner": {"name": "ab_test_round_winner", "schema": "public", "values": ["CURRENT_BEST", "CONTENDER"]}, "public.ab_test_status": {"name": "ab_test_status", "schema": "public", "values": ["NOT_STARTED", "IN_PROGRESS", "COMPLETED", "FAILED", "CANCELLED"]}, "public.ad_creative_file_type": {"name": "ad_creative_file_type", "schema": "public", "values": ["IMAGE", "VIDEO", "DOCUMENT"]}, "public.audience_test_round_status": {"name": "audience_test_round_status", "schema": "public", "values": ["NOT_STARTED", "IN_PROGRESS", "COMPLETED"]}, "public.audience_test_round_winner_audience": {"name": "audience_test_round_winner_audience", "schema": "public", "values": ["CURRENT_BEST", "CONTESTER"]}, "public.status": {"name": "status", "schema": "public", "values": ["DRAFT", "ACTIVE", "ARCHIVED_DRAFT", "ARCHIVED_ACTIVE", "COMPLETED"]}, "public.type": {"name": "type", "schema": "public", "values": ["EVERGREEN", "EVENT_DRIVEN"]}, "public.objective_type": {"name": "objective_type", "schema": "public", "values": ["BRAND_AWARENESS", "CREATIVE_ENGAGEMENT", "ENGAGEMENT", "JOB_APPLICANT", "LEAD_GENERATION", "TALENT_LEAD", "VIDEO_VIEW", "WEBSITE_CONVERSION", "WEBSITE_TRAFFIC", "WEBSITE_VISIT"]}, "public.linkedin_campaign_status_enum": {"name": "linkedin_campaign_status_enum", "schema": "public", "values": ["ACTIVE", "PAUSED", "ARCHIVED", "CANCELLED", "DRAFT", "PENDING_DELETION", "REMOVED"]}, "public.linkedin_campaign_group_status_enum": {"name": "linkedin_campaign_group_status_enum", "schema": "public", "values": ["ACTIVE", "PAUSED", "ARCHIVED", "CANCELLED", "DRAFT", "PENDING_DELETION", "REMOVED"]}, "public.linkedin_deployment_config_status_enum": {"name": "linkedin_deployment_config_status_enum", "schema": "public", "values": ["NOT_STARTED", "RUNNING", "CANCELLED", "INTERRUPTED", "FAILED", "COMPLETED"]}, "public.linkedin_lead_form_state": {"name": "linkedin_lead_form_state", "schema": "public", "values": ["DRAFT", "PUBLISHED", "ARCHIVED"]}, "public.linkedin_post_type_enum": {"name": "linkedin_post_type_enum", "schema": "public", "values": ["SINGLE_IMAGE", "MULTI_IMAGE", "SINGLE_VIDEO", "DOCUMENT"]}, "public.linkedin_sponsored_creative_status_enum": {"name": "linkedin_sponsored_creative_status_enum", "schema": "public", "values": ["ACTIVE", "PAUSED", "ARCHIVED", "CANCELLED", "DRAFT", "PENDING_DELETION", "REMOVED"]}, "public.linkedin_sponsored_creative_type_enum": {"name": "linkedin_sponsored_creative_type_enum", "schema": "public", "values": ["POST", "CONVERSATION", "INMAIL"]}, "public.stage_status_enum": {"name": "stage_status_enum", "schema": "public", "values": ["NOT_STATED", "PROVISIONING", "RUNNING", "INTERRUPTED", "FATAL_PROBLEM", "FINISHED"]}, "public.stage_step_on_end_output_type_enum": {"name": "stage_step_on_end_output_type_enum", "schema": "public", "values": ["CALL_STEP", "CALL_END_WORKFLOW", "CALL_END_STEP", "CALL_CONFIG"]}, "public.stage_step_on_start_output_type_enum": {"name": "stage_step_on_start_output_type_enum", "schema": "public", "values": ["CALL_STEP", "CALL_END_WORKFLOW", "CALL_END_STEP", "CALL_CONFIG"]}, "public.stage_step_status_enum": {"name": "stage_step_status_enum", "schema": "public", "values": ["NOT_STATED", "PROVISIONING", "RUNNING", "INTERRUPTED", "FATAL_PROBLEM", "FINISHING", "FINISHED"]}, "public.value_prop_test_round_status": {"name": "value_prop_test_round_status", "schema": "public", "values": ["NOT_STARTED", "IN_PROGRESS", "COMPLETED"]}, "public.value_prop_test_round_winner": {"name": "value_prop_test_round_winner", "schema": "public", "values": ["CURRENT_BEST", "CONTENDER"]}, "public.segment_status": {"name": "segment_status", "schema": "public", "values": ["GENERATING", "ACTIVE", "ARCHIVED"]}}, "schemas": {"advertising": "advertising", "core": "core", "crm": "crm"}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}