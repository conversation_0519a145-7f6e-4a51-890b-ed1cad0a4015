import { describe, it, expect, vi, beforeEach } from "vitest";
import { EngagementRepository } from "../../../../../src/modules/crm/infrastructure/repositories/engagement.repository";
import { db } from "../../../../../src/database/db"; // Adjusted path to the actual db instance
import { engagementTable, NewEngagement } from "../../../../../src/database/schemas/crm/engagement.table"; // Adjusted path

// Mock the db object
vi.mock("../../../../../src/database/db", () => ({
  db: {
    insert: vi.fn().mockReturnThis(), // Mock insert and return chainable methods
    onConflictDoUpdate: vi.fn().mockResolvedValue([]), // Mock the final execution
  },
}));

// Helper to create mock engagements
const createMockEngagement = (idSuffix: string): NewEngagement => ({
  id: `uuid-${idSuffix}`,
  organizationId: 1,
  contactId: `contact-uuid-${idSuffix}`,
  externalId: `ext-${idSuffix}`,
  source: "hubspot",
  type: "meeting",
  status: "scheduled", // Ensure this matches the enum if strict typing is used
  subject: `Meeting ${idSuffix}`,
  startTime: new Date(),
  endTime: new Date(Date.now() + 3600 * 1000), // 1 hour later
  metadata: {},
  createdAt: new Date(),
  updatedAt: new Date(),
});

describe("EngagementRepository", () => {
  let engagementRepository: EngagementRepository;
  // Use aliased mock for easier access in tests
  const mockDb = db as unknown as {
      insert: vi.Mock;
      onConflictDoUpdate: vi.Mock;
  };

  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();
    engagementRepository = new EngagementRepository();
  });

  describe("createOrUpdateIfExistsMany", () => {
    it("should not call db.insert if the engagements array is empty", async () => {
      await engagementRepository.createOrUpdateIfExistsMany([]);
      expect(mockDb.insert).not.toHaveBeenCalled();
    });

    it("should call db.insert and onConflictDoUpdate with correct arguments for valid engagements", async () => {
      const engagementsToSave: NewEngagement[] = [
        createMockEngagement("1"),
        createMockEngagement("2"),
      ];
      // We need sql imported to check the set clause properly
      const { sql } = await import("drizzle-orm"); 

      await engagementRepository.createOrUpdateIfExistsMany(engagementsToSave);

      // Check if db.insert was called once with the engagementTable
      expect(mockDb.insert).toHaveBeenCalledTimes(1);
      expect(mockDb.insert).toHaveBeenCalledWith(engagementTable);

      // Check if the final method in the chain was called
      // This implies the intermediate calls like .values() happened.
      expect(mockDb.onConflictDoUpdate).toHaveBeenCalledTimes(1);

      // Check arguments of onConflictDoUpdate
      expect(mockDb.onConflictDoUpdate).toHaveBeenCalledWith(expect.objectContaining({
        target: [engagementTable.externalId, engagementTable.source, engagementTable.organizationId],
        set: expect.objectContaining({ // Check some key fields are being updated
            subject: sql`excluded.subject`,
            startTime: sql`excluded.start_time`,
            endTime: sql`excluded.end_time`,
            status: sql`excluded.status`,
            metadata: sql`excluded.metadata`,
            updatedAt: expect.any(Object), // Could check for sql placeholder if needed
          })
      }));
    });

    it("should throw an error if the database operation fails", async () => {
        const mockError = new Error("Database connection error");
        // Configure the mock to throw an error
        mockDb.onConflictDoUpdate.mockRejectedValueOnce(mockError);

        const engagementsToSave = [createMockEngagement("fail")];

        // Use try-catch or .rejects matcher to assert the error is thrown
        await expect(engagementRepository.createOrUpdateIfExistsMany(engagementsToSave))
              .rejects.toThrow(mockError);

         expect(mockDb.insert).toHaveBeenCalledTimes(1);
         expect(mockDb.onConflictDoUpdate).toHaveBeenCalledTimes(1);
    });

  });

  // Add tests for other methods if they exist in EngagementRepository
});
