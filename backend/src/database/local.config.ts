import type { Config } from "drizzle-kit";

const envArg = process.argv
  .find((arg) => arg.startsWith("--env="))
  ?.split("=")[1];
const PG_ENV_VAR =
  envArg == "prod"
    ? "PG_PROD_URL"
    : envArg == "staging"
      ? "PG_STAGING_URL"
      : "PG_URL";

if (envArg === "staging" || envArg === "prod") {
  const readline = require("readline").createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  readline.question(
    `WARNING: ARE YOU SURE YOU WANT TO PUSH TO ${envArg}? (Y/N) \n`,
    (confirmation: string) => {
      if (confirmation.toLowerCase() !== "y") {
        console.log("Aborting...");
        process.exit(1);
      }
      readline.close();
    },
  );
}

const DB_URL = process.env[PG_ENV_VAR];
if (!DB_URL) {
  throw new Error("PG_URL is not set");
}

export default {
  schema: "./src/database/schemas/**/*.ts",
  out: "./drizzle/local",
  dialect: "postgresql",
  dbCredentials: { url: DB_URL },
  schemaFilter: ["core", "advertising", "crm", "public"],
} satisfies Config;
