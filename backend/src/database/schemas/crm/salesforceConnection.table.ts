import { text, timestamp, uuid } from "drizzle-orm/pg-core";

import {
  createdAtCol,
  updatedAtCol,
  uuIdCol,
  uuIdFk,
} from "../../commonDbCols";
import { crmSchema } from "../schemas";
import { crmCredentialTable } from "./crmCredential.table";

export const salesforceConnectionTable = crmSchema.table(
  "salesforce_connection",
  {
    id: uuIdCol.primaryKey(),
    crmCredentialId: uuIdFk("crm_credential_id")
      .notNull()
      .references(() => crmCredentialTable.id),
    salesforceOrganizationId: text("salesforce_organization_id").notNull(),
    instanceUrl: text("instance_url").notNull(),
    salesforceUserId: text("salesforce_user_id").notNull(),
    lastSyncedAt: timestamp("last_synced_at", { mode: "date" }),
    createdAt: createdAtCol,
    updatedAt: updatedAtCol,
  },
);
