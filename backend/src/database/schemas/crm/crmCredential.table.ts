import {
  bigint,
  boolean,
  date,
  pgEnum,
  unique,
  varchar,
} from "drizzle-orm/pg-core";

import { organizationUser } from "../../../../../packages/database/src/schema/organizationUser";
import {
  bigIntFk,
  createdAtCol,
  updatedAtCol,
  uuIdCol,
  uuIdFk,
} from "../../commonDbCols";
import { organizationTable } from "../core/organization.table";
import { organizationUserTable } from "../core/organizationUser.table";
import { crmSchema } from "../schemas";

export const crmEnum = pgEnum("crm_type", ["hubspot", "salesforce"]);

export const crmCredentialTable = crmSchema.table("crm_credential", {
  id: uuIdCol,
  organizationId: bigIntFk("organization_id")
    .notNull()
    .references(() => organizationTable.id),
  organizationUserId: uuIdFk("organization_user_id")
    .notNull()
    .references(() => organizationUserTable.userId),
  crmType: crmEnum("crm_type").notNull(),
  accessToken: varchar("access_token", { length: 1000 }),
  refreshToken: varchar("refresh_token", { length: 1000 }),
  accessTokenExpiresAt: date("access_token_expires_at", {
    mode: "date",
  }),
  refreshTokenExpiresAt: date("refresh_token_expires_at", {
    mode: "date",
  }),

  createdAt: createdAtCol,
  updatedAt: updatedAtCol,
});
