import {
  integer,
  jsonb,
  pgEnum,
  text,
  timestamp,
  uuid,
} from "drizzle-orm/pg-core";

import {
  bigIntFk,
  createdAtCol,
  updatedAtCol,
  uuIdCol,
  uuIdFk,
} from "../../commonDbCols";
import { organizationTable } from "../core/organization.table";
import { crmSchema } from "../schemas";

export const mappingTypeEnum = pgEnum("mapping_type", ["utm", "first_meeting"]);

export const mappingDefinitionTable = crmSchema.table(
  "mapping_definition",
  {
    id: uuIdCol,
    mappingType: mappingTypeEnum("mapping_type").notNull(), // e.g. 'utm'
    fields: jsonb("fields").notNull(), // e.g. { utm_source: { required: true } }
    version: integer("version").notNull(),
    createdAt: createdAtCol,
    updatedAt: updatedAtCol,
  },
  (t) => ({
    uniqueTypeVersion: {
      unique: true,
      columns: [t.mappingType, t.version],
    },
  }),
);
