import { jsonb, pgEnum } from "drizzle-orm/pg-core";

import {
  bigIntFk,
  createdAtCol,
  updatedAtCol,
  uuIdCol,
  uuIdFk,
} from "../../commonDbCols";
import { organizationTable } from "../core/organization.table";
import { crmSchema } from "../schemas";
import { mappingDefinitionTable } from "./mappingDefinition.table";

export const crmEnum = pgEnum("crm_type", ["hubspot", "salesforce"]);

export const crmMappingTable = crmSchema.table(
  "crm_mapping",
  {
    id: uuIdCol,
    organizationId: bigIntFk("organization_id")
      .notNull()
      .references(() => organizationTable.id),
    crmType: crmEnum("crm_type"), // e.g. 'salesforce'
    mappingDefinitionId: uuIdFk("mapping_definition_id")
      .notNull()
      .references(() => mappingDefinitionTable.id),
    mappings: jsonb("mappings").notNull(), // e.g. { utm_source: "UTM_Source__c" }
    createdAt: createdAtCol,
    updatedAt: updatedAtCol,
  },
  (t) => ({
    uniqueMapping: {
      unique: true,
      columns: [t.organizationId, t.crmType, t.mappingDefinitionId],
    },
  }),
);
