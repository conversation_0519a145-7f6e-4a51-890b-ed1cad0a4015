import { jsonb, pgEnum, timestamp, unique, varchar } from "drizzle-orm/pg-core";

import {
  bigIntFk,
  createdAtCol,
  updatedAtCol,
  uuIdCol,
  uuIdFk,
} from "../../commonDbCols";
import { organizationTable } from "../core/organization.table";
import { crmSchema } from "../schemas";
import { contactTable } from "./contact.table";
import { sourceEnum } from "./crmCommon.enums"; // Use shared enum

// Define the types of engagements we can store
export const engagementTypeEnum = pgEnum("engagement_type", ["meeting"]);

// Define the possible statuses for engagements
export const engagementStatusEnum = pgEnum("engagement_status", [
  "scheduled",
  "completed",
  "rescheduled",
  "no_show",
  "canceled",
]);

export const engagementTable = crmSchema.table(
  "engagement",
  {
    id: uuIdCol, // Primary Key
    organizationId: bigIntFk("organization_id")
      .notNull()
      .references(() => organizationTable.id), // Removed onDelete
    contactId: uuIdFk("contact_id")
      .notNull()
      .references(() => contactTable.id), // Removed onDelete
    externalId: varchar("external_id", { length: 255 }).notNull(), // ID from the source CRM (HubSpot meeting ID, Salesforce Event ID)
    source: sourceEnum("source").notNull(), // Source CRM (hubspot or salesforce)
    type: engagementTypeEnum("type").notNull(), // Type of engagement (e.g., meeting)
    status: engagementStatusEnum("status"), // Status of the engagement
    subject: varchar("subject", { length: 512 }), // Meeting subject/title
    startTime: timestamp("start_time", { mode: "date" }), // Meeting start time
    endTime: timestamp("end_time", { mode: "date" }), // Meeting end time
    metadata: jsonb("metadata"), // For storing raw payload or extra fields from CRM
    createdAt: createdAtCol, // Standard created_at timestamp
    updatedAt: updatedAtCol, // Standard updated_at timestamp
  },
  (t) => ({
    // Ensure uniqueness for each engagement from a specific source within an organization
    engagementUniqueExternalId: unique("engagement_unique_external_id").on(
      t.externalId,
      t.source,
      t.organizationId,
      t.contactId,
    ),
  }),
);

export type Engagement = typeof engagementTable.$inferSelect; // TypeScript type
export type NewEngagement = typeof engagementTable.$inferInsert; // TypeScript type for insertion
