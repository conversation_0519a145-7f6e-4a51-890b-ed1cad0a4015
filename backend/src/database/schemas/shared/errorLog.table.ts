import { boolean, json, pgTable, text, timestamp, varchar } from "drizzle-orm/pg-core";
import { uuIdCol, bigIntFk } from "../../commonDbCols";
import { sharedSchema } from "../schemas";

export const errorLogTable = sharedSchema.table("error_log", {
  id: uuIdCol,
  organizationId: bigIntFk("organization_id"), // Optional for system-level errors
  errorType: varchar("error_type", { length: 50 }).notNull(),
  title: varchar("title", { length: 500 }).notNull(),
  description: text("description").notNull(),
  endpoint: varchar("endpoint", { length: 200 }),
  method: varchar("method", { length: 10 }),
  statusCode: varchar("status_code", { length: 10 }),
  metadata: json("metadata"),
  severity: varchar("severity", { length: 20 }).default("MEDIUM"), // LOW, MEDIUM, HIGH, CRITICAL
  isProcessed: boolean("is_processed").default(false).notNull(), // For tracking aggregation
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
});

export type ErrorLog = typeof errorLogTable.$inferSelect;
export type NewErrorLog = typeof errorLogTable.$inferInsert; 