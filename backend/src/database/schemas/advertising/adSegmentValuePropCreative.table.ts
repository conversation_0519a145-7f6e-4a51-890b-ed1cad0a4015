import { unique } from "drizzle-orm/pg-core";

import { uuIdCol, uuIdFk } from "../../commonDbCols";
import { adSchema } from "../schemas";
import { linkedInAdProgramCreativeTable } from "./linkedInAdProgramCreative.table";
import { linkedInAdSegmentValuePropTable } from "./linkedInAdSegmentValueProp.table";

export const adSegmentValuePropCreativeTable = adSchema.table(
  "ad_segment_value_prop_creative",
  {
    id: uuIdCol,
    adSegmentValuePropId: uuIdFk("ad_segment_value_prop_id")
      .references(() => linkedInAdSegmentValuePropTable.id)
      .notNull(),
    adProgramCreativeId: uuIdFk("ad_program_creative_id")
      .references(() => linkedInAdProgramCreativeTable.id)
      .notNull(),
  },
  (t) => ({
    uniqueAdSegmentValuePropAndCreative: unique(
      "unique_ad_segment_value_prop_and_creative",
    ).on(t.adSegmentValuePropId, t.adProgramCreativeId),
  }),
);
