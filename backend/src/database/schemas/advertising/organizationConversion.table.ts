import { varchar } from "drizzle-orm/pg-core";

import { bigIntFk, uuIdCol, uuIdFk } from "../../commonDbCols";
import { organizationTable } from "../core/organization.table";
import { adSchema } from "../schemas";

export const organizationConversionTable = adSchema.table(
  "organizationConversion",
  {
    id: uuIdCol,
    organizationId: bigIntFk("organization_id")
      .notNull()
      .references(() => organizationTable.id),
    conversionUrn: varchar("conversion_urn", { length: 255 })
      .notNull()
      .unique(),
    name: varchar("name", { length: 255 }).notNull(),
  },
);
