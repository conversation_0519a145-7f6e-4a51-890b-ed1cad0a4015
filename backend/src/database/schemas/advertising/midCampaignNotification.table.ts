import { varchar } from "drizzle-orm/pg-core";
import { z } from "zod";

import { abTestTypesSchema } from "../../../modules/advertising/domain/entites/abTest";
import { uuIdCol, uuIdFk } from "../../commonDbCols";
import { adSchema } from "../schemas";
import { linkedInAdSegmentTable } from "./linkedInAdSegment.table";

export const midCampaignNotificationTable = adSchema.table(
  "mid_campaign_notification",
  {
    adSegmentId: uuIdFk("ad_segment_id")
      .notNull()
      .references(() => linkedInAdSegmentTable.id)
      .primaryKey(),
    variantType: varchar("variant_type", { length: 255 })
      .notNull()
      .$type<z.infer<typeof abTestTypesSchema>>(),
  },
);
