import { varchar } from "drizzle-orm/pg-core";

import { uuIdCol, uuIdFk } from "../../commonDbCols";
import { adSchema } from "../schemas";
import { linkedInAdSegmentTable } from "./linkedInAdSegment.table";

export const adSegmentSelectedConversationCallToActionTypeTable =
  adSchema.table("ad_segment_selected_conversation_call_to_action_type", {
    id: uuIdCol,
    adSegmentId: uuIdFk("ad_segment_id")
      .references(() => linkedInAdSegmentTable.id)
      .notNull(),
    type: varchar("type", { length: 255 }).notNull(),
  });
