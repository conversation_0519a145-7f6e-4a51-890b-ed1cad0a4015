import { boolean, date, text, timestamp, varchar } from "drizzle-orm/pg-core";

import {
  createdAtCol,
  updatedAtCol,
  uuIdCol,
  uuIdFk,
} from "../../commonDbCols";
import { adSchema } from "../schemas";
import { linkedInAdAccountTable } from "./linkedInAdAccount.table";
import { linkedInLeadFormTable } from "./linkedInLeadForm.table";
import { contactTable } from "../crm/contact.table";

export const linkedInLeadFormLeadTable = adSchema.table(
  "linkedin_lead_form_lead",
  {
    id: uuIdCol,
    linkedInLeadFormResponseId: varchar("linkedin_lead_form_response_id", {
      length: 255,
    }).notNull(),
    linkedInLeadFormId: uuIdFk("linkedin_lead_form_id")
      .notNull()
      .references(() => linkedInLeadFormTable.id),
    linkedinCampaignUrn: varchar("linkedin_campaign_urn", {
      length: 255,
    }),
    linkedInAdAccountId: uuIdFk("linkedin_ad_account_id")
      .notNull()
      .references(() => linkedInAdAccountTable.id),
    firstName: text("first_name"),
    lastName: text("last_name"),
    phoneNumber: text("phone_number"),
    email: text("email"),
    city: text("city"),
    state: text("state"),
    country: text("country"),
    zipCode: text("zip_code"),
    jobTitle: text("job_title"),
    jobFunction: text("job_function"),
    seniority: text("seniority"),
    companyName: text("company_name"),
    companySize: text("company_size"),
    industry: text("industry"),
    degree: text("degree"),
    fieldOfStudy: text("field_of_study"),
    school: text("school"),
    startDate: date("start_date"),
    graduationDate: date("graduation_date"),
    gender: text("gender"),
    workEmail: text("work_email"),
    linkedinProfileLink: text("linkedin_profile_link"),
    workPhoneNumber: text("work_phone_number"),
    leadCreatedAt: timestamp("lead_created_at"),
    // Link to the main contact record if found/created via CRM import
    contactId: uuIdFk("contact_id").references(() => contactTable.id),
    leadType: text("lead_type"),
    testLead: boolean("test_lead").default(false),
    createdAt: createdAtCol,
    updatedAt: updatedAtCol,
  },
);
