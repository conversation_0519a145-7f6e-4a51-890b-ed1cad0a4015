import { text, unique, varchar } from "drizzle-orm/pg-core";

import { ConversationMessageCopy } from "../../../modules/advertising/domain/entites/conversationMessageCopy";
import { uuIdCol, uuIdFk } from "../../commonDbCols";
import { adSchema } from "../schemas";
import { conversationCopyTypeTable } from "./conversationCopyType.table";
import { conversationSubjectCopyTable } from "./conversationSubjectCopy.table";
import { linkedInAdSegmentValuePropTable } from "./linkedInAdSegmentValueProp.table";

export const conversationMessageCopyTable = adSchema.table(
  "conversation_message_copy",
  {
    id: uuIdCol,
    content: text("content").notNull(),
    conversationSubjectCopyId: uuIdFk("conversation_subject_copy_id")
      .notNull()
      .references(() => conversationSubjectCopyTable.id),
    type: varchar("type", { length: 255 })
      .notNull()
      .$type<ConversationMessageCopy["type"]>(),
    status: varchar("status", { length: 255 })
      .default("DRAFT")
      .notNull()
      .$type<"DRAFT" | "ACTIVE" | "ARCHIVED">(),
  },
  (t) => ({
    uniqueCopyTypeAndSubjectCopyId: unique(
      "unique_copy_type_and_subject_copy_id",
    ).on(t.conversationSubjectCopyId, t.type),
  }),
);
