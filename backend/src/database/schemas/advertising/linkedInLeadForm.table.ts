import { integer, pgEnum, text, timestamp, varchar } from "drizzle-orm/pg-core";

import {
  createdAtCol,
  updatedAtCol,
  uuIdCol,
  uuIdFk,
} from "../../commonDbCols";
import { adSchema } from "../schemas";
import { linkedInAdAccountTable } from "./linkedInAdAccount.table";
import { linkedInCampaignTable } from "./linkedInCampaign.table";

export const linkedInLeadFormReviewStatusEnum = pgEnum(
  "linkedin_lead_form_state",
  ["DRAFT", "PUBLISHED", "ARCHIVED"],
);

export const linkedInLeadFormTable = adSchema.table("linkedin_lead_form", {
  id: uuIdCol,
  linkedInAdAccountId: uuIdFk("linkedin_ad_account_id")
    .notNull()
    .references(() => linkedInAdAccountTable.id),

  leadGenFormUrn: varchar("lead_gen_form_urn", { length: 255 }).notNull(),
  version: integer("version").notNull().default(1),
  name: text("name").notNull(),
  state: linkedInLeadFormReviewStatusEnum("state").notNull().default("DRAFT"),
  createdAt: createdAtCol,
  updatedAt: updatedAtCol,
});
