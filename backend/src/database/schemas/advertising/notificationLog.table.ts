import { date, jsonb, timestamp, varchar } from "drizzle-orm/pg-core";

import { bigIntFk, createdAtCol, uuIdCol } from "../../commonDbCols";
import { organizationTable } from "../core/organization.table";
import { adSchema } from "../schemas";

export const notificationLogTable = adSchema.table("notification_log", {
  id: uuIdCol,
  organizationId: bigIntFk("organization_id")
    .notNull()
    .references(() => organizationTable.id),
  notificationType: varchar("notification_type", { length: 50 }).notNull(),
  weekStartDate: date("week_start_date").notNull(),
  weekEndDate: date("week_end_date").notNull(),
  sentAt: timestamp("sent_at").notNull().defaultNow(),
  status: varchar("status", { length: 20 }).notNull().default("sent"),
  slackMessageTs: varchar("slack_message_ts", { length: 50 }),
  campaignData: jsonb("campaign_data"),
  createdAt: createdAtCol,
}); 