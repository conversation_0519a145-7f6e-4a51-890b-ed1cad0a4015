import { boolean, unique, varchar } from "drizzle-orm/pg-core";

import { uuIdCol, uuIdFk } from "../../commonDbCols";
import { adSchema } from "../schemas";

export const createNewConversationValuePropStateTable = adSchema.table(
  "create_new_conversation_value_prop_state",
  {
    id: uuIdCol,
    adSegmentId: uuIdFk("ad_segment").notNull(),
    valueProp: varchar("value_prop", { length: 255 }).notNull(),
    conversationSubjectCopyType: varchar("conversation_subject_copy_type", {
      length: 255,
    }).notNull(),
    conversationSubjectCopy: varchar("conversation_subject_copy", {
      length: 255,
    }).notNull(),
    conversationMessageCopyType: varchar("conversation_message_copy_type", {
      length: 255,
    }).notNull(),
    conversationMessageCopy: varchar("conversation_message_copy", {
      length: 255,
    }).notNull(),
    conversationCallToActionCopyType: varchar(
      "conversation_call_to_action_copy_type",
      {
        length: 255,
      },
    ).notNull(),
    conversationCallToActionCopy: varchar("conversation_call_to_action_copy", {
      length: 255,
    }).notNull(),
    toBeUsed: boolean("to_be_used").notNull().default(true),
  },
  (t) => [unique("cncvps_unique_adSegment_vp").on(t.adSegmentId, t.valueProp)],
);
