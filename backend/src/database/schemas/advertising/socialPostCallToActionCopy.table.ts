import { foreignKey, unique, varchar } from "drizzle-orm/pg-core";

import { uuIdCol, uuIdFk } from "../../commonDbCols";
import { adSchema } from "../schemas";
import { linkedInAdSegmentValuePropTable } from "./linkedInAdSegmentValueProp.table";

export const socialPostCallToActionCopyTable = adSchema.table(
  "social_post_call_to_action_copy",
  {
    id: uuIdCol,
    type: varchar("type", { length: 255 }).notNull(),
    callToAction: varchar("call_to_action", { length: 255 }).notNull(),
    adSegmentValuePropId: uuIdFk("ad_segment_value_prop_id")
      .notNull()
      .references(() => linkedInAdSegmentValuePropTable.id),
    status: varchar("status", { length: 255 })
      .default("DRAFT")
      .notNull()
      .$type<"DRAFT" | "ACTIVE" | "ARCHIVED">(),
  },
  (t) => [
    foreignKey({
      columns: [t.adSegmentValuePropId],
      foreignColumns: [linkedInAdSegmentValuePropTable.id],
      name: "social_post_call_to_action_copy_ad_segment_value_prop_id_linked",
    }),
    unique("unique_call_to_action_and_type").on(t.adSegmentValuePropId, t.type),
  ],
);
