import { sql } from "drizzle-orm";
import {
  check,
  PgColumn,
  pgEnum,
  real,
  smallint,
  timestamp,
  uuid,
  varchar,
} from "drizzle-orm/pg-core";

import { linkedInDeploymentConfig } from "../../../../../../packages/database/src/schema/linkedInDeployment/linkedInDeploymentConfig";
import { uuIdCol, uuIdFk } from "../../../commonDbCols";
import { adSchema } from "../../schemas";
import { conversationCallToActionCopyTable } from "../conversationCallToActionCopy.table";
import { conversationMessageCopyTable } from "../conversationMessageCopy.table";
import { conversationSubjectCopyTable } from "../conversationSubjectCopy.table";
import { linkedInAdProgramCreativeTable } from "../linkedInAdProgramCreative.table";
import { linkedInAdSegmentValuePropTable } from "../linkedInAdSegmentValueProp.table";
import { linkedInAudienceTable } from "../linkedInAudience.table";
import { linkedInDeploymentConfigTable } from "../linkedInDeploymentConfig.table";
import { socialPostCallToActionCopyTable } from "../socialPostCallToActionCopy.table";
import { socialPostCopyTable } from "../socialPostCopy.table";
import { stageTable } from "../stage.table";

export const abTestStatusEnum = pgEnum("ab_test_status", [
  "NOT_STARTED",
  "IN_PROGRESS",
  "COMPLETED",
  "FAILED",
  "CANCELLED",
  "AUTO_RESOLVED",
  "USER_RESOLVED",
]);

export const abTestRoundWinnerEnum = pgEnum("ab_test_round_winner", [
  "CURRENT_BEST",
  "CONTENDER",
]);

export const createAbTestTables = (
  abTestType: string,
  testTargetForiengKeyCol: PgColumn,
) => {
  const abTestTable = baseAbTestTableSchemaCreator(
    abTestType,
    testTargetForiengKeyCol,
  );
  const abTestRoundTable = baseAbTestRoundTableSchemaCreator(
    abTestType,
    abTestTable.stageId,
    testTargetForiengKeyCol,
  );
  const abTestRoundDayTable = baseAbTestRoundDayTableSchemaCreator(
    abTestType,
    abTestRoundTable.id,
  );

  const abTestRoundDayMetricsTable = abTestRoundDayMetricsTableCreator(
    abTestType,
    abTestRoundDayTable.id,
  );

  const abTestRoundMetadataTable = baseAbTestRoundTableMetadataSchemaCreator(
    abTestType,
    abTestRoundTable.id,
  );

  return {
    abTestTable,
    abTestRoundTable,
    abTestRoundDayTable,
    abTestRoundDayMetricsTable,
    abTestRoundMetadataTable,
  };
};

const baseAbTestTableSchemaCreator = (
  abTestType: string,
  testTargetForiengKeyCol: PgColumn,
) => {
  return adSchema.table(
    `${abTestType}_ab_test`,
    {
      stageId: uuIdFk("stage_id")
        .notNull()
        .references(() => stageTable.id)
        .primaryKey(),
      status: abTestStatusEnum("status").notNull(),
      winnerId: uuIdFk("winner_id").references(() => testTargetForiengKeyCol),
    },
    (t) => ({}),
  );
};

export type IAbTestTable = ReturnType<typeof baseAbTestTableSchemaCreator>;
export type IAbTestRoundTable = ReturnType<
  typeof baseAbTestRoundTableSchemaCreator
>;
export type IAbTestRoundDayTable = ReturnType<
  typeof baseAbTestRoundDayTableSchemaCreator
>;
export type IAbTestRoundDayMetricsTable = ReturnType<
  typeof abTestRoundDayMetricsTableCreator
>;
export type IAbTestRoundMetadataTable = ReturnType<
  typeof baseAbTestRoundTableMetadataSchemaCreator
>;

const baseAbTestRoundTableSchemaCreator = (
  abTestType: string,
  abTestTableStageIdCol: PgColumn,
  testTargetForiengKeyCol: PgColumn,
) => {
  return adSchema.table(
    `${abTestType}_ab_test_round`,
    {
      id: uuIdCol,
      abTestId: uuIdFk("ab_test_id")
        .references(() => abTestTableStageIdCol)
        .notNull(),
      status: abTestStatusEnum("status").notNull(),
      roundIndex: smallint("round_index").notNull(),
      currentBestId: uuIdFk("current_best_id")
        .references(() => testTargetForiengKeyCol)
        .notNull(),
      contenderId: uuIdFk("contender_id")
        .references(() => testTargetForiengKeyCol)
        .notNull(),
      winner: abTestRoundWinnerEnum("winner"),
    },
    (t) => ({}),
  );
};

const baseAbTestRoundTableMetadataSchemaCreator = (
  abTestType: string,
  abTestRoundTableStageIdCol: PgColumn,
) => {
  return adSchema.table(`${abTestType}_ab_test_round_metadata`, {
    abTestRoundId: uuIdFk("ab_test_round_id")
      .references(() => abTestRoundTableStageIdCol)
      .notNull()
      .primaryKey(),
    currentBestAudienceId: uuIdFk("audience_id")
      .references(() => linkedInAudienceTable.id)
      .notNull(),
    currentBestAudienceType: varchar("audience_type", {
      length: 255,
    }).notNull(),
    currentBestValuePropId: uuIdFk("value_prop_id")
      .references(() => linkedInAdSegmentValuePropTable.id)
      .notNull(),
    currentBestValuePropType: varchar("value_prop_type", {
      length: 255,
    }).notNull(),
    currentBestAdProgramCreativeId: uuIdFk("ad_program_creative_id").references(
      () => linkedInAdProgramCreativeTable.id,
    ),
    currentBestAdProgramCreativeType: varchar("ad_program_creative_type", {
      length: 255,
    }),
    currentBestSocialPostBodyCopyId: uuIdFk(
      "social_post_body_copy_id",
    ).references(() => socialPostCopyTable.id),
    currentBestSocialPostBodyCopyType: varchar("social_post_body_copy_type", {
      length: 255,
    }),
    currentBestSocialPostCallToActionId: uuIdFk(
      "social_post_call_to_action_id",
    ).references(() => socialPostCallToActionCopyTable.id),
    currentBestSocialPostCallToActionType: varchar(
      "social_post_call_to_action_type",
      {
        length: 255,
      },
    ),
    currentBestConversationSubjectCopyId: uuIdFk(
      "conversation_subject_copy_id",
    ).references(() => conversationSubjectCopyTable.id),
    currentBestConversationSubjectCopyType: varchar(
      "conversation_subject_copy_type",
      {
        length: 255,
      },
    ),
    currentBestConversationMessageCopyId: uuIdFk(
      "conversation_message_copy_id",
    ).references(() => conversationMessageCopyTable.id),
    currentBestConversationMessageCopyType: varchar(
      "conversation_message_copy_type",
      {
        length: 255,
      },
    ),
    currentBestConversationCallToActionId: uuIdFk(
      "conversation_call_to_action_id",
    ).references(() => conversationCallToActionCopyTable.id),
    currentBestConversationCallToActionType: varchar(
      "conversation_call_to_action_type",
      {
        length: 255,
      },
    ),
    contenderAudienceId: uuIdFk("contender_audience_id")
      .references(() => linkedInAudienceTable.id)
      .notNull(),
    contenderAudienceType: varchar("contender_audience_type", {
      length: 255,
    }).notNull(),
    contenderValuePropId: uuIdFk("contender_value_prop_id")
      .references(() => linkedInAdSegmentValuePropTable.id)
      .notNull(),
    contenderValuePropType: varchar("contender_value_prop_type", {
      length: 255,
    }).notNull(),
    contenderAdProgramCreativeId: uuIdFk(
      "contender_ad_program_creative_id",
    ).references(() => linkedInAdProgramCreativeTable.id),
    contenderAdProgramCreativeType: varchar(
      "contender_ad_program_creative_type",
      {
        length: 255,
      },
    ),
    contenderSocialPostBodyCopyId: uuIdFk(
      "contender_social_post_body_copy_id",
    ).references(() => socialPostCopyTable.id),
    contenderSocialPostBodyCopyType: varchar(
      "contender_social_post_body_copy_type",
      {
        length: 255,
      },
    ),
    contenderSocialPostCallToActionId: uuIdFk(
      "contender_social_post_call_to_action_id",
    ).references(() => socialPostCallToActionCopyTable.id),
    contenderSocialPostCallToActionType: varchar(
      "contender_social_post_call_to_action_type",
      {
        length: 255,
      },
    ),
    contenderConversationSubjectCopyId: uuIdFk(
      "contender_conversation_subject_copy_id",
    ).references(() => conversationSubjectCopyTable.id),
    contenderConversationSubjectCopyType: varchar(
      "contender_conversation_subject_copy_type",
      {
        length: 255,
      },
    ),
    contenderConversationMessageCopyId: uuIdFk(
      "contender_conversation_message_copy_id",
    ).references(() => conversationMessageCopyTable.id),
    contenderConversationMessageCopyType: varchar(
      "contender_conversation_message_copy_type",
      {
        length: 255,
      },
    ),
    contenderConversationCallToActionId: uuIdFk(
      "contender_conversation_call_to_action_id",
    ).references(() => conversationCallToActionCopyTable.id),
    contenderConversationCallToActionType: varchar(
      "contender_conversation_call_to_action_type",
      {
        length: 255,
      },
    ),
  });
};

const baseAbTestRoundDayTableSchemaCreator = (
  abTestType: string,
  abTestRoundTableStageIdCol: PgColumn,
) => {
  return adSchema.table(
    `${abTestType}_ab_test_round_day`,
    {
      id: uuIdCol,
      abTestRoundId: uuIdFk("ab_test_round_id")
        .references(() => abTestRoundTableStageIdCol)
        .notNull(),
      dayIndex: smallint("day_index").notNull(),
      winner: varchar("winner", { length: 255 }).$type<
        "CURRENT_BEST" | "CONTENDER" | "NO_DECISION"
      >(),
      status: abTestStatusEnum("status").notNull(),
      currentBestResult: smallint("current_best_result"),
      contenderResult: smallint("contender_result"),
      deploymentConfigId: uuIdFk("deployment_config_id")
        .references(() => linkedInDeploymentConfigTable.id)
        .notNull(),
    },
    (t) => ({}),
  );
};

const abTestRoundDayMetricsTableCreator = (
  abTestType: string,
  abTestRoundDayTableStageIdCol: PgColumn,
) => {
  return adSchema.table(`${abTestType}_ab_test_round_day_metrics`, {
    abTestRoundDayId: uuIdFk("ab_test_round_day_id")
      .references(() => abTestRoundDayTableStageIdCol)
      .notNull()
      .primaryKey(),
    currentBestPrimaryMetricForDay: real(
      "current_best_primary_metric_for_day",
    ).notNull(),
    currentBestSecondaryMetricForDay: real(
      "current_best_secondary_metric_for_day",
    ).notNull(),
    contenderPrimaryMetricCountForDay: real(
      "contender_primary_metric_count_for_day",
    ).notNull(),
    contenderSecondaryMetricCountForDay: real(
      "contender_secondary_metric_count_for_day",
    ).notNull(),
    currentBestPrimaryMetricMean: real(
      "current_best_primary_metric_mean",
    ).notNull(),
    currentBestSecondaryMetricMean: real(
      "current_best_secondary_metric_mean",
    ).notNull(),
    contenderPrimaryMetricMean: real("contender_primary_metric_mean").notNull(),
    contenderSecondaryMetricMean: real(
      "contender_secondary_metric_mean",
    ).notNull(),
    currentBestPrimaryMetricSum: real(
      "current_best_primary_metric_sum",
    ).notNull(),
    currentBestSecondaryMetricSum: real(
      "current_best_secondary_metric_sum",
    ).notNull(),
    contenderPrimaryMetricSum: real("contender_primary_metric_sum").notNull(),
    contenderSecondaryMetricSum: real(
      "contender_secondary_metric_sum",
    ).notNull(),
    primaryMetricTTestResult: real("primary_metric_t_test_result"),
    secondaryMetricTTestResult: real("secondary_metric_t_test_result"),
    decision: varchar("decision", { length: 255 })
      .notNull()
      .$type<"CURRENT_BEST" | "CONTENDER" | "NO_DECISION">(),
    decisionType: varchar("decision_type", { length: 255 })
      .notNull()
      .$type<
        | "PRIMARY_METRIC_T_TEST"
        | "SECONDARY_METRIC_T_TEST"
        | "PRIMARY_METRIC_SUM"
        | "SECONDARY_METRIC_SUM"
        | "DATA_LENGTH_IS_ONE"
        | "NO_STATISTICAL_DIFFERENCE_IN_PRIMARY_AND_SECONDARY_METRICS_BEFORE_5_DAYS"
        | "RANDOM"
      >(),
    createdAt: timestamp("created_at").notNull(),
  });
};
