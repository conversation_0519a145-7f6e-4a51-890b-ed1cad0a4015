import { conversationCallToActionCopyTable } from "../conversationCallToActionCopy.table";
import { conversationMessageCopyTable } from "../conversationMessageCopy.table";
import { conversationSubjectCopyTable } from "../conversationSubjectCopy.table";
import { linkedInAdProgramCreativeTable } from "../linkedInAdProgramCreative.table";
import { linkedInAdSegmentValuePropTable } from "../linkedInAdSegmentValueProp.table";
import { linkedInAudienceTable } from "../linkedInAudience.table";
import { socialPostCallToActionCopyTable } from "../socialPostCallToActionCopy.table";
import { socialPostCopyTable } from "../socialPostCopy.table";
import { baseAdSegmentBestVariantTableSchemaCreator } from "./_baseAdSegmentBestVariantUtils";

export const adSegmentBestAudienceVariantTable =
  baseAdSegmentBestVariantTableSchemaCreator(
    "audience",
    linkedInAudienceTable.id,
  );

export const adSegmentBestValuePropVariantTable =
  baseAdSegmentBestVariantTableSchemaCreator(
    "valueProp",
    linkedInAdSegmentValuePropTable.id,
  );

export const adSegmentBestCreativeVariantTable =
  baseAdSegmentBestVariantTableSchemaCreator(
    "creative",
    linkedInAdProgramCreativeTable.id,
  );

export const adSegmentBestConversationSubjectVariantTable =
  baseAdSegmentBestVariantTableSchemaCreator(
    "conversationSubject",
    conversationSubjectCopyTable.id,
  );

export const adSegmentBestSocialPostBodyCopyVariantTable =
  baseAdSegmentBestVariantTableSchemaCreator(
    "socialPostBodyCopy",
    socialPostCopyTable.id,
  );

export const adSegmentBestConversationMessageCopyVariantTable =
  baseAdSegmentBestVariantTableSchemaCreator(
    "conversationMessageCopy",
    conversationMessageCopyTable.id,
  );

export const adSegmentBestConversationCallToActionCopyVariantTable =
  baseAdSegmentBestVariantTableSchemaCreator(
    "conversationCallToActionCopy",
    conversationCallToActionCopyTable.id,
  );

export const adSegmentBestSocialPostCallToActionCopyVariantTable =
  baseAdSegmentBestVariantTableSchemaCreator(
    "socialPostCallToActionCopy",
    socialPostCallToActionCopyTable.id,
  );
