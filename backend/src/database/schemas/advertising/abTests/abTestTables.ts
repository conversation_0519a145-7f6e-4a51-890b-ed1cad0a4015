import { conversationCallToActionCopySchema } from "../../../../modules/advertising/domain/entites/conversationCallToActionCopy";
import { conversationCallToActionCopyTable } from "../conversationCallToActionCopy.table";
import { conversationMessageCopyTable } from "../conversationMessageCopy.table";
import { conversationSubjectCopyTable } from "../conversationSubjectCopy.table";
import { linkedInAdProgramCreativeTable } from "../linkedInAdProgramCreative.table";
import { linkedInAdSegmentValuePropTable } from "../linkedInAdSegmentValueProp.table";
import { linkedInCampaignTable } from "../linkedInCampaign.table";
import { linkedInSponsoredCreativeTable } from "../linkedInSponsoredCreative.table";
import { socialPostCallToActionCopyTable } from "../socialPostCallToActionCopy.table";
import { socialPostCopyTable } from "../socialPostCopy.table";
import { createAbTestTables } from "./_baseAbTestTableSchemaUtils";

// Audience Test Tables
export const audienceTestTables = createAbTestTables(
  "audience",
  linkedInCampaignTable.linkedInAudienceId,
);
export const audienceTestTable = audienceTestTables.abTestTable;
export const audienceTestRoundTable = audienceTestTables.abTestRoundTable;
export const audienceTestRoundDayTable = audienceTestTables.abTestRoundDayTable;
export const audienceTestRoundDayMetricsTable =
  audienceTestTables.abTestRoundDayMetricsTable;
export const audienceTestRoundMetadataTable =
  audienceTestTables.abTestRoundMetadataTable;

// Value Test Tables
export const valueTestTables = createAbTestTables(
  "valueProp",
  linkedInAdSegmentValuePropTable.id,
);
export const valuePropTestTable = valueTestTables.abTestTable;
export const valuePropTestRoundTable = valueTestTables.abTestRoundTable;
export const valuePropTestRoundDayTable = valueTestTables.abTestRoundDayTable;
export const valuePropTestRoundDayMetricsTable =
  valueTestTables.abTestRoundDayMetricsTable;
export const valuePropTestRoundMetadataTable =
  valueTestTables.abTestRoundMetadataTable;

// Creative Test Tables
export const creativeTestTables = createAbTestTables(
  "creative",
  linkedInAdProgramCreativeTable.id,
);
export const creativeTestTable = creativeTestTables.abTestTable;
export const creativeTestRoundTable = creativeTestTables.abTestRoundTable;
export const creativeTestRoundDayTable = creativeTestTables.abTestRoundDayTable;
export const creativeTestRoundDayMetricsTable =
  creativeTestTables.abTestRoundDayMetricsTable;
export const creativeTestRoundMetadataTable =
  creativeTestTables.abTestRoundMetadataTable;

// Conversation Test Tables
export const conversationSubjectTestTables = createAbTestTables(
  "conversationSubject",
  conversationSubjectCopyTable.id,
);
export const conversationSubjectTestTable =
  conversationSubjectTestTables.abTestTable;
export const conversationSubjectTestRoundTable =
  conversationSubjectTestTables.abTestRoundTable;
export const conversationSubjectTestRoundDayTable =
  conversationSubjectTestTables.abTestRoundDayTable;
export const conversationSubjectTestRoundDayMetricsTable =
  conversationSubjectTestTables.abTestRoundDayMetricsTable;
export const conversationSubjectTestRoundMetadataTable =
  conversationSubjectTestTables.abTestRoundMetadataTable;

// Social Post Body Copy Test Tables
export const socialPostBodyCopyTestTables = createAbTestTables(
  "socialPostBodyCopy",
  socialPostCopyTable.id,
);
export const socialPostBodyCopyTestTable =
  socialPostBodyCopyTestTables.abTestTable;
export const socialPostBodyCopyTestRoundTable =
  socialPostBodyCopyTestTables.abTestRoundTable;
export const socialPostBodyCopyTestRoundDayTable =
  socialPostBodyCopyTestTables.abTestRoundDayTable;
export const socialPostBodyCopyTestRoundDayMetricsTable =
  socialPostBodyCopyTestTables.abTestRoundDayMetricsTable;
export const socialPostBodyCopyTestRoundMetadataTable =
  socialPostBodyCopyTestTables.abTestRoundMetadataTable;

// Conversation Message Copy Test Tables
export const conversationMessageCopyTestTables = createAbTestTables(
  "conversationMessageCopy",
  conversationMessageCopyTable.id,
);
export const conversationMessageCopyTestTable =
  conversationMessageCopyTestTables.abTestTable;
export const conversationMessageCopyTestRoundTable =
  conversationMessageCopyTestTables.abTestRoundTable;
export const conversationMessageCopyTestRoundDayTable =
  conversationMessageCopyTestTables.abTestRoundDayTable;
export const conversationMessageCopyTestRoundDayMetricsTable =
  conversationMessageCopyTestTables.abTestRoundDayMetricsTable;
export const conversationMessageCopyTestRoundMetadataTable =
  conversationMessageCopyTestTables.abTestRoundMetadataTable;

// Conversation Call To Action Test Tables
export const conversationCallToActionTestTables = createAbTestTables(
  "conversationCallToAction",
  conversationCallToActionCopyTable.id,
);
export const conversationCallToActionTestTable =
  conversationCallToActionTestTables.abTestTable;
export const conversationCallToActionTestRoundTable =
  conversationCallToActionTestTables.abTestRoundTable;
export const conversationCallToActionTestRoundDayTable =
  conversationCallToActionTestTables.abTestRoundDayTable;
export const conversationCallToActionTestRoundDayMetricsTable =
  conversationCallToActionTestTables.abTestRoundDayMetricsTable;
export const conversationCallToActionTestRoundMetadataTable =
  conversationCallToActionTestTables.abTestRoundMetadataTable;

// Social Post Call To Action Test Tables
export const socialPostCallToActionTestTables = createAbTestTables(
  "socialPostCallToAction",
  socialPostCallToActionCopyTable.id,
);
export const socialPostCallToActionTestTable =
  socialPostCallToActionTestTables.abTestTable;
export const socialPostCallToActionTestRoundTable =
  socialPostCallToActionTestTables.abTestRoundTable;
export const socialPostCallToActionTestRoundDayTable =
  socialPostCallToActionTestTables.abTestRoundDayTable;
export const socialPostCallToActionTestRoundDayMetricsTable =
  socialPostCallToActionTestTables.abTestRoundDayMetricsTable;
export const socialPostCallToActionTestRoundMetadataTable =
  socialPostCallToActionTestTables.abTestRoundMetadataTable;
