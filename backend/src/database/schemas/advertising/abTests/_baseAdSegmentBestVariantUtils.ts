import { PgColumn, unique } from "drizzle-orm/pg-core";

import { uuIdCol, uuIdFk } from "../../../commonDbCols";
import { adSchema } from "../../schemas";
import { linkedInAdSegmentTable } from "../linkedInAdSegment.table";

export const baseAdSegmentBestVariantTableSchemaCreator = (
  variant: string,
  testTargetForiengKeyCol: PgColumn,
) => {
  return adSchema.table(
    `ad_segment_best_${variant}`,
    {
      id: uuIdCol,
      adSegmentId: uuIdFk("ad_segment_id")
        .notNull()
        .references(() => linkedInAdSegmentTable.id)
        .notNull(),
      variantId: uuIdFk("variant_id")
        .references(() => testTargetForiengKeyCol)
        .notNull(),
    },
    (t) => [unique(`$asb_${variant}_unqiue_ad_segment_id`).on(t.adSegmentId)],
  );
};

export type IAdSegmentBestVariantTable = ReturnType<
  typeof baseAdSegmentBestVariantTableSchemaCreator
>;
