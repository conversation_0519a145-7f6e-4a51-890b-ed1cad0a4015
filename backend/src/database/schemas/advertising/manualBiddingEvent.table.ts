import { numeric, timestamp, varchar } from "drizzle-orm/pg-core";

import { uuIdCol, uuIdFk } from "../../commonDbCols";
import { adSchema } from "../schemas";
import { linkedInCampaignTable } from "./linkedInCampaign.table";

export const manualBiddingEventTable = adSchema.table("manual_bidding_event", {
  id: uuIdCol,
  linkedInCampaignId: uuIdFk("campaign_id")
    .references(() => linkedInCampaignTable.linkedInAudienceId)
    .notNull(),
  newBid: numeric("cost", { precision: 12, scale: 2 }).notNull(),
  budgetUsedPercentage: numeric("budget_used_percentage", {
    precision: 12,
    scale: 2,
  }).notNull(),
  timeElapsedPercentage: numeric("time_elapsed_percentage", {
    precision: 12,
    scale: 2,
  }).notNull(),
  timestamp: timestamp("timestamp").notNull(),
  originalBid: numeric("original_bid", {
    precision: 12,
    scale: 2,
  }).notNull(),
  timeElapsed: numeric("time_elapsed", {
    precision: 12,
    scale: 2,
  }).notNull(),
  budgetUsed: numeric("budget_used", {
    precision: 12,
    scale: 2,
  }).notNull(),
  budget: numeric("budget", {
    precision: 12,
    scale: 2,
  }).notNull(),
  dailyOrTotalBudget: varchar("daily_or_total_budget", {
    length: 255,
  })
    .notNull()
    .$type<"monthly" | "total">(),
  minBid: numeric("min_bid", {
    precision: 12,
    scale: 2,
  }).notNull(),
  maxBid: numeric("max_bid", {
    precision: 12,
    scale: 2,
  }).notNull(),
  minSuggestedBid: numeric("min_suggested_bid", {
    precision: 12,
    scale: 2,
  }).notNull(),
  maxSuggestedBid: numeric("max_suggested_bid", {
    precision: 12,
    scale: 2,
  }).notNull(),
  suggestedBid: numeric("suggested_bid", {
    precision: 12,
    scale: 2,
  }).notNull(),
  decision: varchar("decision", {
    length: 255,
  }).notNull(),
  bidType: varchar("bid_type", {
    length: 255,
  }).notNull(),
});
