import { text, unique, varchar } from "drizzle-orm/pg-core";

import { ConversationCallToActionCopy } from "../../../modules/advertising/domain/entites/conversationCallToActionCopy";
import { ConversationMessageCopy } from "../../../modules/advertising/domain/entites/conversationMessageCopy";
import { uuIdCol, uuIdFk } from "../../commonDbCols";
import { adSchema } from "../schemas";
import { conversationCallToActionCopyTypeTable } from "./conversationCallToActionCopyType.table";
import { conversationCopyTypeTable } from "./conversationCopyType.table";
import { conversationMessageCopyTable } from "./conversationMessageCopy.table";
import { conversationSubjectCopyTable } from "./conversationSubjectCopy.table";
import { linkedInAdSegmentValuePropTable } from "./linkedInAdSegmentValueProp.table";

export const conversationCallToActionCopyTable = adSchema.table(
  "conversation_call_to_action_copy",
  {
    id: uuIdCol,
    content: text("content").notNull(),
    conversationMessageCopyId: uuIdFk("conversation_message_copy_id")
      .notNull()
      .references(() => conversationMessageCopyTable.id),
    type: varchar("type", { length: 255 })
      .notNull()
      .$type<ConversationCallToActionCopy["type"]>(),
    status: varchar("status", { length: 255 })
      .notNull()
      .default("DRAFT")
      .$type<ConversationCallToActionCopy["status"]>(),
  },
  (t) => ({
    uniqueCopyTypeAndMessageCopyId: unique(
      "unique_copy_type_and_message_copy_id",
    ).on(t.conversationMessageCopyId, t.type),
  }),
);
