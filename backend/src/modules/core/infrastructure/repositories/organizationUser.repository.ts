import { eq } from "drizzle-orm";

import { db } from "../../../../database/db";
import { organizationUserTable } from "../../../../database/schemas/core/organizationUser.table";
import { IOrganizationUserRepository } from "../../application/interfaces/repositories/organizationUser.repository.interface";
import { Organization } from "../../domain/entites/organization";
import { OrganizationUser } from "../../domain/entites/organizationUser";

export class OrganizationUserRepository implements IOrganizationUserRepository {
  async selectAll(): Promise<OrganizationUser[]> {
    const res = await db.select().from(organizationUserTable);
    return res.map((row) =>
      OrganizationUser({
        organizationId: row.organizationId,
        userId: row.userId,
      }),
    );
  }

  async getOneByOrganizationId(
    organizationId: number,
  ): Promise<OrganizationUser | null> {
    const res = await db
      .select()
      .from(organizationUserTable)
      .where(eq(organizationUserTable.organizationId, organizationId));
    if (!res[0]) {
      return null;
    }
    return OrganizationUser({
      organizationId: res[0].organizationId,
      userId: res[0].userId,
    });
  }
}
