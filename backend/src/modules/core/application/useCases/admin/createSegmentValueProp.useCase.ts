import { createUuid } from "../../../utils/uuid";
import { CreateSegmentValuePropRequestDto } from "../../dtos/segmentValueProp/createSegmentValueProp.dto";
import { ISegmentRepository } from "../../interfaces/repositories/segment.repository.interface";
import { ISegmentValuePropRepository } from "../../interfaces/repositories/segmentValueProp.repository.interface";

export class CreateSegmentValuePropUseCase {
  constructor(
    private readonly segmentRepository: ISegmentRepository,
    private readonly segmentValuePropRepository: ISegmentValuePropRepository,
  ) {}

  async execute(input: CreateSegmentValuePropRequestDto) {
    const segment = await this.segmentRepository.getOne(input.segmentId);

    if (!segment) {
      throw new Error("Segment not found");
    }

    const segmentValueProp =
      await this.segmentValuePropRepository.createValueProp({
        id: createUuid(),
        segmentId: input.segmentId,
        name: input.name,
      });

    return segmentValueProp;
  }
}
