import { SegmentValuePropRepository } from "../../../infrastructure/repositories/segmentValueProp.repository";
import { DeleteSegmentValuePropRequestDto } from "../../dtos/segmentValueProp/deleteSegmentValueProp.dto";

export class DeleteSegmentValuePropUseCase {
  constructor(
    private readonly segmentValuePropRepository: SegmentValuePropRepository,
  ) {}

  async execute(input: DeleteSegmentValuePropRequestDto) {
    await this.segmentValuePropRepository.deleteOne(input.valuePropId);
  }
}
