import { EditSegmentValuePropRequestDto } from "../../dtos/segmentValueProp/editSegmentValueProp.dto";
import { ISegmentValuePropRepository } from "../../interfaces/repositories/segmentValueProp.repository.interface";

export class EditSegmentValuePropUseCase {
  constructor(
    private readonly segmentValuePropRepository: ISegmentValuePropRepository,
  ) {}

  async execute(input: EditSegmentValuePropRequestDto) {
    await this.segmentValuePropRepository.updateOne(
      input.valuePropId,
      input.name,
    );
  }
}
