import { GetSegmentRequestDto } from "../../dtos/segment/getSegment.dto";
import { ISegmentRepository } from "../../interfaces/repositories/segment.repository.interface";

export class GetSegmentAdminUseCase {
  constructor(private readonly segmentRepository: ISegmentRepository) {}

  async execute(input: GetSegmentRequestDto) {
    const segment = await this.segmentRepository.getOne(input.segmentId);
    return segment;
  }
}
