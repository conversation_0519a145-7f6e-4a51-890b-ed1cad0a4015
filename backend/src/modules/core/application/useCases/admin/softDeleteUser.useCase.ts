import { <PERSON><PERSON><PERSON> } from "@clerk/backend";
import { UserService } from "../../../domain/services/user.service";
import { OrganizationRepository } from "../../../infrastructure/repositories/organization.repository";
import { SoftDeleteUserRequestDto } from "../../dtos/user/softDeleteUser.dto";
import { ITransaction } from "../../interfaces/infrastructure/services/transactionManager.service.interface";

export class SoftDeleteUserUseCase {
  constructor(
    private readonly ctx: {
      userService: UserService;
      organizationRepository: OrganizationRepository;
      clerkClient: ClerkClient;
    },
  ) {}

  async execute(input: SoftDeleteUserRequestDto, tx?: ITransaction) {
    const organization = await this.ctx.organizationRepository.getOne(
      input.organizationId,
    );
    if (!organization) {
      throw new Error("Organization not found");
    }

    // Check if user exists and is not already deleted
    const user = await this.ctx.userService.getUser(input.userId);
    if (!user) {
      throw new Error("User not found or already deleted");
    }

    // Verify user belongs to the organization
    if (user.organizationId !== input.organizationId) {
      throw new Error("User does not belong to this organization");
    }

    // Soft delete user in our database
    await this.ctx.userService.softDeleteUser(input.userId, tx);

    // Delete user from Clerk
    try {
      await this.ctx.clerkClient.users.deleteUser(input.userId);
    } catch (error) {
      console.error("Failed to delete user from Clerk:", error);
      // For now, we'll log the error but not fail the operation
      // since the user is already soft deleted in our database
    }
  }
} 