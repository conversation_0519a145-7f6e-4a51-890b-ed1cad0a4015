import { GetSegmentValuePropForSegmentRequestDto } from "../../dtos/segmentValueProp/getSegmentValuePropForSegment.dto";
import { ISegmentValuePropRepository } from "../../interfaces/repositories/segmentValueProp.repository.interface";

export class GetValuePropsForSegmentUseCase {
  constructor(
    private readonly segmentValuePropRepository: ISegmentValuePropRepository,
  ) {}

  async execute(input: GetSegmentValuePropForSegmentRequestDto) {
    const valueProps =
      await this.segmentValuePropRepository.getValuePropsForSegment(
        input.segmentId,
      );

    return valueProps;
  }
}
