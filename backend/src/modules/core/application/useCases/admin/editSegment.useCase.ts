import { Segment } from "../../../domain/entites/segment";
import { createUuid } from "../../../utils/uuid";
import { EditSegmentDto } from "../../dtos/segment/editSegment.dto";
import { ISegmentRepository } from "../../interfaces/repositories/segment.repository.interface";

export class EditSegmentUseCase {
  constructor(private readonly segmentRepository: ISegmentRepository) {}

  async execute(dto: EditSegmentDto): Promise<void> {
    const segment = Segment({
      id: dto.id,
      name: dto.name,
      verticals: dto.verticals,
      annualRevenueLowBound: dto.annualRevenueLowBound,
      annualRevenueHighBound: dto.annualRevenueHighBound,
      annualContractValueLowBound: dto.annualContractValueLowBound,
      annualContractValueHighBound: dto.annualContractValueHighBound,
      numberOfEmployeesLowBound: dto.numberOfEmployeesLowBound,
      numberOfEmployeesHighBound: dto.numberOfEmployeesHighBound,
      jobFunction: dto.jobFunction,
      jobSeniority: dto.jobSeniority,
      status: "ACTIVE",
      organizationId: dto.organizationId,
    });
    await this.segmentRepository.updateOne(segment);
  }
}
