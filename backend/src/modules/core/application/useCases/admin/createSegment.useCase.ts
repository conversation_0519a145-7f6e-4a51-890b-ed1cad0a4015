import { Segment } from "../../../domain/entites/segment";
import { createUuid } from "../../../utils/uuid";
import { CreateSegmentDto } from "../../dtos/segment/createSegment.dto";
import { ISegmentRepository } from "../../interfaces/repositories/segment.repository.interface";

export class CreateSegmentUseCase {
  constructor(private readonly segmentRepository: ISegmentRepository) {}

  async execute(dto: CreateSegmentDto): Promise<Segment> {
    const segment = Segment({
      id: createUuid(),
      name: dto.name,
      verticals: dto.verticals,
      annualRevenueLowBound: dto.annualRevenueLowBound,
      annualRevenueHighBound: dto.annualRevenueHighBound,
      annualContractValueLowBound: dto.annualContractValueLowBound,
      annualContractValueHighBound: dto.annualContractValueHighBound,
      numberOfEmployeesLowBound: dto.numberOfEmployeesLowBound,
      numberOfEmployeesHighBound: dto.numberOfEmployeesHighBound,
      jobFunction: dto.jobFunction,
      jobSeniority: dto.jobSeniority,
      status: "ACTIVE",
      organizationId: dto.organizationId,
    });
    await this.segmentRepository.createOne(segment);
    return segment;
  }
}
