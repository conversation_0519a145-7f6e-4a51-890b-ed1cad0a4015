import { UpdateSegmentDto } from "../../dtos/segment/updateSegmentStatus.dto";
import { ISegmentRepository } from "../../interfaces/repositories/segment.repository.interface";

export class UpdateSegmentStatusUseCase {
  constructor(private readonly segmentRepository: ISegmentRepository) {}

  async execute(input: UpdateSegmentDto): Promise<void> {
    await this.segmentRepository.updateStatus(input.segmentId, input.status);
  }
}
