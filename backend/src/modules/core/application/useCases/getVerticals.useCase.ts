import { IAccountRepository } from "../../../crm/application/interfaces/infrastructure/repositories/account.repository.interface";
import { GetVerticalsRequestDto } from "../dtos/account/getVerticals.dto";

export class GetVerticalsUseCase {
  constructor(private readonly accountRepository: IAccountRepository) {}
  async execute(request: GetVerticalsRequestDto) {
    const verticals = await this.accountRepository.getVerticalsForOrganization(
      request.organizationId,
    );
    return verticals;
  }
}
