import { z } from "zod";

import { JobFunction } from "../../../domain/valueObjects/jobFunction";
import { JobSeniority } from "../../../domain/valueObjects/jobSenitory";

export const createSegmentDto = z.object({
  organizationId: z.number().positive(),
  name: z.string().optional().nullable(),
  verticals: z.array(z.string()),
  subVertical: z.string().nullable(),
  annualRevenueLowBound: z.number().nullable(),
  annualRevenueHighBound: z.number().nullable(),
  annualContractValueLowBound: z.number().nullable(),
  annualContractValueHighBound: z.number().nullable(),
  numberOfEmployeesLowBound: z.number().nullable(),
  numberOfEmployeesHighBound: z.number().nullable(),
  jobFunction: JobFunction.nullable(),
  jobSeniority: JobSeniority.nullable(),
});

export type CreateSegmentDto = z.infer<typeof createSegmentDto>;
