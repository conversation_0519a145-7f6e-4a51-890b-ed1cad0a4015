import { eq } from "drizzle-orm";

import { db } from "../../../database/db";
import { Transaction } from "../../../database/dbTransactionType";
import { midCampaignNotificationTable } from "../../../database/schemas/advertising/midCampaignNotification.table";
import { IMidCampaignNotificationRepository } from "../application/interfaces/infrastructure/repositories/midCampaignNotification.repository.interface";
import { MidCampaignNotification } from "../domain/entites/midCampaignNotification";

export class MidCampaignNotificationRepository
  implements IMidCampaignNotificationRepository
{
  async createOne(
    midCampaignNotification: MidCampaignNotification,
    tx?: Transaction,
  ): Promise<MidCampaignNotification> {
    const invoker = tx ?? db;
    const result = await invoker
      .insert(midCampaignNotificationTable)
      .values(midCampaignNotification)
      .returning();
    return midCampaignNotification;
  }
  async getForAdSegment(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<MidCampaignNotification | null> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(midCampaignNotificationTable)
      .where(eq(midCampaignNotificationTable.adSegmentId, adSegmentId));
    if (!result[0]) {
      return null;
    }
    return result[0];
  }
  async deleteOne(adSegmentId: string, tx?: Transaction): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .delete(midCampaignNotificationTable)
      .where(eq(midCampaignNotificationTable.adSegmentId, adSegmentId));
  }
}
