import { eq, inArray } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { adSegmentSelectedConversationMessageTypeTable } from "../../../../database/schemas/advertising/adSegmentSelectedConversationMessageType.table";
import { IAdSegmentSelectedConversationMessageTypeRepository } from "../../application/interfaces/infrastructure/repositories/adSegmentSelectedConversationMessageType.repository.interface";
import { AdSegmentSelectedConversationMessageType } from "../../domain/entites/adSegmentSelectedConversationMessageType";

export class AdSegmentSelectedConversationMessageTypeRepository
  implements IAdSegmentSelectedConversationMessageTypeRepository
{
  async createOne(
    input: AdSegmentSelectedConversationMessageType,
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker.insert(adSegmentSelectedConversationMessageTypeTable).values({
      adSegmentId: input.adSegmentId,
      type: input.type,
      id: input.id,
    });
  }
  async deleteOne(id: string, tx?: Transaction): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .delete(adSegmentSelectedConversationMessageTypeTable)
      .where(eq(adSegmentSelectedConversationMessageTypeTable.id, id));
    return;
  }
  async createMany(
    input: AdSegmentSelectedConversationMessageType[],
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (input.length === 0) {
      return;
    }
    await invoker.insert(adSegmentSelectedConversationMessageTypeTable).values(
      input.map((i) => ({
        adSegmentId: i.adSegmentId,
        type: i.type,
        id: i.id,
      })),
    );
  }
  async deleteManyById(
    conversationSubjectCopyIds: string[],
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (conversationSubjectCopyIds.length === 0) {
      return;
    }
    await invoker
      .delete(adSegmentSelectedConversationMessageTypeTable)
      .where(
        inArray(
          adSegmentSelectedConversationMessageTypeTable.id,
          conversationSubjectCopyIds,
        ),
      );
  }
  async deleteAllForAdSegment(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .delete(adSegmentSelectedConversationMessageTypeTable)
      .where(
        eq(
          adSegmentSelectedConversationMessageTypeTable.adSegmentId,
          adSegmentId,
        ),
      );
  }
  async getAllForAdSegment(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<AdSegmentSelectedConversationMessageType[]> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(adSegmentSelectedConversationMessageTypeTable)
      .where(
        eq(
          adSegmentSelectedConversationMessageTypeTable.adSegmentId,
          adSegmentId,
        ),
      );
    return res.map((r) => AdSegmentSelectedConversationMessageType(r));
  }
}
