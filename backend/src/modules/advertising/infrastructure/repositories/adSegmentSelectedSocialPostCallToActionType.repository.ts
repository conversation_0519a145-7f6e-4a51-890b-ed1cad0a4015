import { eq, inArray } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { adSegmentSelectedSocialPostCallToActionTypeTable } from "../../../../database/schemas/advertising/adSegmentSelectedSocialPostCallToActionType.table";
import { IAdSegmentSelectedSocialPostCallToActionTypeRepository } from "../../application/interfaces/infrastructure/repositories/adSegmentSelectedSocialPostCallToActionType.repository.interface";
import { AdSegmentSelectedSocialPostCallToActionType } from "../../domain/entites/adSegmentSelectedSocialPostCallToActionType";

export class AdSegmentSelectedSocialPostCallToActionTypeRepository
  implements IAdSegmentSelectedSocialPostCallToActionTypeRepository
{
  async createOne(
    input: AdSegmentSelectedSocialPostCallToActionType,
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .insert(adSegmentSelectedSocialPostCallToActionTypeTable)
      .values({
        adSegmentId: input.adSegmentId,
        type: input.type,
        id: input.id,
      });
  }

  async deleteOne(id: string, tx?: Transaction): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .delete(adSegmentSelectedSocialPostCallToActionTypeTable)
      .where(eq(adSegmentSelectedSocialPostCallToActionTypeTable.id, id));
    return;
  }

  async createMany(
    input: AdSegmentSelectedSocialPostCallToActionType[],
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (input.length === 0) {
      return;
    }
    await invoker
      .insert(adSegmentSelectedSocialPostCallToActionTypeTable)
      .values(
        input.map((i) => ({
          adSegmentId: i.adSegmentId,
          type: i.type,
          id: i.id,
        })),
      );
  }

  async deleteManyById(
    socialPostCallToActionTypeIds: string[],
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (socialPostCallToActionTypeIds.length === 0) {
      return;
    }
    await invoker
      .delete(adSegmentSelectedSocialPostCallToActionTypeTable)
      .where(
        inArray(
          adSegmentSelectedSocialPostCallToActionTypeTable.id,
          socialPostCallToActionTypeIds,
        ),
      );
  }

  async deleteAllForAdSegment(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .delete(adSegmentSelectedSocialPostCallToActionTypeTable)
      .where(
        eq(
          adSegmentSelectedSocialPostCallToActionTypeTable.adSegmentId,
          adSegmentId,
        ),
      );
  }

  async getAllForAdSegment(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<AdSegmentSelectedSocialPostCallToActionType[]> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(adSegmentSelectedSocialPostCallToActionTypeTable)
      .where(
        eq(
          adSegmentSelectedSocialPostCallToActionTypeTable.adSegmentId,
          adSegmentId,
        ),
      );
    return res.map((r) => AdSegmentSelectedSocialPostCallToActionType(r));
  }
}
