import { eq } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import {
  adSegmentBestAudienceVariantTable,
  adSegmentBestConversationCallToActionCopyVariantTable,
  adSegmentBestConversationMessageCopyVariantTable,
  adSegmentBestConversationSubjectVariantTable,
  adSegmentBestCreativeVariantTable,
  adSegmentBestSocialPostBodyCopyVariantTable,
  adSegmentBestSocialPostCallToActionCopyVariantTable,
  adSegmentBestValuePropVariantTable,
} from "../../../../database/schemas/advertising/abTests/adsegmentBestVariantTables";
import { IAdSegmentBestVariantRepository } from "../../application/interfaces/infrastructure/repositories/adSegmentBestVariant.repository.interface";
import { AdSegmentBestVariant } from "../../domain/entites/adSegmentBestVarient";

export const adSegmentBestVariantTableRegistry = {
  audience: adSegmentBestAudienceVariantTable,
  valueProp: adSegmentBestValuePropVariantTable,
  creative: adSegmentBestCreativeVariantTable,
  conversationSubject: adSegmentBestConversationSubjectVariantTable,
  socialPostBodyCopy: adSegmentBestSocialPostBodyCopyVariantTable,
  conversationMessageCopy: adSegmentBestConversationMessageCopyVariantTable,
  conversationCallToAction:
    adSegmentBestConversationCallToActionCopyVariantTable,
  socialPostCallToAction: adSegmentBestSocialPostCallToActionCopyVariantTable,
};
export class AdSegmentBestVariantRepository
  implements IAdSegmentBestVariantRepository
{
  async upsertOne(
    adSegmentBestVariant: AdSegmentBestVariant,
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    const table = adSegmentBestVariantTableRegistry[adSegmentBestVariant.type];
    await invoker
      .insert(table)
      .values({
        id: adSegmentBestVariant.id,
        adSegmentId: adSegmentBestVariant.adSegmentId,
        variantId: adSegmentBestVariant.variantId,
      })
      .onConflictDoUpdate({
        target: [table.adSegmentId],
        set: {
          variantId: adSegmentBestVariant.variantId,
        },
      });
  }
  async getOne(
    adSegmentId: string,
    type: AdSegmentBestVariant["type"],
    tx?: Transaction,
  ): Promise<AdSegmentBestVariant | null> {
    const invoker = tx ?? db;
    const table = adSegmentBestVariantTableRegistry[type];
    const result = await invoker
      .select()
      .from(table)
      .where(eq(table.adSegmentId, adSegmentId));
    if (!result[0]) {
      return null;
    }
    return {
      type,
      id: result[0].id,
      adSegmentId: result[0].adSegmentId,
      variantId: result[0].variantId,
    };
  }
}
