import { eq } from "drizzle-orm";

import { db, Transaction } from "../../../../database/db";
import { manualBiddingEventTable } from "../../../../database/schemas/advertising/manualBiddingEvent.table";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { IManualBiddingEventRepository } from "../../application/interfaces/infrastructure/repositories/manualBiddingEvent.repository.interface";
import { ManualBiddingEvent } from "../../domain/entites/manualBiddingEvent";

export class ManualBiddingEventRepository
  implements IManualBiddingEventRepository
{
  async createOne(
    input: ManualBiddingEvent,
    tx?: Transaction,
  ): Promise<ManualBiddingEvent> {
    const invoker = tx ?? db;
    await invoker.insert(manualBiddingEventTable).values({
      id: input.id,
      linkedInCampaignId: input.linkedInCampaignId,
      newBid: input.newBid.toFixed(2),
      budgetUsedPercentage: (input.budgetUsedPercentage * 100).toFixed(2),
      timeElapsedPercentage: (input.timeElapsedPercentage * 100).toFixed(2),
      timestamp: input.timestamp,
      originalBid: input.originalBid.toFixed(2),
      timeElapsed: input.timeElapsed.toFixed(2),
      budgetUsed: input.budgetUsed.toFixed(2),
      budget: input.budget.toFixed(2),
      dailyOrTotalBudget: input.dailyOrTotalBudget,
      minBid: input.minBid.toFixed(2),
      maxBid: input.maxBid.toFixed(2),
      minSuggestedBid: input.minSuggestedBid.toFixed(2),
      maxSuggestedBid: input.maxSuggestedBid.toFixed(2),
      suggestedBid: input.suggestedBid.toFixed(2),
      decision: input.decision,
      bidType: input.bidType,
    });
    return input;
  }
  async getOne(
    id: string,
    tx?: Transaction,
  ): Promise<ManualBiddingEvent | null> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(manualBiddingEventTable)
      .where(eq(manualBiddingEventTable.id, id));
    if (!result[0]) {
      return null;
    }
    return ManualBiddingEvent({
      id: result[0].id,
      linkedInCampaignId: result[0].linkedInCampaignId,
      newBid: Number(result[0].newBid),
      budgetUsedPercentage: Number(result[0].budgetUsedPercentage),
      timeElapsedPercentage: Number(result[0].timeElapsedPercentage),
      timestamp: result[0].timestamp,
      originalBid: Number(result[0].originalBid),
      timeElapsed: Number(result[0].timeElapsed),
      budgetUsed: Number(result[0].budgetUsed),
      budget: Number(result[0].budget),
      dailyOrTotalBudget: result[0].dailyOrTotalBudget,
      minBid: Number(result[0].minBid),
      maxBid: Number(result[0].maxBid),
      minSuggestedBid: Number(result[0].minSuggestedBid),
      maxSuggestedBid: Number(result[0].maxSuggestedBid),
      suggestedBid: Number(result[0].suggestedBid),
      decision: result[0].decision,
      bidType: result[0].bidType,
    });
  }
  getForCampaign(
    campaignId: string,
    tx?: Transaction,
  ): Promise<ManualBiddingEvent[]> {
    throw new Error("Method not implemented.");
  }
}
