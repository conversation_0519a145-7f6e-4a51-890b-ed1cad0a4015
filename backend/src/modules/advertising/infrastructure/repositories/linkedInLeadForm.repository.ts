import { and, eq, inArray } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { linkedInAdAccountTable } from "../../../../database/schemas/advertising/linkedInAdAccount.table";
import { linkedInLeadFormTable } from "../../../../database/schemas/advertising/linkedInLeadForm.table";
import { ILinkedInLeadFormRepositoryInterface } from "../../application/interfaces/infrastructure/repositories/linkedInLeadForm.repository.interface";
import { LinkedInLeadForm } from "../../domain/entites/linkedInLeadForm";

export class LinkedInLeadFormRepository
  implements ILinkedInLeadFormRepositoryInterface
{
  async createOne(input: LinkedInLeadForm, tx?: Transaction): Promise<void> {
    const invoker = tx ?? db;
    const linkedInAdAccount = await invoker
      .select()
      .from(linkedInAdAccountTable)
      .where(eq(linkedInAdAccountTable.id, input.linkedInAdAccountId))
      .limit(1);

    if (!linkedInAdAccount[0]) {
      throw new Error("LinkedIn Ad Account not found");
    }

    const existingLinkedInLeadForm = await this.getOneByLinkedInUrn(
      input.leadGenFormUrn,
      tx,
    );

    if (existingLinkedInLeadForm) {
      throw new Error("LinkedIn Lead Form already exists");
    }

    await invoker.insert(linkedInLeadFormTable).values({
      id: input.id,
      linkedInAdAccountId: input.linkedInAdAccountId,
      leadGenFormUrn: input.leadGenFormUrn,
      name: input.name,
      state: input.state,
      version: input.version ?? 1,
    });
  }

  async getOneById(
    id: string,
    tx?: Transaction,
  ): Promise<LinkedInLeadForm | null> {
    const invoker = tx ?? db;
    const linkedInLeadForm = await invoker
      .select()
      .from(linkedInLeadFormTable)
      .where(eq(linkedInLeadFormTable.id, id))
      .limit(1);

    if (!linkedInLeadForm[0]) {
      return null;
    }

    return linkedInLeadForm[0];
  }

  async getAllByLinkedInAdAccountId(
    linkedInAdAccountId: string,
    tx?: Transaction,
  ): Promise<LinkedInLeadForm[]> {
    const invoker = tx ?? db;
    const linkedInLeadForms = await invoker
      .select()
      .from(linkedInLeadFormTable)
      .where(
        eq(linkedInLeadFormTable.linkedInAdAccountId, linkedInAdAccountId),
      );

    return linkedInLeadForms;
  }

  async getOneByLinkedInUrn(
    linkedInUrn: string,
    tx?: Transaction,
  ): Promise<LinkedInLeadForm | null> {
    const invoker = tx ?? db;
    const linkedInLeadForm = await invoker
      .select()
      .from(linkedInLeadFormTable)
      .where(eq(linkedInLeadFormTable.leadGenFormUrn, linkedInUrn))
      .limit(1);

    if (!linkedInLeadForm[0]) {
      return null;
    }

    return linkedInLeadForm[0];
  }
  // Add to LinkedInLeadFormRepository.ts
  async createMany(forms: LinkedInLeadForm[]): Promise<string[]> {
    if (forms.length === 0) return [];

    console.log(
      `[LinkedInLeadFormRepository] Batch inserting ${forms.length} lead forms`,
    );

    // Prepare values for bulk insert
    const insertData = forms.map((form) => ({
      id: form.id,
      linkedInAdAccountId: form.linkedInAdAccountId,
      leadGenFormUrn: form.leadGenFormUrn,
      name: form.name,
      state: form.state,
      version: form.version,
    }));

    // Perform bulk insert
    const result = await db.insert(linkedInLeadFormTable).values(insertData);

    console.log(
      `[LinkedInLeadFormRepository] Successfully inserted ${forms.length} lead forms`,
    );

    // Return the IDs of inserted forms
    return forms.map((form) => form.id);
  }

  // Add a method to check existing forms by URNs
  async getExistingFormsByUrns(
    urns: string[],
  ): Promise<{ leadGenFormUrn: string; id: string }[]> {
    if (urns.length === 0) return [];

    console.log(
      `[LinkedInLeadFormRepository] Checking existence of ${urns.length} lead form URNs`,
    );

    const result = await db
      .select({
        id: linkedInLeadFormTable.id,
        leadGenFormUrn: linkedInLeadFormTable.leadGenFormUrn,
      })
      .from(linkedInLeadFormTable)
      .where(inArray(linkedInLeadFormTable.leadGenFormUrn, urns));

    console.log(
      `[LinkedInLeadFormRepository] Found ${result.length} existing lead forms`,
    );

    return result;
  }
}
