import { eq, inArray } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { adSegmentSelectedSocialPostBodyTypeTable } from "../../../../database/schemas/advertising/adSegmentSelectedSocialPostBodyType.table";
import { IAdSegmentSelectedSocialPostBodyTypeRepository } from "../../application/interfaces/infrastructure/repositories/adSegmentSelectedSocialPostBodyType.repository.interface";
import { AdSegmentSelectedSocialPostBodyType } from "../../domain/entites/adSegmentSelectedSocialPostBodyType";

export class AdSegmentSelectedSocialPostBodyTypeRepository
  implements IAdSegmentSelectedSocialPostBodyTypeRepository
{
  async createOne(
    input: AdSegmentSelectedSocialPostBodyType,
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker.insert(adSegmentSelectedSocialPostBodyTypeTable).values({
      adSegmentId: input.adSegmentId,
      type: input.type,
      id: input.id,
    });
  }

  async deleteOne(id: string, tx?: Transaction): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .delete(adSegmentSelectedSocialPostBodyTypeTable)
      .where(eq(adSegmentSelectedSocialPostBodyTypeTable.id, id));
    return;
  }

  async createMany(
    input: AdSegmentSelectedSocialPostBodyType[],
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (input.length === 0) {
      return;
    }
    await invoker.insert(adSegmentSelectedSocialPostBodyTypeTable).values(
      input.map((i) => ({
        adSegmentId: i.adSegmentId,
        type: i.type,
        id: i.id,
      })),
    );
  }

  async deleteManyById(
    socialPostBodyTypeIds: string[],
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (socialPostBodyTypeIds.length === 0) {
      return;
    }
    await invoker
      .delete(adSegmentSelectedSocialPostBodyTypeTable)
      .where(
        inArray(
          adSegmentSelectedSocialPostBodyTypeTable.id,
          socialPostBodyTypeIds,
        ),
      );
  }

  async deleteAllForAdSegment(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .delete(adSegmentSelectedSocialPostBodyTypeTable)
      .where(
        eq(adSegmentSelectedSocialPostBodyTypeTable.adSegmentId, adSegmentId),
      );
  }

  async getAllForAdSegment(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<AdSegmentSelectedSocialPostBodyType[]> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(adSegmentSelectedSocialPostBodyTypeTable)
      .where(
        eq(adSegmentSelectedSocialPostBodyTypeTable.adSegmentId, adSegmentId),
      );
    return res.map((r) => AdSegmentSelectedSocialPostBodyType(r));
  }
}
