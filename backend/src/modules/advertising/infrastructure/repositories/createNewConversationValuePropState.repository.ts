import { and, eq } from "drizzle-orm";

import { db } from "../../../../database/db";
import { createNewConversationValuePropStateTable } from "../../../../database/schemas/advertising/createNewConversationValuePropState.table";
import { ICreateNewConversationValuePropStateRepository } from "../../application/interfaces/infrastructure/repositories/createNewConversationValuePropState.repository.interface";
import { CreateNewConversationValuePropState } from "../../domain/entites/createNewConversationValuePropState";

export class CreateNewConversationValuePropStateRepository
  implements ICreateNewConversationValuePropStateRepository
{
  async upsertOne(state: CreateNewConversationValuePropState): Promise<void> {
    await db
      .insert(createNewConversationValuePropStateTable)
      .values(state)
      .onConflictDoUpdate({
        target: [
          createNewConversationValuePropStateTable.adSegmentId,
          createNewConversationValuePropStateTable.valueProp,
        ],
        set: {
          conversationSubjectCopyType: state.conversationSubjectCopyType,
          conversationSubjectCopy: state.conversationSubjectCopy,
          conversationMessageCopyType: state.conversationMessageCopyType,
          conversationMessageCopy: state.conversationMessageCopy,
          conversationCallToActionCopyType:
            state.conversationCallToActionCopyType,
          conversationCallToActionCopy: state.conversationCallToActionCopy,
          toBeUsed: state.toBeUsed,
        },
      });
  }
  async getForAdSegment(
    adSegmentId: string,
  ): Promise<CreateNewConversationValuePropState[]> {
    const res = await db
      .select()
      .from(createNewConversationValuePropStateTable)
      .where(
        eq(createNewConversationValuePropStateTable.adSegmentId, adSegmentId),
      );
    return res;
  }
  async getOne(
    adSegmentId: string,
    valueProp: string,
  ): Promise<CreateNewConversationValuePropState | null> {
    const result = await db
      .select()
      .from(createNewConversationValuePropStateTable)
      .where(
        eq(createNewConversationValuePropStateTable.adSegmentId, adSegmentId),
      );
    if (!result[0]) {
      return null;
    }
    return result[0];
  }
  async setToBeUsed(
    adSegmentId: string,
    valueProp: string,
    toBeUsed: boolean,
  ): Promise<void> {
    await db
      .update(createNewConversationValuePropStateTable)
      .set({ toBeUsed })
      .where(
        and(
          eq(createNewConversationValuePropStateTable.adSegmentId, adSegmentId),
          eq(createNewConversationValuePropStateTable.valueProp, valueProp),
        ),
      );
  }
}
