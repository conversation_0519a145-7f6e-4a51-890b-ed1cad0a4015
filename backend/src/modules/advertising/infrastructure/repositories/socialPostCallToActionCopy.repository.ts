import { and, eq, inArray } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { socialPostCallToActionCopyTable } from "../../../../database/schemas/advertising/socialPostCallToActionCopy.table";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { ISocialPostCallToActionCopyRepository } from "../../application/interfaces/infrastructure/repositories/socialPostCallToActionCopy.repository.interface";
import { SocialPostCallToActionCopy } from "../../domain/entites/socialPostCallToActionCopy";

export class SocialPostCallToActionCopyRepository
  implements ISocialPostCallToActionCopyRepository
{
  async getManyForLinkedInAdSegmentValueProp(
    input: {
      linkedInAdSegmentValuePropId: string;
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: Transaction,
  ): Promise<SocialPostCallToActionCopy[]> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(socialPostCallToActionCopyTable)
      .where(
        eq(
          socialPostCallToActionCopyTable.adSegmentValuePropId,
          input.linkedInAdSegmentValuePropId,
        ),
      );

    return res.map((row) =>
      SocialPostCallToActionCopy({
        id: row.id,
        adSegmentValuePropId: row.adSegmentValuePropId,
        callToAction: row.callToAction,
        type: row.type,
        status: row.status,
      }),
    );
  }

  async getOneById(
    input: { id: string; status: "DRAFT" | "ACTIVE" | "ARCHIVED" },
    tx?: Transaction,
  ): Promise<SocialPostCallToActionCopy | null> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(socialPostCallToActionCopyTable)
      .where(
        and(
          eq(socialPostCallToActionCopyTable.id, input.id),
          eq(socialPostCallToActionCopyTable.status, input.status),
        ),
      );
    if (!result[0]) {
      return null;
    }
    return SocialPostCallToActionCopy({
      id: result[0].id,
      adSegmentValuePropId: result[0].adSegmentValuePropId,
      callToAction: result[0].callToAction,
      type: result[0].type,
      status: result[0].status,
    });
  }

  async createOneOrUpdateOneIfExists(
    input: SocialPostCallToActionCopy,
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .insert(socialPostCallToActionCopyTable)
      .values(input)
      .onConflictDoUpdate({
        target: [
          socialPostCallToActionCopyTable.adSegmentValuePropId,
          socialPostCallToActionCopyTable.type,
        ],
        set: {
          callToAction: input.callToAction,
        },
      });
  }
  async getOne(
    input: {
      valuePropId: string;
      socialPostCopyType: string;
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: Transaction,
  ): Promise<SocialPostCallToActionCopy | null> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(socialPostCallToActionCopyTable)
      .where(
        and(
          eq(
            socialPostCallToActionCopyTable.adSegmentValuePropId,
            input.valuePropId,
          ),
          eq(socialPostCallToActionCopyTable.type, input.socialPostCopyType),
        ),
      );
    if (!result[0]) {
      return null;
    }
    return SocialPostCallToActionCopy({
      id: result[0].id,
      adSegmentValuePropId: result[0].adSegmentValuePropId,
      callToAction: result[0].callToAction,
      type: result[0].type,
      status: result[0].status,
    });
  }
  async deleteManyForLinkedInAdSegmentValueProps(
    input: {
      linkedInAdSegmentValuePropIds: string[];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (input.linkedInAdSegmentValuePropIds.length == 0) {
      return;
    }
    await invoker
      .delete(socialPostCallToActionCopyTable)
      .where(
        inArray(
          socialPostCallToActionCopyTable.adSegmentValuePropId,
          input.linkedInAdSegmentValuePropIds,
        ),
      );
  }

  async updateManyToActive(ids: string[], tx?: Transaction): Promise<void> {
    const invoker = tx ?? db;
    if (ids.length == 0) {
      return;
    }

    await invoker
      .update(socialPostCallToActionCopyTable)
      .set({ status: "ACTIVE" })
      .where(
        and(
          inArray(socialPostCallToActionCopyTable.id, ids),
          eq(socialPostCallToActionCopyTable.status, "DRAFT"),
        ),
      );
  }

  async deleteManyForLinkedInAdSegmentValuePropAndTypes(
    input: {
      linkedInAdSegmentValuePropId: string;
      types: string[];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (input.types.length == 0) {
      return;
    }
    await invoker
      .delete(socialPostCallToActionCopyTable)
      .where(
        and(
          eq(
            socialPostCallToActionCopyTable.adSegmentValuePropId,
            input.linkedInAdSegmentValuePropId,
          ),
          inArray(socialPostCallToActionCopyTable.type, input.types),
          eq(socialPostCallToActionCopyTable.status, input.status),
        ),
      );
  }
}
