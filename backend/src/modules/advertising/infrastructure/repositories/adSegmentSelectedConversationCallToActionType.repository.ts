import { eq, inArray } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { adSegmentSelectedConversationCallToActionTypeTable } from "../../../../database/schemas/advertising/adSegmentSelectedConversationCallToActionType.table";
import { IAdSegmentSelectedConversationCallToActionTypeRepository } from "../../application/interfaces/infrastructure/repositories/adSegmentSelectedConversationCallToActionType.repository.interface";
import { AdSegmentSelectedConversationCallToActionType } from "../../domain/entites/adSegmentSelectedConversationCallToActionType";

export class AdSegmentSelectedConversationCallToActionTypeRepository
  implements IAdSegmentSelectedConversationCallToActionTypeRepository
{
  async createOne(
    input: AdSegmentSelectedConversationCallToActionType,
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .insert(adSegmentSelectedConversationCallToActionTypeTable)
      .values({
        adSegmentId: input.adSegmentId,
        type: input.type,
        id: input.id,
      });
  }

  async deleteOne(id: string, tx?: Transaction): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .delete(adSegmentSelectedConversationCallToActionTypeTable)
      .where(eq(adSegmentSelectedConversationCallToActionTypeTable.id, id));
    return;
  }

  async createMany(
    input: AdSegmentSelectedConversationCallToActionType[],
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (input.length === 0) {
      return;
    }
    await invoker
      .insert(adSegmentSelectedConversationCallToActionTypeTable)
      .values(
        input.map((i) => ({
          adSegmentId: i.adSegmentId,
          type: i.type,
          id: i.id,
        })),
      );
  }

  async deleteManyById(
    conversationCallToActionTypeIds: string[],
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (conversationCallToActionTypeIds.length === 0) {
      return;
    }
    await invoker
      .delete(adSegmentSelectedConversationCallToActionTypeTable)
      .where(
        inArray(
          adSegmentSelectedConversationCallToActionTypeTable.id,
          conversationCallToActionTypeIds,
        ),
      );
  }

  async deleteAllForAdSegment(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .delete(adSegmentSelectedConversationCallToActionTypeTable)
      .where(
        eq(
          adSegmentSelectedConversationCallToActionTypeTable.adSegmentId,
          adSegmentId,
        ),
      );
  }

  async getAllForAdSegment(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<AdSegmentSelectedConversationCallToActionType[]> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(adSegmentSelectedConversationCallToActionTypeTable)
      .where(
        eq(
          adSegmentSelectedConversationCallToActionTypeTable.adSegmentId,
          adSegmentId,
        ),
      );
    return res.map((r) => AdSegmentSelectedConversationCallToActionType(r));
  }
}
