import { eq, inArray } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { adSegmentSelectedConversationSubjectTypeTable } from "../../../../database/schemas/advertising/adSegmentSelectedConversationSubjectType.table";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { IAdSegmentSelectedConversationSubjectTypeRepository } from "../../application/interfaces/infrastructure/repositories/adSegmentSelectedConversationSubjectType.repository.interface";
import { AdSegmentSelectedConversationSubjectType } from "../../domain/entites/adSegmentSelectedConversationSubjectType";

export class AdSegmentSelectedConversationSubjectTypeRepository
  implements IAdSegmentSelectedConversationSubjectTypeRepository
{
  async createOne(
    input: AdSegmentSelectedConversationSubjectType,
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker.insert(adSegmentSelectedConversationSubjectTypeTable).values({
      adSegmentId: input.adSegmentId,
      type: input.type,
      id: input.id,
    });
  }
  async deleteOne(id: string, tx?: Transaction): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .delete(adSegmentSelectedConversationSubjectTypeTable)
      .where(eq(adSegmentSelectedConversationSubjectTypeTable.id, id));
    return;
  }
  async createMany(
    input: AdSegmentSelectedConversationSubjectType[],
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (input.length === 0) {
      return;
    }
    await invoker.insert(adSegmentSelectedConversationSubjectTypeTable).values(
      input.map((i) => ({
        adSegmentId: i.adSegmentId,
        type: i.type,
        id: i.id,
      })),
    );
  }
  async deleteManyById(
    conversationSubjectCopyIds: string[],
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (conversationSubjectCopyIds.length === 0) {
      return;
    }
    await invoker
      .delete(adSegmentSelectedConversationSubjectTypeTable)
      .where(
        inArray(
          adSegmentSelectedConversationSubjectTypeTable.id,
          conversationSubjectCopyIds,
        ),
      );
  }
  async deleteAllForAdSegment(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .delete(adSegmentSelectedConversationSubjectTypeTable)
      .where(
        eq(
          adSegmentSelectedConversationSubjectTypeTable.adSegmentId,
          adSegmentId,
        ),
      );
  }
  async getAllForAdSegment(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<AdSegmentSelectedConversationSubjectType[]> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(adSegmentSelectedConversationSubjectTypeTable)
      .where(
        eq(
          adSegmentSelectedConversationSubjectTypeTable.adSegmentId,
          adSegmentId,
        ),
      );
    return res.map((r) => AdSegmentSelectedConversationSubjectType(r));
  }
}
