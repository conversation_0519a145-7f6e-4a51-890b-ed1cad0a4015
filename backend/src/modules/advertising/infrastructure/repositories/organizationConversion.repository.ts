import { eq } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { organizationConversionTable } from "../../../../database/schemas/advertising/organizationConversion.table";
import { IOrganizationConversionRepository } from "../../application/interfaces/infrastructure/repositories/organizationConversation.repository.interface";
import { OrganizationConversion } from "../../domain/entites/organizationConversion";

export class OrganizationConversionRepository
  implements IOrganizationConversionRepository
{
  async createOne(
    organizationConversion: OrganizationConversion,
    tx?: Transaction,
  ): Promise<OrganizationConversion> {
    const invoker = tx ?? db;
    const res = await invoker
      .insert(organizationConversionTable)
      .values(organizationConversion);
    return organizationConversion;
  }
  async getOne(
    id: string,
    tx?: Transaction,
  ): Promise<OrganizationConversion | null> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(organizationConversionTable)
      .where(eq(organizationConversionTable.id, id));
    if (!res[0]) {
      return null;
    }
    return OrganizationConversion(res[0]);
  }

  async getOneForOrganization(
    organizationId: number,
    tx?: Transaction,
  ): Promise<OrganizationConversion[]> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(organizationConversionTable)
      .where(eq(organizationConversionTable.organizationId, organizationId));
    return res.map(OrganizationConversion);
  }

  async deleteOne(id: string, tx?: Transaction): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .delete(organizationConversionTable)
      .where(eq(organizationConversionTable.id, id));
  }
}
