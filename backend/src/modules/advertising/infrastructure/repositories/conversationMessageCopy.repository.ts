import { and, eq, inArray } from "drizzle-orm";

import { db, Transaction } from "../../../../database/db";
import { conversationMessageCopyTable } from "../../../../database/schemas/advertising/conversationMessageCopy.table";
import { conversationSubjectCopyTable } from "../../../../database/schemas/advertising/conversationSubjectCopy.table";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { IConversationMessageCopyRepository } from "../../application/interfaces/infrastructure/repositories/conversationMessageCopy.repository.interface";
import { ConversationCallToActionCopy } from "../../domain/entites/conversationCallToActionCopy";
import { ConversationMessageCopy } from "../../domain/entites/conversationMessageCopy";
import { ConversationSubjectCopy } from "../../domain/entites/conversationSubjectCopy";

export class ConversationMessageCopyRepository
  implements IConversationMessageCopyRepository
{
  async upsertOne(
    input: Omit<ConversationMessageCopy, "subjectCopyId">,
    tx?: Transaction,
  ): Promise<ConversationMessageCopy> {
    const invoker = tx ?? db;
    const subjectCopyId = (
      await invoker
        .select()
        .from(conversationSubjectCopyTable)
        .where(
          and(
            eq(
              conversationSubjectCopyTable.adSegmentValuePropId,
              input.valuePropId,
            ),
            eq(conversationSubjectCopyTable.type, input.subjectType),
          ),
        )
    )[0]?.id;

    if (!subjectCopyId) {
      throw new Error("Subject copy not found");
    }

    await invoker
      .insert(conversationMessageCopyTable)
      .values({
        id: input.id,
        content: input.content,
        conversationSubjectCopyId: subjectCopyId,
        type: input.type,
      })
      .onConflictDoUpdate({
        target: [
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationMessageCopyTable.type,
        ],
        set: {
          content: input.content,
        },
      });
    return {
      ...input,
      subjectCopyId,
    };
  }
  async getOneById(
    id: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ): Promise<ConversationMessageCopy | null> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(conversationMessageCopyTable)
      .innerJoin(
        conversationSubjectCopyTable,
        eq(
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationSubjectCopyTable.id,
        ),
      )
      .where(
        and(
          eq(conversationMessageCopyTable.id, id),
          eq(conversationMessageCopyTable.status, status),
        ),
      );
    if (!result[0]) {
      return null;
    }
    return ConversationMessageCopy({
      id: result[0].conversation_message_copy.id,
      subjectCopyId:
        result[0].conversation_message_copy.conversationSubjectCopyId,
      content: result[0].conversation_message_copy.content,
      subjectType: result[0].conversation_subject_copy.type,
      valuePropId: result[0].conversation_subject_copy.adSegmentValuePropId,
      type: result[0].conversation_message_copy.type,
      status: result[0].conversation_message_copy.status,
    });
  }

  async getOneByValuePropMessageTypeAndSubjectType(
    valuePropId: string,
    messageType: ConversationMessageCopy["type"],
    subjectType: ConversationSubjectCopy["type"],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ): Promise<ConversationMessageCopy | null> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(conversationMessageCopyTable)
      .innerJoin(
        conversationSubjectCopyTable,
        eq(
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationSubjectCopyTable.id,
        ),
      )
      .where(
        and(
          eq(conversationSubjectCopyTable.adSegmentValuePropId, valuePropId),
          eq(conversationMessageCopyTable.type, messageType),
          eq(conversationSubjectCopyTable.type, subjectType),
          eq(conversationMessageCopyTable.status, status),
        ),
      );
    if (!result[0]) {
      return null;
    }
    return ConversationMessageCopy({
      id: result[0].conversation_message_copy.id,
      subjectCopyId:
        result[0].conversation_message_copy.conversationSubjectCopyId,
      content: result[0].conversation_message_copy.content,
      subjectType: result[0].conversation_subject_copy.type,
      valuePropId: result[0].conversation_subject_copy.adSegmentValuePropId,
      type: result[0].conversation_message_copy.type,
      status: result[0].conversation_message_copy.status,
    });
  }

  async getAllForValueProp(
    valuePropId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ): Promise<ConversationMessageCopy[]> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(conversationMessageCopyTable)
      .innerJoin(
        conversationSubjectCopyTable,
        eq(
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationSubjectCopyTable.id,
        ),
      )
      .where(
        and(
          eq(conversationSubjectCopyTable.adSegmentValuePropId, valuePropId),
          eq(conversationMessageCopyTable.status, status),
        ),
      );
    return result.map((r) =>
      ConversationMessageCopy({
        id: r.conversation_message_copy.id,
        subjectCopyId: r.conversation_message_copy.conversationSubjectCopyId,
        content: r.conversation_message_copy.content,
        subjectType: r.conversation_subject_copy.type,
        valuePropId: r.conversation_subject_copy.adSegmentValuePropId,
        type: r.conversation_message_copy.type,
        status: r.conversation_message_copy.status,
      }),
    );
  }

  async getAllForValuePropAndSubjectType(
    valuePropId: string,
    subjectType: ConversationSubjectCopy["type"],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ): Promise<ConversationMessageCopy[]> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(conversationMessageCopyTable)
      .innerJoin(
        conversationSubjectCopyTable,
        eq(
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationSubjectCopyTable.id,
        ),
      )
      .where(
        and(
          eq(conversationSubjectCopyTable.adSegmentValuePropId, valuePropId),
          eq(conversationSubjectCopyTable.type, subjectType),
          eq(conversationMessageCopyTable.status, status),
        ),
      );
    return result.map((r) =>
      ConversationMessageCopy({
        id: r.conversation_message_copy.id,
        subjectCopyId: r.conversation_message_copy.conversationSubjectCopyId,
        content: r.conversation_message_copy.content,
        subjectType: r.conversation_subject_copy.type,
        valuePropId: r.conversation_subject_copy.adSegmentValuePropId,
        type: r.conversation_message_copy.type,
        status: r.conversation_message_copy.status,
      }),
    );
  }

  async deleteOneById(
    id: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .delete(conversationMessageCopyTable)
      .where(
        and(
          eq(conversationMessageCopyTable.id, id),
          eq(conversationMessageCopyTable.status, status),
        ),
      );
  }

  async deleteOneByValuePropAndMessageTypeAndSubjectType(
    valuePropId: string,
    messageType: ConversationMessageCopy["type"],
    subjectType: ConversationSubjectCopy["type"],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    const conversationMessageCopyId = (
      await invoker
        .select({
          id: conversationMessageCopyTable.id,
        })
        .from(conversationMessageCopyTable)
        .innerJoin(
          conversationSubjectCopyTable,
          eq(
            conversationMessageCopyTable.conversationSubjectCopyId,
            conversationSubjectCopyTable.id,
          ),
        )
        .where(
          and(
            eq(conversationSubjectCopyTable.adSegmentValuePropId, valuePropId),
            eq(conversationMessageCopyTable.type, messageType),
            eq(conversationSubjectCopyTable.type, subjectType),
            eq(conversationMessageCopyTable.status, status),
          ),
        )
    )[0]?.id;
    if (!conversationMessageCopyId) {
      return;
    }
    await invoker
      .delete(conversationMessageCopyTable)
      .where(eq(conversationMessageCopyTable.id, conversationMessageCopyId));
  }
  async deleteAllForValueProp(
    valuePropId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    const conversationMessageCopyIds = await invoker
      .select({
        id: conversationMessageCopyTable.id,
      })
      .from(conversationMessageCopyTable)
      .innerJoin(
        conversationSubjectCopyTable,
        eq(
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationSubjectCopyTable.id,
        ),
      )
      .where(
        and(
          eq(conversationSubjectCopyTable.adSegmentValuePropId, valuePropId),
          eq(conversationMessageCopyTable.status, status),
        ),
      );
    if (conversationMessageCopyIds.length === 0) {
      return;
    }
    await invoker.delete(conversationMessageCopyTable).where(
      inArray(
        conversationMessageCopyTable.id,
        conversationMessageCopyIds.map((c) => c.id),
      ),
    );
  }
  async deleteAllForManyValueProps(
    valuePropIds: string[],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (valuePropIds.length === 0) {
      return;
    }
    const conversationMessageCopyIds = await invoker
      .select({
        id: conversationMessageCopyTable.id,
      })
      .from(conversationMessageCopyTable)
      .innerJoin(
        conversationSubjectCopyTable,
        eq(
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationSubjectCopyTable.id,
        ),
      )
      .where(
        and(
          inArray(
            conversationSubjectCopyTable.adSegmentValuePropId,
            valuePropIds,
          ),
          eq(conversationMessageCopyTable.status, status),
        ),
      );
    if (conversationMessageCopyIds.length === 0) {
      return;
    }
    await invoker.delete(conversationMessageCopyTable).where(
      inArray(
        conversationMessageCopyTable.id,
        conversationMessageCopyIds.map((c) => c.id),
      ),
    );
  }
  async deleteAllForValuePropAndSubjectType(
    valuePropId: string,
    subjectType: ConversationSubjectCopy["type"],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    const conversationMessageCopyIds = await invoker
      .select({
        id: conversationMessageCopyTable.id,
      })
      .from(conversationMessageCopyTable)
      .innerJoin(
        conversationSubjectCopyTable,
        eq(
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationSubjectCopyTable.id,
        ),
      )
      .where(
        and(
          eq(conversationSubjectCopyTable.adSegmentValuePropId, valuePropId),
          eq(conversationSubjectCopyTable.type, subjectType),
          eq(conversationMessageCopyTable.status, status),
        ),
      );

    if (conversationMessageCopyIds.length === 0) {
      return;
    }
    await invoker.delete(conversationMessageCopyTable).where(
      inArray(
        conversationMessageCopyTable.id,
        conversationMessageCopyIds.map((c) => c.id),
      ),
    );
  }
  async deleteAllForValuePropAndManySubjectTypes(
    valuePropId: string,
    subjectTypes: ConversationSubjectCopy["type"][],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (subjectTypes.length === 0) {
      return;
    }
    const conversationMessageCopyIds = await invoker
      .select({
        id: conversationMessageCopyTable.id,
      })
      .from(conversationMessageCopyTable)
      .innerJoin(
        conversationSubjectCopyTable,
        eq(
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationSubjectCopyTable.id,
        ),
      )
      .where(
        and(
          eq(conversationSubjectCopyTable.adSegmentValuePropId, valuePropId),
          inArray(conversationSubjectCopyTable.type, subjectTypes),
          eq(conversationMessageCopyTable.status, status),
        ),
      );
    if (conversationMessageCopyIds.length === 0) {
      return;
    }
    await invoker.delete(conversationMessageCopyTable).where(
      inArray(
        conversationMessageCopyTable.id,
        conversationMessageCopyIds.map((c) => c.id),
      ),
    );
  }

  async updateManyToActive(
    input: {
      ids: string[];
    },
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (input.ids.length === 0) {
      return;
    }
    await invoker
      .update(conversationMessageCopyTable)
      .set({ status: "ACTIVE" })
      .where(
        and(
          inArray(conversationMessageCopyTable.id, input.ids),
          eq(conversationMessageCopyTable.status, "DRAFT"),
        ),
      );
  }

  async deleteManyForValuePropSubjectTypeAndManyMessageTypes(
    valuePropId: string,
    subjectType: ConversationSubjectCopy["type"],
    messageTypes: ConversationMessageCopy["type"][],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ) {
    const invoker = tx ?? db;

    if (messageTypes.length === 0) {
      return;
    }

    const conversationMessageCopyIds = await invoker
      .select({
        id: conversationMessageCopyTable.id,
      })
      .from(conversationMessageCopyTable)
      .innerJoin(
        conversationSubjectCopyTable,
        eq(
          conversationMessageCopyTable.conversationSubjectCopyId,
          conversationSubjectCopyTable.id,
        ),
      )
      .where(
        and(
          eq(conversationSubjectCopyTable.adSegmentValuePropId, valuePropId),
          eq(conversationSubjectCopyTable.type, subjectType),
          inArray(conversationMessageCopyTable.type, messageTypes),
          eq(conversationMessageCopyTable.status, status),
        ),
      );
    if (conversationMessageCopyIds.length === 0) {
      return;
    }
    await invoker.delete(conversationMessageCopyTable).where(
      inArray(
        conversationMessageCopyTable.id,
        conversationMessageCopyIds.map((c) => c.id),
      ),
    );
  }
}
