import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

import { CampaignGroupMetrics, TotalMetrics } from "../../domain/entites/slackNotification";
import { LinkedInAdAccountRepository } from "../repositories/linkedInAdAccount.repository";
import { LinkedInCampaignGroupRepository } from "../repositories/linkedInCampaignGroup.repository";
import { AbTestRepository } from "../repositories/abTest.repository";
import { StageRepository } from "../repositories/stage.repository";
import { StageStepRepository } from "../repositories/stageStep.repository";
import { LinkedInDeploymentConfigRepository } from "../repositories/linkedInDeploymentConfig.repository";
import { AbTestFacadeService } from "../../application/services/abTest/abTest.facade.service";
import { LinkedInService } from "./linkedIn.service";
import { ConversationSubjectCopyRepository } from "../repositories/conversationSubjectCopy.repository";
import { AbTest, abTestTypesSchema } from "../../domain/entites/abTest";

interface WeeklyMetricsData {
  campaignGroups: CampaignGroupMetrics[];
  totals: TotalMetrics;
}

interface AdMetrics {
  adId: string;
  adName: string;
  leads: number;
  clicks: number;
  engagements: number;
  opens: number;
  sends: number;
  spend: number;
}

interface TestBasedInsight {
  testType: string;
  adsCount: number;
  bestPerformingAd: {
    name: string;
    metric: string;
    value: number;
    percentage: number;
  };
  worstPerformingAd: {
    name: string;
    value: number;
  };
  improvementPercentage: number;
  primaryMetric: 'leads' | 'clicks' | 'engagements';
}

export class CampaignMetricsAggregationService {
  // SIMPLIFIED APPROACH:
  // 1. Find completed tests for each campaign group
  // 2. Get ad metrics from test data (already includes metrics)
  // 3. Find winner ad and compare with worst performer
  // 4. Focus on core metrics: leads, clicks, engagements (no rates/CTR/impressions in final output)
  
  constructor(
    private linkedInAdAccountRepository: LinkedInAdAccountRepository,
    private linkedInCampaignGroupRepository: LinkedInCampaignGroupRepository,
    private abTestRepository: AbTestRepository = new AbTestRepository(),
    private stageRepository: StageRepository = new StageRepository(),
    private stageStepRepository: StageStepRepository = new StageStepRepository(),
    private linkedInDeploymentConfigRepository: LinkedInDeploymentConfigRepository = new LinkedInDeploymentConfigRepository(),
    private conversationSubjectCopyRepository: ConversationSubjectCopyRepository = new ConversationSubjectCopyRepository()
  ) {}

  /**
   * Get deployment configs for a stage that were completed within the time range
   */
  private async getCompletedDeploymentConfigsForStage(
    stageId: string,
    startDate: Date,
    endDate: Date
  ): Promise<Array<{ stepId: string; endDatetime: Date }>> {
    try {
      // Get all steps for this stage
      const steps = await this.stageStepRepository.getForStage(stageId);
      if (!steps.length) return [];

      const completedDeployments: Array<{ stepId: string; endDatetime: Date }> = [];

      // Check each step for completed deployment configs
      for (const step of steps) {
        try {
          const deploymentConfig = await this.linkedInDeploymentConfigRepository.getOne(step.id);
          if (deploymentConfig && 
              deploymentConfig.status === 'COMPLETED' && 
              deploymentConfig.endDatetime) {
            
            const completedAt = deploymentConfig.endDatetime;
            
            // Check if completion date is within our time range
            if (completedAt >= startDate && completedAt <= endDate) {
              completedDeployments.push({
                stepId: step.id,
                endDatetime: completedAt
              });
            }
          }
        } catch (error) {
          // Continue if deployment config doesn't exist for this step
          continue;
        }
      }

      return completedDeployments;
    } catch (error) {
      console.error(`Error getting completed deployment configs for stage ${stageId}:`, error);
      return [];
    }
  }

  /**
   * Find the last completed test for a campaign group within the given time range
   */
  private async findLastCompletedTest(
    campaignGroupId: string,
    startDate: Date,
    endDate: Date
  ): Promise<{ stageId: string; testType: AbTest["type"]; completedAt: Date } | null> {
    try {
      // Get all stages for this campaign group
      const stages = await this.stageRepository.getStagesForAdSegment(campaignGroupId);
      if (!stages.length) return null;

      let lastCompletedTest: { stageId: string; testType: AbTest["type"]; completedAt: Date } | null = null;

      // Check each stage for completed tests
      for (const stage of stages) {
        // Get completed deployment configs for this stage within the time range
        const completedDeployments = await this.getCompletedDeploymentConfigsForStage(
          stage.id,
          startDate,
          endDate
        );

        if (!completedDeployments.length) continue;

        // Check each test type for this stage
        for (const testType of abTestTypesSchema.options) {
          try {
            const abTest = await this.abTestRepository.getOne(stage.id, testType);
            if (abTest && abTest.status === 'COMPLETED') {
              // Find the most recent completed deployment for this stage
              const latestDeployment = completedDeployments
                .sort((a, b) => b.endDatetime.getTime() - a.endDatetime.getTime())[0];
              
              if (latestDeployment) {
                const completedAt = latestDeployment.endDatetime;
                
                if (!lastCompletedTest || completedAt > lastCompletedTest.completedAt) {
                  lastCompletedTest = {
                    stageId: stage.id,
                    testType,
                    completedAt
                  };
                }
              }
            }
          } catch (error) {
            // Continue if this test type doesn't exist for this stage
            continue;
          }
        }
      }

      return lastCompletedTest;
    } catch (error) {
      console.error(`Error finding last completed test for campaign group ${campaignGroupId}:`, error);
      return null;
    }
  }

  /**
   * Get ad-level analytics for a completed test - SIMPLIFIED WITH DATABASE TITLES ONLY
   */
  private async getAdAnalyticsForTest(
    stageId: string,
    testType: string,
    startDate: Date,
    endDate: Date
  ): Promise<{ adMetrics: AdMetrics[]; winner: any; testInfo: any } | null> {
    try {
      // Get the test data (this already has ads with metrics)
      const abTestFacadeService = AbTestFacadeService.createFactory(testType as any);
      const testData = await abTestFacadeService.getData(stageId, startDate, endDate);
      
      if (!testData?.ads || testData.ads.length === 0) {
        console.log(`No ads found for test ${stageId}`);
        return null;
      }

      // Get the completed A/B test to find the winner ID
      const abTest = await this.abTestRepository.getOne(stageId, testType as any);
      if (!abTest || abTest.status !== 'COMPLETED' || !('winnerId' in abTest)) {
        console.log(`Test ${stageId} is not completed or has no winner`);
        return null;
      }

      // Convert ads to our metrics format with database titles
      const adMetrics: AdMetrics[] = [];
      for (const ad of testData.ads) {
        if (ad.metrics) {
          const metrics = ad.metrics as any;
          
          // Get actual ad name from database
          const adName = await this.getAdName(ad, testType);
          
          adMetrics.push({
            adId: ad.id,
            adName: adName,
            leads: metrics.oneClickLeads || 0,
            clicks: metrics.clicks || 0,
            engagements: metrics.totalEngagements || 0,
            opens: metrics.opens || 0,
            sends: metrics.sends || 0,
            spend: metrics.costInUsd || 0,
          });
        }
      }

      const winnerAd = adMetrics.reduce((best, current) => {
          // Pick the ad with most leads, then engagements, then clicks
          const bestScore = (best.leads * 10) + (best.engagements * 2) + best.clicks;
          const currentScore = (current.leads * 10) + (current.engagements * 2) + current.clicks;
          return currentScore > bestScore ? current : best;
        });

      return {
        adMetrics,
        winner: winnerAd,
        testInfo: {
          testType,
          stageId,
          winnerId: abTest.winnerId
        }
      };
    } catch (error) {
      console.error(`Error getting ad analytics for test ${stageId}:`, error);
      return null;
    }
  }

  /**
   * Extract title from social post body content
   */
  private extractTitleFromPostBody(postBody: string): string {
    if (!postBody || postBody.trim().length === 0) {
      return 'Untitled Post';
    }
    
    // Get the first line or first sentence
    let title = postBody.split('\n')[0]?.trim() || postBody.trim();
    
    // If first line is too short, try first sentence
    if (title.length < 10) {
      const firstSentence = postBody.split('.')[0]?.trim();
      if (firstSentence && firstSentence.length > title.length) {
        title = firstSentence;
      }
    }
    
    // Truncate if too long
    if (title.length > 60) {
      title = title.substring(0, 60) + '...';
    }
    
    return title;
  }

  /**
   * Get actual variant name based on test type - matches UI display logic
   */
  private async getAdName(ad: any, testType: string): Promise<string> {
    try {
      // The ad object from testData has adVarients with variant IDs/types
      // We need to trace from these to get actual content
      const adVarients = ad.adVarients;
      
      if (!adVarients) {
        return `${testType} ${ad.id.slice(-6)}`;
      }

      console.log('adVarients getAdName for testType', testType, adVarients);

      switch (testType) {
        case 'valueProp':
          // For value prop testing, the actual value prop text is in adVarients.valueProp
          return adVarients.valueProp || `ValueProp ${ad.id.slice(-6)}`;
          
        case 'conversationSubject':
          // For conversation subject, use the subjectCopyType directly or messageCopyContent if available
          if (adVarients.messageCopyContent) {
            return adVarients.messageCopyContent;
          }
          if (adVarients.subjectCopyType) {
            return adVarients.subjectCopyType;
          }
          // Fallback to database lookup with correct field name
          return await this.getConversationSubjectContent(adVarients);
          
        case 'conversationMessageCopy':
          // For message copy, use messageCopyContent if available, otherwise try database
          if (adVarients.messageCopyContent) {
            return adVarients.messageCopyContent.length > 60 ? 
              adVarients.messageCopyContent.substring(0, 60) + '...' : 
              adVarients.messageCopyContent;
          }
          return await this.getConversationMessageContent(adVarients);
          
        case 'conversationCallToAction':
          // For CTA, need to trace from callToActionCopyType to actual content
          return await this.getConversationCTAContent(adVarients);
          
        case 'socialPostBodyCopy':
          // For social post copy, need to trace from adCopyType + valuePropId to actual content
          return await this.getSocialPostBodyContent(adVarients);
          
        case 'socialPostCallToAction':
          // For social post CTA, need to trace from callToActionCopyType to actual content
          return await this.getSocialPostCTAContent(adVarients);
          
        case 'audience':
          // For audience testing, extract targeting criteria from campaign field
          if (adVarients.campaign) {
            const targetingParams = this.parseCampaignUrns(adVarients.campaign);
            if (targetingParams.length > 0) {
              return targetingParams.join(', ');
            }
          }
          return `Audience ${ad.id.slice(-6)}`;
          
        case 'creative':
          // For creative testing, check if we have file name in adCreativeMetadata
          if (adVarients.adCreativeMetadata?.adCreative?.fileName) {
            return adVarients.adCreativeMetadata.adCreative.fileName;
          }
          return `Creative ${ad.id.slice(-6)}`;
          
        default:
          return `${testType} ${ad.id.slice(-6)}`;
      }
    } catch (error) {
      console.error(`Error getting variant name for ${ad.id} (${testType}):`, error);
      return `${testType} Error ${ad.id.slice(-6)}`;
    }
  }

  /**
   * Get conversation subject content from variant data
   */
  private async getConversationSubjectContent(adVarients: any): Promise<string> {
    try {
      if (adVarients.subjectCopyType && adVarients.valuePropId) {
        const subjectCopy = await this.conversationSubjectCopyRepository.getOne({
          valuePropId: adVarients.valuePropId,
          conversationCopyType: adVarients.subjectCopyType,
          status: 'ACTIVE'
        });
        if (subjectCopy?.content) {
          return subjectCopy.content;
        }
      }
      return `Subject ${adVarients.campaignId?.slice(-6) || 'Unknown'}`;
    } catch (error) {
      console.error('Error getting conversation subject content:', error);
      return `Subject Error`;
    }
  }

  /**
   * Get conversation message content from variant data
   */
  private async getConversationMessageContent(adVarients: any): Promise<string> {
    try {
      if (adVarients.messageCopyType && adVarients.valuePropId && adVarients.subjectCopyType) {
        const conversationMessageCopyRepository = new (await import('../repositories/conversationMessageCopy.repository')).ConversationMessageCopyRepository();
        const messageCopy = await conversationMessageCopyRepository.getOneByValuePropMessageTypeAndSubjectType(
          adVarients.valuePropId,
          adVarients.messageCopyType,
          adVarients.subjectCopyType, // Use subjectCopyType instead of subjectType
          'ACTIVE'
        );
        if (messageCopy?.content) {
          return messageCopy.content.length > 60 ? messageCopy.content.substring(0, 60) + '...' : messageCopy.content;
        }
      }
      return `Message ${adVarients.campaignId?.slice(-6) || 'Unknown'}`;
    } catch (error) {
      console.error('Error getting conversation message content:', error);
      return `Message Error`;
    }
  }

  /**
   * Get conversation CTA content from variant data
   */
  private async getConversationCTAContent(adVarients: any): Promise<string> {
    try {
      if (adVarients.callToActionCopyType && adVarients.valuePropId && adVarients.subjectCopyType && adVarients.messageCopyType) {
        const conversationCallToActionCopyRepository = new (await import('../repositories/conversationCallToActionCopy.repository')).ConversationCallToActionCopyRepository();
        const ctaCopy = await conversationCallToActionCopyRepository.getOne({
          valuePropId: adVarients.valuePropId,
          conversationMessageCopyType: adVarients.messageCopyType,
          conversationSubjectCopyType: adVarients.subjectCopyType,
          conversationCallToActionCopyType: adVarients.callToActionCopyType,
          status: 'ACTIVE'
        });
        if (ctaCopy?.content) {
          return ctaCopy.content;
        }
      }
      return `CTA ${adVarients.campaignId?.slice(-6) || 'Unknown'}`;
    } catch (error) {
      console.error('Error getting conversation CTA content:', error);
      return `CTA Error`;
    }
  }

  /**
   * Get social post body content from variant data
   */
  private async getSocialPostBodyContent(adVarients: any): Promise<string> {
    try {
      if (adVarients.adCopyType && adVarients.valuePropId) {
        const socialPostCopyRepository = new (await import('../repositories/socialPostCopy.repository')).SocialPostCopyRepository();
        const socialPostCopy = await socialPostCopyRepository.getOne({
          valuePropId: adVarients.valuePropId,
          socialPostCopyType: adVarients.adCopyType,
          status: 'ACTIVE'
        });
        if (socialPostCopy?.body) {
          return this.extractTitleFromPostBody(socialPostCopy.body);
        }
      }
      return `PostCopy ${adVarients.campaignId?.slice(-6) || 'Unknown'}`;
    } catch (error) {
      console.error('Error getting social post body content:', error);
      return `PostCopy Error`;
    }
  }

  /**
   * Get social post CTA content from variant data
   */
  private async getSocialPostCTAContent(adVarients: any): Promise<string> {
    try {
      if (adVarients.callToActionCopyType && adVarients.valuePropId) {
        const socialPostCallToActionCopyRepository = new (await import('../repositories/socialPostCallToActionCopy.repository')).SocialPostCallToActionCopyRepository();
        const ctaCopy = await socialPostCallToActionCopyRepository.getOne({
          valuePropId: adVarients.valuePropId,
          socialPostCopyType: adVarients.callToActionCopyType,
          status: 'ACTIVE'
        });
        if (ctaCopy?.callToAction) {
          return ctaCopy.callToAction;
        }
      }
      return `PostCTA ${adVarients.campaignId?.slice(-6) || 'Unknown'}`;
    } catch (error) {
      console.error('Error getting social post CTA content:', error);
      return `PostCTA Error`;
    }
  }

  /**
   * Parse campaign URNs to extract targeting parameters (similar to frontend logic)
   */
  private parseCampaignUrns(campaignData: string): string[] {
    try {
      if (!campaignData) return [];
      
      // Split by comma to get individual URNs
      const urns = campaignData.split(',');
      const targetingParams: string[] = [];
      
      for (const urn of urns) {
        if (urn.includes('urn:li:adTargetingFacet:')) {
          // Extract the targeting type from URN
          const parts = urn.split(':');
          if (parts.length > 3) {
            const facetPart = parts[3];
            // Convert facet identifiers to readable names
            if (facetPart && facetPart.includes('industry')) {
              targetingParams.push('Industry');
            } else if (facetPart && facetPart.includes('jobFunction')) {
              targetingParams.push('Job Function');
            } else if (facetPart && facetPart.includes('location')) {
              targetingParams.push('Location');
            } else if (facetPart && facetPart.includes('company')) {
              targetingParams.push('Companies');
            } else if (facetPart && facetPart.includes('skill')) {
              targetingParams.push('Skills');
            } else if (facetPart && facetPart.includes('revenue')) {
              targetingParams.push('Annual Revenue');
            } else if (facetPart) {
              targetingParams.push('Targeting');
            }
          }
        }
      }
      
      // Remove duplicates
      return [...new Set(targetingParams)];
    } catch (error) {
      console.error('Error parsing campaign URNs:', error);
      return [];
    }
  }

  /**
   * Determine the correct metric based on ad format and test type - SIMPLIFIED
   */
  private getCorrectMetricForTest(testType: string, adFormat: string): { 
    metric: string; 
    calculator: (ad: AdMetrics) => number 
  } {
    // For SPONSORED_INMAIL, some specific metrics are still needed
    if (adFormat === 'SPONSORED_INMAIL') {
      switch (testType) {
        case 'conversationSubject':
          // Opens are the primary metric for subject testing
          return {
            metric: 'opens',
            calculator: (ad) => ad.opens
          };
        case 'audience':
          return {
            metric: 'opens',
            calculator: (ad) => ad.opens
          };
        default:
          // For message copy and CTA testing, clicks are more important
          return {
            metric: 'clicks',
            calculator: (ad) => ad.clicks
          };
      }
    } 
    
    // For SPONSORED_CONTENT and other formats, use raw engagement numbers
    switch (testType) {
      case 'audience':
        return {
          metric: 'clicks',
          calculator: (ad) => ad.clicks
        };
      case 'creative':
      case 'valueProp':
      case 'socialPostBodyCopy':
      case 'socialPostCallToAction':
      default:
        // Use raw engagements instead of engagement rate
        return {
          metric: 'engagements',
          calculator: (ad) => ad.engagements
        };
    }
  }

  /**
   * Generate test-based insights - SIMPLIFIED
   */
  private generateTestBasedInsights(
    adMetrics: AdMetrics[],
    winner: AdMetrics | null,
    testType: string,
    adFormat: string = 'SPONSORED_CONTENT'
  ): TestBasedInsight | null {

    if (adMetrics.length < 2 || !winner) {
      console.log('Not enough ads or no winner - need at least 2 ads and a winner');
      return null;
    }

    // Get the correct metric for this test type and ad format
    const { metric, calculator } = this.getCorrectMetricForTest(testType, adFormat);

    // Calculate the metric for all ads
    const adsWithMetrics = adMetrics.map(ad => ({
      ...ad,
      calculatedMetric: calculator(ad)
    }));

    // Find winner
    const winnerWithMetric = adsWithMetrics.find(ad => ad.adId === winner.adId);
    if (!winnerWithMetric) {
      console.log('Winner not found in metrics');
      return null;
    }

    // Find the worst performing ad (excluding winner)
    const losers = adsWithMetrics.filter(ad => ad.adId !== winner.adId);
    if (losers.length === 0) {
      console.log('No loser ads found');
      return null;
    }

    // Sort losers by performance (worst first)
    const worstAd = losers.sort((a, b) => a.calculatedMetric - b.calculatedMetric)[0];
    if (!worstAd) {
      console.log('No worst ad found');
      return null;
    }

    // For audience testing, use simpler names if the current names are too long
    let winnerName = winnerWithMetric.adName;
    let worstName = worstAd.adName;
    
    if (testType === 'audience') {
      // If names are too long or confusing, use simple labels
      if (winnerName.length > 60 || worstName.length > 60) {
        // Sort ads by performance to assign consistent labels
        const sortedAds = adsWithMetrics.sort((a, b) => b.calculatedMetric - a.calculatedMetric);
        const winnerIndex = sortedAds.findIndex(ad => ad.adId === winnerWithMetric.adId);
        const worstIndex = sortedAds.findIndex(ad => ad.adId === worstAd.adId);
        
        winnerName = `Audience ${String.fromCharCode(65 + winnerIndex)}`; // A, B, C, etc.
        worstName = `Audience ${String.fromCharCode(65 + worstIndex)}`;
      }
    }

    // Calculate improvement percentage
    let improvementPercentage = 0;
    if (worstAd.calculatedMetric > 0) {
      improvementPercentage = Math.round(
        ((winnerWithMetric.calculatedMetric - worstAd.calculatedMetric) / worstAd.calculatedMetric) * 100
      );
    } else if (winnerWithMetric.calculatedMetric > 0) {
      improvementPercentage = 100; // Winner has value, loser has 0
    }

    return {
      testType: this.getTestTypeName(testType),
      adsCount: adMetrics.length,
      bestPerformingAd: {
        name: winnerName,
        metric: metric,
        value: winnerWithMetric.calculatedMetric,
        percentage: 0, // Not used
      },
      worstPerformingAd: {
        name: worstName,
        value: worstAd.calculatedMetric,
      },
      improvementPercentage,
      primaryMetric: 'engagements', // Legacy field
    };
  }

  /**
   * Convert test type to human readable name
   */
  private getTestTypeName(testType: string): string {
    const typeMap: Record<string, string> = {
      'creative': 'Creative Testing',
      'valueProp': 'Value Proposition Testing',
      'audience': 'Audience Testing',
      'conversationSubject': 'Subject Testing',
      'socialPostBodyCopy': 'Copy Testing',
      'conversationMessageCopy': 'Message Copy Testing',
      'conversationCallToAction': 'Call to Action Testing',
      'socialPostCallToAction': 'Social Post Call to Action Testing',
    };
    return typeMap[testType] || 'A/B Testing';
  }

  /**
   * Updated getMetrics method with test-based insights
   */
  async getMetrics(
    organizationId: number,
    startDate: Date,
    endDate: Date
  ): Promise<WeeklyMetricsData> {
    try {
      // Get LinkedIn API client for the organization
      const linkedInClient = await getLinkedInApiClientFromOrganizationId(organizationId);
      if (!linkedInClient) {
        throw new Error("LinkedIn client not found for organization");
      }

      const linkedInService = new LinkedInService(linkedInClient);

      // Get all ad accounts for the organization
      const adAccounts = await this.linkedInAdAccountRepository.getForOrganization(organizationId);
      if (!adAccounts.length) {
        return { campaignGroups: [], totals: { totalSpend: 0 } };
      }

      const campaignGroups: CampaignGroupMetrics[] = [];
      let totalSpend = 0;
      let totalLeads = 0;
      let totalEngagements = 0;
      let totalClicks = 0;

      // Process each ad account
      for (const adAccount of adAccounts) {
        // Get only ACTIVE campaign groups for this ad account
        const campaignGroupsWithAdProgram = await this.linkedInCampaignGroupRepository.getAllForLinkedInAdAccountIdWithAdProgram(
          adAccount.id,
          ["ACTIVE"]
        );

        for (const { campaignGroup, adProgram } of campaignGroupsWithAdProgram) {
          try {
            console.log("Processing campaign group", campaignGroup.linkedInAdSegmentId);
            // Always get campaign analytics for leads and spend
            let groupLeads = 0;
            let groupEngagements = 0;
            let groupClicks = 0;
            let groupSpend = 0;

            const analytics = await linkedInService.getCampaignGroupAnalytics(
              [campaignGroup.linkedInCampaignGroupUrn],
              startDate,
              endDate
            );

            if (analytics.elements && analytics.elements.length > 0) {
              const metrics = analytics.elements[0];
              if (metrics) {
                groupLeads = metrics.oneClickLeads || 0;
                groupEngagements = metrics.totalEngagements || 0;
                groupClicks = metrics.clicks || 0;
                groupSpend = Number(metrics.costInUsd) || 0;
              }
            }

            // Separately, try to get test-based insights for learnings
            const completedTest = await this.findLastCompletedTest(
              campaignGroup.linkedInAdSegmentId,
              startDate,
              endDate
            );

            let testInsights: string | undefined;

            if (completedTest) {
              // Get ad-level analytics for the completed test (only for insights)
              const testResult = await this.getAdAnalyticsForTest(
                completedTest.stageId,
                completedTest.testType,
                startDate,
                endDate
              );

              if (testResult && testResult.adMetrics.length > 0) {
                // Determine ad format from adProgram
                const adFormat = adProgram?.adFormat?.format || 'SPONSORED_CONTENT';
                
                // Generate test-based insights using winner vs other variants
                const insights = this.generateTestBasedInsights(
                  testResult.adMetrics, 
                  testResult.winner, 
                  completedTest.testType,
                  adFormat
                );
                                
                if (insights) {
                  // Always show raw numbers, not rates
                  testInsights = `Performed ${insights.testType} for ${insights.adsCount} ads. ${insights.bestPerformingAd.name} had ${insights.bestPerformingAd.value} ${insights.bestPerformingAd.metric}, ${insights.improvementPercentage}% higher than ${insights.worstPerformingAd.name}`;
                }
              }
            }

            campaignGroups.push({
              campaignGroupId: campaignGroup.linkedInAdSegmentId,
              campaignGroupName: campaignGroup.name,
              leads: groupLeads > 0 ? groupLeads : undefined,
              engagements: groupEngagements > 0 ? groupEngagements : undefined,
              clicks: groupClicks > 0 ? groupClicks : undefined,
              spend: groupSpend,
              learnings: testInsights,
            });

            // Add to totals
            totalSpend += groupSpend;
            totalLeads += groupLeads;
            totalEngagements += groupEngagements;
            totalClicks += groupClicks;
          } catch (error) {
            console.error(`Error getting analytics for campaign group ${campaignGroup.linkedInAdSegmentId}:`, error);
            // Continue with other campaign groups
          }
        }
      }

      return {
        campaignGroups,
        totals: {
          totalSpend,
          totalLeads: totalLeads > 0 ? totalLeads : undefined,
          totalEngagements: totalEngagements > 0 ? totalEngagements : undefined,
          totalClicks: totalClicks > 0 ? totalClicks : undefined,
        },
      };
    } catch (error) {
      console.error("Error aggregating weekly metrics:", error);
      return { campaignGroups: [], totals: { totalSpend: 0 } };
    }
  }
} 