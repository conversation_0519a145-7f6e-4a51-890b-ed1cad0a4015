import type { LinkedInAudienceTargetCriteria } from "../domain/valueObjects/linkedinAudienceTargeting/linkedinTargetAudienceCriteria";
import { EXCLUDED_EMPLOYERS_FACET, EXCLUDED_EMPLOYERS_FACET_ENTITIES } from "../constants/excludedEmployers";

/**
 * Adds excluded employers to targeting criteria
 * @param audienceTargets - The audience targeting criteria to modify
 * @param launchingOrganization - Optional organization info to exclude them from seeing their own ads
 * @returns Modified targeting criteria with excluded employers
 */
export function addExcludedEmployersToTargeting(
    audienceTargets: LinkedInAudienceTargetCriteria,
    launchingOrganization?: {
        organizationUrn: string;
        organizationName: string;
    }
): LinkedInAudienceTargetCriteria {
    const modifiedTargets: LinkedInAudienceTargetCriteria = {
        include: {
            and: [...audienceTargets.include.and]
        },
        exclude: audienceTargets.exclude ? {
            or: [...audienceTargets.exclude.or]
        } : {
            or: []
        }
    };

    if (!modifiedTargets.exclude) {
        modifiedTargets.exclude = { or: [] };
    }

    let allExcludedEntities = [...EXCLUDED_EMPLOYERS_FACET_ENTITIES];

    if (launchingOrganization) {
        allExcludedEntities.push({
            entityName: launchingOrganization.organizationName,
            entityUrn: launchingOrganization.organizationUrn,
            facetUrn: "urn:li:adTargetingFacet:employers"
        });
    }

    const existingEmployersFacetIndex = modifiedTargets.exclude.or.findIndex(
        facet => facet.facetUrn === "urn:li:adTargetingFacet:employers"
    );

    if (existingEmployersFacetIndex >= 0) {
        const existingFacet = modifiedTargets.exclude.or[existingEmployersFacetIndex];
        if (existingFacet) {
            const existingEntityUrns = new Set(
                existingFacet.facetEntites.map(entity => entity.entityUrn)
            );

            const newEmployersToExclude = allExcludedEntities.filter(
                entity => !existingEntityUrns.has(entity.entityUrn)
            );

            modifiedTargets.exclude.or[existingEmployersFacetIndex] = {
                facetUrn: existingFacet.facetUrn,
                facetName: existingFacet.facetName,
                facetEntites: [...existingFacet.facetEntites, ...newEmployersToExclude]
            };
        }
    } else {
        modifiedTargets.exclude.or.push({
            facetUrn: EXCLUDED_EMPLOYERS_FACET.facetUrn,
            facetName: EXCLUDED_EMPLOYERS_FACET.facetName,
            facetEntites: allExcludedEntities
        });
    }

    return modifiedTargets;
} 