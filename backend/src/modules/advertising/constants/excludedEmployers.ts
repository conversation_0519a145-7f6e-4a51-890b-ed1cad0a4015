import type { z } from "zod";
import { linkedInFacetEntitySchema } from "../domain/valueObjects/linkedinAudienceTargeting/linkedinFacetEntity";

type LinkedInFacetEntity = z.infer<typeof linkedInFacetEntitySchema>;

export const EXCLUDED_EMPLOYERS_FACET_ENTITIES: LinkedInFacetEntity[] = [
    {
        entityName: "Google",
        entityUrn: "urn:li:organization:1441",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Microsoft",
        entityUrn: "urn:li:organization:1035",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Microsoft",
        entityUrn: "urn:li:organization:69051716",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Microsoft",
        entityUrn: "urn:li:organization:78468719",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Meta",
        entityUrn: "urn:li:organization:10667",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Meta",
        entityUrn: "urn:li:organization:487488",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Cisco",
        entityUrn: "urn:li:organization:1063",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Palo Alto Networks",
        entityUrn: "urn:li:organization:30086",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Apple",
        entityUrn: "urn:li:organization:162479",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "NVIDIA",
        entityUrn: "urn:li:organization:3608",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "NVIDIA",
        entityUrn: "urn:li:organization:33253442",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Amazon",
        entityUrn: "urn:li:organization:1586",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Amazon",
        entityUrn: "urn:li:organization:88390279",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Amazon",
        entityUrn: "urn:li:organization:42879190",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Oracle",
        entityUrn: "urn:li:organization:1028",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "SAP",
        entityUrn: "urn:li:organization:1115",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "TSMC",
        entityUrn: "urn:li:organization:8869",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Broadcom",
        entityUrn: "urn:li:organization:3072",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "BroadCom",
        entityUrn: "urn:li:organization:29018558",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Tesla",
        entityUrn: "urn:li:organization:8819091",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Tesla ",
        entityUrn: "urn:li:organization:73998086",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Tesla",
        entityUrn: "urn:li:organization:15564",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "TESLA",
        entityUrn: "urn:li:organization:69604317",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Netflix",
        entityUrn: "urn:li:organization:165158",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Netflix",
        entityUrn: "urn:li:organization:102573813",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Palantir",
        entityUrn: "urn:li:organization:64898",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Palantir",
        entityUrn: "urn:li:organization:507786",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Alibaba Group",
        entityUrn: "urn:li:organization:3839570",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Alibaba",
        entityUrn: "urn:li:organization:3863386",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Samsung Electronics",
        entityUrn: "urn:li:organization:1753",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "IBM",
        entityUrn: "urn:li:organization:1009",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "IBM",
        entityUrn: "urn:li:organization:539407",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Salesforce",
        entityUrn: "urn:li:organization:3185",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
    {
        entityName: "Salesforce",
        entityUrn: "urn:li:organization:90795688",
        facetUrn: "urn:li:adTargetingFacet:employers"
    },
];

export const EXCLUDED_EMPLOYERS_FACET = {
    facetName: "Employers",
    facetUrn: "urn:li:adTargetingFacet:employers" as const,
    facetEntites: EXCLUDED_EMPLOYERS_FACET_ENTITIES
}; 