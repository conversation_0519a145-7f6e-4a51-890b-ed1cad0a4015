import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedConversationCallToActionType } from "../../../../domain/entites/adSegmentSelectedConversationCallToActionType";

export interface IAdSegmentSelectedConversationCallToActionTypeRepository {
  getAllForAdSegment(
    adSegmentId: string,
    tx?: ITransaction,
  ): Promise<AdSegmentSelectedConversationCallToActionType[]>;
  createMany(
    input: AdSegmentSelectedConversationCallToActionType[],
    tx?: ITransaction,
  ): Promise<void>;
  deleteManyById(
    conversationSubjectCopyIds: string[],
    tx?: ITransaction,
  ): Promise<void>;
  deleteAllForAdSegment(adSegmentId: string, tx?: ITransaction): Promise<void>;
  createOne(
    input: AdSegmentSelectedConversationCallToActionType,
    tx?: ITransaction,
  ): Promise<void>;
  deleteOne(id: string, tx?: ITransaction): Promise<void>;
}
