import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedSocialPostBodyType } from "../../../../domain/entites/adSegmentSelectedSocialPostBodyType";

export interface IAdSegmentSelectedSocialPostBodyTypeRepository {
  getAllForAdSegment(
    adSegmentId: string,
    tx?: ITransaction,
  ): Promise<AdSegmentSelectedSocialPostBodyType[]>;
  createMany(
    input: AdSegmentSelectedSocialPostBodyType[],
    tx?: ITransaction,
  ): Promise<void>;
  deleteManyById(
    socialPostBodyTypeIds: string[],
    tx?: ITransaction,
  ): Promise<void>;
  deleteAllForAdSegment(adSegmentId: string, tx?: ITransaction): Promise<void>;
  createOne(
    input: AdSegmentSelectedSocialPostBodyType,
    tx?: ITransaction,
  ): Promise<void>;
  deleteOne(id: string, tx?: ITransaction): Promise<void>;
}
