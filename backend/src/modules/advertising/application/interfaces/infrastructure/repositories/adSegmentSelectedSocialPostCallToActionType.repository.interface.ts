import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedSocialPostCallToActionType } from "../../../../domain/entites/adSegmentSelectedSocialPostCallToActionType";

export interface IAdSegmentSelectedSocialPostCallToActionTypeRepository {
  getAllForAdSegment(
    adSegmentId: string,
    tx?: ITransaction,
  ): Promise<AdSegmentSelectedSocialPostCallToActionType[]>;
  createMany(
    input: AdSegmentSelectedSocialPostCallToActionType[],
    tx?: ITransaction,
  ): Promise<void>;
  deleteManyById(
    socialPostCallToActionTypeIds: string[],
    tx?: ITransaction,
  ): Promise<void>;
  deleteAllForAdSegment(adSegmentId: string, tx?: ITransaction): Promise<void>;
  createOne(
    input: AdSegmentSelectedSocialPostCallToActionType,
    tx?: ITransaction,
  ): Promise<void>;
  deleteOne(id: string, tx?: ITransaction): Promise<void>;
}
