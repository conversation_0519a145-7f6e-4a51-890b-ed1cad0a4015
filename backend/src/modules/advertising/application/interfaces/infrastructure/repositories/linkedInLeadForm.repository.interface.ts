import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { LinkedInLeadForm } from "../../../../domain/entites/linkedInLeadForm";

export interface ILinkedInLeadFormRepositoryInterface {
  createOne(input: LinkedInLeadForm, tx?: ITransaction): Promise<void>;
  getOneById(id: string, tx?: ITransaction): Promise<LinkedInLeadForm | null>;
  getOneByLinkedInUrn(
    linkedInUrn: string,
    tx?: ITransaction,
  ): Promise<LinkedInLeadForm | null>;
  createMany(forms: LinkedInLeadForm[], tx?: ITransaction): Promise<string[]>;
  getExistingFormsByUrns(
    urns: string[],
    tx?: ITransaction,
  ): Promise<{ leadGenFormUrn: string; id: string }[]>;
  getAllByLinkedInAdAccountId(
    linkedInAdAccountId: string,
    tx?: ITransaction,
  ): Promise<LinkedInLeadForm[]>;
}
