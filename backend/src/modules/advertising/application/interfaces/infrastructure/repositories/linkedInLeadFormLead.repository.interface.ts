import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { LinkedInLeadAndOpportunity } from "../../../../domain/entites/linkedInLeadAndOpportunity";
import { LinkedInLeadFormLead } from "../../../../domain/entites/linkedInLeadFormLead";

export interface ILinkedInLeadFormLeadRepositoryInterface {
  createOne(input: LinkedInLeadFormLead, tx?: ITransaction): Promise<string>;
  getOneById(
    id: string,
    tx?: ITransaction,
  ): Promise<LinkedInLeadFormLead | null>;
  getAllByLeadFormId(
    leadFormId: string,
    tx?: ITransaction,
  ): Promise<LinkedInLeadFormLead[]>;
  getOneByNameAndLeadCreatedAt(
    firstName: string,
    lastName: string,
    leadCreatedAt: Date,
    tx?: ITransaction,
  ): Promise<LinkedInLeadFormLead | null>;
  getAllLeadsAndOpportunitiesByAccountId(
    accountId: string,
    startDateFilter?: Date,
    endDateFilter?: Date,
    tx?: ITransaction,
  ): Promise<LinkedInLeadAndOpportunity[]>;
  getOneByLinkedInLeadFormResponseId(
    linkedInLeadFormResponseId: string,
    tx?: ITransaction,
  ): Promise<LinkedInLeadFormLead | null>;
  createMany(
    leads: Array<{
      id: string;
      linkedInLeadFormId: string;
      linkedInLeadFormResponseId: string;
    }>,
  ): Promise<string[]>;
  getExistingLeadsByResponseIds(
    responseIds: string[],
    tx?: ITransaction,
  ): Promise<{ linkedInLeadFormResponseId: string; id: string }[]>;
  getLeadsByOrganizationInTimeWindow(
    organizationId: number,
    startDate: Date,
    endDate: Date,
    tx?: ITransaction,
  ): Promise<LinkedInLeadFormLead[]>;
}
