import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { ManualBiddingEvent } from "../../../../domain/entites/manualBiddingEvent";

export interface IManualBiddingEventRepository {
  createOne(
    input: ManualBiddingEvent,
    tx?: ITransaction,
  ): Promise<ManualBiddingEvent>;
  getOne(id: string, tx?: ITransaction): Promise<ManualBiddingEvent | null>;
  getForCampaign(
    campaignId: string,
    tx?: ITransaction,
  ): Promise<ManualBiddingEvent[]>;
}
