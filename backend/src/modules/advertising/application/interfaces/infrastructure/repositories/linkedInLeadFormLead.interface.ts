import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { LinkedInLeadAndOpportunity } from "../../../../domain/entites/linkedInLeadAndOpportunity";
import { LinkedInLeadFormLead } from "../../../../domain/entites/linkedInLeadFormLead";

export interface ILinkedInLeadFormLeadRepositoryInterface {
  createOne(input: LinkedInLeadFormLead, tx?: ITransaction): Promise<void>;
  getOneById(
    id: string,
    tx?: ITransaction,
  ): Promise<LinkedInLeadFormLead | null>;
  getAllByLeadFormId(
    leadFormId: string,
    tx?: ITransaction,
  ): Promise<LinkedInLeadFormLead[]>;
  getAllLeadsAndOpportunitiesByAccountId(
    accountId: string,
    startDateFilter?: Date,
    endDateFilter?: Date,
    tx?: ITransaction,
  ): Promise<LinkedInLeadAndOpportunity[]>;
}
