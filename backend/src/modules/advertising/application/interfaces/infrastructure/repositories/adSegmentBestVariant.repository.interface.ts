import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentBestVariant } from "../../../../domain/entites/adSegmentBestVarient";

export interface IAdSegmentBestVariantRepository {
  upsertOne(
    adSegmentBestVariant: AdSegmentBestVariant,
    tx?: ITransaction,
  ): Promise<void>;
  getOne(
    adSegmentId: string,
    type: AdSegmentBestVariant["type"],
    tx?: ITransaction,
  ): Promise<AdSegmentBestVariant | null>;
}
