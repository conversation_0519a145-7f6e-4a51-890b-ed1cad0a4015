import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { ConversationMessageCopy } from "../../../../domain/entites/conversationMessageCopy";
import { ConversationSubjectCopy } from "../../../../domain/entites/conversationSubjectCopy";

export interface IConversationMessageCopyRepository {
  upsertOne(
    input: Omit<ConversationMessageCopy, "subjectCopyId">,
    tx?: ITransaction,
  ): Promise<ConversationMessageCopy>;
  getOneById(
    id: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<ConversationMessageCopy | null>;
  getOneByValuePropMessageTypeAndSubjectType(
    valuePropId: string,
    messageType: ConversationMessageCopy["type"],
    subjectType: ConversationSubjectCopy["type"],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<ConversationMessageCopy | null>;
  getAllForValueProp(
    valuePropId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<ConversationMessageCopy[]>;
  getAllForValuePropAndSubjectType(
    valuePropId: string,
    subjectType: ConversationSubjectCopy["type"],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<ConversationMessageCopy[]>;
  deleteOneById(
    id: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<void>;
  deleteOneByValuePropAndMessageTypeAndSubjectType(
    valuePropId: string,
    messageType: ConversationMessageCopy["type"],
    subjectType: ConversationSubjectCopy["type"],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<void>;
  deleteAllForValueProp(
    valuePropId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<void>;
  deleteAllForManyValueProps(
    valuePropIds: string[],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<void>;
  deleteAllForValuePropAndSubjectType(
    valuePropId: string,
    subjectType: ConversationSubjectCopy["type"],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<void>;
  deleteAllForValuePropAndManySubjectTypes(
    valuePropId: string,
    subjectTypes: ConversationSubjectCopy["type"][],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<void>;
  updateManyToActive(
    input: {
      ids: string[];
    },
    tx?: ITransaction,
  ): Promise<void>;
  deleteManyForValuePropSubjectTypeAndManyMessageTypes(
    valuePropId: string,
    subjectType: ConversationSubjectCopy["type"],
    messageTypes: ConversationMessageCopy["type"][],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ): Promise<void>;
}
