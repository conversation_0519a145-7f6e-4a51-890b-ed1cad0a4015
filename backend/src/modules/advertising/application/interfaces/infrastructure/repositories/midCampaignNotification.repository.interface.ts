import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { MidCampaignNotification } from "../../../../domain/entites/midCampaignNotification";

export interface IMidCampaignNotificationRepository {
  createOne(
    midCampaignNotification: MidCampaignNotification,
    tx?: ITransaction,
  ): Promise<MidCampaignNotification>;
  getForAdSegment(
    adSegmentId: string,
    tx?: ITransaction,
  ): Promise<MidCampaignNotification | null>;
  deleteOne(adSegmentId: string, tx?: ITransaction): Promise<void>;
}
