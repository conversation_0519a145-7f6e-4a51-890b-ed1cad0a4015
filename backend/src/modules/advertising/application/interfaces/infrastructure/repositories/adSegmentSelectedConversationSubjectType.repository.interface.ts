import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedConversationSubjectType } from "../../../../domain/entites/adSegmentSelectedConversationSubjectType";

export interface IAdSegmentSelectedConversationSubjectTypeRepository {
  getAllForAdSegment(
    adSegmentId: string,
    tx?: ITransaction,
  ): Promise<AdSegmentSelectedConversationSubjectType[]>;
  createMany(
    input: AdSegmentSelectedConversationSubjectType[],
    tx?: ITransaction,
  ): Promise<void>;
  deleteManyById(
    conversationSubjectCopyIds: string[],
    tx?: ITransaction,
  ): Promise<void>;
  deleteAllForAdSegment(adSegmentId: string, tx?: ITransaction): Promise<void>;
  createOne(
    input: AdSegmentSelectedConversationSubjectType,
    tx?: ITransaction,
  ): Promise<void>;
  deleteOne(id: string, tx?: ITransaction): Promise<void>;
}
