import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { SocialPostCallToActionCopy } from "../../../../domain/entites/socialPostCallToActionCopy";

export interface ISocialPostCallToActionCopyRepository {
  createOneOrUpdateOneIfExists(
    input: SocialPostCallToActionCopy,
    tx?: ITransaction,
  ): Promise<void>;

  getManyForLinkedInAdSegmentValueProp(
    input: {
      linkedInAdSegmentValuePropId: string;
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ): Promise<SocialPostCallToActionCopy[]>;

  getOne(
    input: {
      valuePropId: string;
      socialPostCopyType: string;
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ): Promise<SocialPostCallToActionCopy | null>;

  getOneById(
    input: {
      id: string;
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ): Promise<SocialPostCallToActionCopy | null>;

  deleteManyForLinkedInAdSegmentValueProps(
    input: {
      linkedInAdSegmentValuePropIds: string[];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ): Promise<void>;

  updateManyToActive(ids: string[], tx?: ITransaction): Promise<void>;

  deleteManyForLinkedInAdSegmentValuePropAndTypes(
    input: {
      linkedInAdSegmentValuePropId: string;
      types: string[];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ): Promise<void>;
}
