import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedConversationMessageType } from "../../../../domain/entites/adSegmentSelectedConversationMessageType";

export interface IAdSegmentSelectedConversationMessageTypeRepository {
  getAllForAdSegment(
    adSegmentId: string,
    tx?: ITransaction,
  ): Promise<AdSegmentSelectedConversationMessageType[]>;
  createMany(
    input: AdSegmentSelectedConversationMessageType[],
    tx?: ITransaction,
  ): Promise<void>;
  deleteManyById(
    conversationSubjectCopyIds: string[],
    tx?: ITransaction,
  ): Promise<void>;
  deleteAllForAdSegment(adSegmentId: string, tx?: ITransaction): Promise<void>;
  createOne(
    input: AdSegmentSelectedConversationMessageType,
    tx?: ITransaction,
  ): Promise<void>;
  deleteOne(id: string, tx?: ITransaction): Promise<void>;
}
