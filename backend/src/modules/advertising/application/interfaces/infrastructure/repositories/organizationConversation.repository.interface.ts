import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { OrganizationConversion } from "../../../../domain/entites/organizationConversion";

export interface IOrganizationConversionRepository {
  createOne(
    organizationConversion: OrganizationConversion,
    tx?: ITransaction,
  ): Promise<OrganizationConversion>;

  getOne(id: string, tx?: ITransaction): Promise<OrganizationConversion | null>;

  getOneForOrganization(
    organizationId: number,
    tx?: ITransaction,
  ): Promise<OrganizationConversion[]>;

  deleteOne(id: string, tx?: ITransaction): Promise<void>;
}
