import { CreateNewConversationValuePropState } from "../../../../domain/entites/createNewConversationValuePropState";

export interface ICreateNewConversationValuePropStateRepository {
  upsertOne: (state: CreateNewConversationValuePropState) => Promise<void>;
  getForAdSegment: (
    adSegmentId: string,
  ) => Promise<CreateNewConversationValuePropState[]>;
  getOne: (
    adSegmentId: string,
    valueProp: string,
  ) => Promise<CreateNewConversationValuePropState | null>;
  setToBeUsed: (
    adSegmentId: string,
    valueProp: string,
    toBeUsed: boolean,
  ) => Promise<void>;
}
