import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { ValuePropCreative } from "../../../../domain/entites/valuePropCreative";

export interface IAdSegmentValuePropCreativeRepository {
  createOne(
    valuePropCreative: ValuePropCreative,
    tx?: ITransaction,
  ): Promise<ValuePropCreative>;
  createMany(
    valuePropCreatives: ValuePropCreative[],
    tx?: ITransaction,
  ): Promise<ValuePropCreative[]>;
  getOne(id: string, tx?: ITransaction): Promise<ValuePropCreative | null>;
  getAllForValueProp(
    valuePropId: string,
    tx?: ITransaction,
  ): Promise<ValuePropCreative[]>;
  deleteOne(id: string, tx?: ITransaction): Promise<void>;
  deleteAllForValueProp(valuePropId: string, tx?: ITransaction): Promise<void>;
  deleteAllForManyValueProps(
    valuePropIds: string[],
    tx?: ITransaction,
  ): Promise<void>;
  deleteAllForAdProgramCreativeIds(
    adProgramCreativeIds: string[],
    tx?: ITransaction,
  ): Promise<void>;
  getAllForAdProgramCreativeId(
    adProgramCreativeId: string,
    tx?: ITransaction,
  ): Promise<ValuePropCreative[]>;
  getAllForAdSegment(
    adSegmentId: string,
    tx?: ITransaction,
  ): Promise<ValuePropCreative[]>;
  getAllForAdSegmentAndAdProgramCreativeId(
    adSegmentId: string,
    adProgramCreativeId: string,
    tx?: ITransaction,
  ): Promise<ValuePropCreative[]>;
  getOneByAdProgramCreativeIdAndValuePropId(
    adProgramCreativeId: string,
    valuePropId: string,
    tx?: ITransaction,
  ): Promise<ValuePropCreative | null>;
}
