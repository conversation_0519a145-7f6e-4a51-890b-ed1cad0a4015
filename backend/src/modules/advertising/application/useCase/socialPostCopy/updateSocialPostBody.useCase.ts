import { SocialPostAdCopyService } from "../../../domain/services/socialPostAdCopy.service";
import { UpdateSocialPostBodyCopyRequestDto } from "../../dtos/controllerDtos/socialPostCopy/updateSocialPostBodyCopy.dto";

export class UpdateSocialPostBodyUseCase {
  constructor(
    private readonly socialPostAdCopyService: SocialPostAdCopyService,
  ) {}

  async execute(input: UpdateSocialPostBodyCopyRequestDto) {
    await this.socialPostAdCopyService.createOneOrUpdateOneIfExists({
      linkedInAdSegmentValuePropId: input.valuePropId,
      socialPostCopyType: input.type,
      body: input.body,
      status: "DRAFT",
    });
  }
}
