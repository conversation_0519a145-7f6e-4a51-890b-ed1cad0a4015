import { LinkedInLeadFormLeadService } from "../../../domain/services/linkedInLeadFormLead.service";
import { GetLinkedInLeadFormLeadByIdRequestDto } from "../../dtos/controllerDtos/linkedInLeadFormLead/getLinkedInLeadFormLead.dto";

export async function getLinkedInLeadFormLeadByIdUseCase(
  input: GetLinkedInLeadFormLeadByIdRequestDto,
  ctx: {
    linkedInLeadFormLeadService: LinkedInLeadFormLeadService;
  },
) {
  const leadFormLead = await ctx.linkedInLeadFormLeadService.getOneById(
    input.id,
  );

  if (!leadFormLead) {
    throw new Error("Lead form lead not found");
  }

  return leadFormLead;
}
