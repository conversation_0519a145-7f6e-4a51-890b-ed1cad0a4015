import { createUuid } from "../../../../core/utils/uuid";
import { LinkedInLeadFormService } from "../../../domain/services/linkedInLeadForm.service";
import { LinkedInLeadFormLeadService } from "../../../domain/services/linkedInLeadFormLead.service";
import { CreateLinkedInLeadFormLeadRequestDto } from "../../dtos/controllerDtos/linkedInLeadFormLead/createLinkedInLeadFormLead.dto";

export async function createLinkedInLeadFormLeadUseCase(
  input: CreateLinkedInLeadFormLeadRequestDto,
  ctx: {
    linkedInLeadFormService: LinkedInLeadFormService;
    linkedInLeadFormLeadService: LinkedInLeadFormLeadService;
  },
) {
  // Verify the lead form exists
  const leadForm = await ctx.linkedInLeadFormService.getOneById(
    input.linkedInLeadFormId,
  );

  if (!leadForm) {
    throw new Error("Lead form not found");
  }

  // Create the lead form lead
  const res = await ctx.linkedInLeadFormLeadService.createOne({
    ...input,
    id: createUuid(),
    leadCreatedAt: input.leadCreatedAt,
  });

  return {
    id: res.id,
  };
}
