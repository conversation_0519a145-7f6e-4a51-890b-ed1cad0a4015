import { LinkedInLeadFormService } from "../../../domain/services/linkedInLeadForm.service";
import { LinkedInLeadFormLeadService } from "../../../domain/services/linkedInLeadFormLead.service";
import { GetLinkedInLeadFormLeadsByFormIdRequestDto } from "../../dtos/controllerDtos/linkedInLeadFormLead/getLinkedInLeadFormLead.dto";

export async function getLinkedInLeadFormLeadsByFormIdUseCase(
  input: GetLinkedInLeadFormLeadsByFormIdRequestDto,
  ctx: {
    linkedInLeadFormService: LinkedInLeadFormService;
    linkedInLeadFormLeadService: LinkedInLeadFormLeadService;
  },
) {
  // Verify the lead form exists
  const leadForm = await ctx.linkedInLeadFormService.getOneById(
    input.leadFormId,
  );

  if (!leadForm) {
    throw new Error("Lead form not found");
  }

  // Get all leads for the form
  const leads = await ctx.linkedInLeadFormLeadService.getAllByLeadFormId(
    input.leadFormId,
  );

  return {
    leads,
    formName: leadForm.name,
    formState: leadForm.state,
  };
}
