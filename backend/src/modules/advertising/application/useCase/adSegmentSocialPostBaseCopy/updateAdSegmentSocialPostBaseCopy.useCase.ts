import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { SocialPostAdCopyService } from "../../../domain/services/socialPostAdCopy.service";
import { UpdateBaseCopyRequestDto } from "../../dtos/controllerDtos/adSegmentSocialPostBaseCopy/updateBaseCopy.dto";
import { ILinkedInAdSegmentSocialPostBaseCopyRepository } from "../../interfaces/infrastructure/repositories/linkedInAdSegmentSocialPostBaseCopy.repository.interface";
import { ISocialPostCopyRepository } from "../../interfaces/infrastructure/repositories/socialPostCopy.repository.interface";

export class UpdateAdSegmentSocialPostBaseCopyUseCase {
  constructor(
    private readonly adSegmentSocialPostBaseCopyRepository: ILinkedInAdSegmentSocialPostBaseCopyRepository,
    private readonly valuePropService: AdSegmentValuePropService,
    private readonly socialPostCopyService: SocialPostAdCopyService,
  ) {}

  async execute(input: UpdateBaseCopyRequestDto, tx: ITransaction) {
    await this.adSegmentSocialPostBaseCopyRepository.createOrUpdateAdSegmentBaseSocialPostCopy(
      {
        adSegmentId: input.adSegmentId,
        baseCopy: input.baseCopy,
      },
      tx,
    );
    const valueProps = await this.valuePropService.getManyForAdSegment(
      input.adSegmentId,
      "DRAFT",
      tx,
    );
    await this.socialPostCopyService.deleteManyForLinkedInAdSegmentValueProps(
      {
        linkedInAdSegmentValuePropIds: valueProps.map((each) => each.id),
        status: "DRAFT",
      },
      tx,
    );
  }
}
