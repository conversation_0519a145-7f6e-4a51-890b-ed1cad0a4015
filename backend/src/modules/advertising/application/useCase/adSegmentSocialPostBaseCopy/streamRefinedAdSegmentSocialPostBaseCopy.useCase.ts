import { ILLMCompletionService } from "../../../../core/application/interfaces/infrastructure/services/llmCompletionService.interface";
import { IPromptStorageService } from "../../../../core/application/interfaces/infrastructure/services/promptStorage.service.interface";
import { LinkedInAdSegmentSocialPostBaseCopyService } from "../../../domain/services/linkedInAdSegmentSocialPostBaseCopy.service";
import { IAdvertisingLlmCompletionsService } from "../../interfaces/infrastructure/services/advertisingLlmCompletions.service.interface";

interface SocialPostConfig {
  type: "socialPost";
  field: "title" | "body";
}

interface ConversationConfig {
  type: "conversation";
  field: "subject" | "message";
}

type Config = SocialPostConfig | ConversationConfig;

export class StreamRefinedAdSegmentSocialPostBaseCopyUseCase {
  constructor(
    private readonly advertisingLlmCompletionsService: IAdvertisingLlmCompletionsService,
    private readonly promptStorageService: IPromptStorageService,
    private readonly llmCompletions: ILLMCompletionService,
  ) {}

  async *execute(input: {
    baseCopy: string;
    feedback: string;
    config: Config;
  }) {
    let completions: AsyncGenerator<string, any, any> | undefined = undefined;
    if (input.config.type == "socialPost") {
      if (input.config.field == "body") {
        completions =
          await this.advertisingLlmCompletionsService.generateRefinedAdSegmentSocialPostBaseCopy(
            input,
            {
              lllmCompletions: this.llmCompletions,
              promptStorage: this.promptStorageService,
            },
          );
      } else if (input.config.field == "title") {
        completions =
          await this.advertisingLlmCompletionsService.generateRefinedSocialPostTitle(
            input,
            {
              lllmCompletions: this.llmCompletions,
              promptStorage: this.promptStorageService,
            },
          );
      }
    } else if (input.config.type == "conversation") {
      if (input.config.field == "subject") {
        completions =
          await this.advertisingLlmCompletionsService.generateRefinedConversationSubject(
            input,
            {
              lllmCompletions: this.llmCompletions,
              promptStorage: this.promptStorageService,
            },
          );
      } else if (input.config.field == "message") {
        completions =
          await this.advertisingLlmCompletionsService.generateRefinedConversationMessage(
            input,
            {
              lllmCompletions: this.llmCompletions,
              promptStorage: this.promptStorageService,
            },
          );
      }
    }

    if (!completions) {
      throw new Error("No completions found");
    }

    for await (const each of completions) {
      yield each;
    }
  }
}
