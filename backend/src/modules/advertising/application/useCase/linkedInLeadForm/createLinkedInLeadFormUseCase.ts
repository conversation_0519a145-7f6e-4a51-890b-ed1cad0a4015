import { createUuid } from "../../../../core/utils/uuid";
import { LinkedInAdAccountService } from "../../../domain/services/linkedInAdAccount.service";
import { LinkedInLeadFormService } from "../../../domain/services/linkedInLeadForm.service";
import { CreateLinkedInLeadFormRequestDto } from "../../dtos/controllerDtos/linkedInLeadForm/createLinkedInLeadForm.dto";

export async function createLinkedInLeadFormUseCase(
  input: CreateLinkedInLeadFormRequestDto,
  ctx: {
    linkedInLeadFormService: LinkedInLeadFormService;
    linkedInAdAccountService: LinkedInAdAccountService;
    organizationId: number;
  },
) {
  // Verify the ad account exists and belongs to the organization
  const adAccount = await ctx.linkedInAdAccountService.getOneById(
    input.linkedInAdAccountId,
  );

  if (!adAccount) {
    throw new Error("Ad account not found");
  }

  if (adAccount.organizationId !== ctx.organizationId) {
    throw new Error("Ad account does not belong to user's organization");
  }

  // Check if a form with this URN already exists
  const existingForm = await ctx.linkedInLeadFormService.getOneByLinkedInUrn(
    input.leadGenFormUrn,
  );

  if (existingForm) {
    throw new Error("A Lead Form with this URN already exists");
  }

  // Create the lead form
  const res = await ctx.linkedInLeadFormService.createOne({
    ...input,
    id: createUuid(),
  });

  return {
    id: res.id,
  };
}
