import { LinkedInLeadFormService } from "../../../domain/services/linkedInLeadForm.service";
import { GetLinkedInLeadFormByUrnRequestDto } from "../../dtos/controllerDtos/linkedInLeadForm/getLinkedInLeadForm.dto";

export async function getLinkedInLeadFormByUrnUseCase(
  input: GetLinkedInLeadFormByUrnRequestDto,
  ctx: {
    linkedInLeadFormService: LinkedInLeadFormService;
  },
) {
  const leadForm = await ctx.linkedInLeadFormService.getOneByLinkedInUrn(
    input.urn,
  );

  if (!leadForm) {
    throw new Error("Lead form not found");
  }

  return {
    id: leadForm.id,
    linkedInAdAccountId: leadForm.linkedInAdAccountId,
    leadGenFormUrn: leadForm.leadGenFormUrn,
    name: leadForm.name,
    state: leadForm.state,
    version: leadForm.version,
  };
}
