import { LinkedInLeadFormService } from "../../../domain/services/linkedInLeadForm.service";
import { GetLinkedInLeadFormByIdRequestDto } from "../../dtos/controllerDtos/linkedInLeadForm/getLinkedInLeadForm.dto";

export async function getLinkedInLeadFormByIdUseCase(
  input: GetLinkedInLeadFormByIdRequestDto,
  ctx: {
    linkedInLeadFormService: LinkedInLeadFormService;
  },
) {
  const leadForm = await ctx.linkedInLeadFormService.getOneById(input.id);

  if (!leadForm) {
    throw new Error("Lead form not found");
  }

  return {
    id: leadForm.id,
    linkedInAdAccountId: leadForm.linkedInAdAccountId,
    leadGenFormUrn: leadForm.leadGenFormUrn,
    name: leadForm.name,
    state: leadForm.state,
    version: leadForm.version,
  };
}
