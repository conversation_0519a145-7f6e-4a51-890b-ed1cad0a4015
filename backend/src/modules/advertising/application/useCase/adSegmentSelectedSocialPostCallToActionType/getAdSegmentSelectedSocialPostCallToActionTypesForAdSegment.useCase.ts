import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedSocialPostCallToActionTypeService } from "../../../domain/services/adSegmentSelectedSocialPostCallToActionType.service";
import { GetAdSegmentSelectedSocialPostCallToActionTypesForAdSegmentRequestDto } from "../../dtos/controllerDtos/adSegmentSelectedSocialPostCallToActionType/getAdSegmentSelectedSocialPostCallToActionTypesForAdSegment.dto";

export class GetAdSegmentSelectedSocialPostCallToActionTypesForAdSegmentUseCase {
  constructor(
    private readonly adSegmentSelectedSocialPostCallToActionTypeService: AdSegmentSelectedSocialPostCallToActionTypeService,
    private readonly transaction: ITransaction,
  ) {}

  async execute(
    input: GetAdSegmentSelectedSocialPostCallToActionTypesForAdSegmentRequestDto,
  ) {
    return this.adSegmentSelectedSocialPostCallToActionTypeService.getAllForAdSegment(
      input.adSegmentId,
      this.transaction,
    );
  }
}
