import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedSocialPostCallToActionTypeService } from "../../../domain/services/adSegmentSelectedSocialPostCallToActionType.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { SocialPostCallToActionCopyService } from "../../../domain/services/socialPostCallToActionCopy.service";
import { SetSingleAdSegmentSelectedSocialPostCallToActionTypesRequestDto } from "../../dtos/controllerDtos/adSegmentSelectedSocialPostCallToActionType/setSingleAdSegmentSelectedSocialPostCallToActionTypes.dto";

export class SetSingleAdSegmentSelectedSocialPostCallToActionTypesUseCase {
  constructor(
    private readonly adSegmentSelectedSocialPostCallToActionTypeService: AdSegmentSelectedSocialPostCallToActionTypeService,
    private readonly adSegmentValuePropService: AdSegmentValuePropService,
    private readonly socialPostCallToActionCopyService: SocialPostCallToActionCopyService,
    private readonly transaction: ITransaction,
  ) {}

  async execute(
    input: SetSingleAdSegmentSelectedSocialPostCallToActionTypesRequestDto,
  ) {
    await this.adSegmentSelectedSocialPostCallToActionTypeService.setForAdSegment(
      input.adSegmentId,
      input.valuePropId,
      input.socialPostCallToActionTypes,
      this.socialPostCallToActionCopyService,
      this.adSegmentValuePropService,
      this.transaction,
    );
  }
}
