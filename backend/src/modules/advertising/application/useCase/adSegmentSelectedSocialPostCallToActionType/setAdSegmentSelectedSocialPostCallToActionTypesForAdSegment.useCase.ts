import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedSocialPostCallToActionTypeService } from "../../../domain/services/adSegmentSelectedSocialPostCallToActionType.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { SocialPostCallToActionCopyService } from "../../../domain/services/socialPostCallToActionCopy.service";
import { SetAdSegmentSelectedSocialPostCallToActionTypesForAdSegmentRequestDto } from "../../dtos/controllerDtos/adSegmentSelectedSocialPostCallToActionType/setAdSegmentSelectedSocialPostCallToActionTypesForAdSegment.dto";

export class SetAdSegmentSelectedSocialPostCallToActionTypesForAdSegmentUseCase {
  constructor(
    private readonly adSegmentSelectedSocialPostCallToActionTypeService: AdSegmentSelectedSocialPostCallToActionTypeService,
    private readonly adSegmentValuePropService: AdSegmentValuePropService,
    private readonly socialPostCallToActionCopyService: SocialPostCallToActionCopyService,
    private readonly transaction: ITransaction,
  ) {}

  async execute(
    input: SetAdSegmentSelectedSocialPostCallToActionTypesForAdSegmentRequestDto,
  ) {
    for (const adSegment of input.adSegments) {
      await this.adSegmentSelectedSocialPostCallToActionTypeService.setForAdSegment(
        adSegment.adSegmentId,
        adSegment.valuePropId,
        adSegment.socialPostCallToActionTypes,
        this.socialPostCallToActionCopyService,
        this.adSegmentValuePropService,
        this.transaction,
      );
    }
  }
}
