import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedConversationMessageTypeService } from "../../../domain/services/adSegmentSelectedConversationMessageType.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../../domain/services/conversationCallToActionCopy.service";
import { ConversationMessageCopyService } from "../../../domain/services/conversationMessageCopy.service";
import { SetAdSegmentSelectedConversationMessageTypesForAdSegmentRequestDto } from "../../dtos/controllerDtos/adSegmentSelectedConversationMessageType/setAdSegmentSelectedConversationMessageTypesForAdSegment.dto";

export class SetAdSegmentSelectedConversationMessageTypesForAdSegmentUseCase {
  constructor(
    private readonly adSegmentSelectedConversationMessageTypeService: AdSegmentSelectedConversationMessageTypeService,
    private readonly adSegmentValuePropService: AdSegmentValuePropService,
    private readonly conversationMessageCopyService: ConversationMessageCopyService,
    private readonly conversationCallToActionCopyService: ConversationCallToActionCopyService,
    private readonly transaction: ITransaction,
  ) {}

  async execute(
    input: SetAdSegmentSelectedConversationMessageTypesForAdSegmentRequestDto,
  ) {
    for (const adSegment of input.adSegments) {
      await this.adSegmentSelectedConversationMessageTypeService.setForAdSegment(
        adSegment.adSegmentId,
        adSegment.valuePropId,
        adSegment.conversationSubjectCopyType,
        adSegment.conversationMessageCopyTypes,
        this.conversationMessageCopyService,
        this.conversationCallToActionCopyService,
        this.adSegmentValuePropService,
        this.transaction,
      );
    }
  }
}
