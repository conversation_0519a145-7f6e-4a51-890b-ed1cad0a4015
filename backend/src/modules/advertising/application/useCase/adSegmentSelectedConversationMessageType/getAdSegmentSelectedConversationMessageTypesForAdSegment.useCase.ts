import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedConversationMessageTypeService } from "../../../domain/services/adSegmentSelectedConversationMessageType.service";
import { GetAdSegmentSelectedConversationMessageTypesForAdSegmentRequestDto } from "../../dtos/controllerDtos/adSegmentSelectedConversationMessageType/getAdSegmentSelectedConversationMessageTypesForAdSegment.dto";

export class GetAdSegmentSelectedConversationMessageTypesForAdSegmentUseCase {
  constructor(
    private readonly adSegmentSelectedConversationMessageTypeService: AdSegmentSelectedConversationMessageTypeService,
    private readonly transaction: ITransaction,
  ) {}

  async execute(
    input: GetAdSegmentSelectedConversationMessageTypesForAdSegmentRequestDto,
  ) {
    return this.adSegmentSelectedConversationMessageTypeService.getAllForAdSegment(
      input.adSegmentId,
      this.transaction,
    );
  }
}
