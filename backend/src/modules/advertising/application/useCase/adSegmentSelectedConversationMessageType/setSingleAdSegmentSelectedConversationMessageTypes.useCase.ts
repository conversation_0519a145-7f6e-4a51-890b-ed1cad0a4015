import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedConversationMessageTypeService } from "../../../domain/services/adSegmentSelectedConversationMessageType.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../../domain/services/conversationCallToActionCopy.service";
import { ConversationMessageCopyService } from "../../../domain/services/conversationMessageCopy.service";
import { SetSingleAdSegmentSelectedConversationMessageTypesRequestDto } from "../../dtos/controllerDtos/adSegmentSelectedConversationMessageType/setSingleAdSegmentSelectedConversationMessageTypes.dto";

export class SetSingleAdSegmentSelectedConversationMessageTypesUseCase {
  constructor(
    private readonly adSegmentSelectedConversationMessageTypeService: AdSegmentSelectedConversationMessageTypeService,
    private readonly adSegmentValuePropService: AdSegmentValuePropService,
    private readonly conversationMessageCopyService: ConversationMessageCopyService,
    private readonly conversationCallToActionCopyService: ConversationCallToActionCopyService,
    private readonly transaction: ITransaction,
  ) {}

  async execute(
    input: SetSingleAdSegmentSelectedConversationMessageTypesRequestDto,
  ) {
    await this.adSegmentSelectedConversationMessageTypeService.setForAdSegment(
      input.adSegmentId,
      input.valuePropId,
      input.conversationSubjectCopyType,
      input.conversationMessageCopyTypes,
      this.conversationMessageCopyService,
      this.conversationCallToActionCopyService,
      this.adSegmentValuePropService,
      this.transaction,
    );
  }
}
