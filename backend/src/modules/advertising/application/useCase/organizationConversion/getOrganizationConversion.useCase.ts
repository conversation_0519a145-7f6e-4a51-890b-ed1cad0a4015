import { OrganizationConversion } from "../../../domain/entites/organizationConversion";
import { OrganizationConversionService } from "../../../domain/services/organizationConversion.service";

export class GetOrganizationConversionUseCase {
  constructor(
    private readonly organizationConversionService: OrganizationConversionService,
    private readonly ctx: {
      organizationId: number;
    },
  ) {}

  async execute(): Promise<OrganizationConversion[]> {
    return this.organizationConversionService.getForOrganization(
      this.ctx.organizationId,
    );
  }
}
