import { OrganizationRepository } from "../../../../core/infrastructure/repositories/organization.repository";
import { OrganizationConversionService } from "../../../domain/services/organizationConversion.service";
import { DeleteOrganizationConversionRequestDto } from "../../dtos/controllerDtos/organizationConversion/deleteOrganizationConversion.dto";

export class DeleteOrganizationConversionUseCase {
  constructor(
    private readonly organizationConversionService: OrganizationConversionService,
    private readonly ctx: {
      organizationId: number;
    },
  ) {}

  async execute(input: DeleteOrganizationConversionRequestDto): Promise<void> {
    const organziationConversion =
      await this.organizationConversionService.getOne(input.id);

    if (!organziationConversion) {
      throw new Error("Organization conversion not found");
    }
    if (organziationConversion.organizationId !== this.ctx.organizationId) {
      throw new Error(
        "Organization conversion does not belong to organization",
      );
    }

    await this.organizationConversionService.deleteOne(input.id);
  }
}
