import { createUuid } from "../../../../core/utils/uuid";
import { OrganizationConversion } from "../../../domain/entites/organizationConversion";
import { OrganizationConversionService } from "../../../domain/services/organizationConversion.service";
import { CreateOrganizationConversionRequestDto } from "../../dtos/controllerDtos/organizationConversion/createOrganizationConversion.dto";

export class CreateOrganizationConversionUseCase {
  constructor(
    private readonly organizationConversionService: OrganizationConversionService,
    private readonly ctx: {
      organizationId: number;
    },
  ) {}

  async execute(
    input: CreateOrganizationConversionRequestDto,
  ): Promise<OrganizationConversion> {
    const organizationConversion =
      await this.organizationConversionService.createOne(
        OrganizationConversion({
          id: createUuid(),
          organizationId: this.ctx.organizationId,
          conversionUrn: input.conversionUrn,
          name: input.name,
        }),
      );
    return organizationConversion;
  }
}
