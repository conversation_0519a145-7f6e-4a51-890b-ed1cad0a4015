import { GetCopyVariantStreamingRequestDto } from "../../dtos/controllerDtos/copyVariant/getCopyVariantStreaming.dto";
import { IAdvertisingLlmCompletionsService } from "../../interfaces/infrastructure/services/advertisingLlmCompletions.service.interface";
import { CopyVariantServiceFactory } from "../../services/copyVariant/copyVariant.service.factory";

export class GetCopyVariantStreamingUseCase {
  constructor(
    private readonly ctx: {
      organizationId: number;
      advertisingLlmCompletionsService: IAdvertisingLlmCompletionsService;
    },
  ) {}

  async *executeStream(
    input: GetCopyVariantStreamingRequestDto,
  ): AsyncGenerator<{
    data: string;
    done: boolean;
  }> {
    try {
      // Create the appropriate service using the factory
      const copyVariantService = CopyVariantServiceFactory.create(
        input,
        this.ctx.advertisingLlmCompletionsService,
      );

      // Call the streaming method
      const stream = copyVariantService.getCopyVariantStreaming(
        input.data.standardCopy,
        input.data.type,
      );

      // Yield each chunk from the stream
      for await (const chunk of stream) {
        yield chunk;
      }
    } catch (error) {
      console.error("Error streaming copy variant:", error);
      throw error;
    }
  }
}
