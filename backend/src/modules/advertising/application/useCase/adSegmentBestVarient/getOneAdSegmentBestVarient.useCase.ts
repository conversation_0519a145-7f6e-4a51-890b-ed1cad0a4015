import { AdSegmentBestVariantRepository } from "../../../infrastructure/repositories/adSegmentBestVariant.repository";
import { GetOneAdSegmentBestVarientDto } from "../../dtos/controllerDtos/adSegmentBestVarient/getOneAdSegmentBestVarient.dto";

export class GetOneAdSegmentBestVarientUseCase {
  constructor(
    private readonly adSegmentBestVariantRepository: AdSegmentBestVariantRepository,
  ) {}

  async execute(dto: GetOneAdSegmentBestVarientDto) {
    const adSegmentBestVariant =
      await this.adSegmentBestVariantRepository.getOne(
        dto.adSegmentId,
        dto.adFormatType,
      );
    if (!adSegmentBestVariant) {
      return null;
    }
    return adSegmentBestVariant;
  }
}
