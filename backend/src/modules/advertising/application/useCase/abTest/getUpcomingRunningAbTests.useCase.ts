import { linkedInAd } from "../../../../../../../packages/database/src/schema/linkedInAd";
import { AbTest } from "../../../domain/entites/abTest";
import { AdSegmentValueProp } from "../../../domain/entites/adSegmentValueProp";
import { AdCreativeService } from "../../../domain/services/adCreative.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../../domain/services/conversationCallToActionCopy.service";
import { ConversationMessageCopyService } from "../../../domain/services/conversationMessageCopy.service";
import { ConversationSubjectCopyService } from "../../../domain/services/conversationSubjectCopy.service";
import { LinkedInAdProgramAdCreativeService } from "../../../domain/services/linkedInAdProgramAdCreative.service";
import { SocialPostAdCopyService } from "../../../domain/services/socialPostAdCopy.service";
import { SocialPostCallToActionCopyService } from "../../../domain/services/socialPostCallToActionCopy.service";
import { AdSegmentValuePropCreativeRepository } from "../../../infrastructure/repositories/adSegmentValuePropCreative.repository";
import { GetUpcomingRunningAbTestsDto } from "../../dtos/controllerDtos/abTest/getUpcomingRunningAbTests.dto";
import { IStageRepository } from "../../interfaces/infrastructure/repositories/stage.repository.interface";
import { AbTestFacadeService } from "../../services/abTest/abTest.facade.service";

export interface UpcomingAbTest {
  stageId: string;
  stageType:
    | "audienceTest"
    | "valuePropTest"
    | "creativeTest"
    | "adCopyTest"
    | "conversationSubjectTest"
    | "socialPostBodyCopyTest"
    | "socialPostCallToActionTest"
    | "conversationCallToActionTest"
    | "conversationMessageCopyTest";
  valueProps?: AdSegmentValueProp[];
  creatives?: {
    id: string;
    fileName: string;
    fileType: string;
    presignedUrl: string;
  }[];
  conversationSubjects?: {}[];
}

export class GetUpcomingRunningAbTestsUseCase {
  constructor(
    private readonly ctx: {
      stageRepository: IStageRepository;
      adSegmentValuePropService: AdSegmentValuePropService;
      adProgramAdCreativeService: LinkedInAdProgramAdCreativeService;
      adCreativeService: AdCreativeService;
      socialPostCopyService: SocialPostAdCopyService;
      socialPostCallToActionCopyService: SocialPostCallToActionCopyService;
      conversationCopyService: ConversationSubjectCopyService;
      conversationMessageCopyService: ConversationMessageCopyService;
      conversationCallToActionCopyService: ConversationCallToActionCopyService;
      adSegmentValuePropCreativeRepository: AdSegmentValuePropCreativeRepository;
    },
  ) {}

  async execute(dto: GetUpcomingRunningAbTestsDto) {
    try {
      // Validate adSegmentId
      if (!dto.adSegmentId) {
        return []; // Return empty array if no adSegmentId provided
      }

      const stages = await this.ctx.stageRepository.getStagesForAdSegment(
        dto.adSegmentId,
      );

      if (!stages || stages.length === 0) {
        return []; // Return empty array if no stages found
      }

      const res: UpcomingAbTest[] = [];
      for (const [index, stage] of stages.entries()) {
        if (stage.status !== "NOT_STATED") {
          continue;
        }

        let curRes: UpcomingAbTest = {
          stageId: stage.id,
          stageType: stage.stageType,
        };

        let abTest: AbTest["type"] | undefined = undefined;

        if (stage.stageType == "audienceTest") {
          abTest = "audience";
        } else if (stage.stageType == "valuePropTest") {
          abTest = "valueProp";
          const valueProps =
            await this.ctx.adSegmentValuePropService.getManyForAdSegment(
              stage.adSegmentId,
              "ACTIVE",
            );

          // Only Get Value props if this isn't a SPONSORED_CONVERSATION or SPONSORED_INMAIL
          // aka only get value props if this is SPONSORED_CONTENT
          if (
            dto.adFormat !== "SPONSORED_CONVERSATION" &&
            dto.adFormat !== "SPONSORED_INMAIL"
          ) {
            curRes["valueProps"] = valueProps;
          }

          // Gets Conversation Subjects -- can only be done if 3rd stage is valuePropTest and 2nd is conversationSubjectTest
          if (index > 0) {
            const prevStage = stages[index - 1];

            if (prevStage?.stageType === "conversationSubjectTest") {
              const adSegmentValuePropId = valueProps[0]?.id;

              if (!adSegmentValuePropId) {
                continue;
              }

              const conversationCopies =
                await this.ctx.conversationCopyService.getAllForAdSegment(
                  adSegmentValuePropId,
                  "ACTIVE",
                );

              //TODO-BR: change to ACTIVE
              if (!conversationCopies) {
                console.log(
                  "No conversation copies found for adsegmeintId",
                  dto.adSegmentId,
                );
                continue;
              }

              let conversationCopiesData = [];
              for (const copy of conversationCopies) {
                conversationCopiesData.push({
                  id: copy.id,
                  content: copy.content,
                  type: copy.type,
                });
              }

              if (index > 0) {
                if (index > 0 && res[index - 1] !== undefined) {
                  (res[index - 1] as UpcomingAbTest).conversationSubjects =
                    conversationCopiesData;
                }
              }
            }
          }
        } else if (stage.stageType == "creativeTest") {
          curRes.creatives = [];

          // only get creatives that are tied to value props
          const adSegmentValuePropCreatives =
            await this.ctx.adSegmentValuePropCreativeRepository.getAllForAdSegment(
              stage.adSegmentId,
            );

          // There might be duplicates in adSegmentValuePropCreatives
          // map to de-dupe
          const adCreativeMap = new Map<string, boolean>();

          for (const adSegmentValuePropCreative of adSegmentValuePropCreatives) {
            if (
              adCreativeMap.has(adSegmentValuePropCreative.adProgramCreativeId)
            ) {
              continue; // Skip if already processed
            }
            adCreativeMap.set(
              adSegmentValuePropCreative.adProgramCreativeId,
              true,
            );

            const adCreative = await this.ctx.adProgramAdCreativeService.getOne(
              { id: adSegmentValuePropCreative.adProgramCreativeId },
            );

            if (!adCreative) {
              continue;
            }

            const adCreativeData =
              await this.ctx.adCreativeService.getOneWithDownloadPreSignedUrlAndMetadata(
                adCreative.adCreativeId,
              );
            if (!adCreativeData) {
              continue;
            }

            curRes.creatives.push({
              id: adCreativeData.adCreative.id,
              fileName: adCreativeData.adCreative.fileName,
              fileType: adCreativeData.adCreative.fileType,
              presignedUrl: adCreativeData.presignedUrl,
            });
          }

          abTest = "creative";
        } else if (stage.stageType == "conversationSubjectTest") {
          abTest = "conversationSubject";
        } else if (stage.stageType == "adCopyTest") {
          abTest = "conversationSubject";
        } else if (stage.stageType === "conversationMessageCopyTest") {
          abTest = "conversationMessageCopy";
        } else if (stage.stageType == "socialPostBodyCopyTest") {
          abTest = "socialPostBodyCopy";
        } else if (stage.stageType === "socialPostCallToActionTest") {
          abTest = "socialPostCallToAction";
        } else if (stage.stageType === "conversationCallToActionTest") {
          abTest = "conversationCallToAction";
        } else {
          console.error(`Invalid stage type: ${stage.stageType}`);
          continue; // Skip this stage and continue with the next one
        }

        try {
          //For Sponsored_Conversation or sponsored_inmail, don't get mid-campaign data (valuePropTest)
          if (
            !(
              (dto.adFormat === "SPONSORED_CONVERSATION" ||
                dto.adFormat === "SPONSORED_INMAIL") &&
              stage.stageType === "valuePropTest"
            )
          ) {
            const abTestFacadeService =
              AbTestFacadeService.createFactory(abTest);

            const data = await abTestFacadeService.getData(stage.id);
            curRes = {
              ...curRes,
              ...data,
            };
            res.push(curRes);
          }
        } catch (error) {
          console.error(`Error fetching data for stage ${stage.id}:`, error);
          // Continue to the next stage instead of failing the entire request
        }
      }
      return res;
    } catch (error) {
      console.error("Error in getUpcomingRunningAbTests:", error);
      return []; // Return empty array in case of errors
    }
  }
}
