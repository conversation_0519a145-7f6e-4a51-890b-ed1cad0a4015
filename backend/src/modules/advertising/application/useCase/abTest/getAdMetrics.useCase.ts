import { dot } from "node:test/reporters";

import { linkedInAd } from "../../../../../../../packages/database/src/schema/linkedInAd";
import { AbTest } from "../../../domain/entites/abTest";
import { AdSegmentValueProp } from "../../../domain/entites/adSegmentValueProp";
import { AdCreativeService } from "../../../domain/services/adCreative.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../../domain/services/conversationCallToActionCopy.service";
import { ConversationMessageCopyService } from "../../../domain/services/conversationMessageCopy.service";
import { ConversationSubjectCopyService } from "../../../domain/services/conversationSubjectCopy.service";
import { LinkedInAdProgramAdCreativeService } from "../../../domain/services/linkedInAdProgramAdCreative.service";
import { SocialPostAdCopyService } from "../../../domain/services/socialPostAdCopy.service";
import { SocialPostCallToActionCopyService } from "../../../domain/services/socialPostCallToActionCopy.service";
import { GetAdMetricsDto } from "../../dtos/controllerDtos/abTest/getAdMetrics.dto";
import { IStageRepository } from "../../interfaces/infrastructure/repositories/stage.repository.interface";
import { AbTestFacadeService } from "../../services/abTest/abTest.facade.service";

export interface UpcomingAbTest {
  stageId: string;
  stageType:
    | "audienceTest"
    | "valuePropTest"
    | "creativeTest"
    | "adCopyTest"
    | "conversationSubjectTest"
    | "socialPostBodyCopyTest"
    | "socialPostCallToActionTest"
    | "conversationCallToActionTest";
  valueProps?: AdSegmentValueProp[];
  creatives?: {
    id: string;
    fileName: string;
    fileType: string;
    presignedUrl: string;
  }[];
  conversationSubjects?: {}[];
  sponsoredCreativeId: string;
  sponsoredCreativeUrn: string;
  adVarients: Record<string, string>;
  rounds?: Record<string, string>;
  status: string;
  type: string;
}

export class GetAdMetricsUseCase {
  constructor() {}

  async execute(dto: GetAdMetricsDto) {
    // Validate adSegmentId
    if (!dto.sponsoredCreativeUrns) {
      return []; // Return empty array if no adSegmentId provided
    }

    try {
      // hard code audience to createFactory, abTest type doesn't matter here
      //TODO-BR: don't hardcode
      const abTestFacadeService = AbTestFacadeService.createFactory("audience");

      const sponsoredCreativeUrnsWithMetrics =
        await abTestFacadeService.getMetricDataForCreatives(
          dto.sponsoredCreativeUrns,
          dto.linkedInAdProgramId,
          dto.fromDate,
          dto.toDate,
        );

      return sponsoredCreativeUrnsWithMetrics;
    } catch (error) {
      console.error(`[getAdMetrics.useCase] Error fetching data:`, error);
      // Continue to the next stage instead of failing the entire request
    }
  }
}
