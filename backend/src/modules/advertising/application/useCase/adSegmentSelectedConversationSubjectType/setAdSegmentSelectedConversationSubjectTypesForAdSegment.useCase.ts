import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedConversationSubjectTypeService } from "../../../domain/services/adSegmentSelectedConversationSubjectType.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { ConversationSubjectCopyService } from "../../../domain/services/conversationSubjectCopy.service";
import { SetAdSegmentSelectedConversationSubjectTypesForAdSegmentRequestDto } from "../../dtos/controllerDtos/adSegmentSelectedConversationSubjectType/setAdSegmentSelectedConversationSubjectTypesForAdSegment.dto";

export class SetAdSegmentSelectedConversationSubjectTypesForAdSegmentUseCase {
  constructor(
    private readonly adSegmentSelectedConversationSubjectTypeService: AdSegmentSelectedConversationSubjectTypeService,
    private readonly adSegmentValuePropService: AdSegmentValuePropService,
    private readonly conversationSubjectCopyService: ConversationSubjectCopyService,
    private readonly transaction: ITransaction,
  ) {}

  async execute(
    input: SetAdSegmentSelectedConversationSubjectTypesForAdSegmentRequestDto,
  ) {
    for (const adSegment of input.adSegments) {
      await this.adSegmentSelectedConversationSubjectTypeService.setForAdSegment(
        adSegment.adSegmentId,
        adSegment.conversationSubjectCopyTypes,
        this.conversationSubjectCopyService,
        this.adSegmentValuePropService,
        this.transaction,
      );
    }
  }
}
