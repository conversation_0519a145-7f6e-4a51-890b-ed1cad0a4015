import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedConversationSubjectTypeService } from "../../../domain/services/adSegmentSelectedConversationSubjectType.service";
import { GetAdSegmentSelectedConversationSubjectTypesForAdSegmentRequestDto } from "../../dtos/controllerDtos/adSegmentSelectedConversationSubjectType/getAdSegmentSelectedConversationSubjectTypesForAdSegment.dto";

export class GetAdSegmentSelectedConversationSubjectTypesForAdSegmentUseCase {
  constructor(
    private readonly adSegmentSelectedConversationSubjectTypeService: AdSegmentSelectedConversationSubjectTypeService,
    private readonly transaction: ITransaction,
  ) {}

  async execute(
    input: GetAdSegmentSelectedConversationSubjectTypesForAdSegmentRequestDto,
  ) {
    return this.adSegmentSelectedConversationSubjectTypeService.getAllForAdSegment(
      input.adSegmentId,
      this.transaction,
    );
  }
}
