import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedConversationSubjectTypeService } from "../../../domain/services/adSegmentSelectedConversationSubjectType.service";
import { DeleteAdSegmentSelectedConversationSubjectTypesForAdSegmentRequestDto } from "../../dtos/controllerDtos/adSegmentSelectedConversationSubjectType/deleteAdSegmentSelectedConversationSubjectTypesForAdSegment.dto";

export class DeleteAdSegmentSelectedConversationSubjectTypesForAdSegmentUseCase {
  constructor(
    private readonly adSegmentSelectedConversationSubjectTypeService: AdSegmentSelectedConversationSubjectTypeService,
  ) {}

  async execute(
    input: DeleteAdSegmentSelectedConversationSubjectTypesForAdSegmentRequestDto,
    transaction?: ITransaction,
  ) {
    await this.adSegmentSelectedConversationSubjectTypeService.deleteOne(
      input.id,
      transaction,
    );
  }
}
