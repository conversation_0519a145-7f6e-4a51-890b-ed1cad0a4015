import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { createUuid } from "../../../../core/utils/uuid";
import { AdSegmentSelectedConversationSubjectType } from "../../../domain/entites/adSegmentSelectedConversationSubjectType";
import { AdSegmentSelectedConversationSubjectTypeService } from "../../../domain/services/adSegmentSelectedConversationSubjectType.service";
import { CreateAdSegmentSelectedConversationSubjectTypeRequestDto } from "../../dtos/controllerDtos/adSegmentSelectedConversationSubjectType/create.dto";

export class CreateAdSegmentSelectedConversationSubjectTypeUseCase {
  constructor(
    private readonly adSegmentSelectedConversationSubjectTypeService: AdSegmentSelectedConversationSubjectTypeService,
  ) {}

  async execute(
    input: CreateAdSegmentSelectedConversationSubjectTypeRequestDto,
    transaction?: ITransaction,
  ) {
    const id = createUuid();
    await this.adSegmentSelectedConversationSubjectTypeService.createOne(
      AdSegmentSelectedConversationSubjectType({
        id,
        adSegmentId: input.adSegmentId,
        type: input.type,
      }),
      transaction,
    );
  }
}
