import { SegmentService } from "../../../../core/domain/services/segment.service";
import { AdSegmentService } from "../../../domain/services/adSegment.service";
import {
  GetAllAdSegmentsForSegmentRequestDto,
  GetAllAdSegmentsForSegmentResponseDto
} from "../../dtos/controllerDtos/adSegment/getAllAdSegmentsForSegment.dto";

/**
 * Use case for retrieving all ad segments associated with a core segment ID
 * 
 * This use case validates that the segment belongs to the requesting organization
 * and retrieves all ad segments linked to that core segment.
 */
export async function getAllAdSegmentsForSegmentUseCase(
  input: GetAllAdSegmentsForSegmentRequestDto,
  ctx: {
    adSegmentService: AdSegmentService;
    segmentService: SegmentService;
    organizationId: number;
  }
): Promise<GetAllAdSegmentsForSegmentResponseDto> {
  // First validate that the segment exists and belongs to the organization
  const segment = await ctx.segmentService.getSegmentById(
    input.segmentId,
    { segmentRepository: ctx.segmentService["segmentRepository"] }
  );

  if (!segment) {
    throw new Error("Segment not found");
  }

  if (segment.organizationId !== ctx.organizationId) {
    throw new Error("Segment does not belong to this organization");
  }

  // Get all ad segments for the segment ID
  const adSegments = await ctx.adSegmentService.getAllForSegment(input.segmentId);

  // Map to response format
  return adSegments.map(adSegment => ({
    id: adSegment.id,
    segmentId: adSegment.segmentId,
    adProgramId: adSegment.linkedInAdProgramId,
    ready: adSegment.ready
  }));
}
