import { SegmentService } from "../../../../core/domain/services/segment.service";
import { AdSegmentService } from "../../../domain/services/adSegment.service";
import { LinkedInAdProgramService } from "../../../domain/services/linkedInAdProgram.service";

/**
 * Use case for retrieving detailed information about a segment with its associated ads
 */
export class GetSegmentDetailWithAdsUseCase {
  /**
   * @param services - The services required by this use case
   */
  constructor(
    private services: {
      adSegmentService: AdSegmentService;
      linkedInAdProgramService: LinkedInAdProgramService;
      segmentService: SegmentService;
    },
  ) {}

  /**
   * Executes the use case to retrieve segment details and associated ads
   * @param params - Parameters including segmentId and organizationId
   * @returns Segment details with active, past, and upcoming ads
   */
  async execute(params: { segmentId: string; organizationId: number }) {
    // Get the segment details
    const segment = await this.services.segmentService.getSegmentById(
      params.segmentId,
      { segmentRepository: this.services.segmentService["segmentRepository"] },
    );

    if (!segment) {
      throw new Error("Segment not found");
    }

    // Get ad segments associated with this segment using the proper repository method
    const adSegments = await this.services.adSegmentService.getAllForSegment(
      params.segmentId,
    );

    // Get ad program IDs from the ad segments
    const adProgramIds = adSegments.map(
      (adSegment) => adSegment.linkedInAdProgramId,
    );

    // Fetch all ad programs in a single query
    const adPrograms =
      await this.services.linkedInAdProgramService.getByIds(adProgramIds);

    // Categorize ads by status
    const activeAds = adPrograms.filter((ad) => ad.status === "ACTIVE");
    const completedAds = adPrograms.filter((ad) => ad.status === "COMPLETED");
    const draftAds = adPrograms.filter(
      (ad) => ad.status === "DRAFT" || ad.status === "ARCHIVED_DRAFT",
    );

    // Get analytics data for metrics calculation
    const analytics = await this.services.adSegmentService[
      "adSegmentRepository"
    ].analytics2(params.segmentId);

    // Calculate total budget
    const totalBudget = adPrograms.reduce(
      (sum, ad) => sum + (ad.totalBudget || 0),
      0,
    );

    // Get total impressions from analytics
    const totalImpressions = analytics.reduce(
      (sum: number, a: any) => sum + (a.impressions || 0),
      0,
    );

    // Extract campaign URNs to fetch LinkedIn metrics
    const campaignUrns = analytics
      .map((item: any) => item.campaignLinkedInUrn)
      .filter(Boolean);

    // Default values in case LinkedIn API isn't available
    let totalLeads = 0;
    let totalEngagements = 0;
    let totalSpent = Math.round(totalBudget * 0.1); // Conservative estimate

    // TODO: In the future, integrate with LinkedIn API to get real metrics
    // The ideal implementation would use the LinkedInService to get real-time metrics
    // But for now we'll use analytics data and estimates

    return {
      segment,
      metrics: {
        totalLeads,
        totalEngagements,
        totalImpressions,
        totalBudget,
        totalSpent,
      },
      ads: {
        active: activeAds,
        completed: completedAds,
        upcoming: draftAds,
      },
      abTests: [], // To be implemented in future
    };
  }
}
