import { AdSegmentService } from "../../../domain/services/adSegment.service";
import { LinkedInAdProgramService } from "../../../domain/services/linkedInAdProgram.service";
import {
  GetAllAdSegmentsForAdProgramsRequestDto,
  GetAllAdSegmentsForAdProgramsResponseDto,
} from "../../dtos/controllerDtos/adSegment/getAllAdSegmentsForAdPrograms.dto";

/**
 * Use case for retrieving all ad segments for multiple ad programs
 *
 * This use case validates that all programs belong to the requesting organization
 * and retrieves all segments for the specified programs.
 */
export async function getAllAdSegmentsForAdProgramsUseCase(
  input: GetAllAdSegmentsForAdProgramsRequestDto,
  ctx: {
    adProgramService: LinkedInAdProgramService;
    adSegmentService: AdSegmentService;
    organizationId: number;
  },
): Promise<GetAllAdSegmentsForAdProgramsResponseDto> {
  // Skip validation if no ad program IDs are provided
  if (input.adProgramIds.length === 0) {
    return [];
  }

  // Validate all programs belong to the organization
  const programValidations = await Promise.all(
    input.adProgramIds.map((adProgramId) =>
      ctx.adProgramService.checkIfAdProgramBelongsToOrganization(
        adProgramId,
        ctx.organizationId,
      ),
    ),
  );

  // Check if any validation failed
  const invalidIndex = programValidations.findIndex((valid) => !valid);
  if (invalidIndex !== -1) {
    throw new Error(
      `Ad program ${input.adProgramIds[invalidIndex]} does not belong to organization`,
    );
  }

  // Fetch segments for all programs
  const allSegmentsPromises = input.adProgramIds.map((adProgramId) =>
    ctx.adSegmentService.getAllForAdProgram(adProgramId),
  );

  const segmentsByProgram = await Promise.all(allSegmentsPromises);

  // Flatten the results
  const allSegments = segmentsByProgram.flat();

  // Map to response format
  return allSegments.map((adSegment) => ({
    id: adSegment.id,
    segmentId: adSegment.segmentId,
    adProgramId: adSegment.linkedInAdProgramId,
    ready: adSegment.ready,
  }));
}
