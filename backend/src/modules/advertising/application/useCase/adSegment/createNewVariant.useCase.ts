import { z } from "zod";

import { Transaction } from "../../../../../database/db";
import { SegmentService } from "../../../../core/domain/services/segment.service";
import { createUuid } from "../../../../core/utils/uuid";
import { abTestTypesSchema } from "../../../domain/entites/abTest";
import { AdAudienceService } from "../../../domain/services/adAudience.service";
import { AdCreativeService } from "../../../domain/services/adCreative.service";
import { AdSegmentService } from "../../../domain/services/adSegment.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../../domain/services/conversationCallToActionCopy.service";
import { ConversationMessageCopyService } from "../../../domain/services/conversationMessageCopy.service";
import { ConversationSubjectCopyService } from "../../../domain/services/conversationSubjectCopy.service";
import { LinkedInAdAccountService } from "../../../domain/services/linkedInAdAccount.service";
import { LinkedInAdProgramService } from "../../../domain/services/linkedInAdProgram.service";
import { LinkedInAdProgramAdCreativeService } from "../../../domain/services/linkedInAdProgramAdCreative.service";
import { LinkedInCampaignService } from "../../../domain/services/linkedInCampaign.service";
import { LinkedInCampaignGroupService } from "../../../domain/services/linkedInCampaignGroup.service";
import { LinkedInPostService } from "../../../domain/services/linkedInPost.service";
import { LinkedInSponsoredCreativeService } from "../../../domain/services/linkedInSponsoredCreative.service";
import { OrganizationConversionService } from "../../../domain/services/organizationConversion.service";
import { SocialPostAdCopyService } from "../../../domain/services/socialPostAdCopy.service";
import { AdSegmentBestVariantRepository } from "../../../infrastructure/repositories/adSegmentBestVariant.repository";
import { AdSegmentValuePropCreativeRepository } from "../../../infrastructure/repositories/adSegmentValuePropCreative.repository";
import { SocialPostCallToActionCopyRepository } from "../../../infrastructure/repositories/socialPostCallToActionCopy.repository";
import { advertisingInngestClient } from "../../../utils/advertisingInngestClient";
import { CreateNewVariantsDto } from "../../dtos/controllerDtos/adSegment/createNewVariants.dto";
import { IMidCampaignNotificationRepository } from "../../interfaces/infrastructure/repositories/midCampaignNotification.repository.interface";
import { IAdCreativeStorageService } from "../../interfaces/infrastructure/services/adCreativeStorage.service.interface";
import { ILinkedInService } from "../../interfaces/infrastructure/services/thirdPartyApis/linkedInApi/linkedIn.service.interface";

const stageEventBodySchema = z.object({
  adSegmentId: z.string(),
  organizationId: z.number(),
  runStageAfterApproval: z.boolean().optional().nullable(),
  stageTypeToRun: abTestTypesSchema.nullable().optional(),
});

type StageEventBody = z.infer<typeof stageEventBodySchema>;
export class CreateNewVariantUseCase {
  constructor(
    private readonly ctx: {
      linkedInAdProgramService: LinkedInAdProgramService;
      adSegmentService: AdSegmentService;
      linkedInService: ILinkedInService;
      adAccountService: LinkedInAdAccountService;
      segmentService: SegmentService;
      organizationId: number;
      linkedInCampaignGroupService: LinkedInCampaignGroupService;
      adAudienceService: AdAudienceService;
      linkedInCampaignService: LinkedInCampaignService;
      linkedInPostService: LinkedInPostService;
      adSegmentValuePropService: AdSegmentValuePropService;
      socialPostCopySerice: SocialPostAdCopyService;
      adProgramCreativeService: LinkedInAdProgramAdCreativeService;
      adCreativeService: AdCreativeService;
      adCreativeStorageService: IAdCreativeStorageService;
      linkedInSponsoredCreativeService: LinkedInSponsoredCreativeService;
      organizationConversionService: OrganizationConversionService;
      adSegmentValuePropCreativeRepository: AdSegmentValuePropCreativeRepository;
      socialPostCallToActionCopyRepository: SocialPostCallToActionCopyRepository;
      adSegmentBestVariantRepository: AdSegmentBestVariantRepository;
      conversationSubjectCopyService: ConversationSubjectCopyService;
      conversationMessageCopyService: ConversationMessageCopyService;
      conversationCallToActionCopyService: ConversationCallToActionCopyService;
      midCampaignNotificationRepository: IMidCampaignNotificationRepository;
    },
  ) {}

  async execute(dto: CreateNewVariantsDto, tx: Transaction) {
    const adSegment = await this.ctx.adSegmentService.getOne(
      dto.adSegmentId,
      tx,
    );
    if (!adSegment) {
      throw new Error("Ad segment not found");
    }
    const adProgram = await this.ctx.linkedInAdProgramService.getOne(
      adSegment.linkedInAdProgramId,
    );
    if (!adProgram) {
      throw new Error("Ad program not found");
    }
    const adAccount = await this.ctx.adAccountService.getOneById(
      adProgram.linkedInAdAccountId,
    );
    if (!adAccount) {
      throw new Error("Ad account not found");
    }
    const audiences = await this.ctx.adAudienceService.getManyForAdSegment(
      adSegment.id,
      tx,
    );
    if (dto.adFormatType == "SPONSORED_CONTENT") {
      const leadGenFormUrn = (
        await this.ctx.socialPostCopySerice.getOne({
          valuePropId: dto.data.data.valuePropId,
          socialPostCopyType: "standard",
          status: "ACTIVE",
        })
      )?.leadGenFormUrn;
      if (
        (!leadGenFormUrn ||
          leadGenFormUrn == undefined ||
          leadGenFormUrn == null) &&
        adProgram.objectiveType == "LEAD_GENERATION"
      ) {
        throw new Error("Lead gen form not found");
      }

      const adProgramCreatives =
        await this.ctx.adProgramCreativeService.getAllForLinkedInAdProgram(
          adProgram.id,
        );
      const valueProp = await this.ctx.adSegmentValuePropService.getOne(
        dto.data.data.valuePropId,
        "ACTIVE",
        tx,
      );
      if (!valueProp) {
        throw new Error("Value prop not found");
      }
      let dataThing: {
        socialPostBodyCopy: string;
        socialPostBodyCopyType: string;
        socialPostBodyCopyId: string;
        socialPostCtaCopy: string;
        socialPostCtaCopyType: string;
        socialPostCtaCopyId: string;
      }[] = [];
      if (dto.data.type == "socialPostBodyCopy") {
        const ctaCopiesForValueProp =
          await this.ctx.socialPostCallToActionCopyRepository.getManyForLinkedInAdSegmentValueProp(
            {
              linkedInAdSegmentValuePropId: valueProp.id,
              status: "ACTIVE",
            },
            tx,
          );

        for (const bodyCopy of dto.data.data.data) {
          const bodyCopyFromDb = await this.ctx.socialPostCopySerice.getOne(
            {
              valuePropId: valueProp.id,
              socialPostCopyType: bodyCopy.socialPostBodyCopyType,
              status: "DRAFT",
            },
            tx,
          );
          if (!bodyCopyFromDb) {
            throw new Error("Body copy not found");
          }
          for (const ctaCopy of ctaCopiesForValueProp) {
            dataThing.push({
              socialPostBodyCopyId: bodyCopyFromDb.id,
              socialPostBodyCopyType: bodyCopyFromDb.socialPostCopyType,
              socialPostBodyCopy: bodyCopyFromDb.body,
              socialPostCtaCopyId: ctaCopy.id,
              socialPostCtaCopyType: ctaCopy.type,
              socialPostCtaCopy: ctaCopy.callToAction,
            });
          }
        }
      } else if ((dto.data.type = "socialPostCtaCopy")) {
        const socialPostCopyBodies =
          await this.ctx.socialPostCopySerice.getAllForAdSegmentValueProp(
            valueProp.id,
            "ACTIVE",
            tx,
          );

        for (const ctaCopy of dto.data.data.data) {
          const ctaCopyFromDb =
            await this.ctx.socialPostCallToActionCopyRepository.getOne(
              {
                valuePropId: valueProp.id,
                socialPostCopyType: ctaCopy.ctaType,
                status: "DRAFT",
              },
              tx,
            );
          if (!ctaCopyFromDb) {
            throw new Error("Cta copy not found");
          }
          for (const bodyCopy of socialPostCopyBodies) {
            dataThing.push({
              socialPostBodyCopyType: bodyCopy.socialPostCopyType,
              socialPostBodyCopyId: bodyCopy.id,
              socialPostBodyCopy: bodyCopy.body,
              socialPostCtaCopyId: ctaCopyFromDb.id,
              socialPostCtaCopyType: ctaCopyFromDb.type,
              socialPostCtaCopy: ctaCopyFromDb.callToAction,
            });
          }
        }
      }
      const valuePropCreatives =
        await this.ctx.adSegmentValuePropCreativeRepository.getAllForValueProp(
          valueProp.id,
          tx,
        );
      for (const eachAdProgramCreativeId of valuePropCreatives.map(
        (valuePropCreative) => valuePropCreative.adProgramCreativeId,
      )) {
        for (const eachDataThing of dataThing) {
          if (adProgram.adFormat.format == "SINGLE_IMAGE") {
            await this.ctx.linkedInPostService.createOne(
              {
                content: {
                  type: "SINGLE_IMAGE",
                  adCreativeId: eachAdProgramCreativeId,
                  linkedInAdSegmentValuePropId: valueProp.id,
                  socialPostCopyType: eachDataThing.socialPostBodyCopyType,
                  socialPostCallToActionType:
                    eachDataThing.socialPostCtaCopyType,
                },
              },
              tx,
            );
          } else if (adProgram.adFormat.format == "VIDEO") {
            await this.ctx.linkedInPostService.createOne(
              {
                content: {
                  type: "SINGLE_VIDEO",
                  adCreativeId: eachAdProgramCreativeId,
                  linkedInAdSegmentValuePropId: valueProp.id,
                  socialPostCopyType: eachDataThing.socialPostBodyCopyType,
                  socialPostCallToActionType:
                    eachDataThing.socialPostCtaCopyType,
                },
              },
              tx,
            );
          } else if (adProgram.adFormat.format == "DOCUMENT") {
            await this.ctx.linkedInPostService.createOne(
              {
                content: {
                  type: "DOCUMENT",
                  adCreativeId: eachAdProgramCreativeId,
                  linkedInAdSegmentValuePropId: valueProp.id,
                  socialPostCopyType: eachDataThing.socialPostBodyCopyType,
                  socialPostCallToActionType:
                    eachDataThing.socialPostCtaCopyType,
                },
              },
              tx,
            );
          }
        }
        for (const eachAudience of audiences.filter(
          (eachAudience) => eachAudience.toBeUsed,
        )) {
          const adAudience = await this.ctx.adAudienceService.getOne(
            eachAudience.id,
          );
          if (!adAudience) {
            throw new Error("Audience not found");
          }
          const facetNames: string[] = [];
          for (const orGroup of adAudience.audienceTargetCriteria.include.and) {
            for (const eachFacet of orGroup.or) {
              facetNames.push(eachFacet.facetName);
            }
          }
          const campaignName = `${facetNames.join(", ")}`;

          const campaign = await this.ctx.linkedInCampaignService.getOneById(
            eachAudience.id,
          );
          if (!campaign) {
            continue;
          }
          const postThing: {
            postId: string;
            body: string;
            headline: string;
            valuePropName: string;
            adCreativeId: string;
            leadGenFormUrn: string | null;
            bodyCopyType: string;
            ctaCopyType: string;
          }[] = [];
          for (const eachAdProgramCreativeId of valuePropCreatives.map(
            (valuePropCreative) => valuePropCreative.adProgramCreativeId,
          )) {
            const adCreative = adProgramCreatives.find(
              (adProgramCreative) =>
                adProgramCreative.id == eachAdProgramCreativeId,
            );
            if (!adCreative) {
              throw new Error("Ad creative not found");
            }
            for (const eachDataThing of dataThing) {
              if (adProgram.adFormat.format == "SINGLE_IMAGE") {
                const post =
                  await this.ctx.linkedInPostService.getSingleImagePostByContent(
                    {
                      socialPostCopyType: eachDataThing.socialPostBodyCopyType,
                      socialPostCallToActionType:
                        eachDataThing.socialPostCtaCopyType,
                      linkedInAdSegmentValuePropId: valueProp.id,
                      adProgramCreativeId: eachAdProgramCreativeId,
                    },
                    tx,
                  );
                if (!post) {
                  throw new Error("Post not found");
                }

                postThing.push({
                  postId: post.id,
                  body: eachDataThing.socialPostBodyCopy,
                  headline: eachDataThing.socialPostCtaCopy,
                  valuePropName: valueProp.valueProp,
                  adCreativeId: adCreative.adCreativeId,
                  leadGenFormUrn: leadGenFormUrn ?? null,
                  bodyCopyType: eachDataThing.socialPostBodyCopyType,
                  ctaCopyType: eachDataThing.socialPostCtaCopyType,
                });
              } else if (adProgram.adFormat.format == "VIDEO") {
                const post =
                  await this.ctx.linkedInPostService.getSingleVideoPostByContent(
                    {
                      socialPostCopyType: eachDataThing.socialPostBodyCopyType,
                      socialPostCallToActionType:
                        eachDataThing.socialPostCtaCopyType,
                      linkedInAdSegmentValuePropId: valueProp.id,
                      adProgramCreativeId: adCreative.adCreativeId,
                    },
                    tx,
                  );
                if (!post) {
                  throw new Error("Post not found");
                }

                postThing.push({
                  postId: post.id,
                  body: eachDataThing.socialPostBodyCopy,
                  headline: eachDataThing.socialPostCtaCopy,
                  valuePropName: valueProp.valueProp,
                  adCreativeId: adCreative.adCreativeId,
                  leadGenFormUrn: leadGenFormUrn ?? null,
                  bodyCopyType: eachDataThing.socialPostBodyCopyType,
                  ctaCopyType: eachDataThing.socialPostCtaCopyType,
                });
              } else if (adProgram.adFormat.format == "DOCUMENT") {
                const post =
                  await this.ctx.linkedInPostService.getSingleDocumentPostByContent(
                    {
                      socialPostCopyType: eachDataThing.socialPostBodyCopyType,
                      socialPostCallToActionType:
                        eachDataThing.socialPostCtaCopyType,
                      linkedInAdSegmentValuePropId: valueProp.id,
                      adProgramCreativeId: eachAdProgramCreativeId,
                    },
                    tx,
                  );
                if (!post) {
                  throw new Error("Post not found");
                }

                postThing.push({
                  postId: post.id,
                  body: eachDataThing.socialPostBodyCopy,
                  headline: eachDataThing.socialPostCtaCopy,
                  valuePropName: valueProp.valueProp,
                  adCreativeId: adCreative.adCreativeId,
                  leadGenFormUrn: leadGenFormUrn ?? null,
                  bodyCopyType: eachDataThing.socialPostBodyCopyType,
                  ctaCopyType: eachDataThing.socialPostCtaCopyType,
                });
              }
            }
          }
          for (const post of postThing) {
            const adCreative = await this.ctx.adCreativeService.getOneById(
              post.adCreativeId,
            );
            if (!adCreative) {
              throw "Ad creative not found";
            }
            const adCreativeMetadata =
              await this.ctx.adCreativeService.getOneWithDownloadPreSignedUrlAndMetadata(
                post.adCreativeId,
              );
            if (!adCreativeMetadata?.metadata.size) {
              throw "Cannot get creative file size";
            }
            let imageUrn = null;
            let retryCount = 0;
            const maxRetries = 3;

            while (imageUrn === null && retryCount < maxRetries) {
              if (retryCount > 0) {
                // Wait for 20 seconds before retrying
                await new Promise((resolve) => setTimeout(resolve, 20000));
                console.log(
                  `Retrying LinkedIn upload (attempt ${retryCount + 1}/${maxRetries})...`,
                );
              }
              const adCreativeFile =
                await this.ctx.adCreativeService.getAdCreativeFile(
                  post.adCreativeId,
                );
              if (adCreativeFile == null) {
                throw "Cannot get creative file";
              }

              imageUrn =
                adProgram.adFormat.format == "DOCUMENT"
                  ? await this.ctx.linkedInService.uploadDocument({
                      linkedInOrganizationUrn:
                        adAccount.linkedInOrganizationUrn,
                      body: adCreativeFile,
                      type: "document",
                      fileSizeBytes: adCreativeMetadata.metadata.size,
                    })
                  : await this.ctx.linkedInService.uploadImage({
                      linkedInOrganizationUrn:
                        adAccount.linkedInOrganizationUrn,
                      body: adCreativeFile,
                      type:
                        adProgram.adFormat.format == "VIDEO"
                          ? "video"
                          : "image",
                      fileSizeBytes: adCreativeMetadata.metadata.size,
                    });

              retryCount++;
            }

            if (imageUrn === null) {
              throw new Error(
                "Failed to upload creative to LinkedIn after multiple attempts",
              );
            }

            const destinationUrl =
              adProgram.objectiveType != "LEAD_GENERATION"
                ? adSegment.destinationUrl
                : undefined;

            const creative =
              await this.ctx.linkedInService.createInlineCreative({
                adAccountUrn: adAccount.linkedInAdAccountUrn,
                campaignUrn: campaign.linkedInCampaignUrn,
                imageUrn: imageUrn,
                commentary: post.body,
                headline: post.headline,
                linkedInOrgId: adAccount.linkedInOrganizationUrn,
                destinationUrl:
                  destinationUrl === null ? undefined : destinationUrl,
                adFormUrn:
                  adProgram.objectiveType == "LEAD_GENERATION" &&
                  post.leadGenFormUrn
                    ? post.leadGenFormUrn
                    : undefined,
                adName: `${campaignName} - ${post.valuePropName} - ${post.bodyCopyType} - ${post.ctaCopyType}`,
              });

            await this.ctx.linkedInSponsoredCreativeService.createOne(
              {
                cmapaignId: campaign.linkedInAudienceId,
                linkedInSponseredCreativeUrn: creative,
                status: "PAUSED",
                content: {
                  type: "SPONSORED_CONTENT",
                  postId: post.postId,
                },
              },
              tx,
            );
          }
        }
      }
      if (dto.data.type == "socialPostBodyCopy") {
        await this.ctx.socialPostCopySerice.updateManyToActive(
          dataThing.map((each) => each.socialPostBodyCopyId),
          tx,
        );

        const stageEventBody: StageEventBody = {
          adSegmentId: adSegment.id,
          organizationId: this.ctx.organizationId,
          runStageAfterApproval: true,
          stageTypeToRun: "socialPostBodyCopy",
        };

        await advertisingInngestClient.send({
          name: "linkedin/poll-ad-status",
          data: stageEventBody,
        });
      } else if (dto.data.type == "socialPostCtaCopy") {
        await this.ctx.socialPostCallToActionCopyRepository.updateManyToActive(
          dataThing.map((each) => each.socialPostCtaCopyId),
          tx,
        );

        const stageEventBody: StageEventBody = {
          adSegmentId: adSegment.id,
          organizationId: this.ctx.organizationId,
          runStageAfterApproval: true,
          stageTypeToRun: "socialPostCallToAction",
        };

        await advertisingInngestClient.send({
          name: "linkedin/poll-ad-status",
          data: stageEventBody,
        });
      }
    } else if (dto.adFormatType == "SPONSORED_INMAIL") {
      let dataThing: {
        valuePropId: string;
        subjectCopyType: string;
        subjectCopyId: string;
        subjectCopy: string;
        messageCopyType: string;
        messageCopyId: string;
        messageCopy: string;
        ctaCopyType: string;
        ctaCopyId: string;
        ctaCopy: string;
        leadGenFormUrn: string;
      }[] = [];
      const sender = adSegment.sender;
      if (!sender) {
        throw new Error("Sender not found");
      }
      if (dto.data.type == "valueProp") {
        for (const variant of dto.data.data.data) {
          const valueProp = await this.ctx.adSegmentValuePropService.getOne(
            variant.valuePropId,
            "DRAFT",
          );
          if (!valueProp) {
            throw new Error("Value prop not found");
          }

          const conversationSubject =
            await this.ctx.conversationSubjectCopyService.getOne(
              {
                valuePropId: valueProp.id,
                conversationCopyType: dto.data.data.subjectType,
                status: "DRAFT",
              },
              tx,
            );
          if (!conversationSubject) {
            throw new Error("Conversation subject not found");
          }

          const conversationMessage =
            await this.ctx.conversationMessageCopyService.getOne(
              {
                valuePropId: valueProp.id,
                messageType: variant.messageCopyType,
                subjectType: conversationSubject.type,
                status: "DRAFT",
              },
              tx,
            );
          if (!conversationMessage) {
            throw new Error("Conversation message not found");
          }

          const conversationCallToAction =
            await this.ctx.conversationCallToActionCopyService.getOne(
              {
                valuePropId: valueProp.id,
                conversationMessageCopyType: conversationMessage.type,
                conversationSubjectCopyType: conversationSubject.type,
                conversationCallToActionCopyType: variant.ctaType,
                status: "DRAFT",
              },
              tx,
            );

          if (!conversationCallToAction) {
            throw new Error("Conversation call to action not found");
          }

          const leadGenFormUrn = conversationSubject.leadGenForm;
          if (!leadGenFormUrn) {
            throw new Error("Lead gen form not found");
          }

          dataThing.push({
            valuePropId: valueProp.id,
            subjectCopyType: conversationSubject.type,
            subjectCopy: conversationSubject.content,
            messageCopyType: conversationMessage.type,
            messageCopy: conversationMessage.content,
            ctaCopyType: conversationCallToAction.type,
            ctaCopy: conversationCallToAction.content,
            leadGenFormUrn: leadGenFormUrn,
            subjectCopyId: conversationSubject.id,
            messageCopyId: conversationMessage.id,
            ctaCopyId: conversationCallToAction.id,
          });
        }
      } else {
        const valueProp = await this.ctx.adSegmentValuePropService.getOne(
          dto.data.data.valuePropId,
          "ACTIVE",
        );
        if (!valueProp) {
          throw new Error("Value prop not found");
        }
        const subjectCopy =
          await this.ctx.conversationSubjectCopyService.getOne(
            {
              valuePropId: valueProp.id,
              conversationCopyType: dto.data.data.subjectType,
              status: "ACTIVE",
            },
            tx,
          );
        if (!subjectCopy) {
          throw new Error("Subject copy not found");
        }
        const leadGenFormUrn = subjectCopy.leadGenForm;
        if (!leadGenFormUrn) {
          throw new Error("Lead gen form not found");
        }
        if (dto.data.type == "messageCopy") {
          for (const variant of dto.data.data.data) {
            const conversationMessage =
              await this.ctx.conversationMessageCopyService.getOne(
                {
                  valuePropId: valueProp.id,
                  messageType: variant.messageCopyType,
                  subjectType: subjectCopy.type,
                  status: "DRAFT",
                },
                tx,
              );
            if (!conversationMessage) {
              throw new Error("Conversation message not found");
            }
            const conversationCallToAction =
              await this.ctx.conversationCallToActionCopyService.getOne(
                {
                  valuePropId: valueProp.id,
                  conversationMessageCopyType: conversationMessage.type,
                  conversationSubjectCopyType: subjectCopy.type,
                  conversationCallToActionCopyType: variant.ctaType,
                  status: "DRAFT",
                },
                tx,
              );
            if (!conversationCallToAction) {
              throw new Error("Conversation call to action not found");
            }
            dataThing.push({
              valuePropId: valueProp.id,
              subjectCopyType: subjectCopy.type,
              messageCopyType: conversationMessage.type,
              ctaCopyType: conversationCallToAction.type,
              subjectCopyId: subjectCopy.id,
              messageCopyId: conversationMessage.id,
              ctaCopyId: conversationCallToAction.id,
              leadGenFormUrn: leadGenFormUrn,
              subjectCopy: subjectCopy.content,
              messageCopy: conversationMessage.content,
              ctaCopy: conversationCallToAction.content,
            });
          }
        } else if (dto.data.type == "ctaCopy") {
          const messageCopy =
            await this.ctx.conversationMessageCopyService.getOne(
              {
                valuePropId: valueProp.id,
                messageType: dto.data.data.messageCopyType,
                subjectType: subjectCopy.type,
                status: "ACTIVE",
              },
              tx,
            );
          if (!messageCopy) {
            throw new Error("Message copy not found");
          }

          for (const variant of dto.data.data.data) {
            const conversationCallToAction =
              await this.ctx.conversationCallToActionCopyService.getOne(
                {
                  valuePropId: valueProp.id,
                  conversationMessageCopyType: messageCopy.type,
                  conversationSubjectCopyType: subjectCopy.type,
                  conversationCallToActionCopyType: variant.ctaType,
                  status: "DRAFT",
                },
                tx,
              );
            if (!conversationCallToAction) {
              throw new Error("Conversation call to action not found");
            }
            dataThing.push({
              valuePropId: valueProp.id,
              subjectCopyType: subjectCopy.type,
              messageCopyType: messageCopy.type,
              ctaCopyType: conversationCallToAction.type,
              subjectCopyId: subjectCopy.id,
              messageCopyId: messageCopy.id,
              ctaCopyId: conversationCallToAction.id,
              leadGenFormUrn: leadGenFormUrn,
              subjectCopy: subjectCopy.content,
              messageCopy: messageCopy.content,
              ctaCopy: conversationCallToAction.content,
            });
          }
        }
      }
      for (const audience of audiences) {
        const campaign = await this.ctx.linkedInCampaignService.getOneById(
          audience.id,
          tx,
        );
        if (!campaign) {
          throw new Error("Campaign not found");
        }
        for (const eachVariant of dataThing) {
          const conversationUrn =
            await this.ctx.linkedInService.createLeadGenConversation({
              adAccountUrn: adAccount.linkedInAdAccountUrn,
              leadGenFormUrn: eachVariant.leadGenFormUrn,
              senderUrn: sender,
              campaignUrn: campaign.linkedInCampaignUrn,
              body: eachVariant.messageCopy,
              subject: eachVariant.subjectCopy,
              leadGenButtonText: eachVariant.ctaCopy,
            });

          await this.ctx.linkedInService.updateSponsoredCreativeStatus({
            adAccountUrn: adAccount.linkedInAdAccountUrn,
            adUrn: conversationUrn,
            status: "ACTIVE",
          });

          await this.ctx.linkedInSponsoredCreativeService.createOne(
            {
              cmapaignId: audience.id,
              linkedInSponseredCreativeUrn: conversationUrn,
              status: "ACTIVE",
              content: {
                type: "SPONSORED_INMAIL",
                conversationCallToActionId: eachVariant.ctaCopyId,
              },
            },
            tx,
          );
        }
      }
      if (dto.data.type == "valueProp") {
        await this.ctx.adSegmentValuePropService.updateManyToActive(
          dataThing.map((each) => each.valuePropId),
          tx,
        );

        await this.ctx.conversationSubjectCopyService.updateManyToActive(
          {
            ids: dataThing.map((each) => each.subjectCopyId),
          },
          tx,
        );
      }
      if (dto.data.type == "valueProp" || dto.data.type == "messageCopy") {
        await this.ctx.conversationMessageCopyService.updateManyToActive(
          dataThing.map((each) => each.messageCopyId),
          tx,
        );
      }
      if (
        dto.data.type == "valueProp" ||
        dto.data.type == "messageCopy" ||
        dto.data.type == "ctaCopy"
      ) {
        await this.ctx.conversationCallToActionCopyService.updateManyToActive(
          {
            ids: dataThing.map((each) => each.ctaCopyId),
          },
          tx,
        );
      }

      const stageEventBody: StageEventBody = {
        adSegmentId: adSegment.id,
        organizationId: this.ctx.organizationId,
        runStageAfterApproval: true,
        stageTypeToRun:
          dto.data.type == "valueProp"
            ? "valueProp"
            : dto.data.type == "messageCopy"
              ? "conversationMessageCopy"
              : dto.data.type == "ctaCopy"
                ? "conversationCallToAction"
                : null,
      };

      await advertisingInngestClient.send({
        name: "linkedin/poll-ad-status",
        data: stageEventBody,
      });
    }
    await this.ctx.midCampaignNotificationRepository.deleteOne(
      adSegment.id,
      tx,
    );
  }
}
