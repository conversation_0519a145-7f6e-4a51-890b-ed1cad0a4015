import { LinkedInUserService } from "../../../domain/services/linkedInUser.service";
import { GetLinkedInUserResponseDto } from "../../dtos/controllerDtos/linkerInUserDtos/getLinkedInUser.dto";

export async function upsertLinkedInUserUseCase(ctx: {
  linkedInUserService: LinkedInUserService;
  userId: string;
}): Promise<GetLinkedInUserResponseDto> {
  const linkedInUser = await ctx.linkedInUserService.getLinkedInUser(
    ctx.userId,
  );
  if (!linkedInUser) {
    return null;
  }
  return {
    userId: linkedInUser.userId,
    linkedInAdAccountIds: linkedInUser.linkedInAdAccounts.map(
      (adAccount) => adAccount,
    ),
  };
}
