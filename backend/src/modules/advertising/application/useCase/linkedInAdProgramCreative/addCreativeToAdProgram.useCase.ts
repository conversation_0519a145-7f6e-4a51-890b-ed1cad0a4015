import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { createUuid } from "../../../../core/utils/uuid";
import { LinkedInAdProgramRepository } from "../../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInAdProgramCreativeRepository } from "../../../infrastructure/repositories/linkedInAdProgramCreative.repository";
import { AddCreativeToAdProgramRequestDto } from "../../dtos/controllerDtos/linkedInAdProgramCreative/addCreativeToAdProgram.dto";
import { IAdSegmentValuePropRepository } from "../../interfaces/infrastructure/repositories/adSegmentValueProp.repository.interface";
import { IAdSegmentValuePropCreativeRepository } from "../../interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";
import { LinkedInAdProgramRepositoryInterface } from "../../interfaces/infrastructure/repositories/linkedInAdProgram.repository.interface";
import { ILinkedInAdProgramCreativeRepository } from "../../interfaces/infrastructure/repositories/linkedInAdProgramCreative.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";

export class AddCreativeToAdProgramUseCase {
  constructor(
    private readonly linkedInAdProgramCreativeRepository: ILinkedInAdProgramCreativeRepository,
    private readonly linkedInAdProgramRepository: LinkedInAdProgramRepositoryInterface,
    private readonly linkedInAdSegmentRepository: ILinkedInAdSegmentRepository,
    private readonly linkedInAdSegmentValuePropRepository: IAdSegmentValuePropRepository,
    private readonly linkedInAdSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository,
    private readonly organizationId: number,
  ) {}

  async execute(
    request: AddCreativeToAdProgramRequestDto,
    tx?: ITransaction,
  ): Promise<void> {
    const adProgramBelongsToOrganization =
      await this.linkedInAdProgramRepository.checkIfAdProgramBelongsToOrganization(
        request.adProgramId,
        this.organizationId,
        tx,
      );

    if (!adProgramBelongsToOrganization) {
      throw new Error("Ad program does not belong to organization");
    }

    const linkedInAdProgramCreative =
      await this.linkedInAdProgramCreativeRepository.createOne(
        {
          id: createUuid(),
          linkedInAdProgramId: request.adProgramId,
          adCreativeId: request.adCreativeId,
        },
        tx,
      );

    const adSegmentValueProps =
      await this.linkedInAdSegmentValuePropRepository.getManyForAdProgram(
        request.adProgramId,
        "DRAFT",
        tx,
      );

    await this.linkedInAdSegmentValuePropCreativeRepository.createMany(
      adSegmentValueProps.map((adSegmentValueProp) => ({
        id: createUuid(),
        adSegmentValuePropId: adSegmentValueProp.id,
        adProgramCreativeId: linkedInAdProgramCreative.id,
      })),
      tx,
    );
  }
}
