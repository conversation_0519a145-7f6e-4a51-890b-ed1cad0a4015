import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { DeleteCreativeFromAdProgramRequestDto } from "../../dtos/controllerDtos/linkedInAdProgramCreative/deleteCreativeFromAdProgram.dto";
import { IAdSegmentValuePropCreativeRepository } from "../../interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";
import { LinkedInAdProgramRepositoryInterface } from "../../interfaces/infrastructure/repositories/linkedInAdProgram.repository.interface";
import { ILinkedInAdProgramCreativeRepository } from "../../interfaces/infrastructure/repositories/linkedInAdProgramCreative.repository.interface";

export class DeleteCreativeFromAdProgramUseCase {
  constructor(
    private readonly linkedInAdProgramCreativeRepository: ILinkedInAdProgramCreativeRepository,
    private readonly linkedInAdSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository,
    private readonly linkedInAdProgramRepository: LinkedInAdProgramRepositoryInterface,
    private readonly organizationId: number,
  ) {}

  async execute(
    request: DeleteCreativeFromAdProgramRequestDto,
    tx?: ITransaction,
  ) {
    const adProgramCreative =
      await this.linkedInAdProgramCreativeRepository.getOne(
        request.adProgramCreativeId,
        tx,
      );

    if (!adProgramCreative) {
      throw new Error("Ad program creative not found");
    }

    const adProgramBelongsToOrganization =
      await this.linkedInAdProgramRepository.checkIfAdProgramBelongsToOrganization(
        adProgramCreative.linkedInAdProgramId,
        this.organizationId,
        tx,
      );

    if (!adProgramBelongsToOrganization) {
      throw new Error("Ad program does not belong to organization");
    }

    await this.linkedInAdSegmentValuePropCreativeRepository.deleteAllForAdProgramCreativeIds(
      [adProgramCreative.id],
      tx,
    );

    await this.linkedInAdProgramCreativeRepository.deleteOne(
      adProgramCreative.id,
      tx,
    );
  }
}
