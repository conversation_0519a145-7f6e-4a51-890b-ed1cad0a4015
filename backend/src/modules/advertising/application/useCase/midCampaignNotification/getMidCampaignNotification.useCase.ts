import { GetMidCampaignNotificationRequestDto } from "../../dtos/controllerDtos/midCampaignNotification/getMidCampaignNotification.dto";
import { IMidCampaignNotificationRepository } from "../../interfaces/infrastructure/repositories/midCampaignNotification.repository.interface";

export class GetMidCampaignNotificationUseCase {
  constructor(
    private readonly midCampaignNotificationRepository: IMidCampaignNotificationRepository,
  ) {}

  async execute(
    requestDto: GetMidCampaignNotificationRequestDto,
  ): Promise<void> {
    const midCampaignNotification =
      await this.midCampaignNotificationRepository.getForAdSegment(
        requestDto.adSegmentId,
      );
  }
}
