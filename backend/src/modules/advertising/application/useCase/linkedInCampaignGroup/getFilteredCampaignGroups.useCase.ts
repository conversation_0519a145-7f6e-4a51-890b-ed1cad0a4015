import { LinkedInCampaignGroupService } from "../../../domain/services/linkedInCampaignGroup.service";
import { LinkedInService } from "../../../infrastructure/services/linkedIn.service";
import { GetCampaignGroupBatchAnalyticsDto } from "../../dtos/controllerDtos/linkedInCampaignGroup/linkedinCampaignGroup.dto";
import { ILinkedInAdAccountRepository } from "../../interfaces/infrastructure/repositories/linkedInAdAccount.repository.interface";
import { ILinkedInCampaignGroupRepository } from "../../interfaces/infrastructure/repositories/linkedInCampaignGroup.repository.interface";

export type SegmentAnalytics = {
  name: string;
  totalEngagements: number;
  oneClickLeads: number;
  costInUsd: number;
};

export class GetFilteredCampaignGroups {
  constructor(
    private readonly ctx: {
      linkedInCampaignGroupService: LinkedInCampaignGroupService;
    },
  ) {}

  async execute(params: {
    adAccountId: string;
    status?: (
      | "ACTIVE"
      | "PAUSED"
      | "ARCHIVED"
      | "CANCELLED"
      | "DRAFT"
      | "PENDING_DELETION"
      | "REMOVED"
    )[];
  }) {
    return await this.ctx.linkedInCampaignGroupService.getAllForLinkedInAccountIdWithAdProgram(
      params.adAccountId,
      params.status,
    );
  }
}
