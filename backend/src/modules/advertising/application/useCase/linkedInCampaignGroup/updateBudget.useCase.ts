import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

import { UpdateBudgetRequestDto } from "../../dtos/controllerDtos/linkedInCampaignGroup/updateBudget.dto";
import { ILinkedInCampaignGroupRepository } from "../../interfaces/infrastructure/repositories/linkedInCampaignGroup.repository.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { LinkedInAdAccountService } from "../../../domain/services/linkedInAdAccount.service";
import { LinkedInAdProgramService } from "../../../domain/services/linkedInAdProgram.service";
import { IAbTestRepository } from "../../interfaces/infrastructure/repositories/abTest.repository.interface";
import { LinkedInAdAudienceRepository } from "../../../infrastructure/repositories/linkedInAdAudience.repository";
import { LinkedInService } from "../../../infrastructure/services/linkedIn.service";
import { IStageRepository } from "../../interfaces/infrastructure/repositories/stage.repository.interface";

interface UpdateBudgetUseCaseDependencies {
  linkedInCampaignGroupRepository: ILinkedInCampaignGroupRepository;
  linkedInCampaignRepository: ILinkedInCampaignRepositoryInterface;
  linkedInAdAccountService: LinkedInAdAccountService;
  linkedInAdProgramService: LinkedInAdProgramService;
  abTestRepository: IAbTestRepository;
  linkedInAdAudienceRepository: LinkedInAdAudienceRepository;
  stageRepository: IStageRepository;
  organizationId: number;
}

interface BudgetUpdateContext {
  request: UpdateBudgetRequestDto;
  adProgram: any;
  allCampaigns: any[];
  adAccount: any;
  linkedInService: LinkedInService;
  hasMultipleAudiences: boolean;
  winningCampaignId: string | null;
}

// Main use case function
export const updateBudgetUseCase = async (
  dependencies: UpdateBudgetUseCaseDependencies,
  request: UpdateBudgetRequestDto
): Promise<void> => {
  // 1. Initialize context and validate data
  const context = await initializeContext(dependencies, request);
  
  // 2. Calculate daily budget based on program type and AB test status
  const dailyBudget = calculateDailyBudget(context);
  
  // 3. Update LinkedIn API first
  await updateLinkedInAPI(context, dailyBudget);
  
  // 4. Update database after LinkedIn API success
  await updateDatabase(dependencies, context);
};

// Initialize context and gather all required data
const initializeContext = async (
  dependencies: UpdateBudgetUseCaseDependencies,
  request: UpdateBudgetRequestDto
): Promise<BudgetUpdateContext> => {
  const {
    linkedInCampaignGroupRepository,
    linkedInCampaignRepository,
    linkedInAdAccountService,
    linkedInAdProgramService,
    abTestRepository,
    stageRepository,
    organizationId,
  } = dependencies;

  // Get campaign group and validate it exists
  const campaignGroup = await linkedInCampaignGroupRepository.getOneByAdSegmentId(request.adSegmentId);
  if (!campaignGroup) {
    throw new Error("Campaign group not found");
  }

  // Get ad program and validate it exists
  const adProgram = await linkedInAdProgramService.getByAdSegmentId(request.adSegmentId);
  if (!adProgram) {
    throw new Error("Ad program not found");
  }

  // Get LinkedIn API client and service
  const linkedInClient = await getLinkedInApiClientFromOrganizationId(organizationId);
  if (!linkedInClient) {
    throw new Error("LinkedIn client not found");
  }
  const linkedInService = new LinkedInService(linkedInClient);

  // Get ad account
  const adAccounts = await linkedInAdAccountService.getForOrganization(organizationId);
  const adAccount = adAccounts[0];
  if (!adAccount) {
    throw new Error("Ad account not found");
  }

  // Get all campaigns for this segment
  const allCampaigns = await linkedInCampaignRepository.getManyForAdSegment(request.adSegmentId);
  if (allCampaigns.length === 0) {
    throw new Error("No campaigns found for this ad segment");
  }

  // Check if campaign group has multiple audiences and get winner
  const { hasMultipleAudiences, winningCampaignId } = await checkMultipleAudiences(
    stageRepository,
    abTestRepository,
    request.adSegmentId,
    allCampaigns
  );

  return {
    request,
    adProgram,
    allCampaigns,
    adAccount,
    linkedInService,
    hasMultipleAudiences,
    winningCampaignId,
  };
};

// Check if campaign group has multiple audiences
const checkMultipleAudiences = async (
  stageRepository: IStageRepository,
  abTestRepository: IAbTestRepository,
  adSegmentId: string,
  allCampaigns: any[]
): Promise<{ hasMultipleAudiences: boolean; winningCampaignId: string | null }> => {
  try {
    // Get unique audience IDs from campaigns
    const uniqueAudienceIds = new Set(
      allCampaigns.map(campaign => campaign.linkedInAudienceId).filter(Boolean)
    );
    
    const hasMultipleAudiences = uniqueAudienceIds.size > 1;
    
    if (hasMultipleAudiences) {
      // Get the current or last stage
      let stage = await stageRepository.getCurrentRunningStage(adSegmentId);
      if (!stage) {
        stage = await stageRepository.getLastRanStage(adSegmentId);
      }

      // Check if stage is audience test and if there's a completed A/B test with winner
      if (stage?.stageType === "audienceTest") {
        const audienceAbTest = await abTestRepository.getOne(stage.id, "audience");
        
        if (audienceAbTest?.status === "COMPLETED" && "winnerId" in audienceAbTest) {
          // Find winning campaign
          const winningCampaign = allCampaigns.find(
            campaign => campaign.linkedInAudienceId === audienceAbTest.winnerId
          );
          
          return {
            hasMultipleAudiences: true,
            winningCampaignId: winningCampaign?.linkedInAudienceId || null,
          };
        }
      }
      
      return { hasMultipleAudiences: true, winningCampaignId: null };
    }
    
    return { hasMultipleAudiences: false, winningCampaignId: null };
  } catch (error) {
    console.warn("Error checking multiple audiences:", error);
    return { hasMultipleAudiences: false, winningCampaignId: null };
  }
};

// Calculate daily budget based on program type and number of audiences
const calculateDailyBudget = (context: BudgetUpdateContext): number => {
  const { request, adProgram, hasMultipleAudiences } = context;
  const totalBudget = request.totalBudget;
  
  let dailyBudget: number;
  
  if (adProgram.type === "EVERGREEN") {
    // For EVERGREEN: daily budget = total budget (monthly) / 30
    dailyBudget = totalBudget / 30;
  } else {
    // For EVENT_DRIVEN: daily budget = total budget / number of days
    const startDate = new Date(adProgram.startDatetime);
    const endDate = new Date(adProgram.endDatetime ?? new Date());
    
    const durationInDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (durationInDays <= 0) {
      throw new Error("Invalid date range for EVENT_DRIVEN program");
    }
    
    dailyBudget = Math.min((totalBudget / durationInDays) * 2, totalBudget);
  }
  
  // If campaign group has multiple audiences, divide budget by 2
  if (hasMultipleAudiences) {
    dailyBudget = dailyBudget / 2;
  }
  
  if (dailyBudget <= 0) {
    throw new Error("Daily budget must be greater than 0");
  }
  
  return dailyBudget;
};

// Update LinkedIn API with new daily budgets
const updateLinkedInAPI = async (
  context: BudgetUpdateContext,
  dailyBudget: number
): Promise<void> => {
  const { linkedInService, allCampaigns, adAccount, winningCampaignId } = context;

  try {
    if (winningCampaignId) {
      // If there's a winner, give full daily budget to winner
      for (const campaign of allCampaigns) {
        const campaignDailyBudget = campaign.linkedInAudienceId === winningCampaignId ? dailyBudget : campaign.dailyBudget;
        
        await linkedInService.updateCampaignDailyBudget({
          linkedInCampaignUrn: campaign.linkedInCampaignUrn,
          linkedInAdAccountUrn: adAccount.linkedInAdAccountUrn,
          dailyBudget: Math.min(campaignDailyBudget, context.request.totalBudget),
        });
      }
    } else {
      // No winner, distribute daily budget equally among all campaigns
      for (const campaign of allCampaigns) {
        await linkedInService.updateCampaignDailyBudget({
          linkedInCampaignUrn: campaign.linkedInCampaignUrn,
          linkedInAdAccountUrn: adAccount.linkedInAdAccountUrn,
          dailyBudget: Math.min(dailyBudget, context.request.totalBudget),
        });
      }
    }
  } catch (error) {
    throw new Error(`Failed to update LinkedIn API: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Update database after LinkedIn API success
const updateDatabase = async (
  dependencies: UpdateBudgetUseCaseDependencies,
  context: BudgetUpdateContext
): Promise<void> => {
  const { request, allCampaigns, hasMultipleAudiences, winningCampaignId } = context;
  const totalBudget = request.totalBudget;

  const {
    linkedInCampaignGroupRepository,
    linkedInCampaignRepository,
  } = dependencies;

  try {
    // 1. Update campaign group with total budget
    await linkedInCampaignGroupRepository.updateBudgetByAdSegmentId(
      request.adSegmentId,
      totalBudget
    );

    // 2. Update individual campaigns with total budget
    const campaignTotalBudget = hasMultipleAudiences ? totalBudget / 2 : totalBudget;
    
    if (winningCampaignId) {
      // If there's a winner, give full total budget to winner, 0 to others
      for (const campaign of allCampaigns) {
        const campaignBudget = campaign.linkedInAudienceId === winningCampaignId ? campaignTotalBudget : campaign.dailyBudget;
        
        await linkedInCampaignRepository.updateBudgetByUrn(
          campaign.linkedInCampaignUrn,
          campaignBudget
        );
      }
    } else {
      // No winner, distribute total budget equally among all campaigns
      for (const campaign of allCampaigns) {
        await linkedInCampaignRepository.updateBudgetByUrn(
          campaign.linkedInCampaignUrn,
          campaignTotalBudget
        );
      }
    }
  } catch (error) {
    throw new Error(`LinkedIn API updated successfully, but database update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};