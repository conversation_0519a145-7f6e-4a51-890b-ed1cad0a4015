import { LinkedInService } from "../../../infrastructure/services/linkedIn.service";
import { GetCampaignGroupBatchAnalyticsDto } from "../../dtos/controllerDtos/linkedInCampaignGroup/linkedinCampaignGroup.dto";
import { ILinkedInAdAccountRepository } from "../../interfaces/infrastructure/repositories/linkedInAdAccount.repository.interface";
import { ILinkedInCampaignGroupRepository } from "../../interfaces/infrastructure/repositories/linkedInCampaignGroup.repository.interface";

export type SegmentAnalytics = {
  name: string;
  totalEngagements: number;
  oneClickLeads: number;
  costInUsd: number;
};

export class GetCampaignGroupBatchAnalytics {
  constructor(
    private readonly ctx: {
      linkedInAdAccountRepository: ILinkedInAdAccountRepository;
      linkedinCampaignGroupRepository: ILinkedInCampaignGroupRepository;
      linkedInService: LinkedInService;
    },
  ) {}

  async execute(dto: GetCampaignGroupBatchAnalyticsDto) {
    let campaignGroupUrns: string[] = [];

    const campaignGroupMap: Record<string, any> = {};

    if (dto.linkedInAdSegments) {
      const linkedInCampaignGroups =
        await this.ctx.linkedinCampaignGroupRepository.getManyByLinkedInAdSegmentIds(
          dto.linkedInAdSegments,
        );

      for (const linkedInCampaignGroup of linkedInCampaignGroups) {
        campaignGroupMap[linkedInCampaignGroup.linkedInCampaignGroupUrn] = {
          linkedInAdSegmentId: linkedInCampaignGroup.linkedInAdSegmentId,
          name: linkedInCampaignGroup.name,
        };
        campaignGroupUrns.push(linkedInCampaignGroup.linkedInCampaignGroupUrn);
      }
    } else {
      // TODO: Note we are not creating a map if ad segments are not passed in
      if (!dto.campaignGroupUrns) {
        throw new Error(
          "[getCampaignGroupBatchAnalytics] No Campaign Group urns nor linkedin ad segments passed in",
        );
      }
      campaignGroupUrns = [...dto.campaignGroupUrns];
    }

    const campaignGroupAnalytics =
      await this.ctx.linkedInService.getCampaignGroupAnalytics(
        campaignGroupUrns,
        dto.fromDate,
        dto.toDate,
      );

    const res: Record<string, SegmentAnalytics> = {};

    // Map campaign group urn from linkedin analytics api back to linkedInAdSegmentId
    campaignGroupAnalytics.elements.forEach((element) => {
      const campaignGroupUrn = element.pivotValues[0];
      if (!campaignGroupUrn) {
        return;
      }
      const campaignMapping = campaignGroupMap[campaignGroupUrn];

      console.log(
        `[getCampaignGroupBatchAnalytics] Campaign: ${campaignMapping.name}  Cost: ${element.costInUsd}`,
      );

      res[campaignMapping.linkedInAdSegmentId] = {
        name: campaignMapping.name,
        totalEngagements: element.totalEngagements,
        oneClickLeads: element.oneClickLeads,
        costInUsd: Number(element.costInUsd),
      };
    });
    return res;
  }
}
