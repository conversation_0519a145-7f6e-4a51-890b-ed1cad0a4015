import { LinkedInService } from "../../../infrastructure/services/linkedIn.service";
import { ILinkedInCampaignGroupRepository } from "../../interfaces/infrastructure/repositories/linkedInCampaignGroup.repository.interface";

export class GetSpendInTimeRnageUseCase {
  constructor(
    private readonly ctx: {
      linkedInCampaignGroupRepository: ILinkedInCampaignGroupRepository;
      linkedInService: LinkedInService;
    },
  ) {}

  async execute(input: {
    organizationId: number;
    timeRange: {
      startDate: Date;
      endDate: Date;
    };
  }) {
    const campaignGroups =
      await this.ctx.linkedInCampaignGroupRepository.getForOrganizationInTimeRange(
        input.organizationId,
        input.timeRange,
      );

    const analytics = await this.ctx.linkedInService.getSpendForCampaignGroups({
      campaignGroupUrns: campaignGroups.map((c) => c.linkedInCampaignGroupUrn),
      startDate: input.timeRange.startDate,
      endDate: input.timeRange.endDate,
    });
    console.log(analytics);

    const spend = analytics.reduce((acc, curr) => acc + curr.costInUsd, 0);
    return spend;
  }
}
