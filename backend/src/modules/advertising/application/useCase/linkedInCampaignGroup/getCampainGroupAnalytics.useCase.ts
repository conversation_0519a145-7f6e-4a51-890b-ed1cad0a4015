import { LinkedInService } from "../../../infrastructure/services/linkedIn.service";
import { GetOneCampaignGroupBatchAnalyticsDto } from "../../dtos/controllerDtos/linkedInCampaignGroup/linkedinCampaignGroup.dto";
import { ILinkedInCampaignGroupRepository } from "../../interfaces/infrastructure/repositories/linkedInCampaignGroup.repository.interface";

export type SegmentAnalytics = {
  name: string;
  totalEngagements: number;
  oneClickLeads: number;
  costInUsd: number;
};

export class GetOneCampaignGroupAnalytics {
  constructor(
    private readonly ctx: {
      linkedinCampaignGroupRepository: ILinkedInCampaignGroupRepository;
      linkedInService: LinkedInService;
    },
  ) {}

  async execute(dto: GetOneCampaignGroupBatchAnalyticsDto) {
    let campaignGroupUrn: string;
    let campaignGroupName: string;

    if (dto.linkedInAdSegment) {
      const linkedInCampaignGroup =
        await this.ctx.linkedinCampaignGroupRepository.getOneByAdSegmentId(
          dto.linkedInAdSegment,
        );

      if (!linkedInCampaignGroup) {
        throw new Error(
          `[getCampaignGroupAnalytics] No Campaign Group urns nor linkedin ad segments passed in`,
        );
      }

      campaignGroupUrn = linkedInCampaignGroup.linkedInCampaignGroupUrn;
      campaignGroupName = linkedInCampaignGroup.name;
    } else {
      if (!dto.campaignGroupUrn) {
        throw new Error(
          "[getCampaignGroupAnalytics] No Campaign Group urns nor linkedin ad segments passed in",
        );
      }
      const linkedInCampaignGroup =
        await this.ctx.linkedinCampaignGroupRepository.getOneByLinkedInUrn(
          dto.campaignGroupUrn,
        );

      if (!linkedInCampaignGroup) {
        throw new Error(
          `[getCampaignGroupAnalytics] No Campaign Group urns nor linkedin ad segments passed in`,
        );
      }
      campaignGroupName = linkedInCampaignGroup.name;
      campaignGroupUrn = dto.campaignGroupUrn;
    }

    const campaignGroupAnalytics =
      await this.ctx.linkedInService.getCampaignGroupAnalytics(
        [campaignGroupUrn],
        dto.fromDate,
        dto.toDate,
      );

    const res: Record<string, SegmentAnalytics> = {};

    const campaignGroupData = campaignGroupAnalytics.elements[0];

    if (!campaignGroupData) {
      return res;
    }

    return {
      name: campaignGroupName,
      totalEngagements: campaignGroupData.totalEngagements,
      oneClickLeads: campaignGroupData.oneClickLeads,
      costInUsd: Number(campaignGroupData.costInUsd),
    };
  }
}
