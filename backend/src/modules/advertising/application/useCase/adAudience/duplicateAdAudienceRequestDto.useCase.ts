import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { createUuid } from "../../../../core/utils/uuid";
import { AdAudienceService } from "../../../domain/services/adAudience.service";
import { AdSegmentService } from "../../../domain/services/adSegment.service";
import { SegmentAudienceTargetPrefabService } from "../../../domain/services/segmentAudiencePrefab.service";
import { DuplicateAdAudienceRequestDto } from "../../dtos/controllerDtos/adAudience/duplicateAdAudience.dto";
import { ILinkedInAdAudienceRepository } from "../../interfaces/infrastructure/repositories/linkedInAdAudience.repository.interface";

export class DuplicateAdAudienceRequestDtoUseCase {
  constructor(
    private readonly adAudienceRepository: ILinkedInAdAudienceRepository,
    private readonly adSegmentService: AdSegmentService,
    private readonly segmentAudiencePrefabService: SegmentAudienceTargetPrefabService,
    private readonly adAudienceService: AdAudienceService,
  ) {}

  async execute(requestDto: DuplicateAdAudienceRequestDto, tx: ITransaction) {
    const adAudience = await this.adAudienceRepository.getOne(
      requestDto.adAudienceId,
      tx,
    );

    if (!adAudience) {
      throw new Error("Ad audience not found");
    }

    const newAdAudience = await this.adAudienceRepository.createOne(
      {
        id: createUuid(),
        linkedInAdSegmentId: adAudience.linkedInAdSegmentId,
        toBeUsed: adAudience.toBeUsed,
        audiencePopulated: adAudience.audiencePopulated,
        audienceTargetCriteria: adAudience.audienceTargetCriteria,
      },
      tx,
    );

    const adSegment = await this.adSegmentService.getOne(
      requestDto.adSegmentId,
      tx,
    );

    if (!adSegment) {
      throw new Error("Ad segment not found");
    }

    const adAdueicesInSegment =
      await this.adAudienceService.getManyForAdSegment(
        requestDto.adSegmentId,
        tx,
      );

    const targets = [];
    for (const each of adAdueicesInSegment) {
      const a = await this.adAudienceService.getOne(each.id, tx);
      if (!a) {
        throw new Error("Ad audience not found");
      }
      targets.push(a.audienceTargetCriteria);
    }

    console.log("targets", targets);

    await this.segmentAudiencePrefabService.setForSegment(
      {
        segmentId: adSegment.segmentId,
        audiences: targets,
      },
      tx,
    );

    return newAdAudience;
  }
}
