import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdAudienceService } from "../../../domain/services/adAudience.service";
import { AdSegmentService } from "../../../domain/services/adSegment.service";
import { SegmentAudienceTargetPrefabService } from "../../../domain/services/segmentAudiencePrefab.service";
import { DeleteAdAudienceRequestDto } from "../../dtos/controllerDtos/adAudience/deleteAdAudience.dto";

export class DeleteAdAudienceUseCase {
  constructor(
    private readonly ctx: {
      adAudienceService: AdAudienceService;
      segmentAudiencePrefabService: SegmentAudienceTargetPrefabService;
      adSegmentService: AdSegmentService;
    },
  ) {}

  async execute(
    input: DeleteAdAudienceRequestDto,
    ctx: {
      organizationId: number;
      transaction: ITransaction;
    },
  ): Promise<void> {
    const [audience, audienceBelongsToOrganization] = await Promise.all([
      this.ctx.adAudienceService.getOne(input.adAudienceId, ctx.transaction),
      this.ctx.adAudienceService.checkIfAudienceBelongsToOrganization(
        input.adAudienceId,
        ctx.organizationId,
        ctx.transaction,
      ),
    ]);

    if (!audience) {
      throw new Error("Audience not found");
    }

    if (!audienceBelongsToOrganization) {
      throw new Error("Audience does not belong to organzation");
    }

    await this.ctx.adAudienceService.deleteMany(
      [input.adAudienceId],
      ctx.transaction,
    );

    const audiencesInSegment =
      await this.ctx.adAudienceService.getManyForAdSegment(
        audience.linkedInAdSegmentId,
        ctx.transaction,
      );

    const targets = [];

    for (const audience of audiencesInSegment) {
      const targetCriteria = await this.ctx.adAudienceService.getOne(
        audience.id,
        ctx.transaction,
      );
      if (!targetCriteria) {
        continue;
      }

      targets.push(targetCriteria.audienceTargetCriteria);
    }

    const adSegment = await this.ctx.adSegmentService.getOne(
      audience.linkedInAdSegmentId,
      ctx.transaction,
    );

    if (!adSegment) {
      throw new Error("Ad segment not found");
    }

    await this.ctx.segmentAudiencePrefabService.setForSegment(
      {
        segmentId: adSegment.segmentId,
        audiences: targets,
      },
      ctx.transaction,
    );
  }
}
