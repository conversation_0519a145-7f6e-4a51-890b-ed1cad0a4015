import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdAudienceService } from "../../../domain/services/adAudience.service";
import { AdSegmentService } from "../../../domain/services/adSegment.service";
import { LinkedInAdAccountService } from "../../../domain/services/linkedInAdAccount.service";
import { LinkedInAdProgramService } from "../../../domain/services/linkedInAdProgram.service";
import { LinkedInCampaignService } from "../../../domain/services/linkedInCampaign.service";
import { SegmentAudienceTargetPrefabService } from "../../../domain/services/segmentAudiencePrefab.service";
import { LinkedInService } from "../../../infrastructure/services/linkedIn.service";
import { UpdateAdAudienceTargetCriteriaCompleteRequestDto } from "../../dtos/controllerDtos/adAudience/updateAdAudienceTargetCriteriaComplete";

// ** Use Case to complete the update of the ad audience target criteria
// This use case completes 3 things:
// 1. Update the ad audience target criteria for current campaign
// 2. Update the ad segment target criteria for prefab
// 3. Update the campaign target criteria in linkedin using linkedin api
export class UpdateAdAudienceTargetCriteriaCompleteUseCase {
  constructor(
    private readonly adAudienceService: AdAudienceService,
    private readonly segmentAdAudienceService: SegmentAudienceTargetPrefabService,
    private readonly adSegmentService: AdSegmentService,
    private readonly linkedInAdProgramService: LinkedInAdProgramService,
    private readonly linkedInCampaignService: LinkedInCampaignService,
    private readonly linkedInAdAccountService: LinkedInAdAccountService,
    private readonly organizationId: number,
    private readonly linkedInService: LinkedInService,
  ) {}

  async execute(
    input: UpdateAdAudienceTargetCriteriaCompleteRequestDto,
    tx: ITransaction,
  ) {
    try {
      const [audience, doesAudienceBelongToOrganization] = await Promise.all([
        this.adAudienceService.getOne(input.id),
        this.adAudienceService.checkIfAudienceBelongsToOrganization(
          input.id,
          this.organizationId,
          tx,
        ),
      ]);

      if (!audience) {
        throw new Error("Audience not found");
      }

      if (!doesAudienceBelongToOrganization) {
        throw new Error("Audience does not belong to organization");
      }

      // ** Update the ad audience target criteria for current campaign
      await this.adAudienceService.updateTargetCriteria(
        input.id,
        input.targetCriteria,
        tx,
      );

      const adSegment = await this.adSegmentService.getOne(
        audience.linkedInAdSegmentId,
        tx,
      );

      if (!adSegment) {
        throw new Error("Ad segment not found");
      }

      const adProgram = await this.linkedInAdProgramService.getOne(
        adSegment.linkedInAdProgramId,
      );

      if (!adProgram) {
        throw new Error("Ad program not found");
      }

      const adAccountId = adProgram.linkedInAdAccountId;
      const adAccount =
        await this.linkedInAdAccountService.getOneById(adAccountId);

      if (!adAccount) {
        throw new Error("Ad account not found");
      }
      const adAccountUrn = adAccount?.linkedInAdAccountUrn;

      const audiences = await this.adAudienceService.getManyForAdSegment(
        audience.linkedInAdSegmentId,
        tx,
      );

      const targetCriteriaArr = [];
      for (const eachAudience of audiences) {
        const curr = await this.adAudienceService.getOne(eachAudience.id, tx);
        if (!curr) {
          throw new Error("Audience not found");
        }
        targetCriteriaArr.push(curr.audienceTargetCriteria);
      }

      // ** Update the ad segment target criteria for prefab
      await this.segmentAdAudienceService.setForSegment(
        {
          segmentId: adSegment.segmentId,
          audiences: targetCriteriaArr,
        },
        tx,
      );

      const linkedinCampaign = await this.linkedInCampaignService.getOneById(
        audience.id,
        tx,
      );

      if (!linkedinCampaign) {
        throw new Error("Linkedin campaign not found");
      }

      const linkedinCampaignUrn = linkedinCampaign.linkedInCampaignUrn;

      // ** Update the campaign target criteria in linkedin using linkedin api
      await this.linkedInService.updateCampaign({
        adAccountUrn: adAccountUrn,
        campaignUrn: linkedinCampaignUrn,
        audienceTargets: input.targetCriteria,
      });
    } catch (error) {
      tx.rollback();
      throw error;
    }
  }
}
