import { IContactRepository } from "../../../../crm/application/interfaces/infrastructure/repositories/contact.repository.interface";
import { EngagementRepository } from "../../../../crm/infrastructure/repositories/engagement.repository";
import { LinkedInLeadFormLeadRepository } from "../../../infrastructure/repositories/linkedInLeadFormLead.repository";
import { GetAllLeadsUseCaseDto } from "../../dtos/controllerDtos/leads/getAllLeads.dto";

export class GetAllLeadsMetricsUseCase {
  constructor(
    private readonly ctx: {
      linkedInLeadFormLeadRepository: LinkedInLeadFormLeadRepository;
      contactRepository: IContactRepository;
      engagementRepository: EngagementRepository;
    },
  ) {}

  async execute(input: GetAllLeadsUseCaseDto) {
    const linkedInLeadsMetrics =
      await this.ctx.linkedInLeadFormLeadRepository.getLeadsAndPipelinesStatsByAccountId(
        input.linkedInAccountId,
        input.startDate,
        input.endDate,
      );

    const crmLeadsMetrics = await this.ctx.contactRepository.getLeadsMetrics(
      input.organizationId,
      input.startDate,
      input.endDate,
    );

    const totalMeetings =
      await this.ctx.engagementRepository.getTotalMeetingsForOrganization(
        input.organizationId,
        input.startDate,
        input.endDate,
      );

    return {
      totalCrmLeads: crmLeadsMetrics.totalLeads,
      totalLinkedLeads: linkedInLeadsMetrics.totalLeads,
      totalMeetings,
    };
  }
}
