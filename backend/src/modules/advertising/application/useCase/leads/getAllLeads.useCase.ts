import { desc, sql } from "drizzle-orm";
import { unionAll } from "drizzle-orm/pg-core";

import { Engagement } from "../../../../../database/schemas/crm/engagement.table";
import { IContactRepository } from "../../../../crm/application/interfaces/infrastructure/repositories/contact.repository.interface";
import { EngagementRepository } from "../../../../crm/infrastructure/repositories/engagement.repository";
import { LinkedInLeadFormLeadRepository } from "../../../infrastructure/repositories/linkedInLeadFormLead.repository";
import { GetAllLeadsUseCaseDto } from "../../dtos/controllerDtos/leads/getAllLeads.dto";

export type LeadRow = {
  id: string;
  source: string;
  linkedinCampaignUrn: string | null;
  leadCreatedAt: Date | null;
  firstName: string | null;
  lastName: string | null;
  workEmail: string | null;
  companyName: string | null;
  jobTitle: string | null;
  linkedinProfileLink: string | null;
};

export type LeadRowWithEngagement = LeadRow & {
  engagement?: {
    id: string;
    contactId: string;
    subject: string | null;
    startTime: Date | null;
    endTime: Date | null;
    status: string | null;
    type: string | null;
  } | null;
};

export class GetAllLeadsUseCase {
  constructor(
    private readonly ctx: {
      linkedInLeadFormLeadRepository: LinkedInLeadFormLeadRepository;
      contactRepository: IContactRepository;
      engagementRepository: EngagementRepository;
    },
  ) {}

  async execute(
    input: GetAllLeadsUseCaseDto,
  ): Promise<LeadRowWithEngagement[]> {
    // Step 1: Get all leads using the existing union query
    const linkedInLeadsQuery =
      this.ctx.linkedInLeadFormLeadRepository.getSelectLeadsForDateRange(
        input.linkedInAccountId,
        input.startDate,
        input.endDate,
      );

    const crmLeadsQuery = this.ctx.contactRepository.getSelectLeadsForDateRange(
      input.organizationId,
      input.startDate,
      input.endDate,
    );

    const allLeads = (await unionAll(
      crmLeadsQuery,
      linkedInLeadsQuery as any,
    ).orderBy(desc(sql.identifier("leadCreatedAt")))) as LeadRow[];

    // Step 2: Extract contact IDs from the CRM leads (not LinkedIn leads)
    const contactIds = allLeads
      .filter((lead): lead is LeadRow => !lead.source?.startsWith("K-")) // Filter out LinkedIn leads
      .map((lead) => lead.id);

    // Step 3: If there are contact IDs, fetch their engagements
    let engagementsByContactId: Record<string, Engagement> = {};
    if (contactIds.length > 0) {
      const engagements =
        await this.ctx.engagementRepository.getFirstEngagementsByContactIds(
          contactIds,
          input.organizationId,
        );

      // Create a map of contact ID to engagement
      engagementsByContactId = engagements.reduce(
        (acc, engagement) => {
          acc[engagement.contactId] = engagement;
          return acc;
        },
        {} as Record<string, Engagement>,
      );
    }

    // Step 4: Combine the data
    return allLeads.map((lead) => {
      const engagement = engagementsByContactId[lead.id] || null;
      const result: LeadRowWithEngagement = {
        ...lead,
        engagement: engagement,
      };

      return result as LeadRowWithEngagement;
    });
  }
}
