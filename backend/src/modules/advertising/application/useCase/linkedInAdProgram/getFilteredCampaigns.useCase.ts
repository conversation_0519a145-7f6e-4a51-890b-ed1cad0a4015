import { LinkedInAdProgramService } from "../../../domain/services/linkedInAdProgram.service";
import { LinkedInCampaignGroupService } from "../../../domain/services/linkedInCampaignGroup.service";

/**
 * Use case for retrieving filtered LinkedIn ad programs (campaigns)
 */
export class GetFilteredCampaignsUseCase {
  /**
   * Creates a new instance of GetFilteredCampaignsUseCase
   * @param linkedInAdProgramService - Service for LinkedIn ad program operations
   */
  constructor(private linkedInAdProgramService: LinkedInAdProgramService) {}

  /**
   * Executes the use case to retrieve filtered LinkedIn ad programs
   * @param params - Filter parameters including organizationId, status, and adAccountId
   * @returns Array of filtered LinkedIn ad programs
   */
  async execute(params: {
    organizationId: number;
    adAccountId?: string;
    status?: string;
  }) {
    return this.linkedInAdProgramService.getFilteredCampaigns(
      params.organizationId,
      params.status,
      params.adAccountId,
    );
  }
}
