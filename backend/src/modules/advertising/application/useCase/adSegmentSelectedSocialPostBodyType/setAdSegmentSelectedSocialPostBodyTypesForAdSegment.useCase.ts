import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedSocialPostBodyTypeService } from "../../../domain/services/adSegmentSelectedSocialPostBodyType.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { SocialPostAdCopyService } from "../../../domain/services/socialPostAdCopy.service";
import { SetAdSegmentSelectedSocialPostBodyTypesForAdSegmentRequestDto } from "../../dtos/controllerDtos/adSegmentSelectedSocialPostBodyType/setAdSegmentSelectedSocialPostBodyTypesForAdSegment.dto";

export class SetAdSegmentSelectedSocialPostBodyTypesForAdSegmentUseCase {
  constructor(
    private readonly adSegmentSelectedSocialPostBodyTypeService: AdSegmentSelectedSocialPostBodyTypeService,
    private readonly adSegmentValuePropService: AdSegmentValuePropService,
    private readonly socialPostAdCopyService: SocialPostAdCopyService,
    private readonly transaction: ITransaction,
  ) {}

  async execute(
    input: SetAdSegmentSelectedSocialPostBodyTypesForAdSegmentRequestDto,
  ) {
    for (const adSegment of input.adSegments) {
      for (const valueProp of adSegment.valueProps) {
        await this.adSegmentSelectedSocialPostBodyTypeService.setForAdSegment(
          adSegment.adSegmentId,
          valueProp.valuePropId,
          valueProp.socialPostBodyTypes,
          this.socialPostAdCopyService,
          this.adSegmentValuePropService,
          this.transaction,
        );
      }
    }
  }
}
