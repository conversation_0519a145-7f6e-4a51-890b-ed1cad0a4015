import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedSocialPostBodyTypeService } from "../../../domain/services/adSegmentSelectedSocialPostBodyType.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { SocialPostAdCopyService } from "../../../domain/services/socialPostAdCopy.service";
import { SetSingleAdSegmentSelectedSocialPostBodyTypesRequestDto } from "../../dtos/controllerDtos/adSegmentSelectedSocialPostBodyType/setSingleAdSegmentSelectedSocialPostBodyTypes.dto";

export class SetSingleAdSegmentSelectedSocialPostBodyTypesUseCase {
  constructor(
    private readonly adSegmentSelectedSocialPostBodyTypeService: AdSegmentSelectedSocialPostBodyTypeService,
    private readonly adSegmentValuePropService: AdSegmentValuePropService,
    private readonly socialPostAdCopyService: SocialPostAdCopyService,
    private readonly transaction: ITransaction,
  ) {}

  async execute(
    input: SetSingleAdSegmentSelectedSocialPostBodyTypesRequestDto,
  ) {
    await this.adSegmentSelectedSocialPostBodyTypeService.setForAdSegment(
      input.adSegmentId,
      input.valuePropId,
      input.socialPostBodyTypes,
      this.socialPostAdCopyService,
      this.adSegmentValuePropService,
      this.transaction,
    );
  }
}
