import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedSocialPostBodyTypeService } from "../../../domain/services/adSegmentSelectedSocialPostBodyType.service";
import { GetAdSegmentSelectedSocialPostBodyTypesForAdSegmentRequestDto } from "../../dtos/controllerDtos/adSegmentSelectedSocialPostBodyType/getAdSegmentSelectedSocialPostBodyTypesForAdSegment.dto";

export class GetAdSegmentSelectedSocialPostBodyTypesForAdSegmentUseCase {
  constructor(
    private readonly adSegmentSelectedSocialPostBodyTypeService: AdSegmentSelectedSocialPostBodyTypeService,
    private readonly transaction: ITransaction,
  ) {}

  async execute(
    input: GetAdSegmentSelectedSocialPostBodyTypesForAdSegmentRequestDto,
  ) {
    return this.adSegmentSelectedSocialPostBodyTypeService.getAllForAdSegment(
      input.adSegmentId,
      this.transaction,
    );
  }
}
