import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { GetAdSegmentValuePropCreativesForAdProgramCreativeRequestDto } from "../../dtos/controllerDtos/adSegmentValuePropCreative/getAdSegmentValuePropCreativesForAdProgramCreative.dto";
import { IAdSegmentValuePropCreativeRepository } from "../../interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";

export class GetAdSegmentValuePropCreativesForAdProgramCreativeUseCase {
  constructor(
    private readonly adSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository,
  ) {}

  async execute(
    input: GetAdSegmentValuePropCreativesForAdProgramCreativeRequestDto,
    tx?: ITransaction,
  ) {
    const adSegmentValuePropCreatives =
      await this.adSegmentValuePropCreativeRepository.getAllForAdProgramCreativeId(
        input.adProgramCreativeId,
        tx,
      );
    return adSegmentValuePropCreatives;
  }
}
