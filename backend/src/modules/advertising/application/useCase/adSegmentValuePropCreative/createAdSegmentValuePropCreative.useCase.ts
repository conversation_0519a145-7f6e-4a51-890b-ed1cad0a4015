import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { createUuid } from "../../../../core/utils/uuid";
import { CreateAdSegmentValurPropCreativeRequestDto } from "../../dtos/controllerDtos/adSegmentValuePropCreative/createAdSegmentValuePropCreative.dto";
import { IAdSegmentValuePropCreativeRepository } from "../../interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";

export class CreateAdSegmentValuePropCreativeUseCase {
  constructor(
    private readonly adSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository,
  ) {}

  async execute(
    input: CreateAdSegmentValurPropCreativeRequestDto,
    tx?: ITransaction,
  ) {
    await this.adSegmentValuePropCreativeRepository.createOne(
      {
        id: createUuid(),
        adSegmentValuePropId: input.adSegmentValuePropId,
        adProgramCreativeId: input.adProgramCreativeId,
      },
      tx,
    );
  }
}
