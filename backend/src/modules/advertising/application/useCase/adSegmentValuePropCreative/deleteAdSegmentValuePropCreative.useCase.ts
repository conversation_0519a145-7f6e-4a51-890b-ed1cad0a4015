import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { DeleteAdSegmentValuePropCreativeRequestDto } from "../../dtos/controllerDtos/adSegmentValuePropCreative/deleteAdSegmentValuePropCreative.dto";
import { IAdSegmentValuePropCreativeRepository } from "../../interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";

export class DeleteAdSegmentValuePropCreativeUseCase {
  constructor(
    private readonly adSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository,
  ) {}

  async execute(
    input: DeleteAdSegmentValuePropCreativeRequestDto,
    tx?: ITransaction,
  ) {
    await this.adSegmentValuePropCreativeRepository.deleteOne(
      input.adSegmentValuePropCreativeId,
      tx,
    );
  }
}
