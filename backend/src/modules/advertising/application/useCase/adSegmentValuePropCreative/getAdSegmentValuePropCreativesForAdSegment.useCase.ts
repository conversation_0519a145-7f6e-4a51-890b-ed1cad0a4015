import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { GetAdSegmentValuePropCreativesForAdSegmentRequestDto } from "../../dtos/controllerDtos/adSegmentValuePropCreative/getAdSegmentValuePropCreativesForAdSegment.dto";
import { IAdSegmentValuePropCreativeRepository } from "../../interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";

export class GetAdSegmentValuePropCreativesForAdSegmentUseCase {
  constructor(
    private readonly adSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository,
  ) {}

  async execute(
    input: GetAdSegmentValuePropCreativesForAdSegmentRequestDto,
    tx?: ITransaction,
  ) {
    const adSegmentValuePropCreatives =
      await this.adSegmentValuePropCreativeRepository.getAllForAdSegment(
        input.adSegmentId,
        tx,
      );
    return adSegmentValuePropCreatives;
  }
}
