import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { GetAdSegmentValuePropCreativesForAdSegmentValuePropRequestDto } from "../../dtos/controllerDtos/adSegmentValuePropCreative/getAdSegmentValuePropCreativesForAdSegmentValueProp.dto";
import { IAdSegmentValuePropCreativeRepository } from "../../interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";

export class GetAdSegmentValuePropCreativesForAdSegmentValuePropUseCase {
  constructor(
    private readonly adSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository,
  ) {}

  async execute(
    input: GetAdSegmentValuePropCreativesForAdSegmentValuePropRequestDto,
    tx?: ITransaction,
  ) {
    const adSegmentValuePropCreatives =
      await this.adSegmentValuePropCreativeRepository.getAllForValueProp(
        input.adSegmentValuePropId,
        tx,
      );
    return adSegmentValuePropCreatives;
  }
}
