import { ConversationMessageCopyService } from "../../../domain/services/conversationMessageCopy.service";
import { UpdateConversationMessageCopyRequestDto } from "../../dtos/controllerDtos/conversationCopy/updateConversationMessageCopy.dto";

export class UpdateConversationMessageCopyUseCase {
  constructor(
    private readonly conversationMessageCopyService: ConversationMessageCopyService,
  ) {}

  async execute(input: UpdateConversationMessageCopyRequestDto) {
    const result =
      await this.conversationMessageCopyService.createOneOrUpdateOneIfExists({
        content: input.content,
        type: input.type,
        valuePropId: input.linkedInAdSegmentValuePropId,
        subjectType: input.subjectType,
        status: input.status,
      });

    return result;
  }
}
