import { ConversationCallToActionCopyService } from "../../../domain/services/conversationCallToActionCopy.service";
import { UpdateConversationCallToActionCopyRequestDto } from "../../dtos/controllerDtos/conversationCopy/updateConversationCallToActionCopy.dto";

export class UpdateConversationCallToActionCopyUseCase {
  constructor(
    private readonly conversationCallToActionCopyService: ConversationCallToActionCopyService,
  ) {}

  async execute(input: UpdateConversationCallToActionCopyRequestDto) {
    const result =
      await this.conversationCallToActionCopyService.createOrUpdateOneIfExists({
        content: input.content,
        type: input.type,
        valuePropId: input.linkedInAdSegmentValuePropId,
        subjectType: input.subjectType,
        messageType: input.messageType,
        status: input.status,
      });

    return result;
  }
}
