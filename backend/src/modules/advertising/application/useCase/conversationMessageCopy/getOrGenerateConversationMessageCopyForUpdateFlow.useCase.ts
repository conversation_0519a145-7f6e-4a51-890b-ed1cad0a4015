import { ILLMCompletionService } from "../../../../core/application/interfaces/infrastructure/services/llmCompletionService.interface";
import { IPromptStorageService } from "../../../../core/application/interfaces/infrastructure/services/promptStorage.service.interface";
import { SegmentService } from "../../../../core/domain/services/segment.service";
import { segmentRepository } from "../../../../core/infrastructure/repositories/segment.repository";
import { createUuid } from "../../../../core/utils/uuid";
import { AdSegmentService } from "../../../domain/services/adSegment.service";
import { ConversationSubjectCopyService } from "../../../domain/services/conversationSubjectCopy.service";
import { LinkedInAdProgramService } from "../../../domain/services/linkedInAdProgram.service";
import { LinkedInConversationBaseCopyService } from "../../../domain/services/linkedInConversationBaseCopy.service";
import { PositioningService } from "../../../domain/services/positioning.service";
import { GetOrGenerateConversationCopyForUpdateFlowRequestDto } from "../../dtos/controllerDtos/conversationCopy/getOrGenerateConversationCopyForUpdateFlow.dto";
import { ICreateNewConversationValuePropStateRepository } from "../../interfaces/infrastructure/repositories/createNewConversationValuePropState.repository.interface";
import { IAdvertisingLlmCompletionsService } from "../../interfaces/infrastructure/services/advertisingLlmCompletions.service.interface";

export class GetOrGenerateConversationMessageCopyForUpdateFlowUseCase {
  constructor(
    private readonly ctx: {
      llmCompletions: ILLMCompletionService;
      promptStorage: IPromptStorageService;
      advertisingLlmCompletionsService: IAdvertisingLlmCompletionsService;
      adSegmentService: AdSegmentService;
      segmentService: SegmentService;
      positioningService: PositioningService;
      organizationId: number;
      conversationBaseCopyService: LinkedInConversationBaseCopyService;
      conversationSubjectCopyService: ConversationSubjectCopyService;
      adProgramService: LinkedInAdProgramService;
      createNewConversationValuePropStateRepository: ICreateNewConversationValuePropStateRepository;
    },
  ) {}

  async *execute(
    input: GetOrGenerateConversationCopyForUpdateFlowRequestDto,
  ): AsyncGenerator<{
    subject: string;
    message: string;
    callToAction: string;
    leadGenForm: string | null;
    done: boolean;
  }> {
    console.log("STARTING");

    const adSegment = await this.ctx.adSegmentService.getOne(input.adSegmentId);

    if (!adSegment) {
      throw new Error("Ad segment not found");
    }
    const segment = await this.ctx.segmentService.getSegmentById(
      adSegment.segmentId,
      {
        segmentRepository: segmentRepository,
      },
    );
    if (!segment) {
      throw new Error("Segment not found");
    }

    const adProgram = await this.ctx.adProgramService.getOne(
      adSegment.linkedInAdProgramId,
    );
    if (!adProgram) {
      throw new Error("Ad program not found");
    }

    const positioning = await this.ctx.positioningService.getOne(
      this.ctx.organizationId,
    );

    const baseCopy =
      await this.ctx.conversationBaseCopyService.getConversationBaseCopy(
        adSegment.id,
      );
    if (!baseCopy) {
      throw new Error("Base copy not found");
    }

    const leadGenForm =
      (
        await this.ctx.conversationSubjectCopyService.getAllForAdSegment(
          input.adSegmentId,
          "DRAFT",
        )
      ).find((each) => each.type === input.conversationSubjectCopyType)
        ?.leadGenForm ?? adProgram.leadGenForm;
    if (!leadGenForm) {
      throw new Error("Lead gen form not found");
    }

    const state =
      await this.ctx.createNewConversationValuePropStateRepository.getOne(
        input.adSegmentId,
        input.valueProp,
      );
    if (state) {
      yield {
        subject: state.conversationSubjectCopy,
        message: state.conversationMessageCopy,
        callToAction: state.conversationCallToActionCopy,
        leadGenForm: adProgram.leadGenForm ?? null,
        done: true,
      };
    } else {
      const subjectContent =
        await this.ctx.advertisingLlmCompletionsService.getnerateConversationTitle(
          {
            company: "Not provided",
            adSegmentValueProp: input.valueProp,
            subjectType: input.conversationSubjectCopyType as any,
            adTargeting: {
              jobFunction: segment.jobFunction,
              jobSeniority: segment.jobSeniority,
            },
          },
          {
            lllmCompletions: this.ctx.llmCompletions,
            promptStorage: this.ctx.promptStorage,
          },
        );
      let subjectContentText = "";
      for await (const each of subjectContent) {
        yield {
          subject: each,
          message: "",
          callToAction: "",
          leadGenForm: adProgram.leadGenForm ?? null,
          done: false,
        };
        subjectContentText += each;
      }

      console.log("SUBJECT CONTENT TEXT", subjectContentText);

      let bodyText = "";
      const body =
        this.ctx.advertisingLlmCompletionsService.generateConversationBody(
          {
            adSegmentValueProp: input.valueProp,
            baseCopy: baseCopy.baseCopy,
            subject: subjectContentText,
            positioning: positioning?.content ?? "",
            adTargeting: {
              jobFunction: segment.jobFunction,
              jobSeniority: segment.jobSeniority,
              verticals: segment.verticals,
              annualRevenueLowBound: segment.annualRevenueLowBound,
              annualRevenueHighBound: segment.annualRevenueHighBound,
              numberOfEmployeesLowBound: segment.numberOfEmployeesLowBound,
              numberOfEmployeesHighBound: segment.numberOfEmployeesHighBound,
            },
          },
          {
            lllmCompletions: this.ctx.llmCompletions,
            promptStorage: this.ctx.promptStorage,
          },
        );
      for await (const each of body) {
        yield {
          message: each,
          subject: "",
          callToAction: "",
          leadGenForm: null,
          done: false,
        };
        bodyText += each;
      }

      await this.ctx.createNewConversationValuePropStateRepository.upsertOne({
        id: createUuid(),
        adSegmentId: input.adSegmentId,
        valueProp: input.valueProp,
        conversationSubjectCopy: subjectContentText,
        conversationMessageCopy: bodyText,
        conversationCallToActionCopy: "Want to see the demo?",
        conversationSubjectCopyType: input.conversationSubjectCopyType,
        conversationMessageCopyType: input.conversationMessageCopyType,
        conversationCallToActionCopyType:
          input.conversationCallToActionCopyType,
        toBeUsed: true,
      });

      yield {
        subject: "",
        message: "",
        callToAction: "Want to see the demo?",
        leadGenForm: adProgram.leadGenForm ?? null,
        done: true,
      };
    }
  }
}
