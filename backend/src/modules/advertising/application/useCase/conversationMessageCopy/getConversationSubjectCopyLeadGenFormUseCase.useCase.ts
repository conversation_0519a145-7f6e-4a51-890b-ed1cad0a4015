import { ConversationSubjectCopy } from "../../../domain/entites/conversationSubjectCopy";
import { ConversationSubjectCopyService } from "../../../domain/services/conversationSubjectCopy.service";
import { GetConversationSubjectCopyLeadGenFormRequestDto } from "../../dtos/controllerDtos/conversationCopy/getConversationSubjectCopyLeadGenForm.dto";

export class GetConversationSubjectCopyLeadGenFormUseCase {
  constructor(
    private readonly conversationSubjectCopyService: ConversationSubjectCopyService,
  ) {}

  async execute(input: GetConversationSubjectCopyLeadGenFormRequestDto) {
    return this.conversationSubjectCopyService.getOne({
      ...input,
      status: "DRAFT",
    });
  }
}
