import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedConversationCallToActionTypeService } from "../../../domain/services/adSegmentSelectedConversationCallToActionType.service";
import { GetAdSegmentSelectedConversationCallToActionTypesForAdSegmentRequestDto } from "../../dtos/controllerDtos/adSegmentSelectedConversationCallToActionType/getAdSegmentSelectedConversationCallToActionTypesForAdSegment.dto";

export class GetAdSegmentSelectedConversationCallToActionTypesForAdSegmentUseCase {
  constructor(
    private readonly adSegmentSelectedConversationCallToActionTypeService: AdSegmentSelectedConversationCallToActionTypeService,
    private readonly transaction: ITransaction,
  ) {}

  async execute(
    input: GetAdSegmentSelectedConversationCallToActionTypesForAdSegmentRequestDto,
  ) {
    return this.adSegmentSelectedConversationCallToActionTypeService.getAllForAdSegment(
      input.adSegmentId,
      this.transaction,
    );
  }
}
