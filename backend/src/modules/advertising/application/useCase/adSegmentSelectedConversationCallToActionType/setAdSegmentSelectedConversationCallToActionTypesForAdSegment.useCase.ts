import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedConversationCallToActionTypeService } from "../../../domain/services/adSegmentSelectedConversationCallToActionType.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../../domain/services/conversationCallToActionCopy.service";
import { SetAdSegmentSelectedConversationCallToActionTypesForAdSegmentRequestDto } from "../../dtos/controllerDtos/adSegmentSelectedConversationCallToActionType/setAdSegmentSelectedConversationCallToActionTypesForAdSegment.dto";

export class SetAdSegmentSelectedConversationCallToActionTypesForAdSegmentUseCase {
  constructor(
    private readonly adSegmentSelectedConversationCallToActionTypeService: AdSegmentSelectedConversationCallToActionTypeService,
    private readonly adSegmentValuePropService: AdSegmentValuePropService,
    private readonly conversationCallToActionCopyService: ConversationCallToActionCopyService,
    private readonly transaction: ITransaction,
  ) {}

  async execute(
    input: SetAdSegmentSelectedConversationCallToActionTypesForAdSegmentRequestDto,
  ) {
    for (const adSegment of input.adSegments) {
      await this.adSegmentSelectedConversationCallToActionTypeService.setForAdSegment(
        adSegment.adSegmentId,
        adSegment.valuePropId,
        adSegment.conversationSubjectCopyType,
        adSegment.conversationMessageCopyType,
        adSegment.conversationCallToActionCopyTypes,
        this.conversationCallToActionCopyService,
        this.adSegmentValuePropService,
        this.transaction,
      );
    }
  }
}
