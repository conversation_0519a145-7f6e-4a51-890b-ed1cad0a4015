import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentSelectedConversationCallToActionTypeService } from "../../../domain/services/adSegmentSelectedConversationCallToActionType.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../../domain/services/conversationCallToActionCopy.service";
import { SetSingleAdSegmentSelectedConversationCallToActionTypesRequestDto } from "../../dtos/controllerDtos/adSegmentSelectedConversationCallToActionType/setSingleAdSegmentSelectedConversationCallToActionTypes.dto";

export class SetSingleAdSegmentSelectedConversationCallToActionTypesUseCase {
  constructor(
    private readonly adSegmentSelectedConversationCallToActionTypeService: AdSegmentSelectedConversationCallToActionTypeService,
    private readonly adSegmentValuePropService: AdSegmentValuePropService,
    private readonly conversationCallToActionCopyService: ConversationCallToActionCopyService,
    private readonly transaction: ITransaction,
  ) {}

  async execute(
    input: SetSingleAdSegmentSelectedConversationCallToActionTypesRequestDto,
  ) {
    await this.adSegmentSelectedConversationCallToActionTypeService.setForAdSegment(
      input.adSegmentId,
      input.valuePropId,
      input.conversationSubjectCopyType,
      input.conversationMessageCopyType,
      input.conversationCallToActionCopyTypes,
      this.conversationCallToActionCopyService,
      this.adSegmentValuePropService,
      this.transaction,
    );
  }
}
