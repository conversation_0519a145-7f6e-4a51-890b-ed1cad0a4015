import { ConversationSubjectCopyRepository } from "../../../infrastructure/repositories/conversationSubjectCopy.repository";
import { GetConversationSubjectCopyDto } from "../../dtos/controllerDtos/conversationSubjectCopy/getConversationSubjectCopy.dto";

export class GetConversationSubjectCopyUseCase {
  constructor(
    private readonly conversationSubjectCopyRepository: ConversationSubjectCopyRepository,
  ) {}

  async execute(input: GetConversationSubjectCopyDto) {
    const conversationSubjectCopy =
      await this.conversationSubjectCopyRepository.getOneById(
        input.conversationSubjectCopyId,
        input.status,
      );
    return conversationSubjectCopy;
  }
}
