import { LinkedInAdAccountService } from "../../../domain/services/linkedInAdAccount.service";
import { LinkedInService } from "../../../infrastructure/services/linkedIn.service";
import {
  GetLeadFormResponsesFromLinkedInApiRequestDto,
  GetLeadFormResponsesFromLinkedInApiResponseDto,
} from "../../dtos/controllerDtos/linkedInApi/getLeadGenFormResponsesFromLinkedInApi.dto";

export async function getLeadFormResponsesFromLinkedInApiUseCase(
  input: GetLeadFormResponsesFromLinkedInApiRequestDto,
  ctx: {
    linkedInService: LinkedInService;
    linkedInAdAccountService: LinkedInAdAccountService;
    userId: string;
    organizationId: number;
  },
): Promise<GetLeadFormResponsesFromLinkedInApiResponseDto> {
  const adAccount = await ctx.linkedInAdAccountService.getOneById(
    input.adAccountId,
  );
  if (!adAccount) {
    throw new Error("Ad account not found");
  }
  if (adAccount.organizationId !== ctx.organizationId) {
    throw new Error("Ad account does not belong to the organization");
  }

  const leadGenFormResponses = await ctx.linkedInService.getLeadFormResponses({
    adAccountUrn: adAccount.linkedInAdAccountUrn,
  });
  // console.log("FORM RESPONSES");
  // console.log(leadGenFormResponses);
  // console.log(
  //   leadGenFormResponses.elements[0].formResponse.answers[0]?.answerDetails,
  // );
  return leadGenFormResponses;
}
