import { LinkedInService } from "../../../infrastructure/services/linkedIn.service";
import { GetConversionsForAdAccountRequestDto } from "../../dtos/controllerDtos/linkedInApi/getConversionsForAdAccount.dto";

export class GetConversionsForAdAccountUseCase {
  constructor(private readonly linkedInService: LinkedInService) {}

  async execute(input: GetConversionsForAdAccountRequestDto) {
    return this.linkedInService.getConversionsForAdAccount(input.adAccountId);
  }
}
