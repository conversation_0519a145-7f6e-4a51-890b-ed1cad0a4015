import { LinkedInAdAccountService } from "../../../domain/services/linkedInAdAccount.service";
import { LinkedInService } from "../../../infrastructure/services/linkedIn.service";
import { GetLeadFormResponsesFromLinkedInApiRequestDto } from "../../dtos/controllerDtos/linkedInApi/getLeadGenFormResponsesFromLinkedInApi.dto";

export async function getLeadNotificationsFromLinkedInApiUseCase(
  input: GetLeadFormResponsesFromLinkedInApiRequestDto,
  ctx: {
    linkedInService: LinkedInService;
    linkedInAdAccountService: LinkedInAdAccountService;
    userId: string;
    organizationId: number;
  },
): Promise<{ results: {} }> {
  const adAccount = await ctx.linkedInAdAccountService.getOneById(
    input.adAccountId,
  );
  if (!adAccount) {
    throw new Error("Ad account not found");
  }
  if (adAccount.organizationId !== ctx.organizationId) {
    throw new Error("Ad account does not belong to the organization");
  }
  const leadNotifications = await ctx.linkedInService.getLeadNotifications({
    adAccountUrn: adAccount.linkedInAdAccountUrn,
  });

  return leadNotifications;
}
