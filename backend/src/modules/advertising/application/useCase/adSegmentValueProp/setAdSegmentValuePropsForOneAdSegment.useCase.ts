import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AdSegmentService } from "../../../domain/services/adSegment.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../../domain/services/conversationCallToActionCopy.service";
import { ConversationCopySerivce } from "../../../domain/services/conversationCopy.service";
import { ConversationMessageCopyService } from "../../../domain/services/conversationMessageCopy.service";
import { ConversationSubjectCopyService } from "../../../domain/services/conversationSubjectCopy.service";
import { LinkedInAdProgramService } from "../../../domain/services/linkedInAdProgram.service";
import { LinkedInAdProgramAdCreativeService } from "../../../domain/services/linkedInAdProgramAdCreative.service";
import { SocialPostAdCopyService } from "../../../domain/services/socialPostAdCopy.service";
import { SetAdSegmentValuePropsForManyAdSegmentsRequestDto } from "../../dtos/controllerDtos/adSegmentValueProp/setAdSegmentValuePropsForManyAdSegments.dto";
import { SetAdSegmentValuePropsForOneAdSegmentRequestDto } from "../../dtos/controllerDtos/adSegmentValueProp/setAdSegmentValuePropsForOneAdSegment.dto";
import { IAdSegmentValuePropCreativeRepository } from "../../interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";
import { ISocialPostCallToActionCopyRepository } from "../../interfaces/infrastructure/repositories/socialPostCallToActionCopy.repository.interface";

export async function setAdSegmentValuePropsForOneAdSegmentUseCase(
  input: SetAdSegmentValuePropsForOneAdSegmentRequestDto,
  ctx: {
    linkedInAdProgramService: LinkedInAdProgramService;
    adSegmentService: AdSegmentService;
    adSegmentValuePropService: AdSegmentValuePropService;
    socialPostAdCopyService: SocialPostAdCopyService;
    conversationCopyService: ConversationCopySerivce;
    conversationSubjectCopyService: ConversationSubjectCopyService;
    conversationMessageCopyService: ConversationMessageCopyService;
    conversationCallToActionCopyService: ConversationCallToActionCopyService;
    adSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository;
    socialPostCallToActionCopyRepository: ISocialPostCallToActionCopyRepository;
    organizationId: number;
    tx: ITransaction;
  },
) {
  const doesOrganizationHaveAdSegment =
    await ctx.adSegmentService.checkIfAdSegmentBelongsToOrganization(
      input.adSegmentId,
      ctx.organizationId,
    );
  if (!doesOrganizationHaveAdSegment) {
    throw new Error("Ad segment not found");
  }
  await ctx.adSegmentValuePropService.setManyForAdSegment(
    input.adSegmentId,
    input.valuePropIds,
    "DRAFT",
    ctx.socialPostAdCopyService,
    ctx.conversationCopyService,
    ctx.conversationSubjectCopyService,
    ctx.conversationMessageCopyService,
    ctx.conversationCallToActionCopyService,
    ctx.adSegmentValuePropCreativeRepository,
    ctx.socialPostCallToActionCopyRepository,
    ctx.tx,
  );
}
