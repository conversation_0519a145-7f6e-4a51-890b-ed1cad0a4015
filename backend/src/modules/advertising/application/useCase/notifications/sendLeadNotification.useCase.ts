import { LinkedInLeadFormLead } from "../../../domain/entites/linkedInLeadFormLead";
import { SlackApiService } from "../../../infrastructure/services/slack.service";
import { IOrganizationRepository } from "../../../../core/application/interfaces/repositories/organization.repository.interface";
import { ILinkedInLeadFormRepositoryInterface } from "../../interfaces/infrastructure/repositories/linkedInLeadForm.repository.interface";

interface SendLeadNotificationInput {
  leadId: string;
  organizationId: number;
}

interface SlackLeadBlock {
  type: string;
  text?: {
    type: string;
    text: string;
  };
  fields?: Array<{
    type: string;
    text: string;
  }>;
  elements?: Array<{
    type: string;
    text: string;
  }>;
}

interface SlackLeadMessage {
  blocks: SlackLeadBlock[];
}

export class SendLeadNotificationUseCase {
  constructor(
    private readonly organizationRepository: IOrganizationRepository,
    private readonly slackApiService: SlackApiService,
    private readonly leadFormRepository: ILinkedInLeadFormRepositoryInterface,
  ) {}

  async execute(input: SendLeadNotificationInput, lead: LinkedInLeadFormLead): Promise<{ success: boolean; error?: string }> {
    try {
      // 1. Get organization and check if Slack webhook is configured
      const organization = await this.organizationRepository.getOne(input.organizationId);
      if (!organization) {
        return { success: false, error: "Organization not found" };
      }

      if (!organization.slackNotificationWebhookUrl) {
        console.log(`No Slack webhook configured for organization ${input.organizationId}`);
        return { success: true }; // Not an error, just skip
      }

      // 2. Check if notifications are enabled
      if (!organization.slackNotificationsEnabled) {
        console.log(`Slack notifications are disabled for organization ${input.organizationId}`);
        return { success: true }; // Not an error, just skip
      }

      // 3. Flag test leads
      if (lead.testLead) {
        console.log(`WARNING: Processing test lead ${input.leadId}`);
      }

      // 4. Get additional lead context (form name)
      const leadForm = await this.leadFormRepository.getOneById(lead.linkedInLeadFormId);
      const formName = leadForm?.name || "Unknown Form";
      
      // 5. Format and send Slack message
      const slackMessage = this.formatLeadMessage(lead, formName);
      const slackResponse = await this.slackApiService.sendMessage(
        organization.slackNotificationWebhookUrl,
        slackMessage
      );

      if (!slackResponse.ok) {
        console.error(`Failed to send lead notification for organization ${input.organizationId}:`, slackResponse.error);
        return { success: false, error: slackResponse.error };
      }

      console.log(`Successfully sent lead notification for lead ${input.leadId} to organization ${input.organizationId}`);
      return { success: true };
      
    } catch (error) {
      console.error(`Error sending lead notification for lead ${input.leadId}:`, error);
      return { success: false, error: "Failed to send lead notification" };
    }
  }

  private formatLeadMessage(lead: LinkedInLeadFormLead, formName: string): SlackLeadMessage {
    const blocks: SlackLeadBlock[] = [];

    // Construct lead name
    const leadName = [lead.firstName, lead.lastName].filter(Boolean).join(' ') || 'Anonymous Lead';
    
    // Construct company info with company size if available
    let companyInfo = lead.companyName || 'Unknown Company';
    if (lead.companySize) {
      companyInfo += ` (Company Size: ${lead.companySize})`;
    }
    
    // Construct job title info
    const jobTitle = lead.jobTitle || 'Professional';
    
    // Build all content in one compact section
    const allContent = [
      `:tada: *New Lead Received*`,
      ``,
      companyInfo,
      `${leadName}, ${jobTitle}`
    ];
    
    // Add LinkedIn profile as hyperlink if available
    if (lead.linkedinProfileLink) {
      allContent.push(`*<${lead.linkedinProfileLink}|View LinkedIn Profile>*`);
    }

    // Add email section for manual copying if available
    const emailToCopy = lead.email || lead.workEmail;
    if (emailToCopy) {
      allContent.push(`*Email:* ${emailToCopy}`);
    }

    // Add offer information
    allContent.push(`*Offer:* ${formName}`);

    // Create single section with all content
    blocks.push({
      type: "section",
      text: {
        type: "mrkdwn",
        text: allContent.join("\n")
      }
    });

    return { blocks };
  }
} 