import { IOrganizationRepository } from "../../../../core/application/interfaces/repositories/organization.repository.interface";
import { SendNotificationUseCase } from "./sendNotification.useCase";

export class BatchNotificationsUseCase {
  constructor(
    private organizationRepository: IOrganizationRepository,
    private sendNotificationUseCase: SendNotificationUseCase,
  ) {}

  async execute(): Promise<{
    processed: number;
    succeeded: number;
    failed: number;
  }> {
    const now = new Date();
    const currentDayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const currentHour = now.getHours();

    console.log(
      `Processing notifications for ${now.toISOString().split("T")[0]}, day ${currentDayOfWeek} (0=Sunday, 1=Monday, etc.), hour ${currentHour}`,
    );

    // Get all organizations
    const allOrganizations = await this.organizationRepository.selectAll();

    // Filter organizations that have notifications enabled and webhook configured
    const eligibleOrganizations = allOrganizations.filter((org) => {
      // Must have webhook URL and notifications enabled
      if (!org.slackNotificationWebhookUrl || !org.slackNotificationsEnabled) {
        return false;
      }

      // Check if organization is due for notification based on frequency
      if (
        !this.isOrganizationDueForNotification(
          org.slackNotificationFrequency,
          now,
        )
      ) {
        console.log(
          `Skipping organization ${org.organizationId} - not due for ${org.slackNotificationFrequency} notification today`,
        );
        return false;
      }

      // Check day of week requirement (not applicable for hourly/daily frequencies if set to "everyday")
      const orgDayOfWeek = org.slackNotificationDayOfWeek || 1; // Default to Monday if not set

      if (orgDayOfWeek === 8) {
        // Everyday option - applies to hourly and daily frequencies
        if (
          org.slackNotificationFrequency !== "hourly" &&
          org.slackNotificationFrequency !== "daily"
        ) {
          console.log(
            `Skipping organization ${org.organizationId} - "everyday" option only valid for hourly/daily frequencies`,
          );
          return false;
        }
        // No day restriction for everyday option
      } else {
        // Specific day requirement
        // Convert organization's day setting (1=Monday, 7=Sunday) to JavaScript's day format (0=Sunday, 1=Monday)
        const jsOrgDayOfWeek = orgDayOfWeek === 7 ? 0 : orgDayOfWeek; // Convert Sunday from 7 to 0

        if (currentDayOfWeek !== jsOrgDayOfWeek) {
          console.log(
            `Skipping organization ${org.organizationId} - their notification day is ${orgDayOfWeek} but today is ${currentDayOfWeek}`,
          );
          return false;
        }
      }

      return true;
    });

    console.log(
      `Found ${eligibleOrganizations.length} eligible organizations out of ${allOrganizations.length} total`,
    );

    let processed = 0;
    let succeeded = 0;
    let failed = 0;

    // Process each eligible organization
    for (const organization of eligibleOrganizations) {
      processed++;

      try {
        // Calculate the appropriate date range based on frequency
        const { weekStartDate, weekEndDate } = this.calculateDateRange(
          organization.slackNotificationFrequency,
          now,
        );

        const result = await this.sendNotificationUseCase.execute({
          organizationId: organization.organizationId,
          weekStartDate,
          weekEndDate,
        });

        if (result.success) {
          succeeded++;
        } else {
          failed++;
          console.error(
            `Failed to send notification for organization ${organization.organizationId}: ${result.error}`,
          );
        }
      } catch (error) {
        failed++;
        console.error(
          `Unexpected error processing organization ${organization.organizationId}:`,
          error,
        );
      }

      // Add a small delay to avoid overwhelming Slack API
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    console.log(
      `Batch processing complete. Processed: ${processed}, Succeeded: ${succeeded}, Failed: ${failed}`,
    );

    return { processed, succeeded, failed };
  }

  private isOrganizationDueForNotification(
    frequency: "hourly" | "daily" | "weekly" | "biweekly" | "monthly",
    currentDate: Date,
  ): boolean {
    const today = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      currentDate.getDate(),
    );
    const currentHour = currentDate.getHours();

    // Convert current time to PT (Pacific Time)
    const currentTimePT = new Date(
      currentDate.toLocaleString("en-US", { timeZone: "America/Los_Angeles" }),
    );
    const currentHourPT = currentTimePT.getHours();

    switch (frequency) {
      case "hourly":
        // Hourly notifications run every hour
        return true;

      case "daily":
        // Daily notifications run once per day at 10 AM PT
        return currentHourPT === 10;

      case "weekly":
        // Weekly notifications run every week on the configured day at 10 AM PT
        return currentHourPT === 10;

      case "biweekly":
        // Biweekly notifications run every 2 weeks at 10 AM PT
        // Use week number to determine if this is an even or odd week
        const weekNumber = this.getWeekNumber(today);
        return currentHourPT === 10 && weekNumber % 2 === 0; // Run on even weeks at 10 AM PT
      case "monthly":
        // Monthly notifications run on the configured day of the first week of each month at 10 AM PT
        const dayOfMonth = today.getDate();
        return currentHourPT === 10 && dayOfMonth <= 7; // First week of the month at 10 AM PT
      default:
        return false;
    }
  }

  private calculateDateRange(
    frequency: "hourly" | "daily" | "weekly" | "biweekly" | "monthly",
    currentDate: Date,
  ): { weekStartDate: Date; weekEndDate: Date } {
    // Convert current time to PT for consistent date calculations
    const currentTimePT = new Date(
      currentDate.toLocaleString("en-US", { timeZone: "America/Los_Angeles" }),
    );
    const nowPT = new Date(
      currentTimePT.getFullYear(),
      currentTimePT.getMonth(),
      currentTimePT.getDate(),
    );

    switch (frequency) {
      case "hourly":
        // Calculate last hour's data in PT time
        const oneHourAgo = new Date(currentTimePT.getTime() - 60 * 60 * 1000); // Exactly 1 hour ago in PT
        const hourStartDate = new Date(
          oneHourAgo.getFullYear(),
          oneHourAgo.getMonth(),
          oneHourAgo.getDate(),
          oneHourAgo.getHours(),
          0,
          0,
        ); // Start of that hour
        const hourEndDate = new Date(
          oneHourAgo.getFullYear(),
          oneHourAgo.getMonth(),
          oneHourAgo.getDate(),
          oneHourAgo.getHours(),
          59,
          59,
          999,
        ); // End of that hour

        return { weekStartDate: hourStartDate, weekEndDate: hourEndDate };

      case "daily":
        // Calculate yesterday's data in PT
        const dailyEndDate = new Date(
          nowPT.getFullYear(),
          nowPT.getMonth(),
          nowPT.getDate() - 1,
          23,
          59,
          59,
        ); // End of yesterday PT
        const dailyStartDate = new Date(
          nowPT.getFullYear(),
          nowPT.getMonth(),
          nowPT.getDate() - 1,
          0,
          0,
          0,
        ); // Start of yesterday PT

        return { weekStartDate: dailyStartDate, weekEndDate: dailyEndDate };

      case "weekly":
        // Calculate last 7 days from the selected date in PT
        const weekEndDate = new Date(
          nowPT.getFullYear(),
          nowPT.getMonth(),
          nowPT.getDate() - 1,
        ); // Yesterday PT
        const weekStartDate = new Date(
          nowPT.getFullYear(),
          nowPT.getMonth(),
          nowPT.getDate() - 7,
        ); // 7 days ago PT

        return { weekStartDate, weekEndDate };

      case "biweekly":
        // Calculate previous 2 weeks date range in PT
        const biweeklyEndDate = new Date(
          nowPT.getFullYear(),
          nowPT.getMonth(),
          nowPT.getDate() - 1,
        ); // Yesterday PT
        const biweeklyStartDate = new Date(
          biweeklyEndDate.getFullYear(),
          biweeklyEndDate.getMonth(),
          biweeklyEndDate.getDate() - 13,
        ); // 14 days ago PT

        return {
          weekStartDate: biweeklyStartDate,
          weekEndDate: biweeklyEndDate,
        };

      case "monthly":
        // Calculate previous month's date range in PT
        const lastMonth = new Date(
          nowPT.getFullYear(),
          nowPT.getMonth() - 1,
          1,
        ); // First day of last month PT
        const lastMonthEnd = new Date(nowPT.getFullYear(), nowPT.getMonth(), 0); // Last day of last month PT

        return { weekStartDate: lastMonth, weekEndDate: lastMonthEnd };

      default:
        // Fallback to weekly in PT
        const fallbackEndDate = new Date(
          nowPT.getFullYear(),
          nowPT.getMonth(),
          nowPT.getDate() - 1,
        );
        const fallbackStartDate = new Date(
          fallbackEndDate.getFullYear(),
          fallbackEndDate.getMonth(),
          fallbackEndDate.getDate() - 6,
        );
        return {
          weekStartDate: fallbackStartDate,
          weekEndDate: fallbackEndDate,
        };
    }
  }

  private getWeekNumber(date: Date): number {
    // Get the week number of the year
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear =
      (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }
}
