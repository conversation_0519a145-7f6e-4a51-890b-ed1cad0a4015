import { createUuid } from "../../../../core/utils/uuid";
import { SlackNotification } from "../../../domain/entites/slackNotification";
import { CampaignMetricsAggregationService } from "../../../infrastructure/services/campaignMetricsAggregation.service";
import { SlackApiService } from "../../../infrastructure/services/slack.service";
import { IOrganizationRepository } from "../../../../core/application/interfaces/repositories/organization.repository.interface";
import { LinkedInAdAccountRepository } from "../../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInCampaignGroupRepository } from "../../../infrastructure/repositories/linkedInCampaignGroup.repository";

interface SendNotificationInput {
  organizationId: number;
  weekStartDate: Date;
  weekEndDate: Date;
}

export class SendNotificationUseCase {
  constructor(
    private organizationRepository: IOrganizationRepository,
    private slackApiService: SlackApiService,
    private campaignMetricsAggregationService: CampaignMetricsAggregationService
  ) {}

  async execute(input: SendNotificationInput): Promise<{ success: boolean; error?: string }> {
    try {
      // 1. Get organization and check if Slack webhook is configured
      const organization = await this.organizationRepository.getOne(input.organizationId);
      if (!organization) {
        return { success: false, error: "Organization not found" };
      }

      if (!organization.slackNotificationWebhookUrl) {
        console.log(`No Slack webhook configured for organization ${input.organizationId}`);
        return { success: true }; // Not an error, just skip
      }

      // 2. Check if notifications are enabled
      if (!organization.slackNotificationsEnabled) {
        console.log(`Slack notifications are disabled for organization ${input.organizationId}`);
        return { success: true }; // Not an error, just skip
      }

      // 3. Get metrics
      const metricsData = await this.campaignMetricsAggregationService.getMetrics(
        input.organizationId,
        input.weekStartDate,
        input.weekEndDate
      );

      // 4. Skip if no active campaign data
      if (metricsData.campaignGroups.length === 0) {
        console.log(`No active campaign data for organization ${input.organizationId} for the specified week`);
        return { success: true }; // Not an error, just skip
      }

      // 5. Create notification entity
      const notification = SlackNotification({
        id: createUuid(),
        organizationId: input.organizationId,
        notificationType: 'scheduled' as const,
        weekStartDate: input.weekStartDate,
        weekEndDate: input.weekEndDate,
        campaignGroupMetrics: metricsData.campaignGroups,
        totalMetrics: metricsData.totals,
        slackWebhookUrl: organization.slackNotificationWebhookUrl,
      });

      // 6. Format and send Slack message
      const slackMessage = this.slackApiService.formatPerformanceMessage(notification);
      const slackResponse = await this.slackApiService.sendMessage(
        organization.slackNotificationWebhookUrl,
        slackMessage
      );

      if (!slackResponse.ok) {
        console.error(`Failed to send Slack notification for organization ${input.organizationId}:`, slackResponse.error);
        return { success: false, error: slackResponse.error };
      }

      console.log(`Successfully sent notification for organization ${input.organizationId}`);
      return { success: true };
      
    } catch (error) {
      console.error(`Error sending notification for organization ${input.organizationId}:`, error);
      return { success: false, error: "Failed to send notification" };
    }
  }
} 