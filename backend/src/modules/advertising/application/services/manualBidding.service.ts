import { AdProgram } from "../../domain/entites/adProgram";
import { LinkedInAdAccountService } from "../../domain/services/linkedInAdAccount.service";
import { LinkedInAdProgramService } from "../../domain/services/linkedInAdProgram.service";
import { LinkedInCampaignService } from "../../domain/services/linkedInCampaign.service";
import { ManualBiddingEventService } from "../../domain/services/manualBiddingEvent.service";
import { LinkedInService } from "../../infrastructure/services/linkedIn.service";

export class ManualBiddingService {
  constructor(
    private readonly ctx: {
      linkedinService: LinkedInService;
      linkedInCampaignService: LinkedInCampaignService;
      adProgramService: LinkedInAdProgramService;
      adAccountService: LinkedInAdAccountService;
      manualBiddingEventService: ManualBiddingEventService;
    },
  ) {}

  async adjustBidForCampaign(adAudienceId: string, adProgram: AdProgram) {
    const adAudience =
      await this.ctx.linkedInCampaignService.getOneById(adAudienceId);
    if (!adAudience) {
      throw new Error("Ad audience not found");
    }

    const campaign = await this.ctx.linkedInCampaignService.getOneById(
      adAudience.linkedInAudienceId,
    );

    if (!campaign) {
      throw new Error("Campaign not found");
    }
    if (campaign.status != "ACTIVE") {
      return;
    }

    const keyResultField = await this.getKeyResultField(adProgram);

    const adAccount = await this.ctx.adAccountService.getOneById(
      adProgram.linkedInAdAccountId,
    );
    if (!adAccount) {
      throw new Error("Ad account not found");
    }

    const campaignFromLinkedIn = await this.ctx.linkedinService.getCampaign({
      adAccountUrn: adAccount.linkedInAdAccountUrn,
      campaignUrn: campaign.linkedInCampaignUrn,
    });

    if (campaignFromLinkedIn.status != "ACTIVE") {
      return "PAUSED";
    }

    const data = await this.getData(
      campaign.linkedInCampaignUrn,
      adProgram,
      keyResultField,
      Number(campaignFromLinkedIn.dailyBudget.amount),
    );
    const bid = Number(campaignFromLinkedIn.unitCost.amount);

    const newBid = await this.calculatBid({
      ...data,
      bid: bid,
      campaignFromLinkedIn: campaignFromLinkedIn,
    });

    if (newBid.newBid > Number(campaignFromLinkedIn.dailyBudget.amount)) {
      await this.ctx.linkedinService.updateCampaignBid({
        adAccountUrn: adAccount.linkedInAdAccountUrn,
        campaignUrn: campaign.linkedInCampaignUrn,
        bid: Number(campaignFromLinkedIn.dailyBudget.amount) - 0.01,
      });
    } else {
      await this.ctx.linkedinService.updateCampaignBid({
        adAccountUrn: adAccount.linkedInAdAccountUrn,
        campaignUrn: campaign.linkedInCampaignUrn,
        bid: newBid.newBid,
      });
    }

    await this.ctx.manualBiddingEventService.createOne({
      linkedInCampaignId: adAudienceId,
      newBid: newBid.newBid,
      budgetUsedPercentage: data.budgetUsedPercentage,
      timeElapsedPercentage: data.timeElapsedPercentage,
      timestamp: new Date(),
      budget: campaign.totalBudget,
      dailyOrTotalBudget: adProgram.type === "EVERGREEN" ? "monthly" : "total",
      originalBid: bid,
      timeElapsed: data.timeElapsed,
      budgetUsed: data.budgetUsed,
      minBid: newBid.suggestedBiddingData.minBid,
      maxBid: newBid.suggestedBiddingData.maxBid,
      minSuggestedBid: newBid.suggestedBiddingData.minSuggestedBid,
      maxSuggestedBid: newBid.suggestedBiddingData.maxSuggestedBid,
      suggestedBid: newBid.suggestedBiddingData.suggestedBid,
      decision: newBid.decreaseOrIncrease,
      bidType: newBid.typeBid,
    });

    return {
      newBid: newBid,
      budgetUsedPercentage: data.budgetUsedPercentage,
      timeElapsedPercentage: data.timeElapsedPercentage,
      decreaseOrIncrease: newBid.decreaseOrIncrease,
      typeBid: newBid.typeBid,
    };
  }

  private async calculatBid(data: {
    timeElapsedPercentage: number;
    budgetUsedPercentage: number;
    keyResult: number;
    bid: number;
    campaignFromLinkedIn: Awaited<
      ReturnType<typeof LinkedInService.prototype.getCampaign>
    >;
  }): Promise<{
    newBid: number;
    budgetUsedPercentage: number;
    timeElapsedPercentage: number;
    typeBid: string;
    decreaseOrIncrease: "decrease" | "increase" | "noChange";
    suggestedBiddingData: Awaited<
      ReturnType<typeof LinkedInService.prototype.getSuggestedBidding>
    >;
  }> {
    const res = await this.ctx.linkedinService.getSuggestedBidding({
      adAccountUrn: data.campaignFromLinkedIn.account,
      bidType: data.campaignFromLinkedIn.costType as any,
      objectiveType: data.campaignFromLinkedIn.objectiveType as any,
      campaignType: data.campaignFromLinkedIn.type as any,
      audienceTargets: {
        mode: "linkedIn",
        targetingCriteria: data.campaignFromLinkedIn.targetingCriteria,
      },
      optimizationTargetType: data.campaignFromLinkedIn.optimizationTargetType,
      dailyBudget: Number(data.campaignFromLinkedIn.dailyBudget?.amount),
      currencyCode: data.campaignFromLinkedIn.dailyBudget?.currencyCode,
    });
    let newBid = data.bid;
    if (data.budgetUsedPercentage > data.timeElapsedPercentage * 1.1) {
      newBid = data.bid * 0.9;
    } else if (data.budgetUsedPercentage < data.timeElapsedPercentage * 0.9) {
      newBid = data.bid * 1.1;
    }

    const decreaseOrIncrease =
      data.budgetUsedPercentage > data.timeElapsedPercentage * 1.1
        ? "decrease"
        : data.budgetUsedPercentage < data.timeElapsedPercentage * 0.9
          ? "increase"
          : "noChange";

    if (newBid < res.minBid) {
      return {
        newBid: res.minBid,
        budgetUsedPercentage: data.budgetUsedPercentage,
        timeElapsedPercentage: data.timeElapsedPercentage,
        typeBid: "minBid",
        decreaseOrIncrease: decreaseOrIncrease,
        suggestedBiddingData: res,
      };
    }
    if (newBid > res.maxBid || newBid > res.maxSuggestedBid * 3) {
      if (newBid > res.maxBid) {
        return {
          newBid: res.maxBid - 0.01,
          budgetUsedPercentage: data.budgetUsedPercentage,
          timeElapsedPercentage: data.timeElapsedPercentage,
          typeBid: "maxBid",
          decreaseOrIncrease: decreaseOrIncrease,
          suggestedBiddingData: res,
        };
      } else if (newBid > res.maxSuggestedBid * 3) {
        return {
          newBid: res.maxSuggestedBid * 3 - 0.01,
          budgetUsedPercentage: data.budgetUsedPercentage,
          timeElapsedPercentage: data.timeElapsedPercentage,
          typeBid: "3x maxSuggestedBid",
          decreaseOrIncrease: decreaseOrIncrease,
          suggestedBiddingData: res,
        };
      }
    }

    return {
      newBid: newBid,
      budgetUsedPercentage: data.budgetUsedPercentage,
      timeElapsedPercentage: data.timeElapsedPercentage,
      typeBid: "calculatedBid",
      decreaseOrIncrease: decreaseOrIncrease,
      suggestedBiddingData: res,
    };
  }

  private async getKeyResultField(adProgram: AdProgram) {
    let keyResult:
      | "clicks"
      | "oneClickLeads"
      | "videoViews"
      | "clicks"
      | undefined = undefined;
    switch (adProgram.objectiveType) {
      case "BRAND_AWARENESS":
        keyResult = "clicks";
        break;
      case "LEAD_GENERATION":
        keyResult = "oneClickLeads";
        break;
      case "VIDEO_VIEW":
        keyResult = "videoViews";
        break;
      case "WEBSITE_VISIT":
        keyResult = "clicks";
        break;
      default:
        throw new Error("Invalid objective type");
    }
    return keyResult;
  }

  private async getData(
    campaignUrn: string,
    adProgram: AdProgram,
    keyResultField: "clicks" | "oneClickLeads" | "videoViews" | "clicks",
    dailyBudget: number,
  ) {
    // TOOD: FIX FOR EVERGREEN CAMPAIGNS
    let timeElapsedPercentage: number | undefined = undefined;
    let budgetUsedPercentage: number | undefined = undefined;
    let keyResult: number | undefined = undefined;
    let timeElapsed: number | undefined = undefined;
    let budgetUsed: number | undefined = undefined;
    const startDate = new Date(adProgram.startDatetime);
    const now = new Date();

    // Get the start of the current month
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    const metrics = await this.ctx.linkedinService.getAnalyticsForCampaigns({
      campaignUrns: [campaignUrn],
      startDate: currentMonthStart,
      endDate: now,
      timeGranularity: "ALL",
    });

    const metric = metrics[0] ?? {
      campaignUrn: campaignUrn,
      clicks: 0,
      impressions: 0,
      costInUsd: 0,
      oneClickLeads: 0,
      externalWebsiteConversions: 0,
      videoViews: 0,
    };

    keyResult = metric[keyResultField];

    // If we're in the first month and started mid-month, prorate the budget
    if (
      startDate.getMonth() === now.getMonth() &&
      startDate.getFullYear() === now.getFullYear()
    ) {
      const daysInMonth = new Date(
        startDate.getFullYear(),
        startDate.getMonth() + 1,
        0,
      ).getDate();
      const remainingDays = daysInMonth - startDate.getDate() + 1;

      timeElapsedPercentage =
        (now.getTime() - startDate.getTime()) /
        (new Date(
          startDate.getFullYear(),
          startDate.getMonth() + 1,
          0,
        ).getTime() -
          startDate.getTime());

      // Prorate the budget based on remaining days in first month
      const proratedBudget = dailyBudget * remainingDays;
      budgetUsedPercentage = metric.costInUsd / proratedBudget;
      budgetUsed = metric.costInUsd;
      timeElapsed = Number(
        ((now.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000)).toFixed(
          2,
        ),
      );
    } else {
      // For subsequent months, use the full monthly budget
      timeElapsedPercentage =
        (now.getTime() - currentMonthStart.getTime()) /
        (new Date(now.getFullYear(), now.getMonth() + 1, 0).getTime() -
          currentMonthStart.getTime());

      const daysInCurrentMonth = new Date(
        now.getFullYear(),
        now.getMonth() + 1,
        0,
      ).getDate();
      budgetUsedPercentage =
        metric.costInUsd / (dailyBudget * daysInCurrentMonth);
      budgetUsed = metric.costInUsd;
      timeElapsed = Number(
        ((now.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000)).toFixed(
          2,
        ),
      );
    }

    if (
      !timeElapsedPercentage ||
      !budgetUsedPercentage ||
      !timeElapsed ||
      !budgetUsed
    ) {
      if (!timeElapsedPercentage) {
        throw new Error("Time elapsed percentage not found");
      }
      if (!budgetUsedPercentage) {
        throw new Error("Budget used percentage not found");
      }
      if (!timeElapsed) {
        throw new Error("Time elapsed not found");
      }
      if (!budgetUsed) {
        throw new Error("Budget used not found");
      }
    }

    return {
      keyResult: keyResult,
      timeElapsedPercentage: timeElapsedPercentage,
      budgetUsedPercentage: budgetUsedPercentage,
      timeElapsed: timeElapsed,
      budgetUsed: budgetUsed,
    };
  }
}
