import { LangfusePromptStorageService } from "../../../../core/infrastructure/services/langfusePromptStorage.service";
import { OpenAiCompletionService } from "../../../../core/infrastructure/services/openAiCompletion.service";
import { createUuid } from "../../../../core/utils/uuid";
import { SocialPostAdCopyService } from "../../../domain/services/socialPostAdCopy.service";
import { SocialPostCallToActionCopyRepository } from "../../../infrastructure/repositories/socialPostCallToActionCopy.repository";
import { SocialPostCopyRepository } from "../../../infrastructure/repositories/socialPostCopy.repository";
import { advertisingLangfuseClient } from "../../../utils/advertisingLangfuseClient";
import { IAdvertisingLlmCompletionsService } from "../../interfaces/infrastructure/services/advertisingLlmCompletions.service.interface";
import { ICopyVariantService } from "./copyVariant.service.interface";

export class SponsoredContentCallToActionVarientService
  implements ICopyVariantService
{
  constructor(
    private readonly advertisingLlmCompletionsService: IAdvertisingLlmCompletionsService,
    private readonly valuePropId: string,
    private readonly id: string,
  ) {}

  async *getCopyVariantStreaming(
    standardCopy: string,
    type: string,
  ): AsyncGenerator<{
    data: string;
    done: boolean;
  }> {
    const SocialPostCallToActionRepository =
      new SocialPostCallToActionCopyRepository();

    const socialPostAdCopy = await SocialPostCallToActionRepository.getOne({
      valuePropId: this.valuePropId,
      socialPostCopyType: type,
      status: "DRAFT",
    });
    if (socialPostAdCopy) {
      console.log("socialPostAdCopy", socialPostAdCopy);
      console.log({
        valuePropId: this.valuePropId,
        socialPostCopyType: type,
        status: "DRAFT",
      });
      yield {
        data: socialPostAdCopy.callToAction,
        done: true,
      };
    } else {
      let content = "";
      const stream =
        this.advertisingLlmCompletionsService.generateSocialPostHeadlineCopyVarient(
          {
            standardCopy,
            type,
          },
          {
            lllmCompletions: new OpenAiCompletionService(),
            promptStorage: new LangfusePromptStorageService(
              advertisingLangfuseClient,
            ),
          },
        );

      for await (const each of stream) {
        content += each;
        yield {
          data: each,
          done: false,
        };
      }

      await SocialPostCallToActionRepository.createOneOrUpdateOneIfExists({
        id: createUuid(),
        adSegmentValuePropId: this.valuePropId,
        type: type,
        callToAction: content,
        status: "DRAFT",
      });

      yield {
        data: "",
        done: true,
      };
    }
  }
}
