import { z } from "zod";

import { createUuid } from "../../../../core/utils/uuid";
import { GetCopyVariantStreamingRequestDto } from "../../dtos/controllerDtos/copyVariant/getCopyVariantStreaming.dto";
import { IAdvertisingLlmCompletionsService } from "../../interfaces/infrastructure/services/advertisingLlmCompletions.service.interface";
import { SponsoredContentBodyCopyVariantService } from "./sponsoredContentBodyCopyVarient.service";
import { SponsoredContentCallToActionVarientService } from "./sponsoredContentCallToActionVarient.service";
import { SponsoredConversationCallToActionCopyVariantService } from "./sponsoredConversationCallToActionCopyVariantService.service";
import { SponsoredConversationMessageCopyVariantService } from "./sponsoredConversationMessageCopyVarient.service";

export class CopyVariantServiceFactory {
  static create(
    copyVariantType: GetCopyVariantStreamingRequestDto,
    advertisingLlmCompletionsService: IAdvertisingLlmCompletionsService,
  ) {
    switch (copyVariantType.adFormatType) {
      case "SPONSORED_CONTENT":
        switch (copyVariantType.data.copyVariantTypes) {
          case "socialPostBody":
            console.log("socialPostBody");
            return new SponsoredContentBodyCopyVariantService(
              advertisingLlmCompletionsService,
              copyVariantType.data.valuePropId,
              createUuid(),
            );
          case "socialPostCallToAction":
            return new SponsoredContentCallToActionVarientService(
              advertisingLlmCompletionsService,
              copyVariantType.data.valuePropId,
              createUuid(),
            );
        }
      case "SPONSORED_INMAIL":
        switch (copyVariantType.data.copyVariantTypes) {
          case "conversationMessage":
            return new SponsoredConversationMessageCopyVariantService(
              advertisingLlmCompletionsService,
              copyVariantType.data.valuePropId,
              copyVariantType.data.subjectType,
            );
          case "conversationCallToAction":
            return new SponsoredConversationCallToActionCopyVariantService(
              advertisingLlmCompletionsService,
              copyVariantType.data.valuePropId,
              copyVariantType.data.subjectType,
              copyVariantType.data.messageType,
            );
        }
      default:
        throw new Error("Invalid copy variant type");
    }
  }
}
