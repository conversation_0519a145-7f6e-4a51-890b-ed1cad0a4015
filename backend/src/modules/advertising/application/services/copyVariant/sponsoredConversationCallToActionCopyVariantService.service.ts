import { LangfusePromptStorageService } from "../../../../core/infrastructure/services/langfusePromptStorage.service";
import { OpenAiCompletionService } from "../../../../core/infrastructure/services/openAiCompletion.service";
import { createUuid } from "../../../../core/utils/uuid";
import { ConversationCallToActionCopyRepository } from "../../../infrastructure/repositories/conversationCallToActionCopy.repository";
import { ConversationMessageCopyRepository } from "../../../infrastructure/repositories/conversationMessageCopy.repository";
import { ConversationSubjectCopyRepository } from "../../../infrastructure/repositories/conversationSubjectCopy.repository";
import { advertisingLangfuseClient } from "../../../utils/advertisingLangfuseClient";
import { IAdvertisingLlmCompletionsService } from "../../interfaces/infrastructure/services/advertisingLlmCompletions.service.interface";
import { ICopyVariantService } from "./copyVariant.service.interface";

export class SponsoredConversationCallToActionCopyVariantService
  implements ICopyVariantService
{
  valuePropId: string;
  subjectType: string;
  messageType: string;
  constructor(
    private readonly advertisingLlmCompletionsService: IAdvertisingLlmCompletionsService,
    valuePropId: string,
    subjectType: string,
    messageType: string,
  ) {
    this.valuePropId = valuePropId;
    this.subjectType = subjectType;
    this.messageType = messageType;
  }

  async *getCopyVariantStreaming(
    standardCopy: string,
    type: string,
  ): AsyncGenerator<{
    data: string;
    done: boolean;
  }> {
    const conversationSubjectCopyRepository =
      new ConversationSubjectCopyRepository();

    const conversationCallToActionCopyRepository =
      new ConversationCallToActionCopyRepository();

    const conversationSubjectCopy =
      await conversationSubjectCopyRepository.getOne({
        valuePropId: this.valuePropId,
        conversationCopyType: this.subjectType,
        status: "ACTIVE",
      });

    if (!conversationSubjectCopy) {
      throw new Error("Conversation subject copy not found");
    }

    const conversationMessageCopyRepository =
      new ConversationMessageCopyRepository();

    const conversationMessageCopy =
      await conversationMessageCopyRepository.getOneByValuePropMessageTypeAndSubjectType(
        conversationSubjectCopy.valuePropId,
        this.messageType,
        conversationSubjectCopy.type,
        "ACTIVE",
      );
    console.log("conversationMessageCopy", {
      valuePropId: this.valuePropId,
      messageType: this.messageType,
      subjectType: conversationSubjectCopy.type,
      status: "ACTIVE",
    });

    if (!conversationMessageCopy) {
      throw new Error("Conversation message copy not found");
    }

    const conversationCallToActionCopy =
      await conversationCallToActionCopyRepository.getOne({
        valuePropId: this.valuePropId,
        conversationSubjectCopyType: conversationSubjectCopy.type,
        conversationMessageCopyType: conversationMessageCopy.type,
        conversationCallToActionCopyType: type,
        status: "DRAFT",
      });

    if (conversationCallToActionCopy) {
      yield {
        data: conversationCallToActionCopy.content,
        done: true,
      };
    } else {
      let content = "";
      const stream =
        this.advertisingLlmCompletionsService.generateConversationCallToActionCopyVarient(
          {
            standardCopy,
            type,
          },
          {
            lllmCompletions: new OpenAiCompletionService(),
            promptStorage: new LangfusePromptStorageService(
              advertisingLangfuseClient,
            ),
          },
        );

      for await (const each of stream) {
        content += each;
        yield {
          data: each,
          done: false,
        };
      }

      await conversationCallToActionCopyRepository.createOrUpdateOneIfExists({
        id: createUuid(),
        type: type,
        content: content,
        valuePropId: this.valuePropId,
        subjectType: conversationSubjectCopy.type,
        messageType: conversationMessageCopy.type,
        status: "DRAFT",
      });

      yield {
        data: "",
        done: true,
      };
    }
  }
}
