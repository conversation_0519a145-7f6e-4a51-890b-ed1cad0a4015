import { advertisingLangfuseClient } from "@kalos/llms/utils";

import { LangfusePromptStorageService } from "../../../../core/infrastructure/services/langfusePromptStorage.service";
import { OpenAiCompletionService } from "../../../../core/infrastructure/services/openAiCompletion.service";
import { createUuid } from "../../../../core/utils/uuid";
import { ConversationCallToActionCopyRepository } from "../../../infrastructure/repositories/conversationCallToActionCopy.repository";
import { ConversationMessageCopyRepository } from "../../../infrastructure/repositories/conversationMessageCopy.repository";
import { ConversationSubjectCopyRepository } from "../../../infrastructure/repositories/conversationSubjectCopy.repository";
import { IAdvertisingLlmCompletionsService } from "../../interfaces/infrastructure/services/advertisingLlmCompletions.service.interface";
import { ICopyVariantService } from "./copyVariant.service.interface";

export class SponsoredConversationMessageCopyVariantService
  implements ICopyVariantService
{
  valuePropId: string;
  subjectType: string;
  constructor(
    private readonly advertisingLlmCompletionsService: IAdvertisingLlmCompletionsService,
    valuePropId: string,
    subjectType: string,
  ) {
    this.valuePropId = valuePropId;
    this.subjectType = subjectType;
  }

  async *getCopyVariantStreaming(
    standardCopy: string,
    type: string,
  ): AsyncGenerator<{
    data: string;
    done: boolean;
  }> {
    const conversationSubjectCopyRepository =
      new ConversationSubjectCopyRepository();

    const conversationCallToActionCopyRepository =
      new ConversationCallToActionCopyRepository();

    const conversationSubjectCopy =
      await conversationSubjectCopyRepository.getOne({
        valuePropId: this.valuePropId,
        conversationCopyType: this.subjectType,
        status: "ACTIVE",
      });

    if (!conversationSubjectCopy) {
      throw new Error("Conversation subject copy not found");
    }

    const conversationCallToActionCopy =
      await conversationCallToActionCopyRepository.getOne({
        valuePropId: this.valuePropId,
        conversationSubjectCopyType: conversationSubjectCopy.type,
        conversationMessageCopyType: "standard",
        conversationCallToActionCopyType: "standard",
        status: "ACTIVE",
      });

    if (!conversationCallToActionCopy) {
      throw new Error("Conversation call to action copy not found");
    }

    const conversationMessageCopyRepository =
      new ConversationMessageCopyRepository();

    const conversationMessageCopy =
      await conversationMessageCopyRepository.getOneByValuePropMessageTypeAndSubjectType(
        conversationSubjectCopy.valuePropId,
        type,
        conversationSubjectCopy.type,
        "DRAFT",
      );

    console.log("INPUT", {
      valuePropId: conversationSubjectCopy.valuePropId,
      messageType: type,
      subjectType: conversationSubjectCopy.type,
      status: "DRAFT",
    });
    console.log("conversationMessageCopy", conversationMessageCopy);

    if (conversationMessageCopy) {
      console.log("conversationMessageCopy", conversationMessageCopy);

      yield {
        data: conversationMessageCopy.content,
        done: true,
      };
    } else {
      let content = "";
      const stream =
        this.advertisingLlmCompletionsService.generateConversationMessageCopyVarient(
          {
            standardCopy,
            type,
          },
          {
            lllmCompletions: new OpenAiCompletionService(),
            promptStorage: new LangfusePromptStorageService(
              advertisingLangfuseClient,
            ),
          },
        );

      for await (const each of stream) {
        content += each;
        yield {
          data: each,
          done: false,
        };
      }

      await conversationMessageCopyRepository.upsertOne({
        id: createUuid(),
        content: content,
        type,
        subjectType: conversationSubjectCopy.type,
        status: "DRAFT",
        valuePropId: conversationSubjectCopy.valuePropId,
      });

      await conversationCallToActionCopyRepository.createOrUpdateOneIfExists({
        id: createUuid(),
        content: conversationCallToActionCopy.content,
        type: "standard",
        subjectType: conversationSubjectCopy.type,
        valuePropId: conversationSubjectCopy.valuePropId,
        messageType: type,
        status: "DRAFT",
      });

      yield {
        data: "",
        done: true,
      };
    }
  }
}
