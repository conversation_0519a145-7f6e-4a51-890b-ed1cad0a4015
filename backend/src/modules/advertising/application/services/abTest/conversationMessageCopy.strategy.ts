import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AbTest } from "../../../domain/entites/abTest";
import { AbTestRound } from "../../../domain/entites/abTestRound";
import { AdProgram } from "../../../domain/entites/adProgram";
import { AdSegment } from "../../../domain/entites/adSegment";
import { AbTestService } from "../../../domain/services/abTest/abTest.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { AdSegmentBestVariantRepository } from "../../../infrastructure/repositories/adSegmentBestVariant.repository";
import { ConversationMessageCopyRepository } from "../../../infrastructure/repositories/conversationMessageCopy.repository";
import { IAdSegmentValuePropCreativeRepository } from "../../interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";
import { IConversationSubjectCopyRepository } from "../../interfaces/infrastructure/repositories/conversationSubjectCopy.repository.interface";
import { ILinkedInAdProgramCreativeRepository } from "../../interfaces/infrastructure/repositories/linkedInAdProgramCreative.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { IStageRepository } from "../../interfaces/infrastructure/repositories/stage.repository.interface";
import {
  CreateDeploymentInput,
  DeploymentMetrics,
} from "../deploymentOrchestrator.service";
import { AbTestStrategy } from "./abTest.strategy.interface";

export class ConversationMessageCopyTestStrategy extends AbTestStrategy {
  async processMetrics(
    metrics: DeploymentMetrics,
    abTestRound: AbTestRound,
  ): Promise<{
    currentBestMetrics: {
      impressions: number;
      clicks: number;
      conversions: number;
      leads: number;
      cost: number;
      videoViews: number;
      sends: number;
      opens: number;
    };
    contentderMetrics: {
      impressions: number;
      clicks: number;
      conversions: number;
      leads: number;
      cost: number;
      videoViews: number;
      sends: number;
      opens: number;
    };
  }> {
    if (metrics.adType == "SPONSORED_INMAIL") {
      const campaign = metrics.data.metrics.campaigns[0];
      if (!campaign) {
        throw new Error("Campaign not found");
      }

      const conversationMessageCopyRepository =
        new ConversationMessageCopyRepository();

      const currentBestMessageCopy =
        await conversationMessageCopyRepository.getOneById(
          abTestRound.currentBestId,
          "ACTIVE",
        );

      const contenderBestMessageCopy =
        await conversationMessageCopyRepository.getOneById(
          abTestRound.contenderId,
          "ACTIVE",
        );

      if (!currentBestMessageCopy) {
        throw new Error("Current best message copy not found");
      }
      if (!contenderBestMessageCopy) {
        throw new Error("Contender best message copy not found");
      }

      const currentBestMetrics = campaign.ads.find(
        (metric) =>
          metric.messageCopyType === currentBestMessageCopy.type &&
          metric.subjectCopyType === currentBestMessageCopy.subjectType &&
          metric.valuePropId === currentBestMessageCopy.valuePropId,
      )?.metrics;
      if (!currentBestMetrics) {
        throw new Error("Current best metrics not found");
      }
      const contentderMetrics = campaign.ads.find(
        (metric) =>
          metric.messageCopyType === contenderBestMessageCopy.type &&
          metric.subjectCopyType === contenderBestMessageCopy.subjectType &&
          metric.valuePropId === contenderBestMessageCopy.valuePropId,
      )?.metrics;
      if (!contentderMetrics) {
        throw new Error("Contentder metrics not found");
      }

      return {
        currentBestMetrics: currentBestMetrics,
        contentderMetrics: contentderMetrics,
      };
    } else {
      throw new Error("Invalid ad type");
    }
  }
  async getDataToSetupRounds(
    abTest: AbTest,
    audienceSegmentId: string,
    tx?: ITransaction,
  ): Promise<{
    resourceIds: string[];
  }> {
    const bestVariantRepository = new AdSegmentBestVariantRepository();
    const bestVariantValueProp = await bestVariantRepository.getOne(
      audienceSegmentId,
      "valueProp",
    );
    const bestVariantConversationSubject = await bestVariantRepository.getOne(
      audienceSegmentId,
      "conversationSubject",
    );

    if (!bestVariantValueProp || !bestVariantConversationSubject) {
      throw new Error("Best variant not found");
    }

    const conversationSubject =
      await this.ctx.conversationSubjectRepository.getOneById(
        bestVariantConversationSubject.variantId,
        "ACTIVE",
        tx,
      );
    if (!conversationSubject) {
      throw new Error("Conversation subject not found");
    }

    const messageCopyRepository = new ConversationMessageCopyRepository();
    const messageCopies =
      await messageCopyRepository.getAllForValuePropAndSubjectType(
        bestVariantValueProp.variantId,
        conversationSubject.type,
        "ACTIVE",
      );

    return {
      resourceIds: messageCopies.map((each) => each.id),
    };
  }

  async getDeploymentConfigInputData(
    abTestRound: AbTestRound,
    adProgram: AdProgram,
    adSegment: AdSegment,
    tx?: ITransaction,
  ): Promise<CreateDeploymentInput> {
    const campaigns = await this.ctx.campaignRepository.getManyForAdSegment(
      adSegment.id,
      tx,
    );
    const campaign = campaigns[0];
    if (!campaign) {
      throw new Error("Campaign not found");
    }

    const messageCopyRepository = new ConversationMessageCopyRepository();
    const currentBestMessageCopy = await messageCopyRepository.getOneById(
      abTestRound.currentBestId,
      "ACTIVE",
    );
    const contenderBestMessageCopy = await messageCopyRepository.getOneById(
      abTestRound.contenderId,
      "ACTIVE",
    );

    if (!currentBestMessageCopy || !contenderBestMessageCopy) {
      throw new Error("Message copy not found");
    }

    if (adProgram.adFormat.type == "SPONSORED_INMAIL") {
      return {
        adType: "SPONSORED_INMAIL",
        data: {
          adFormat: "SPONSORED_INMAIL",
          config: {
            adSegmentId: adSegment.id,
            campaigns: [
              {
                campaignId: campaign.linkedInAudienceId,
                ads: [
                  {
                    valuePropId: currentBestMessageCopy.valuePropId,
                    subjectCopyType: currentBestMessageCopy.subjectType,
                    messageCopyType: currentBestMessageCopy.type,
                    callToActionCopyType: "Standard",
                    messageCopyContent: currentBestMessageCopy.content,
                  },
                  {
                    valuePropId: currentBestMessageCopy.valuePropId,
                    subjectCopyType: contenderBestMessageCopy.subjectType,
                    messageCopyType: contenderBestMessageCopy.type,
                    callToActionCopyType: "Standard",
                    messageCopyContent: contenderBestMessageCopy.content,
                  },
                ],
              },
            ],
          },
        },
      };
    }

    throw new Error("Not implemented");
  }

  async setWinner(winnerId: string, tx?: ITransaction): Promise<void> {
    throw new Error("Not implemented");
  }

  constructor(ctx: {
    abTestService: AbTestService;
    campaignRepository: ILinkedInCampaignRepositoryInterface;
    stageRepository: IStageRepository;
    adSegmentValuePropService: AdSegmentValuePropService;
    adProgramCreativeRepository: ILinkedInAdProgramCreativeRepository;
    adSegmentRepository: ILinkedInAdSegmentRepository;
    conversationSubjectRepository: IConversationSubjectCopyRepository;
    adSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository;
  }) {
    super(ctx);
  }
}
