import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AbTest } from "../../../domain/entites/abTest";
import { AbTestRound } from "../../../domain/entites/abTestRound";
import { AdProgram } from "../../../domain/entites/adProgram";
import { AdSegment } from "../../../domain/entites/adSegment";
import { AbTestService } from "../../../domain/services/abTest/abTest.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { AdSegmentBestVariantRepository } from "../../../infrastructure/repositories/adSegmentBestVariant.repository";
import { IAdSegmentValuePropCreativeRepository } from "../../interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";
import { IConversationSubjectCopyRepository } from "../../interfaces/infrastructure/repositories/conversationSubjectCopy.repository.interface";
import { ILinkedInAdProgramCreativeRepository } from "../../interfaces/infrastructure/repositories/linkedInAdProgramCreative.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { IStageRepository } from "../../interfaces/infrastructure/repositories/stage.repository.interface";
import {
  CreateDeploymentInput,
  DeploymentMetrics,
} from "../deploymentOrchestrator.service";
import { AbTestStrategy } from "./abTest.strategy.interface";

export class ConversationSubjectTestStrategy extends AbTestStrategy {
  async processMetrics(
    metrics: DeploymentMetrics,
    abTestRound: AbTestRound,
  ): Promise<{
    currentBestMetrics: {
      impressions: number;
      clicks: number;
      conversions: number;
      leads: number;
      cost: number;
      videoViews: number;
      sends: number;
      opens: number;
    };
    contentderMetrics: {
      impressions: number;
      clicks: number;
      conversions: number;
      leads: number;
      cost: number;
      videoViews: number;
      sends: number;
      opens: number;
    };
  }> {
    if (metrics.adType == "SPONSORED_INMAIL") {
      const campaign = metrics.data.metrics.campaigns[0];
      if (!campaign) {
        throw new Error("Campaign not found");
      }

      const currentBestSubject =
        await this.ctx.conversationSubjectRepository.getOneById(
          abTestRound.currentBestId,
          "ACTIVE",
        );

      const contenderBestSubject =
        await this.ctx.conversationSubjectRepository.getOneById(
          abTestRound.contenderId,
          "ACTIVE",
        );

      if (!currentBestSubject) {
        throw new Error("Current best subject not found");
      }
      if (!contenderBestSubject) {
        throw new Error("Contender best subject not found");
      }

      const currentBestMetrics = campaign.ads.find(
        (metric) =>
          metric.subjectCopyType === currentBestSubject.type &&
          metric.valuePropId === currentBestSubject.valuePropId,
      )?.metrics;
      if (!currentBestMetrics) {
        throw new Error("Current best metrics not found");
      }
      const contentderMetrics = campaign.ads.find(
        (metric) =>
          metric.subjectCopyType === contenderBestSubject.type &&
          metric.valuePropId === contenderBestSubject.valuePropId,
      )?.metrics;
      if (!contentderMetrics) {
        throw new Error("Contentder metrics not found");
      }

      return {
        currentBestMetrics: currentBestMetrics,
        contentderMetrics: contentderMetrics,
      };
    } else {
      throw new Error("Invalid ad type");
    }
  }
  async getDataToSetupRounds(
    abTest: AbTest,
    audienceSegmentId: string,
    tx?: ITransaction,
  ): Promise<{
    resourceIds: string[];
  }> {
    const valueProps =
      await this.ctx.adSegmentValuePropService.getManyForAdSegment(
        audienceSegmentId,
        "ACTIVE",
        tx,
      );

    const valueProp = valueProps[0];
    if (!valueProp) {
      throw new Error("Conversation subject not found");
    }

    const conversationSubjects =
      await this.ctx.conversationSubjectRepository.getAllForValueProp(
        valueProp.id,
        "ACTIVE",
        tx,
      );

    return {
      resourceIds: conversationSubjects.map((each) => each.id),
    };
  }

  async getDeploymentConfigInputData(
    abTestRound: AbTestRound,
    adProgram: AdProgram,
    adSegment: AdSegment,
    tx?: ITransaction,
  ): Promise<CreateDeploymentInput> {
    const campaigns = await this.ctx.campaignRepository.getManyForAdSegment(
      adSegment.id,
      tx,
    );
    const campaignFromDb = campaigns[0];
    if (!campaignFromDb) {
      throw new Error("Campaign not found");
    }

    const adSegmentBestVariantRepository = new AdSegmentBestVariantRepository();
    const bestCampaign = await adSegmentBestVariantRepository.getOne(
      adSegment.id,
      "audience",
    );
    let campaign: string | undefined = undefined;
    if (bestCampaign) {
      campaign = bestCampaign.variantId;
    } else {
      campaign = campaignFromDb.linkedInAudienceId;
    }

    if (!campaign) {
      throw new Error("Campaign not found");
    }

    const currentBestSubject =
      await this.ctx.conversationSubjectRepository.getOneById(
        abTestRound.currentBestId,
        "ACTIVE",
        tx,
      );
    if (!currentBestSubject) {
      throw new Error("Current best subject not found");
    }

    const contenderBestSubject =
      await this.ctx.conversationSubjectRepository.getOneById(
        abTestRound.contenderId,
        "ACTIVE",
        tx,
      );
    if (!contenderBestSubject) {
      throw new Error("Contender best subject not found");
    }

    if (contenderBestSubject.valuePropId !== currentBestSubject.valuePropId) {
      throw new Error(
        "Contender best subject value prop id does not match current best subject value prop id",
      );
    }

    const valuePropId = contenderBestSubject.valuePropId;
    if (adProgram.adFormat.type == "SPONSORED_INMAIL") {
      return {
        adType: "SPONSORED_INMAIL",
        data: {
          adFormat: "SPONSORED_INMAIL",
          config: {
            adSegmentId: adSegment.id,
            campaigns: [
              {
                campaignId: campaign,
                ads: [
                  {
                    valuePropId: valuePropId,
                    subjectCopyType: currentBestSubject.type,
                    messageCopyType: "standard",
                    callToActionCopyType: "standard",
                    messageCopyContent: currentBestSubject.content,
                  },
                  {
                    valuePropId: valuePropId,
                    subjectCopyType: contenderBestSubject.type,
                    messageCopyType: "standard",
                    callToActionCopyType: "standard",
                    messageCopyContent: contenderBestSubject.content,
                  },
                ],
              },
            ],
          },
        },
      };
    }

    throw new Error("Not implemented");
  }

  async setWinner(winnerId: string, tx?: ITransaction): Promise<void> {
    throw new Error("Not implemented");
  }

  constructor(ctx: {
    abTestService: AbTestService;
    campaignRepository: ILinkedInCampaignRepositoryInterface;
    stageRepository: IStageRepository;
    adSegmentValuePropService: AdSegmentValuePropService;
    adProgramCreativeRepository: ILinkedInAdProgramCreativeRepository;
    adSegmentRepository: ILinkedInAdSegmentRepository;
    conversationSubjectRepository: IConversationSubjectCopyRepository;
    adSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository;
  }) {
    super(ctx);
  }
}
