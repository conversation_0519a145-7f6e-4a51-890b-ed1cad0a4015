import { Transaction } from "../../../../../database/dbTransactionType";
import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AbTest } from "../../../domain/entites/abTest";
import { AbTestRound } from "../../../domain/entites/abTestRound";
import { AdProgram } from "../../../domain/entites/adProgram";
import { AdSegment } from "../../../domain/entites/adSegment";
import { AbTestService } from "../../../domain/services/abTest/abTest.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { AdSegmentBestVariantRepository } from "../../../infrastructure/repositories/adSegmentBestVariant.repository";
import { SocialPostCopyRepository } from "../../../infrastructure/repositories/socialPostCopy.repository";
import { IAdSegmentValuePropCreativeRepository } from "../../interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";
import { IConversationSubjectCopyRepository } from "../../interfaces/infrastructure/repositories/conversationSubjectCopy.repository.interface";
import { ILinkedInAdProgramCreativeRepository } from "../../interfaces/infrastructure/repositories/linkedInAdProgramCreative.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { IStageRepository } from "../../interfaces/infrastructure/repositories/stage.repository.interface";
import {
  CreateDeploymentInput,
  DeploymentMetrics,
} from "../deploymentOrchestrator.service";
import { AbTestStrategy } from "./abTest.strategy.interface";

export class SocialPostBodyCopyTestStrategy extends AbTestStrategy {
  async processMetrics(
    metrics: DeploymentMetrics,
    abTestRound: AbTestRound,
  ): Promise<{
    currentBestMetrics: {
      impressions: number;
      clicks: number;
      conversions: number;
      leads: number;
      cost: number;
      videoViews: number;
      sends: number;
      opens: number;
    };
    contentderMetrics: {
      impressions: number;
      clicks: number;
      conversions: number;
      leads: number;
      cost: number;
      videoViews: number;
      sends: number;
      opens: number;
    };
  }> {
    if (metrics.adType !== "SPONSORED_CONTENT") {
      throw new Error("Invalid ad type");
    }
    const campaign = metrics.data.metrics.campaigns[0];
    if (!campaign) {
      throw new Error("Campaign not found");
    }

    const socialPostCopyRepository = new SocialPostCopyRepository();

    const currentBest = await socialPostCopyRepository.getOneById(
      abTestRound.currentBestId,
    );
    if (!currentBest) {
      throw new Error("Current best social post copy not found");
    }

    const contender = await socialPostCopyRepository.getOneById(
      abTestRound.contenderId,
    );
    if (!contender) {
      throw new Error("Contender social post copy not found");
    }

    const currentBestMetrics = campaign.ads.find(
      (metric) =>
        metric.adCopyType === currentBest.socialPostCopyType &&
        metric.valuePropId == currentBest.linkedInAdSegmentValuePropId,
    )?.metrics;
    if (!currentBestMetrics) {
      throw new Error("Current best metrics not found");
    }
    const contentderMetrics = campaign.ads.find(
      (metric) =>
        metric.adCopyType === contender.socialPostCopyType &&
        metric.valuePropId == contender.linkedInAdSegmentValuePropId,
    )?.metrics;
    if (!contentderMetrics) {
      throw new Error("Contentder metrics not found");
    }

    return {
      currentBestMetrics: currentBestMetrics,
      contentderMetrics: contentderMetrics,
    };
  }
  async getDataToSetupRounds(
    abTest: AbTest,
    audienceSegmentId: string,
    tx?: ITransaction,
  ): Promise<{
    resourceIds: string[];
  }> {
    const bestVariantRepository = new AdSegmentBestVariantRepository();
    let valuePropId: string | undefined;

    const bestValueProp = await bestVariantRepository.getOne(
      audienceSegmentId,
      "valueProp",
    );
    if (!bestValueProp) {
      const valuePropsInAdSegment =
        await this.ctx.adSegmentValuePropService.getManyForAdSegment(
          audienceSegmentId,
          "ACTIVE",
        );
      valuePropId = valuePropsInAdSegment[0]?.id;
    } else {
      valuePropId = bestValueProp.variantId;
    }
    if (!valuePropId) {
      throw new Error("Value prop id not found");
    }

    const socialPostCopyRepository = new SocialPostCopyRepository();
    const socialPostCopies =
      await socialPostCopyRepository.getAllForLAdSegmentValueProp(
        valuePropId,
        "ACTIVE",
      );

    return {
      resourceIds: socialPostCopies.map((each) => each.id),
    };
  }

  async getDeploymentConfigInputData(
    abTestRound: AbTestRound,
    adProgram: AdProgram,
    adSegment: AdSegment,
    tx?: ITransaction,
  ): Promise<CreateDeploymentInput> {
    const campaigns = await this.ctx.campaignRepository.getManyForAdSegment(
      adSegment.id,
      tx,
    );
    const campaign = campaigns[0];
    if (!campaign) {
      throw new Error("Campaign not found");
    }

    const socialPostCopyRepository = new SocialPostCopyRepository();

    const currentBest = await socialPostCopyRepository.getOneById(
      abTestRound.currentBestId,
    );
    if (!currentBest) {
      throw new Error("Current best social post copy not found");
    }

    const contender = await socialPostCopyRepository.getOneById(
      abTestRound.contenderId,
    );
    if (!contender) {
      throw new Error("Contender social post copy not found");
    }
    if (adProgram.adFormat.type == "SPONSORED_CONTENT") {
      let adFormat: CreateDeploymentInput["data"]["adFormat"] | undefined =
        undefined;
      if (adProgram.adFormat.format == "SINGLE_IMAGE") {
        adFormat = "SINGLE_IMAGE";
      } else if (adProgram.adFormat.format == "VIDEO") {
        adFormat = "SINGLE_VIDEO";
      } else if (adProgram.adFormat.format == "DOCUMENT") {
        adFormat = "DOCUMENT";
      }
      if (!adFormat) {
        throw new Error("Invalid ad format");
      }

      if (
        currentBest.linkedInAdSegmentValuePropId !==
        contender.linkedInAdSegmentValuePropId
      ) {
        throw new Error("Current best and contender value props do not match");
      }

      const valueProp = currentBest.linkedInAdSegmentValuePropId;

      const bestVariantRepository = new AdSegmentBestVariantRepository();

      const bestCreativeVarient = await bestVariantRepository.getOne(
        adSegment.id,
        "creative",
      );
      const adProgramCreativesForValueProp =
        await this.ctx.adSegmentValuePropCreativeRepository.getAllForValueProp(
          valueProp,
        );
      let adProgramCreativeId: string | undefined;
      if (!bestCreativeVarient) {
        adProgramCreativeId =
          adProgramCreativesForValueProp[0]?.adProgramCreativeId;
      } else {
        const adProgramCreativeInValueProp =
          adProgramCreativesForValueProp.find(
            (each) =>
              each.adProgramCreativeId === bestCreativeVarient.variantId,
          );
        if (!adProgramCreativeInValueProp) {
          adProgramCreativeId =
            adProgramCreativesForValueProp[0]?.adProgramCreativeId;
        } else {
          adProgramCreativeId =
            adProgramCreativeInValueProp.adProgramCreativeId;
        }
      }
      if (!adProgramCreativeId) {
        throw new Error("Ad program creative id not found");
      }

      const deploymentConfig: CreateDeploymentInput = {
        adType: "SPONSORED_CONTENT",
        data: {
          adFormat: adFormat,
          config: {
            adSegmentId: adSegment.id,
            campaigns: [
              {
                campaignId: campaign.linkedInAudienceId,
                ads: [
                  {
                    adCreativeId: adProgramCreativeId,
                    valuePropId: valueProp,
                    adCopyType: currentBest.socialPostCopyType,
                    callToActionCopyType: "Standard",
                    socialPostBodyCopy: currentBest.body,
                  },
                  {
                    adCreativeId: adProgramCreativeId,
                    valuePropId: valueProp,
                    adCopyType: contender.socialPostCopyType,
                    callToActionCopyType: "Standard",
                    socialPostBodyCopy: contender.body,
                  },
                ],
              },
            ],
          },
        },
      };

      return deploymentConfig;
    } else if (
      adProgram.adFormat.type == "SPONSORED_CONVERSATION" ||
      adProgram.adFormat.format == "SPONSORED_INMAIL"
    ) {
      throw new Error("NOT SUPPORTED FORMAT");
    }

    throw new Error("Not implemented");
  }

  async setWinner(winnerId: string, tx?: ITransaction): Promise<void> {
    throw new Error("Not implemented");
  }

  constructor(ctx: {
    abTestService: AbTestService;
    campaignRepository: ILinkedInCampaignRepositoryInterface;
    stageRepository: IStageRepository;
    adSegmentValuePropService: AdSegmentValuePropService;
    adProgramCreativeRepository: ILinkedInAdProgramCreativeRepository;
    adSegmentRepository: ILinkedInAdSegmentRepository;
    conversationSubjectRepository: IConversationSubjectCopyRepository;
    adSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository;
  }) {
    super(ctx);
  }
}
