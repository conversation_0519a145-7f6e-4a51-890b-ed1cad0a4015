import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AbTest } from "../../../domain/entites/abTest";
import { AbTestRound } from "../../../domain/entites/abTestRound";
import { AdProgram } from "../../../domain/entites/adProgram";
import { AdSegment } from "../../../domain/entites/adSegment";
import { AbTestService } from "../../../domain/services/abTest/abTest.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { AdSegmentBestVariantRepository } from "../../../infrastructure/repositories/adSegmentBestVariant.repository";
import { ConversationCallToActionCopyRepository } from "../../../infrastructure/repositories/conversationCallToActionCopy.repository";
import { ConversationMessageCopyRepository } from "../../../infrastructure/repositories/conversationMessageCopy.repository";
import { IAdSegmentValuePropCreativeRepository } from "../../interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";
import { IConversationSubjectCopyRepository } from "../../interfaces/infrastructure/repositories/conversationSubjectCopy.repository.interface";
import { ILinkedInAdProgramCreativeRepository } from "../../interfaces/infrastructure/repositories/linkedInAdProgramCreative.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { IStageRepository } from "../../interfaces/infrastructure/repositories/stage.repository.interface";
import {
  CreateDeploymentInput,
  DeploymentMetrics,
} from "../deploymentOrchestrator.service";
import { AbTestStrategy } from "./abTest.strategy.interface";

export class ConversationCallToActionTestStrategy extends AbTestStrategy {
  async processMetrics(
    metrics: DeploymentMetrics,
    abTestRound: AbTestRound,
  ): Promise<{
    currentBestMetrics: {
      impressions: number;
      clicks: number;
      conversions: number;
      leads: number;
      cost: number;
      videoViews: number;
      sends: number;
      opens: number;
    };
    contentderMetrics: {
      impressions: number;
      clicks: number;
      conversions: number;
      leads: number;
      cost: number;
      videoViews: number;
      sends: number;
      opens: number;
    };
  }> {
    if (metrics.adType == "SPONSORED_INMAIL") {
      const campaign = metrics.data.metrics.campaigns[0];
      if (!campaign) {
        throw new Error("Campaign not found");
      }

      const conversationCallToActionCopyRepository =
        new ConversationCallToActionCopyRepository();

      const currentBestCallToActionCopy =
        await conversationCallToActionCopyRepository.getOneById(
          abTestRound.currentBestId,
          "ACTIVE",
        );

      const contenderBestCallToActionCopy =
        await conversationCallToActionCopyRepository.getOneById(
          abTestRound.contenderId,
          "ACTIVE",
        );

      if (!currentBestCallToActionCopy) {
        throw new Error("Current best call to action copy not found");
      }
      if (!contenderBestCallToActionCopy) {
        throw new Error("Contender best call to action copy not found");
      }

      const currentBestMetrics = campaign.ads.find(
        (metric) =>
          metric.callToActionCopyType === currentBestCallToActionCopy.type &&
          metric.messageCopyType === currentBestCallToActionCopy.messageType &&
          metric.subjectCopyType === currentBestCallToActionCopy.subjectType &&
          metric.valuePropId === currentBestCallToActionCopy.valuePropId,
      )?.metrics;
      if (!currentBestMetrics) {
        throw new Error("Current best metrics not found");
      }
      const contentderMetrics = campaign.ads.find(
        (metric) =>
          metric.callToActionCopyType === contenderBestCallToActionCopy.type &&
          metric.messageCopyType ===
            contenderBestCallToActionCopy.messageType &&
          metric.subjectCopyType ===
            contenderBestCallToActionCopy.subjectType &&
          metric.valuePropId === contenderBestCallToActionCopy.valuePropId,
      )?.metrics;
      if (!contentderMetrics) {
        throw new Error("Contentder metrics not found");
      }

      return {
        currentBestMetrics: currentBestMetrics,
        contentderMetrics: contentderMetrics,
      };
    } else {
      throw new Error("Invalid ad type");
    }
  }
  async getDataToSetupRounds(
    abTest: AbTest,
    audienceSegmentId: string,
    tx?: ITransaction,
  ): Promise<{
    resourceIds: string[];
  }> {
    const bestVariantRepository = new AdSegmentBestVariantRepository();
    const bestVariantValueProp = await bestVariantRepository.getOne(
      audienceSegmentId,
      "valueProp",
    );
    const bestVariantConversationSubject = await bestVariantRepository.getOne(
      audienceSegmentId,
      "conversationSubject",
    );
    const bestVariantConversationMessageCopy =
      await bestVariantRepository.getOne(
        audienceSegmentId,
        "conversationMessageCopy",
      );
    if (
      !bestVariantValueProp ||
      !bestVariantConversationSubject ||
      !bestVariantConversationMessageCopy
    ) {
      throw new Error("Best variant not found");
    }

    const conversationSubject =
      await this.ctx.conversationSubjectRepository.getOneById(
        bestVariantConversationSubject.variantId,
        "ACTIVE",
        tx,
      );
    if (!conversationSubject) {
      throw new Error("Conversation subject not found");
    }

    const messageCopyRepository = new ConversationMessageCopyRepository();

    const messageCopy = await messageCopyRepository.getOneById(
      bestVariantConversationMessageCopy.variantId,
      "ACTIVE",
    );
    if (!messageCopy) {
      throw new Error("Message copy not found");
    }

    const callToActionCopyRepository =
      new ConversationCallToActionCopyRepository();
    const callToActionCopies =
      await callToActionCopyRepository.getAllForValuePropWithMessageAndSubjectCopies(
        bestVariantValueProp.variantId,
        "ACTIVE",
      );

    const callToActionCopiesForMessageCopySubjectCppyAndValueProp =
      callToActionCopies.filter((each) => {
        return (
          each.messageType === messageCopy.type &&
          each.subjectType === conversationSubject.type &&
          each.valuePropId === bestVariantValueProp.variantId
        );
      });

    return {
      resourceIds: callToActionCopiesForMessageCopySubjectCppyAndValueProp.map(
        (each) => each.conversationCallToActionId,
      ),
    };
  }

  async getDeploymentConfigInputData(
    abTestRound: AbTestRound,
    adProgram: AdProgram,
    adSegment: AdSegment,
    tx?: ITransaction,
  ): Promise<CreateDeploymentInput> {
    const campaigns = await this.ctx.campaignRepository.getManyForAdSegment(
      adSegment.id,
      tx,
    );
    const campaign = campaigns[0];
    if (!campaign) {
      throw new Error("Campaign not found");
    }

    const callToActionCopyRepository =
      new ConversationCallToActionCopyRepository();
    const currentBestCallToActionCopy =
      await callToActionCopyRepository.getOneById(
        abTestRound.currentBestId,
        "ACTIVE",
      );
    const contenderBestCallToActionCopy =
      await callToActionCopyRepository.getOneById(
        abTestRound.contenderId,
        "ACTIVE",
      );

    if (!currentBestCallToActionCopy || !contenderBestCallToActionCopy) {
      throw new Error("Call to action copy not found");
    }

    if (adProgram.adFormat.type == "SPONSORED_INMAIL") {
      return {
        adType: "SPONSORED_INMAIL",
        data: {
          adFormat: "SPONSORED_INMAIL",
          config: {
            adSegmentId: adSegment.id,
            campaigns: [
              {
                campaignId: campaign.linkedInAudienceId,
                ads: [
                  {
                    valuePropId: currentBestCallToActionCopy.valuePropId,
                    subjectCopyType: currentBestCallToActionCopy.subjectType,
                    messageCopyType: currentBestCallToActionCopy.messageType,
                    callToActionCopyType: currentBestCallToActionCopy.type,
                    callToAction: currentBestCallToActionCopy.content,
                  },
                  {
                    valuePropId: contenderBestCallToActionCopy.valuePropId,
                    subjectCopyType: contenderBestCallToActionCopy.subjectType,
                    messageCopyType: contenderBestCallToActionCopy.messageType,
                    callToActionCopyType: contenderBestCallToActionCopy.type,
                    callToAction: contenderBestCallToActionCopy.content,
                  },
                ],
              },
            ],
          },
        },
      };
    }

    throw new Error("Not implemented");
  }

  async setWinner(winnerId: string, tx?: ITransaction): Promise<void> {
    throw new Error("Not implemented");
  }

  constructor(ctx: {
    abTestService: AbTestService;
    campaignRepository: ILinkedInCampaignRepositoryInterface;
    stageRepository: IStageRepository;
    adSegmentValuePropService: AdSegmentValuePropService;
    adProgramCreativeRepository: ILinkedInAdProgramCreativeRepository;
    adSegmentRepository: ILinkedInAdSegmentRepository;
    conversationSubjectRepository: IConversationSubjectCopyRepository;
    adSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository;
  }) {
    super(ctx);
  }
}
