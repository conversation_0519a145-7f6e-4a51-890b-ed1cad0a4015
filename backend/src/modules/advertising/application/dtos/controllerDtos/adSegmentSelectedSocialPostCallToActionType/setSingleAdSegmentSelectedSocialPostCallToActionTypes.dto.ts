import { z } from "zod";

export const setSingleAdSegmentSelectedSocialPostCallToActionTypesRequestDto =
  z.object({
    adSegmentId: z.string(),
    valuePropId: z.string(),
    socialPostCallToActionTypes: z.array(z.string()),
  });

export type SetSingleAdSegmentSelectedSocialPostCallToActionTypesRequestDto =
  z.infer<
    typeof setSingleAdSegmentSelectedSocialPostCallToActionTypesRequestDto
  >;
