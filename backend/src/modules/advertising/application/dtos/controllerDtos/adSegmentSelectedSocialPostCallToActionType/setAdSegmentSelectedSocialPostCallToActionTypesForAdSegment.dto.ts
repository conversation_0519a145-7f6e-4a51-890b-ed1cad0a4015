import { z } from "zod";

export const setAdSegmentSelectedSocialPostCallToActionTypesForAdSegmentRequestDto =
  z.object({
    adProgramId: z.string(),
    adSegments: z.array(
      z.object({
        adSegmentId: z.string(),
        socialPostCallToActionTypes: z.array(z.string()),
        valuePropId: z.string(),
      }),
    ),
  });

export type SetAdSegmentSelectedSocialPostCallToActionTypesForAdSegmentRequestDto =
  z.infer<
    typeof setAdSegmentSelectedSocialPostCallToActionTypesForAdSegmentRequestDto
  >;
