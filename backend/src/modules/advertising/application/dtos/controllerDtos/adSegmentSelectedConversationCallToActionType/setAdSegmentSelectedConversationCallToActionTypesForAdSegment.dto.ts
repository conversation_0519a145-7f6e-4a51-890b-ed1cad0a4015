import { z } from "zod";

export const setAdSegmentSelectedConversationCallToActionTypesForAdSegmentRequestDto =
  z.object({
    adProgramId: z.string(),
    adSegments: z.array(
      z.object({
        adSegmentId: z.string(),
        conversationCallToActionCopyTypes: z.array(z.string()),
        valuePropId: z.string(),
        conversationSubjectCopyType: z.string(),
        conversationMessageCopyType: z.string(),
      }),
    ),
  });

export type SetAdSegmentSelectedConversationCallToActionTypesForAdSegmentRequestDto =
  z.infer<
    typeof setAdSegmentSelectedConversationCallToActionTypesForAdSegmentRequestDto
  >;
