import { z } from "zod";

export const setSingleAdSegmentSelectedConversationCallToActionTypesRequestDto =
  z.object({
    adSegmentId: z.string(),
    conversationCallToActionCopyTypes: z.array(z.string()),
    valuePropId: z.string(),
    conversationSubjectCopyType: z.string(),
    conversationMessageCopyType: z.string(),
  });

export type SetSingleAdSegmentSelectedConversationCallToActionTypesRequestDto =
  z.infer<
    typeof setSingleAdSegmentSelectedConversationCallToActionTypesRequestDto
  >;
