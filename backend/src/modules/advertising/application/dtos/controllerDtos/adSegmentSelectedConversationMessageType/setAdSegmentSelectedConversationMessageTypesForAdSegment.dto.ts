import { z } from "zod";

export const setAdSegmentSelectedConversationMessageTypesForAdSegmentRequestDto =
  z.object({
    adProgramId: z.string(),
    adSegments: z.array(
      z.object({
        adSegmentId: z.string(),
        conversationMessageCopyTypes: z.array(z.string()),
        valuePropId: z.string(),
        conversationSubjectCopyType: z.string(),
      }),
    ),
  });

export type SetAdSegmentSelectedConversationMessageTypesForAdSegmentRequestDto =
  z.infer<
    typeof setAdSegmentSelectedConversationMessageTypesForAdSegmentRequestDto
  >;
