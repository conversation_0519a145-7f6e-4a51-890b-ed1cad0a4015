import { z } from "zod";

import { conversationSubjectCopyTypeSchema } from "../../../../domain/valueObjects/conversationSubjectCopyType";

export const setSingleAdSegmentSelectedConversationMessageTypesRequestDto =
  z.object({
    adSegmentId: z.string(),
    valuePropId: z.string(),
    conversationSubjectCopyType: conversationSubjectCopyTypeSchema,
    conversationMessageCopyTypes: z.array(z.string()),
  });

export type SetSingleAdSegmentSelectedConversationMessageTypesRequestDto =
  z.infer<typeof setSingleAdSegmentSelectedConversationMessageTypesRequestDto>;
