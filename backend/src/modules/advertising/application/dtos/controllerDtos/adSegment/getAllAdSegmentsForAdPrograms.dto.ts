import { z } from "zod";

/**
 * Zod schema for validating the request to get all ad segments for multiple ad programs
 */
export const getAllAdSegmentsForAdProgramsRequestDtoSchema = z.object({
  /**
   * Array of ad program IDs to fetch segments for
   */
  adProgramIds: z.array(z.string()),
});

/**
 * Request DTO type derived from the Zod schema
 */
export type GetAllAdSegmentsForAdProgramsRequestDto = z.infer<
  typeof getAllAdSegmentsForAdProgramsRequestDtoSchema
>;

/**
 * Response DTO for the getAllAdSegmentsForAdPrograms use case
 */
export type GetAllAdSegmentsForAdProgramsResponseDto = Array<{
  /**
   * Unique identifier of the ad segment
   */
  id: string;

  /**
   * ID of the segment this ad segment is linked to
   */
  segmentId: string;

  /**
   * ID of the ad program this segment belongs to
   */
  adProgramId: string;

  /**
   * Flag indicating if the ad segment is ready for deployment
   */
  ready: boolean;
}>;
