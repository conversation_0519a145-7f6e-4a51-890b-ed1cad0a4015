import { z } from "zod";

import { abTestTypesSchema } from "../../../../domain/entites/abTest";

const createNewMessageCopyVariantSchema = z.object({
  valuePropId: z.string().uuid(),
  subjectType: z.string(),
  data: z.array(
    z.object({
      messageCopyType: z.string(),
      ctaType: z.string(),
    }),
  ),
});

const createNewCtaCopyVariantSchema = z.object({
  valuePropId: z.string().uuid(),
  subjectType: z.string(),
  messageCopyType: z.string(),
  data: z.array(
    z.object({
      ctaType: z.string(),
    }),
  ),
});

const createNewValuePropVarientSchemaForConvo = z.object({
  subjectType: z.string(),
  data: z.array(
    z.object({
      valuePropId: z.string().uuid(),
      messageCopyType: z.string(),
      ctaType: z.string(),
    }),
  ),
});

const sponsoredInmailNewVariantsSchema = z.discriminatedUnion("type", [
  z.object({
    type: z.literal("messageCopy"),
    data: createNewMessageCopyVariantSchema,
  }),
  z.object({
    type: z.literal("ctaCopy"),
    data: createNewCtaCopyVariantSchema,
  }),
  z.object({
    type: z.literal("valueProp"),
    data: createNewValuePropVarientSchemaForConvo,
  }),
]);

const socialPostBodyCopyVariantSchema = z.object({
  valuePropId: z.string().uuid(),
  data: z.array(
    z.object({
      socialPostBodyCopyType: z.string(),
    }),
  ),
});

const socialPostCtaCopyVariantSchema = z.object({
  valuePropId: z.string().uuid(),
  data: z.array(
    z.object({
      ctaType: z.string(),
    }),
  ),
});
const sponsoredContentNewVariantsSchema = z.discriminatedUnion("type", [
  z.object({
    type: z.literal("socialPostBodyCopy"),
    data: socialPostBodyCopyVariantSchema,
  }),
  z.object({
    type: z.literal("socialPostCtaCopy"),
    data: socialPostCtaCopyVariantSchema,
  }),
]);

export const createNewVariantsDtoSchema = z.discriminatedUnion("adFormatType", [
  z.object({
    adSegmentId: z.string().uuid(),
    adFormatType: z.literal("SPONSORED_INMAIL"),
    data: sponsoredInmailNewVariantsSchema,
  }),
  z.object({
    adSegmentId: z.string().uuid(),
    adFormatType: z.literal("SPONSORED_CONTENT"),
    data: sponsoredContentNewVariantsSchema,
  }),
]);

export type CreateNewVariantsDto = z.infer<typeof createNewVariantsDtoSchema>;
