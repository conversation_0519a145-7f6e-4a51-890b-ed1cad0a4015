import { z } from "zod";

/**
 * Request DTO for getting all ad segments for a specific core segment ID
 */
export const getAllAdSegmentsForSegmentRequestDto = z.object({
  segmentId: z.string().uuid()
});

export type GetAllAdSegmentsForSegmentRequestDto = z.infer<
  typeof getAllAdSegmentsForSegmentRequestDto
>;

/**
 * Response DTO for the list of ad segments
 */
export type GetAllAdSegmentsForSegmentResponseDto = Array<{
  id: string;
  segmentId: string;
  adProgramId: string;
  ready: boolean;
}>;
