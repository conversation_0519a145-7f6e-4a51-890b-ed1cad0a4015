import { z } from "zod";

export const setAdSegmentSelectedSocialPostBodyTypesForAdSegmentRequestDto =
  z.object({
    adProgramId: z.string(),
    adSegments: z.array(
      z.object({
        adSegmentId: z.string(),
        valueProps: z.array(
          z.object({
            valuePropId: z.string(),
            socialPostBodyTypes: z.array(z.string()),
          }),
        ),
      }),
    ),
  });

export type SetAdSegmentSelectedSocialPostBodyTypesForAdSegmentRequestDto =
  z.infer<typeof setAdSegmentSelectedSocialPostBodyTypesForAdSegmentRequestDto>;
