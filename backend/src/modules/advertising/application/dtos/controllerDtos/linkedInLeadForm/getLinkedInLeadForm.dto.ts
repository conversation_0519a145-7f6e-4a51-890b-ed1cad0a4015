import { z } from "zod";

export const getLinkedInLeadFormByIdDtoSchema = z.object({
  id: z.string().uuid(),
});

export type GetLinkedInLeadFormByIdRequestDto = z.infer<
  typeof getLinkedInLeadFormByIdDtoSchema
>;

export const getLinkedInLeadFormByUrnDtoSchema = z.object({
  urn: z.string(),
});

export type GetLinkedInLeadFormByUrnRequestDto = z.infer<
  typeof getLinkedInLeadFormByUrnDtoSchema
>;
