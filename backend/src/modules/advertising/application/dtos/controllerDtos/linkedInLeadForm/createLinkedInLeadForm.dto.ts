import { z } from "zod";

import { linkedInLeadGenFormUrnSchema } from "../../../../domain/valueObjects/linkedInUrns/linkedInLeadGenFormUrn";

export const createLinkedInLeadFormDtoSchema = z.object({
  linkedInAdAccountId: z.string().uuid(),
  leadGenFormUrn: linkedInLeadGenFormUrnSchema,
  name: z.string(),
  state: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED"]),
  version: z.number().optional(),
});

export type CreateLinkedInLeadFormRequestDto = z.infer<
  typeof createLinkedInLeadFormDtoSchema
>;
