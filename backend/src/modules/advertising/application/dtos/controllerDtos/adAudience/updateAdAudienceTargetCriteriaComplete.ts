import { z } from "zod";

import { linkedInAudienceTargetCriteriaSchema } from "../../../../domain/valueObjects/linkedinAudienceTargeting/linkedinTargetAudienceCriteria";

export const updateAdAudienceTargetCriteriaCompleteRequestDtoSchema = z.object({
  id: z.string().uuid(),
  targetCriteria: linkedInAudienceTargetCriteriaSchema,
});

export type UpdateAdAudienceTargetCriteriaCompleteRequestDto = z.infer<
  typeof updateAdAudienceTargetCriteriaCompleteRequestDtoSchema
>;
