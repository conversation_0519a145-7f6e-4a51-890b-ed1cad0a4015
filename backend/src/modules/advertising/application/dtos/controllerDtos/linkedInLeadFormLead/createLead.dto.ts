import { z } from "zod";

export const createLeadRequestDto = z.object({
  linkedInLeadFormId: z.string().uuid(),
  linkedInLeadFormResponseId: z.string(),
  linkedInAdAccountId: z.string().uuid(),
  firstName: z.string().nullable(),
  lastName: z.string().nullable(),
  email: z.string().nullable(),
  phoneNumber: z.string().nullable(),
  city: z.string().nullable(),
  state: z.string().nullable(),
  country: z.string().nullable(),
  zipCode: z.string().nullable(),
  companyName: z.string().nullable(),
  jobTitle: z.string().nullable(),
  jobFunction: z.string().nullable(),
  industry: z.string().nullable(),
  companySize: z.string().nullable(),
  seniority: z.string().nullable(),
  degree: z.string().nullable(),
  fieldOfStudy: z.string().nullable(),
  school: z.string().nullable(),
  startDate: z.date().nullable(),
  graduationDate: z.date().nullable(),
  gender: z.string().nullable(),
  workEmail: z.string().nullable(),
  linkedinProfileLink: z.string().nullable(),
  workPhoneNumber: z.string().nullable(),
  linkedinCampaignUrn: z.string().nullable(),
  leadCreatedAt: z.date().nullable(),
  leadType: z.string().nullable(),
  testLead: z.boolean().nullable(),
});

export type CreateLeadRequestDto = z.infer<typeof createLeadRequestDto>;
