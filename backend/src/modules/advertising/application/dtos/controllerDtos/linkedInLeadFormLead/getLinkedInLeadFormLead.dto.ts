import { z } from "zod";

export const getLinkedInLeadFormLeadByIdDtoSchema = z.object({
  id: z.string().uuid(),
});

export type GetLinkedInLeadFormLeadByIdRequestDto = z.infer<
  typeof getLinkedInLeadFormLeadByIdDtoSchema
>;

export const getLinkedInLeadFormLeadsByFormIdDtoSchema = z.object({
  leadFormId: z.string().uuid(),
});

export type GetLinkedInLeadFormLeadsByFormIdRequestDto = z.infer<
  typeof getLinkedInLeadFormLeadsByFormIdDtoSchema
>;

export const getLeadsAndPipelinesByAccountIdDtoSchema = z.object({
  linkedInAdAccountId: z.string().uuid(),
  startDateFilter: z.date().optional(),
  endDateFilter: z.date().optional(),
});

export type GetLeadsAndPipelinesByAccountIdRequestDto = z.infer<
  typeof getLeadsAndPipelinesByAccountIdDtoSchema
>;
