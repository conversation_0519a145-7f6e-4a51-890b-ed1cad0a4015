import { z } from "zod";

const getCopyVariantStreamingSponsoredContentDto = z.discriminatedUnion(
  "copyVariantTypes",
  [
    z.object({
      copyVariantTypes: z.literal("socialPostBody"),
      standardCopy: z.string(),
      type: z.string(),
      valuePropId: z.string(),
    }),
    z.object({
      copyVariantTypes: z.literal("socialPostCallToAction"),
      standardCopy: z.string(),
      type: z.string(),
      valuePropId: z.string(),
    }),
  ],
);

const getCopyVariantStreamingSponsoredInmailDto = z.discriminatedUnion(
  "copyVariantTypes",
  [
    z.object({
      copyVariantTypes: z.literal("conversationMessage"),
      standardCopy: z.string(),
      type: z.string(),
      valuePropId: z.string(),
      subjectType: z.string(),
    }),
    z.object({
      copyVariantTypes: z.literal("conversationCallToAction"),
      standardCopy: z.string(),
      type: z.string(),
      valuePropId: z.string(),
      subjectType: z.string(),
      messageType: z.string(),
    }),
  ],
);

export const getCopyVariantStreamingRequestDto = z.discriminatedUnion(
  "adFormatType",
  [
    z.object({
      adFormatType: z.literal("SPONSORED_CONTENT"),
      data: getCopyVariantStreamingSponsoredContentDto,
    }),
    z.object({
      adFormatType: z.literal("SPONSORED_INMAIL"),
      data: getCopyVariantStreamingSponsoredInmailDto,
    }),
  ],
);

export type GetCopyVariantStreamingRequestDto = z.infer<
  typeof getCopyVariantStreamingRequestDto
>;
