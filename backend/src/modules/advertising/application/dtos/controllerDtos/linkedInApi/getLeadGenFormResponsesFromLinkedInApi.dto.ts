import { z } from "zod";

import { linkedInAdAccountUrnSchema } from "../../../../domain/valueObjects/linkedInUrns/linkedInAdAccountUrn";
import { linkedInLeadGenFormUrnSchema } from "../../../../domain/valueObjects/linkedInUrns/linkedInLeadGenFormUrn";

export const getLeadFormResponsesFromLinkedInApiRequestDtoSchema = z.object({
  adAccountId: z.string(),
});

export type GetLeadFormResponsesFromLinkedInApiRequestDto = z.infer<
  typeof getLeadFormResponsesFromLinkedInApiRequestDtoSchema
>;

export const getLeadFormResponsesFromLinkedInApiResponseDtoSchema = z.object({
  elements: z.array(
    z.object({
      leadType: z.string(),
      submittedAt: z.number(),
      leadMetadataInfo: z.object({
        sponsoredLeadMetadataInfo: z.object({
          campaign: z.object({
            name: z.string(),
            type: z.string(),
            id: z.string(),
          }),
        }),
      }),
      formResponse: z.object({
        answers: z.array(
          z.object({
            answerDetails: z.object({
              textQuestionAnswer: z
                .object({
                  answer: z.string(),
                })
                .optional(),
              multipleChoiceAnswer: z
                .object({
                  options: z.array(z.number()),
                })
                .optional(),
            }),
            questionId: z.number(),
          }),
        ),
        consentResponses: z.array(
          z.object({
            accepted: z.boolean(),
            consentId: z.number(),
          }),
        ),
      }),
    }),
  ),
});

export type GetLeadFormResponsesFromLinkedInApiResponseDto = z.infer<
  typeof getLeadFormResponsesFromLinkedInApiResponseDtoSchema
>;

// sample_form_response = {
//   elements: [
//     {
//       owner: {
//         sponsoredAccount: "urn:li:sponsoredAccount:*********",
//       },
//       submitter: "urn:li:person:MpGcnvaU_p",
//       versionedLeadGenFormUrn:
//         "urn:li:versionedLeadGenForm:(urn:li:leadGenForm:3162,1)",
//       leadMetadata: {
//         sponsoredLeadMetadata: {
//           campaign: "urn:li:sponsoredCampaign:*********",
//         },
//       },
//       ownerInfo: {
//         sponsoredAccountInfo: {
//           name: "Angela Test",
//         },
//       },
//       testLead: false,
//       associatedEntity: {
//         associatedCreative: "urn:li:sponsoredCreative:*********",
//       },
//       leadType: "SPONSORED",
//       leadMetadataInfo: {
//         sponsoredLeadMetadataInfo: {
//           campaign: {
//             name: "Brand awareness - May 3, 2022",
//             type: "SPONSORED_UPDATES",
//             id: "urn:li:sponsoredCampaign:*********",
//           },
//         },
//       },
//       associatedEntityInfo: {
//         associatedCreative: {
//           intendedStatus: "ACTIVE",
//           content: {
//             reference: "urn:li:ugcPost:6927151919538794496",
//           },
//           id: "urn:li:sponsoredCreative:*********",
//         },
//       },
//       id: "691c02e7-f7f0-430a-ae16-4fc7d76f61a6-5",
//       submittedAt: *************,
//       formResponse: {
//         answers: [
//           {
//             answerDetails: {
//               textQuestionAnswer: {
//                 answer: "Hello World-testing purpose",
//               },
//             },
//             questionId: 1,
//           },
//           {
//             answerDetails: {
//               multipleChoiceAnswer: {
//                 options: [1, 3],
//               },
//             },
//             questionId: 2,
//           },
//         ],
//         consentResponses: [
//           {
//             accepted: true,
//             consentId: 4,
//           },
//         ],
//       },
//     },
//   ],
//   paging: {
//     count: 10,
//     start: 0,
//     total: 1,
//     links: [],
//   },
// };
