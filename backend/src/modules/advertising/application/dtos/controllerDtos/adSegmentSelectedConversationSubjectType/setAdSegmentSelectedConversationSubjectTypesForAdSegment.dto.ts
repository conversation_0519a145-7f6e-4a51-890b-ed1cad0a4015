import { z } from "zod";

export const setAdSegmentSelectedConversationSubjectTypesForAdSegmentRequestDto =
  z.object({
    adProgramId: z.string(),
    adSegments: z.array(
      z.object({
        adSegmentId: z.string(),
        conversationSubjectCopyTypes: z.array(z.string()),
      }),
    ),
  });

export type SetAdSegmentSelectedConversationSubjectTypesForAdSegmentRequestDto =
  z.infer<
    typeof setAdSegmentSelectedConversationSubjectTypesForAdSegmentRequestDto
  >;
