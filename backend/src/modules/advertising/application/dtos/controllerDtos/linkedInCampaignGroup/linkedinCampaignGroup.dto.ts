import { z } from "zod";

export const getCampaignGroupAnalyticsDtoSchema = z.object({
  campaignGroupUrns: z.array(z.string()).optional(),
  linkedInAdSegments: z.array(z.string()).optional(),
  fromDate: z.date().optional(),
  toDate: z.date().optional(),
});

export type GetCampaignGroupBatchAnalyticsDto = z.infer<
  typeof getCampaignGroupAnalyticsDtoSchema
>;

export const getOneCampaignGroupAnalyticsDtoSchema = z.object({
  campaignGroupUrn: z.string().optional(),
  linkedInAdSegment: z.string().optional(),
  fromDate: z.date().optional(),
  toDate: z.date().optional(),
});

export type GetOneCampaignGroupBatchAnalyticsDto = z.infer<
  typeof getOneCampaignGroupAnalyticsDtoSchema
>;
