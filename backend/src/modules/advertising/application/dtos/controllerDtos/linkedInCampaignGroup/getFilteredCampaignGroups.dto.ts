import { z } from "zod";

export const CampaignGroupStatusEnum = z.enum([
  "ACTIVE",
  "PAUSED",
  "ARCHIVED",
  "CANCELLED",
  "DRAFT",
  "PENDING_DELETION",
  "REMOVED",
]);

export const getFilteredCampaignGroupsDtoSchema = z.object({
  adAccountId: z.string(),
  statuses: z.array(CampaignGroupStatusEnum),
});

export type GetFilteredCampaignGroupsDto = z.infer<
  typeof getFilteredCampaignGroupsDtoSchema
>;
