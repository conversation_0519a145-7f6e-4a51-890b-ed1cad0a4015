import { z } from "zod";

export const getAllLeadsDtoSchema = z.object({
  organizationId: z.number().positive(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
});

export type GetAllLeadsRequestDto = z.infer<typeof getAllLeadsDtoSchema>;

export const getAllLeadsUseCaseDtoSchema = z.object({
  linkedInAccountId: z.string(),
  organizationId: z.number().positive(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
});

export type GetAllLeadsUseCaseDto = z.infer<typeof getAllLeadsUseCaseDtoSchema>;
