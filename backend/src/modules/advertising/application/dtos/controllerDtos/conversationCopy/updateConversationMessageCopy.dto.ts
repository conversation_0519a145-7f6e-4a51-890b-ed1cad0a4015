import { z } from "zod";

import { conversationMessageCopyTypeSchema } from "../../../../domain/valueObjects/conversationMessageCopyType";
import { conversationSubjectCopyTypeSchema } from "../../../../domain/valueObjects/conversationSubjectCopyType";

export const updateConversationMessageCopyRequestDto = z.object({
  id: z.string().uuid().optional(),
  linkedInAdSegmentValuePropId: z.string().uuid(),
  content: z.string(),
  type: conversationMessageCopyTypeSchema,
  subjectType: z.string(),
  status: z.enum(["DRAFT", "ACTIVE", "ARCHIVED"]).default("DRAFT"),
});

export type UpdateConversationMessageCopyRequestDto = z.infer<
  typeof updateConversationMessageCopyRequestDto
>;
