import { z } from "zod";

import { conversationCallToActionCopyTypeSchema } from "../../../../domain/valueObjects/conversationCallToActionCopyType";
import { conversationMessageCopyTypeSchema } from "../../../../domain/valueObjects/conversationMessageCopyType";
import { conversationSubjectCopyTypeSchema } from "../../../../domain/valueObjects/conversationSubjectCopyType";

export const updateConversationCallToActionCopyRequestDto = z.object({
  id: z.string().uuid().optional(),
  linkedInAdSegmentValuePropId: z.string().uuid(),
  content: z.string().min(1).max(255),
  type: conversationCallToActionCopyTypeSchema,
  subjectType: conversationSubjectCopyTypeSchema,
  messageType: conversationMessageCopyTypeSchema,
  status: z.enum(["DRAFT", "ACTIVE", "ARCHIVED"]).default("DRAFT"),
});

export type UpdateConversationCallToActionCopyRequestDto = z.infer<
  typeof updateConversationCallToActionCopyRequestDto
>;
