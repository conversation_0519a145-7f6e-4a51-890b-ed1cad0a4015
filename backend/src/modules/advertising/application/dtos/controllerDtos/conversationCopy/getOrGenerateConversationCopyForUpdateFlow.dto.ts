import { z } from "zod";

import { conversationSubjectCopyTypeSchema } from "../../../../domain/valueObjects/conversationSubjectCopyType";

export const getOrGenerateConversationCopyForUpdateFlowRequestDto = z.object({
  valueProp: z.string(),
  conversationMessageCopyType: z.string(),
  conversationSubjectCopyType: z.string(),
  conversationCallToActionCopyType: z.string(),
  adSegmentId: z.string(),
});

export type GetOrGenerateConversationCopyForUpdateFlowRequestDto = z.infer<
  typeof getOrGenerateConversationCopyForUpdateFlowRequestDto
>;
