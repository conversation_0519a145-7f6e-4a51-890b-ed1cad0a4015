import { z } from "zod";

import { linkedInConversionUrnSchema } from "../../../../domain/valueObjects/linkedInUrns/linkedInConversionUrn";

export const adminCreateOrganizationConversionRequestDtoSchema = z.object({
  organizationId: z.number(),
  conversionUrn: linkedInConversionUrnSchema,
  name: z.string(),
});

export type AdminCreateOrganizationConversionRequestDto = z.infer<
  typeof adminCreateOrganizationConversionRequestDtoSchema
>;
