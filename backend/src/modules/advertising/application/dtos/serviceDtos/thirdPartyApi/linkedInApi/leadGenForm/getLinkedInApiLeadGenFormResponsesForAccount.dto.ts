import { z } from "zod";

export const getLinkedInApiLeadGenFormResponsesForAccountRequestDto = z.object({
  adAccountUrn: z.string(),
  versionedLeadGenFormUrn: z.string().optional(),
});

export type GetLinkedInApiLeadGenFormResponsesForAccountRequestDto = z.infer<
  typeof getLinkedInApiLeadGenFormResponsesForAccountRequestDto
>;

export const getLinkedInApiLeadGenFormResponseByIdRequestDto = z.object({
  formLeadResponseId: z.string(),
});

export type GetLinkedInApiLeadGenFormResponseByIdRequestDto = z.infer<
  typeof getLinkedInApiLeadGenFormResponseByIdRequestDto
>;

export const getLinkedInApiLeadGenFormResponseByIdResponseDto = z.object({
  leadType: z.string(),
  submittedAt: z.number(),
  leadMetadataInfo: z.object({
    sponsoredLeadMetadataInfo: z.object({
      campaign: z.object({
        name: z.string(),
        type: z.string(),
        id: z.string(),
      }),
    }),
  }),
  leadMetadata: z.object({
    sponsoredLeadMetadata: z.object({
      campaign: z.string(),
    }),
  }),
  ownerInfo: z.object({
    sponsoredAccountInfo: z.object({
      name: z.string(),
    }),
  }),
  testLead: z.boolean(),
  associatedEntityInfo: z.object({
    associatedCreative: z.object({
      intendedStatus: z.string(),
      content: z.object({
        reference: z.string(),
      }),
      id: z.string(),
    }),
  }),
  id: z.string(),
  formResponse: z.object({
    answers: z.array(
      z.object({
        answerDetails: z.object({
          textQuestionAnswer: z
            .object({
              answer: z.string(),
            })
            .optional(),
          multipleChoiceAnswer: z
            .object({
              options: z.array(z.number()),
            })
            .optional(),
        }),
        questionId: z.number(),
      }),
    ),
    consentResponses: z.array(
      z.object({
        accepted: z.boolean(),
        consentId: z.number(),
      }),
    ),
  }),
});

export type GetLinkedInApiLeadGenFormResponseByIdResponseDto = z.infer<
  typeof getLinkedInApiLeadGenFormResponseByIdResponseDto
>;

export const getLinkedInApiLeadGenFormResponsesForAccountResponseDto = z.object(
  {
    elements: z.array(
      z.object({
        leadType: z.string(),
        submittedAt: z.number(),
        leadMetadataInfo: z.object({
          sponsoredLeadMetadataInfo: z.object({
            campaign: z.object({
              name: z.string(),
              type: z.string(),
              id: z.string(),
            }),
          }),
        }),
        leadMetadata: z.object({
          sponsoredLeadMetadata: z.object({
            campaign: z.string(),
          }),
        }),
        id: z.string(),
        testLead: z.boolean(),
        formResponse: z.object({
          answers: z.array(
            z.object({
              answerDetails: z.object({
                textQuestionAnswer: z
                  .object({
                    answer: z.string(),
                  })
                  .optional(),
                multipleChoiceAnswer: z
                  .object({
                    options: z.array(z.number()),
                  })
                  .optional(),
              }),
              questionId: z.number(),
            }),
          ),
          consentResponses: z.array(
            z.object({
              accepted: z.boolean(),
              consentId: z.number(),
            }),
          ),
        }),
      }),
    ),
  },
);

export type GetLinkedInApiLeadGenFormResponsesForAccountResponseDto = z.infer<
  typeof getLinkedInApiLeadGenFormResponsesForAccountResponseDto
>;
