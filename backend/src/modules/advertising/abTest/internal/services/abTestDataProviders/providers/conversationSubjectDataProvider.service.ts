import { err, ok, Result } from "neverthrow";

import { IAdSegmentBestVariantRepository } from "../../../../../application/interfaces/infrastructure/repositories/adSegmentBestVariant.repository.interface";
import { IAdSegmentValuePropRepository } from "../../../../../application/interfaces/infrastructure/repositories/adSegmentValueProp.repository.interface";
import { IAdSegmentValuePropCreativeRepository } from "../../../../../application/interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";
import { IConversationSubjectCopyRepository } from "../../../../../application/interfaces/infrastructure/repositories/conversationSubjectCopy.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../../../../application/interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../../../../application/interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { AdProgram } from "../../../../../domain/entites/adProgram";
import { AdSegment } from "../../../../../domain/entites/adSegment";
import { LinkedInAdProgramRepository } from "../../../../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInStateInput } from "../../../../../linkedInStateOrchestrator/domain/linkedInStateInput.valueObject";
import { LinkedInStateOutput } from "../../../../../linkedInStateOrchestrator/domain/linkedInStateOutput.entity";
import { ConversationSubjectAbTestDomain } from "../../../domain/abTestTypeDataProcessors/conversationSubjectTestDataProcessor.domain";
import { IAbTestTypeDataProviderService } from "../abTestTypeDataProvider.serivce.interface";

export const ConversationSubjectAbTestGetVariantsToSetupRounds = (deps: {
  adSegmentValuePropRepository: IAdSegmentValuePropRepository;
  adSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository;
  adSegmentBestVariantRepository: IAdSegmentBestVariantRepository;
  adSegmentRepository: ILinkedInAdSegmentRepository;
  adProgramRepository: LinkedInAdProgramRepository;
  linkedInCampaignRepository: ILinkedInCampaignRepositoryInterface;
  conversationSubjectCopyRepository: IConversationSubjectCopyRepository;
}): IAbTestTypeDataProviderService => ({
  async getVariantsToSetupRounds(input: {
    adProgram: AdProgram;
    adSegment: AdSegment;
  }) {
    // Get value props for SPONSORED_CONTENT
    const SPONSORED_CONTENT = async (): Promise<
      Result<
        string[],
        {
          type:
            | "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS"
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
        }
      >
    > => {
      return err({ type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" });
    };

    // Get value props for SPONSORED_INMAIL
    const SPONSORED_INMAIL = async (): Promise<
      Result<
        string[],
        {
          type:
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
            | "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS";
        }
      >
    > => {
      const conversationSubjectCopies =
        await deps.conversationSubjectCopyRepository.getAllForAdSegment(
          input.adSegment.id,
          "ACTIVE",
        );

      return ok(
        ConversationSubjectAbTestDomain.constructResourceIdsList.forSponsoredInmail(
          {
            conversationSubjectCopies,
          },
        ),
      );
    };

    // Get value props for SPONSORED_CONVERSATION
    const SPONSORED_CONVERSATION = async (): Promise<
      Result<string[], { type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" }>
    > => {
      return err({ type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" });
    };

    return ok({
      SPONSORED_CONTENT,
      SPONSORED_CONVERSATION,
      SPONSORED_INMAIL,
    });
  },

  async getLinkedInStateOrchestratorInput(input: {
    contenderVariantId: string;
    currentBestVariantId: string;
    adProgram: AdProgram;
    adSegment: AdSegment;
  }) {
    const SPONSORED_CONTENT = async (): Promise<
      Result<
        LinkedInStateInput,
        {
          type:
            | "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT"
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
        }
      >
    > => {
      return err({ type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" });
    };

    const SPONSORED_INMAIL = async (): Promise<
      Result<
        LinkedInStateInput,
        {
          type:
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
            | "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT";
        }
      >
    > => {
      const bestAudience = await deps.adSegmentBestVariantRepository.getOne(
        input.adSegment.id,
        "audience",
      );
      const audienceId =
        bestAudience?.variantId ??
        (
          await deps.linkedInCampaignRepository.getManyForAdSegment(
            input.adSegment.id,
          )
        )[0]?.linkedInAudienceId;

      if (!audienceId) {
        return err({ type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT" });
      }

      const currentBestConversation =
        await deps.conversationSubjectCopyRepository.getOneById(
          input.currentBestVariantId,
          "ACTIVE",
        );
      const contenderConversation =
        await deps.conversationSubjectCopyRepository.getOneById(
          input.contenderVariantId,
          "ACTIVE",
        );
      if (!currentBestConversation || !contenderConversation) {
        return err({ type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT" });
      }

      return ok(
        ConversationSubjectAbTestDomain.constructLinkerInStateOrchestratorInput.forSponsoredInmail(
          {
            adSegmentId: input.adSegment.id,
            audienceId: audienceId,
            currentBestConversationSubject: currentBestConversation,
            contenderConversationSubject: contenderConversation,
            bestConversationMessageCopyType: null,
            bestConversationCallToActionCopyType: null,
          },
        ),
      );
    };

    const SPONSORED_CONVERSATION = async (): Promise<
      Result<
        LinkedInStateInput,
        { type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" }
      >
    > => {
      return err({ type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" });
    };
    return ok({
      SPONSORED_CONTENT,
      SPONSORED_INMAIL,
      SPONSORED_CONVERSATION,
    });
  },

  async getMetricsForDecisionEngine(input: {
    adProgram: AdProgram;
    adSegment: AdSegment;
    contenderVariantId: string;
    currentBestVariantId: string;
    linkedInStateOutput: LinkedInStateOutput;
  }) {
    const SPONSORED_CONTENT = async (): Promise<
      Result<
        {
          currentBestMetrics: {
            primaryMetric: number;
            secondaryMetric: number;
          };
          contenderMetrics: { primaryMetric: number; secondaryMetric: number };
        },
        {
          type:
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
            | "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE";
        }
      >
    > => {
      return err({ type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" });
    };

    const SPONSORED_INMAIL = async (): Promise<
      Result<
        {
          currentBestMetrics: {
            primaryMetric: number;
            secondaryMetric: number;
          };
          contenderMetrics: { primaryMetric: number; secondaryMetric: number };
        },
        {
          type:
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
            | "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE";
        }
      >
    > => {
      const currentBestConversation =
        await deps.conversationSubjectCopyRepository.getOneById(
          input.currentBestVariantId,
          "ACTIVE",
        );
      const contenderConversation =
        await deps.conversationSubjectCopyRepository.getOneById(
          input.contenderVariantId,
          "ACTIVE",
        );

      if (!currentBestConversation || !contenderConversation) {
        return err({ type: "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE" });
      }

      const res =
        ConversationSubjectAbTestDomain.constructMetricsForDecisionEngine.forSponsoredInmail(
          {
            adProgram: input.adProgram,
            linkedInStateOutput: input.linkedInStateOutput,
            currentBestConversationSubject: currentBestConversation,
            contenderConversationSubject: contenderConversation,
          },
        );
      if (res.isErr()) {
        return err({ type: "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE" });
      }
      return ok(res.value);
    };

    const SPONSORED_CONVERSATION = async (): Promise<
      Result<
        {
          currentBestMetrics: {
            primaryMetric: number;
            secondaryMetric: number;
          };
          contenderMetrics: { primaryMetric: number; secondaryMetric: number };
        },
        {
          type:
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
            | "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE";
        }
      >
    > => {
      return err({ type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" });
    };

    return ok({
      SPONSORED_CONTENT: SPONSORED_CONTENT,
      SPONSORED_INMAIL: SPONSORED_INMAIL,
      SPONSORED_CONVERSATION: SPONSORED_CONVERSATION,
    });
  },
});
