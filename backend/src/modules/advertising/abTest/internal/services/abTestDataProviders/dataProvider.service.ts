import { err, ok, Result } from "neverthrow";

import { ILinkedInAdSegmentRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { LinkedInAdProgramRepository } from "../../../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInStateInput } from "../../../../linkedInStateOrchestrator/domain/linkedInStateInput.valueObject";
import { LinkedInStateOutput } from "../../../../linkedInStateOrchestrator/domain/linkedInStateOutput.entity";
import { AbTestType } from "../../domain/abTestType.valueObject";
import { DataProviderRegistry } from "./dataProvider.registry";

export class DataProviderService {
  constructor(
    private readonly ctx: {
      adSegmentRepository: ILinkedInAdSegmentRepository;
      adProgramRepository: LinkedInAdProgramRepository;
    },
  ) {}
  async getVariantsToSetupRounds(input: {
    adSegmentId: string;
    abTestType: AbTestType;
  }): Promise<
    Result<
      string[],
      {
        type:
          | "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS"
          | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
      }
    >
  > {
    const adSegment = await this.ctx.adSegmentRepository.getOne(
      input.adSegmentId,
    );
    if (!adSegment) {
      return err({
        type: "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS",
      });
    }
    const adProgram = await this.ctx.adProgramRepository.getOne(
      adSegment.linkedInAdProgramId,
    );
    if (!adProgram) {
      return err({
        type: "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS",
      });
    }

    const abTestDataProvider = DataProviderRegistry[input.abTestType];
    const getVariantsToSetupRoundsFunctionForAbTestType =
      await abTestDataProvider.getVariantsToSetupRounds({
        adProgram,
        adSegment,
      });
    if (getVariantsToSetupRoundsFunctionForAbTestType.isErr()) {
      return err({
        type: "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS",
      });
    }
    const res =
      await getVariantsToSetupRoundsFunctionForAbTestType.value[
        adProgram.adFormat.type
      ]();
    return res;
  }

  async getLinkedInStateOrchestratorInput(input: {
    adSegmentId: string;
    abTestType: AbTestType;
    currentBestVariantId: string;
    contenderVariantId: string;
  }): Promise<
    Result<
      LinkedInStateInput,
      {
        type:
          | "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT"
          | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
      }
    >
  > {
    const adSegment = await this.ctx.adSegmentRepository.getOne(
      input.adSegmentId,
    );
    if (!adSegment) {
      return err({
        type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT",
      });
    }

    const adProgram = await this.ctx.adProgramRepository.getOne(
      adSegment.linkedInAdProgramId,
    );
    if (!adProgram) {
      return err({
        type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT",
      });
    }
    const abTestDataProvider = DataProviderRegistry[input.abTestType];
    const getLinkedInStateOrchestratorInputFunctionForAbTestType =
      await abTestDataProvider.getLinkedInStateOrchestratorInput({
        adProgram,
        adSegment,
        currentBestVariantId: input.currentBestVariantId,
        contenderVariantId: input.contenderVariantId,
      });
    if (getLinkedInStateOrchestratorInputFunctionForAbTestType.isErr()) {
      return err({
        type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT",
      });
    }
    const res =
      await getLinkedInStateOrchestratorInputFunctionForAbTestType.value[
        adProgram.adFormat.type
      ]();
    return res;
  }

  async getMetricsForDecisionEngine(input: {
    currentBestVariantId: string;
    contenderVariantId: string;
    linkedInStateOutput: LinkedInStateOutput;
    abTestType: AbTestType;
  }): Promise<
    Result<
      {
        currentBestMetrics: {
          primaryMetric: number;
          secondaryMetric: number;
        };
        contenderMetrics: {
          primaryMetric: number;
          secondaryMetric: number;
        };
      },
      {
        type:
          | "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE"
          | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
      }
    >
  > {
    const adSegment = await this.ctx.adSegmentRepository.getOne(
      input.linkedInStateOutput.adSegmentId,
    );
    if (!adSegment) {
      return err({
        type: "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE",
      });
    }

    const adProgram = await this.ctx.adProgramRepository.getOne(
      adSegment.linkedInAdProgramId,
    );
    if (!adProgram) {
      return err({
        type: "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE",
      });
    }

    const abTestDataProvider = DataProviderRegistry[input.abTestType];
    const getMetricsForDecisionEngineFunctionForAbTestType =
      await abTestDataProvider.getMetricsForDecisionEngine({
        contenderVariantId: input.contenderVariantId,
        currentBestVariantId: input.currentBestVariantId,
        adProgram,
        adSegment,
        linkedInStateOutput: input.linkedInStateOutput,
      });
    if (getMetricsForDecisionEngineFunctionForAbTestType.isErr()) {
      return err({
        type: "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE",
      });
    }

    const res =
      await getMetricsForDecisionEngineFunctionForAbTestType.value[
        adProgram.adFormat.type
      ]();
    return res;
  }
}
