import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AbTestRoundDay } from "../domain/abTestRoundDay.entity";
import { AbTestType } from "../domain/abTestType.valueObject";

export interface IAbTestRoundDayRepository {
  createOne(
    abTestRoundDay: AbTestRoundDay,
    abTestType: AbTestType,
    tx: ITransaction,
  ): Promise<AbTestRoundDay>;
  getOne(
    abTestRoundDayId: string,
    abTestType: AbTestType,
    tx: ITransaction,
  ): Promise<AbTestRoundDay | null>;
  getAllForAbTestRound(
    abTestRoundId: string,
    abTestType: AbTestType,
    tx: ITransaction,
  ): Promise<AbTestRoundDay[]>;
  updateOne(
    abTestRoundDay: AbTestRoundDay,
    abTestType: AbTestType,
    tx: ITransaction,
  ): Promise<AbTestRoundDay>;
}
