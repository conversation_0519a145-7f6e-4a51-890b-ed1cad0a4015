import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AbTestRoundMetadata } from "../domain/abTestRoundMetadata.entity";
import { AbTestType } from "../domain/abTestType.valueObject";

export interface IAbTestRoundMetadataRepository {
  createOne(
    abTestRoundMetadata: AbTestRoundMetadata,
    abTestType: AbTestType,
    tx: ITransaction,
  ): Promise<AbTestRoundMetadata>;
  getOne(
    abTestRoundId: string,
    abTestType: AbTestType,
    tx: ITransaction,
  ): Promise<AbTestRoundMetadata | null>;
}
