import { eq } from "drizzle-orm";

import { Transaction } from "../../../../../database/dbTransactionType";
import { AbTestRoundDayMetrics } from "../domain/abTestRoundDayMetrics.entity";
import { AbTestType } from "../domain/abTestType.valueObject";
import { IAbTestRoundDayMetricsRepository } from "./abTestRoundDayMetrics.repository.interface";
import { abTestTableRegistry } from "./abTestTables.registry";

export class AbTestRoundDayMetricsRepository
  implements IAbTestRoundDayMetricsRepository
{
  async createOne(
    metrics: AbTestRoundDayMetrics,
    abTestType: AbTestType,
    tx: Transaction,
  ): Promise<AbTestRoundDayMetrics> {
    const metricsTable =
      abTestTableRegistry[abTestType].abTestRoundDayMetricsTable;
    await tx.insert(metricsTable).values({
      abTestRoundDayId: metrics.abTestRoundDayId,
      createdAt: metrics.createdAt,
      currentBestPrimaryMetricForDay:
        metrics.currentBestMetrics.primaryMetric.forDay,
      currentBestSecondaryMetricForDay:
        metrics.currentBestMetrics.secondaryMetric.forDay,
      contenderPrimaryMetricCountForDay:
        metrics.contenderMetrics.primaryMetric.forDay,
      contenderSecondaryMetricCountForDay:
        metrics.contenderMetrics.secondaryMetric.forDay,
      currentBestPrimaryMetricSum: metrics.currentBestMetrics.primaryMetric.sum,
      currentBestSecondaryMetricSum:
        metrics.currentBestMetrics.secondaryMetric.sum,
      contenderPrimaryMetricSum: metrics.contenderMetrics.primaryMetric.sum,
      contenderSecondaryMetricSum: metrics.contenderMetrics.secondaryMetric.sum,
      primaryMetricTTestResult: metrics.primaryMetricTTestResult,
      secondaryMetricTTestResult: metrics.secondaryMetricTTestResult,
      decision: metrics.decision,
      decisionType: metrics.decisionType,
      currentBestPrimaryMetricMean:
        metrics.currentBestMetrics.primaryMetric.mean,
      currentBestSecondaryMetricMean:
        metrics.currentBestMetrics.secondaryMetric.mean,
      contenderPrimaryMetricMean: metrics.contenderMetrics.primaryMetric.mean,
      contenderSecondaryMetricMean:
        metrics.contenderMetrics.secondaryMetric.mean,
    });
    return metrics;
  }

  async getOne(
    abTestRoundDayId: string,
    abTestType: AbTestType,
    tx: Transaction,
  ): Promise<AbTestRoundDayMetrics | null> {
    const metricsTable =
      abTestTableRegistry[abTestType].abTestRoundDayMetricsTable;
    const abTestRoundDayTable =
      abTestTableRegistry[abTestType].abTestRoundDayTable;
    const res = await tx
      .select({
        abTestRoundDayId: metricsTable.abTestRoundDayId,
        abTestRoundId: abTestRoundDayTable.abTestRoundId,
        dayIndex: abTestRoundDayTable.dayIndex,
        createdAt: metricsTable.createdAt,
        currentBestPrimaryMetricForDay:
          metricsTable.currentBestPrimaryMetricForDay,
        currentBestPrimaryMetricSum: metricsTable.currentBestPrimaryMetricSum,
        currentBestSecondaryMetricForDay:
          metricsTable.currentBestSecondaryMetricForDay,
        currentBestSecondaryMetricSum:
          metricsTable.currentBestSecondaryMetricSum,
        contenderPrimaryMetricForDay:
          metricsTable.contenderPrimaryMetricCountForDay,
        contenderPrimaryMetricSum: metricsTable.contenderPrimaryMetricSum,
        contenderSecondaryMetricForDay:
          metricsTable.contenderSecondaryMetricCountForDay,
        contenderSecondaryMetricSum: metricsTable.contenderSecondaryMetricSum,
        primaryMetricTTestResult: metricsTable.primaryMetricTTestResult,
        secondaryMetricTTestResult: metricsTable.secondaryMetricTTestResult,
        decision: metricsTable.decision,
        decisionType: metricsTable.decisionType,
        currentBestPrimaryMetricMean: metricsTable.currentBestPrimaryMetricMean,
        currentBestSecondaryMetricMean:
          metricsTable.currentBestSecondaryMetricMean,
        contenderPrimaryMetricMean: metricsTable.contenderPrimaryMetricMean,
        contenderSecondaryMetricMean: metricsTable.contenderSecondaryMetricMean,
      })
      .from(metricsTable)
      .innerJoin(
        abTestRoundDayTable,
        eq(metricsTable.abTestRoundDayId, abTestRoundDayTable.id),
      )
      .where(eq(metricsTable.abTestRoundDayId, abTestRoundDayId));
    if (!res[0]) {
      return null;
    }
    return AbTestRoundDayMetrics({
      abTestRoundDayId: res[0].abTestRoundDayId,
      abTestRoundId: res[0].abTestRoundId,
      dayIndex: res[0].dayIndex,
      createdAt: res[0].createdAt,
      currentBestMetrics: {
        primaryMetric: {
          forDay: res[0].currentBestPrimaryMetricForDay,
          sum: res[0].currentBestPrimaryMetricSum,
          mean: res[0].currentBestPrimaryMetricMean,
        },
        secondaryMetric: {
          forDay: res[0].currentBestSecondaryMetricForDay,
          sum: res[0].currentBestSecondaryMetricSum,
          mean: res[0].currentBestSecondaryMetricMean,
        },
      },
      contenderMetrics: {
        primaryMetric: {
          forDay: res[0].contenderPrimaryMetricForDay,
          sum: res[0].contenderPrimaryMetricSum,
          mean: res[0].contenderPrimaryMetricMean,
        },
        secondaryMetric: {
          forDay: res[0].contenderSecondaryMetricForDay,
          sum: res[0].contenderSecondaryMetricSum,
          mean: res[0].contenderSecondaryMetricMean,
        },
      },
      primaryMetricTTestResult: res[0].primaryMetricTTestResult,
      secondaryMetricTTestResult: res[0].secondaryMetricTTestResult,
      decision: res[0].decision,
      decisionType: res[0].decisionType,
    });
  }

  async getAllForAbTestRound(
    abTestRoundId: string,
    abTestType: AbTestType,
    tx: Transaction,
  ): Promise<AbTestRoundDayMetrics[]> {
    const metricsTable =
      abTestTableRegistry[abTestType].abTestRoundDayMetricsTable;
    const abTestRoundDayTable =
      abTestTableRegistry[abTestType].abTestRoundDayTable;
    const res = await tx
      .select({
        abTestRoundDayId: metricsTable.abTestRoundDayId,
        abTestRoundId: abTestRoundDayTable.abTestRoundId,
        dayIndex: abTestRoundDayTable.dayIndex,
        createdAt: metricsTable.createdAt,
        currentBestPrimaryMetricForDay:
          metricsTable.currentBestPrimaryMetricForDay,
        currentBestPrimaryMetricSum: metricsTable.currentBestPrimaryMetricSum,
        currentBestSecondaryMetricForDay:
          metricsTable.currentBestSecondaryMetricForDay,
        currentBestSecondaryMetricSum:
          metricsTable.currentBestSecondaryMetricSum,
        contenderPrimaryMetricForDay:
          metricsTable.contenderPrimaryMetricCountForDay,
        contenderPrimaryMetricSum: metricsTable.contenderPrimaryMetricSum,
        contenderSecondaryMetricForDay:
          metricsTable.contenderSecondaryMetricCountForDay,
        contenderSecondaryMetricSum: metricsTable.contenderSecondaryMetricSum,
        primaryMetricTTestResult: metricsTable.primaryMetricTTestResult,
        secondaryMetricTTestResult: metricsTable.secondaryMetricTTestResult,
        decision: metricsTable.decision,
        decisionType: metricsTable.decisionType,
        currentBestPrimaryMetricMean: metricsTable.currentBestPrimaryMetricMean,
        currentBestSecondaryMetricMean:
          metricsTable.currentBestSecondaryMetricMean,
        contenderPrimaryMetricMean: metricsTable.contenderPrimaryMetricMean,
        contenderSecondaryMetricMean: metricsTable.contenderSecondaryMetricMean,
      })
      .from(metricsTable)
      .innerJoin(
        abTestRoundDayTable,
        eq(metricsTable.abTestRoundDayId, abTestRoundDayTable.id),
      )
      .where(eq(abTestRoundDayTable.abTestRoundId, abTestRoundId));
    return res.map((r) =>
      AbTestRoundDayMetrics({
        abTestRoundDayId: r.abTestRoundDayId,
        abTestRoundId: r.abTestRoundId,
        dayIndex: r.dayIndex,
        createdAt: r.createdAt,
        currentBestMetrics: {
          primaryMetric: {
            forDay: r.currentBestPrimaryMetricForDay,
            sum: r.currentBestPrimaryMetricSum,
            mean: r.currentBestPrimaryMetricMean,
          },
          secondaryMetric: {
            forDay: r.currentBestSecondaryMetricForDay,
            sum: r.currentBestSecondaryMetricSum,
            mean: r.currentBestSecondaryMetricMean,
          },
        },
        contenderMetrics: {
          primaryMetric: {
            forDay: r.contenderPrimaryMetricForDay,
            sum: r.contenderPrimaryMetricSum,
            mean: r.contenderPrimaryMetricMean,
          },
          secondaryMetric: {
            forDay: r.contenderSecondaryMetricForDay,
            sum: r.contenderSecondaryMetricSum,
            mean: r.contenderSecondaryMetricMean,
          },
        },
        primaryMetricTTestResult: r.primaryMetricTTestResult,
        secondaryMetricTTestResult: r.secondaryMetricTTestResult,
        decision: r.decision,
        decisionType: r.decisionType,
      }),
    );
  }
}
