import { Result } from "neverthrow";

import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AbTest } from "../domain/abTest.entity";
import { AbTestType } from "../domain/abTestType.valueObject";

export interface IAbTestRepository {
  createOne(
    input: AbTest,
    tx?: ITransaction,
  ): Promise<
    Result<
      AbTest,
      {
        type: "STAGE_NOT_FOUND" | "AB_TEST_ALREADY_EXISTS_FOR_STAGE";
      }
    >
  >;
  updateOne(input: AbTest, tx: ITransaction): Promise<void>;
  getOne(
    stageId: string,
    type: AbTestType,
    tx?: ITransaction,
  ): Promise<AbTest | null>;
}
