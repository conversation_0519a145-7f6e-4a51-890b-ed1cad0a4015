import { eq } from "drizzle-orm";

import { db, Transaction } from "../../../../../database/db";
import { adSegmentBestVariantTableRegistry } from "../../../infrastructure/repositories/adSegmentBestVariant.repository";
import { AdSegmentBestVariant } from "../domain/adSegmentBestVarient.entity";
import { IAdSegmentBestVariantRepository } from "./adSegmentBestVariant.repository.interface";

export class AdSegmentBestVariantRepository
  implements IAdSegmentBestVariantRepository
{
  async upsertOne(
    adSegmentBestVariant: AdSegmentBestVariant,
    tx: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    const table = adSegmentBestVariantTableRegistry[adSegmentBestVariant.type];
    await invoker
      .insert(table)
      .values({
        id: adSegmentBestVariant.id,
        adSegmentId: adSegmentBestVariant.adSegmentId,
        variantId: adSegmentBestVariant.variantId,
      })
      .onConflictDoUpdate({
        target: [table.adSegmentId],
        set: {
          variantId: adSegmentBestVariant.variantId,
        },
      });
  }
  async getOne(
    adSegmentId: string,
    type: AdSegmentBestVariant["type"],
    tx: Transaction,
  ): Promise<AdSegmentBestVariant | null> {
    const invoker = tx ?? db;
    const table = adSegmentBestVariantTableRegistry[type];
    const result = await invoker
      .select()
      .from(table)
      .where(eq(table.adSegmentId, adSegmentId));
    if (!result[0]) {
      return null;
    }
    return {
      id: result[0].id,
      adSegmentId: result[0].adSegmentId,
      variantId: result[0].variantId,
      type: type,
    };
  }
}
