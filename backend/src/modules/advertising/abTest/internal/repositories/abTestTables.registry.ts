import {
  IAbTestRoundDayMetricsTable,
  IAbTestRoundDayTable,
  IAbTestRoundMetadataTable,
  IAbTestRoundTable,
  IAbTestTable,
} from "../../../../../database/schemas/advertising/abTests/_baseAbTestTableSchemaUtils";
import { IAdSegmentBestVariantTable } from "../../../../../database/schemas/advertising/abTests/_baseAdSegmentBestVariantUtils";
import {
  audienceTestRoundDayMetricsTable,
  audienceTestRoundDayTable,
  audienceTestRoundMetadataTable,
  audienceTestRoundTable,
  audienceTestTable,
  conversationCallToActionTestRoundDayMetricsTable,
  conversationCallToActionTestRoundDayTable,
  conversationCallToActionTestRoundMetadataTable,
  conversationCallToActionTestRoundTable,
  conversationCallToActionTestTable,
  conversationMessageCopyTestRoundDayMetricsTable,
  conversationMessageCopyTestRoundDayTable,
  conversationMessageCopyTestRoundMetadataTable,
  conversationMessageCopyTestRoundTable,
  conversationMessageCopyTestTable,
  conversationSubjectTestRoundDayMetricsTable,
  conversationSubjectTestRoundDayTable,
  conversationSubjectTestRoundMetadataTable,
  conversationSubjectTestRoundTable,
  conversationSubjectTestTable,
  creativeTestRoundDayMetricsTable,
  creativeTestRoundDayTable,
  creativeTestRoundMetadataTable,
  creativeTestRoundTable,
  creativeTestTable,
  socialPostBodyCopyTestRoundDayMetricsTable,
  socialPostBodyCopyTestRoundDayTable,
  socialPostBodyCopyTestRoundMetadataTable,
  socialPostBodyCopyTestRoundTable,
  socialPostBodyCopyTestTable,
  socialPostCallToActionTestRoundDayMetricsTable,
  socialPostCallToActionTestRoundDayTable,
  socialPostCallToActionTestRoundMetadataTable,
  socialPostCallToActionTestRoundTable,
  socialPostCallToActionTestTable,
  valuePropTestRoundDayMetricsTable,
  valuePropTestRoundDayTable,
  valuePropTestRoundMetadataTable,
  valuePropTestRoundTable,
  valuePropTestTable,
} from "../../../../../database/schemas/advertising/abTests/abTestTables";
import {
  adSegmentBestAudienceVariantTable,
  adSegmentBestConversationCallToActionCopyVariantTable,
  adSegmentBestConversationMessageCopyVariantTable,
  adSegmentBestConversationSubjectVariantTable,
  adSegmentBestCreativeVariantTable,
  adSegmentBestSocialPostBodyCopyVariantTable,
  adSegmentBestSocialPostCallToActionCopyVariantTable,
  adSegmentBestValuePropVariantTable,
} from "../../../../../database/schemas/advertising/abTests/adsegmentBestVariantTables";
import { AbTestType } from "../domain/abTestType.valueObject";

type AbTestRepositoryMap = {
  [K in AbTestType]: {
    abTestTable: IAbTestTable;
    abTestRoundTable: IAbTestRoundTable;
    abTestRoundDayTable: IAbTestRoundDayTable;
    bestVariantTable: IAdSegmentBestVariantTable;
    abTestRoundDayMetricsTable: IAbTestRoundDayMetricsTable;
    abTestRoundMetadataTable: IAbTestRoundMetadataTable;
  };
};
export const abTestTableRegistry: AbTestRepositoryMap = {
  audience: {
    abTestTable: audienceTestTable,
    abTestRoundTable: audienceTestRoundTable,
    abTestRoundDayTable: audienceTestRoundDayTable,
    bestVariantTable: adSegmentBestAudienceVariantTable,
    abTestRoundDayMetricsTable: audienceTestRoundDayMetricsTable,
    abTestRoundMetadataTable: audienceTestRoundMetadataTable,
  },
  valueProp: {
    abTestTable: valuePropTestTable,
    abTestRoundTable: valuePropTestRoundTable,
    abTestRoundDayTable: valuePropTestRoundDayTable,
    bestVariantTable: adSegmentBestValuePropVariantTable,
    abTestRoundDayMetricsTable: valuePropTestRoundDayMetricsTable,
    abTestRoundMetadataTable: valuePropTestRoundMetadataTable,
  },
  creative: {
    abTestTable: creativeTestTable,
    abTestRoundTable: creativeTestRoundTable,
    abTestRoundDayTable: creativeTestRoundDayTable,
    bestVariantTable: adSegmentBestCreativeVariantTable,
    abTestRoundDayMetricsTable: creativeTestRoundDayMetricsTable,
    abTestRoundMetadataTable: creativeTestRoundMetadataTable,
  },
  conversationSubject: {
    abTestTable: conversationSubjectTestTable,
    abTestRoundTable: conversationSubjectTestRoundTable,
    abTestRoundDayTable: conversationSubjectTestRoundDayTable,
    bestVariantTable: adSegmentBestConversationSubjectVariantTable,
    abTestRoundDayMetricsTable: conversationSubjectTestRoundDayMetricsTable,
    abTestRoundMetadataTable: conversationSubjectTestRoundMetadataTable,
  },
  conversationMessageCopy: {
    abTestTable: conversationMessageCopyTestTable,
    abTestRoundTable: conversationMessageCopyTestRoundTable,
    abTestRoundDayTable: conversationMessageCopyTestRoundDayTable,
    bestVariantTable: adSegmentBestConversationMessageCopyVariantTable,
    abTestRoundDayMetricsTable: conversationMessageCopyTestRoundDayMetricsTable,
    abTestRoundMetadataTable: conversationMessageCopyTestRoundMetadataTable,
  },
  conversationCallToAction: {
    abTestTable: conversationCallToActionTestTable,
    abTestRoundTable: conversationCallToActionTestRoundTable,
    abTestRoundDayTable: conversationCallToActionTestRoundDayTable,
    bestVariantTable: adSegmentBestConversationCallToActionCopyVariantTable,
    abTestRoundDayMetricsTable:
      conversationCallToActionTestRoundDayMetricsTable,
    abTestRoundMetadataTable: conversationCallToActionTestRoundMetadataTable,
  },
  socialPostBodyCopy: {
    abTestTable: socialPostBodyCopyTestTable,
    abTestRoundTable: socialPostBodyCopyTestRoundTable,
    abTestRoundDayTable: socialPostBodyCopyTestRoundDayTable,
    bestVariantTable: adSegmentBestSocialPostBodyCopyVariantTable,
    abTestRoundDayMetricsTable: socialPostBodyCopyTestRoundDayMetricsTable,
    abTestRoundMetadataTable: socialPostBodyCopyTestRoundMetadataTable,
  },
  socialPostCallToAction: {
    abTestTable: socialPostCallToActionTestTable,
    abTestRoundTable: socialPostCallToActionTestRoundTable,
    abTestRoundDayTable: socialPostCallToActionTestRoundDayTable,
    bestVariantTable: adSegmentBestSocialPostCallToActionCopyVariantTable,
    abTestRoundDayMetricsTable: socialPostCallToActionTestRoundDayMetricsTable,
    abTestRoundMetadataTable: socialPostCallToActionTestRoundMetadataTable,
  },
};
