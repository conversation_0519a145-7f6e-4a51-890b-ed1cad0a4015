import { and, eq, inArray, sql, SQL } from "drizzle-orm";

import { db } from "../../../../../database/db";
import { Transaction } from "../../../../../database/dbTransactionType";
import { AbTestRound } from "../domain/abTestRound.entity";
import { AbTestType } from "../domain/abTestType.valueObject";
import { IAbTestRoundRepository } from "./abTestRound.repository.interface";
import { abTestTableRegistry } from "./abTestTables.registry";

export class AbTestRoundRepository implements IAbTestRoundRepository {
  async getOne(
    abTestRoundId: string,
    type: AbTestType,
    tx: Transaction,
  ): Promise<AbTestRound | null> {
    const invoker = tx ?? db;
    const abTestRoundTable = abTestTableRegistry[type].abTestRoundTable;
    const res = await invoker
      .select()
      .from(abTestRoundTable)
      .where(eq(abTestRoundTable.id, abTestRoundId));
    if (!res[0]) {
      return null;
    }
    if (
      res[0].status === "COMPLETED" ||
      res[0].status === "AUTO_RESOLVED" ||
      res[0].status === "USER_RESOLVED"
    ) {
      if (!res[0].winner) {
        throw new Error("Winner is null when round is completed");
      }
      return AbTestRound({
        id: res[0].id,
        abTestId: res[0].abTestId,
        status: res[0].status,
        roundIndex: res[0].roundIndex,
        currentBestId: res[0].currentBestId,
        contenderId: res[0].contenderId,
        winner: res[0].winner,
      });
    }
    return AbTestRound({
      id: res[0].id,
      abTestId: res[0].abTestId,
      status: res[0].status,
      roundIndex: res[0].roundIndex,
      currentBestId: res[0].currentBestId,
      contenderId: res[0].contenderId,
      winner: null,
    });
  }

  async getAllForAbTest(
    abTestId: string,
    type: AbTestType,
    tx: Transaction,
  ): Promise<AbTestRound[]> {
    const invoker = tx ?? db;
    const abTestRoundTable = abTestTableRegistry[type].abTestRoundTable;
    const res = await invoker
      .select()
      .from(abTestRoundTable)
      .where(eq(abTestRoundTable.abTestId, abTestId));
    const rounds: AbTestRound[] = [];
    for (const round of res) {
      if (
        round.status === "COMPLETED" ||
        round.status === "AUTO_RESOLVED" ||
        round.status === "USER_RESOLVED"
      ) {
        if (!round.winner) {
          throw new Error("Winner is null when round is completed");
        }
        rounds.push(
          AbTestRound({
            id: round.id,
            abTestId: round.abTestId,
            status: round.status,
            roundIndex: round.roundIndex,
            currentBestId: round.currentBestId,
            contenderId: round.contenderId,
            winner: round.winner,
          }),
        );
      } else {
        rounds.push(
          AbTestRound({
            id: round.id,
            abTestId: round.abTestId,
            status: round.status,
            roundIndex: round.roundIndex,
            currentBestId: round.currentBestId,
            contenderId: round.contenderId,
            winner: null,
          }),
        );
      }
    }
    return rounds;
  }

  async createMany(
    rounds: AbTestRound[],
    type: AbTestType,
    tx: Transaction,
  ): Promise<AbTestRound[]> {
    const invoker = tx ?? db;
    const abTestRoundTable = abTestTableRegistry[type].abTestRoundTable;
    if (rounds.length === 0) {
      return [];
    }
    await invoker.insert(abTestRoundTable).values(rounds);
    return rounds;
  }

  async update(
    round: AbTestRound,
    type: AbTestType,
    tx: Transaction,
  ): Promise<AbTestRound> {
    const invoker = tx ?? db;
    const abTestRoundTable = abTestTableRegistry[type].abTestRoundTable;
    await invoker
      .update(abTestRoundTable)
      .set(round)
      .where(eq(abTestRoundTable.id, round.id));
    return round;
  }

  async updateCurrentBestIdForNonStartedRoundsForAbTest(
    abTestId: string,
    currentBestId: string,
    type: AbTestType,
    tx: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    const abTestRoundTable = abTestTableRegistry[type].abTestRoundTable;
    await invoker
      .update(abTestRoundTable)
      .set({ currentBestId: currentBestId })
      .where(
        and(
          eq(abTestRoundTable.abTestId, abTestId),
          eq(abTestRoundTable.status, "NOT_STARTED"),
        ),
      );
  }
}
