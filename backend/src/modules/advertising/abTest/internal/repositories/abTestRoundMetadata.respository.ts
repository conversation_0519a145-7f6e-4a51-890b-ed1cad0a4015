import { Transaction } from "../../../../../database/dbTransactionType";
import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AbTestRoundMetadata } from "../domain/abTestRoundMetadata.entity";
import { AbTestType } from "../domain/abTestType.valueObject";
import { IAbTestRoundMetadataRepository } from "./abTestRoundMetadata.repository.interface";
import { abTestTableRegistry } from "./abTestTables.registry";

export class AbTestRoundMetadataRepository
  implements IAbTestRoundMetadataRepository
{
  async createOne(
    abTestRoundMetadata: AbTestRoundMetadata,
    abTestType: AbTestType,
    tx: Transaction,
  ): Promise<AbTestRoundMetadata> {
    const abTestRoundMetadataTable =
      abTestTableRegistry[abTestType].abTestRoundMetadataTable;
    await tx.insert(abTestRoundMetadataTable).values({

    });
    return abTestRoundMetadata;
  }

  getOne(
    abTestRoundId: string,
    abTestType: AbTestType,
    tx: Transaction,
  ): Promise<AbTestRoundMetadata | null> {
    throw new Error("Method not implemented.");
  }
}
