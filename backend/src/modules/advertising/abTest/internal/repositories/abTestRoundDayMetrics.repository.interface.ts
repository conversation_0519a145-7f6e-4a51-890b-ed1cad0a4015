import { Result } from "neverthrow";

import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { AbTestRoundDayMetrics } from "../domain/abTestRoundDayMetrics.entity";
import { AbTestType } from "../domain/abTestType.valueObject";

export interface IAbTestRoundDayMetricsRepository {
  createOne(
    metrics: AbTestRoundDayMetrics,
    abTestType: AbTestType,
    tx: ITransaction,
  ): Promise<AbTestRoundDayMetrics>;
  getOne(
    abTestRoundDayId: string,
    abTestType: AbTestType,
    tx: ITransaction,
  ): Promise<AbTestRoundDayMetrics | null>;
  getAllForAbTestRound(
    abTestRoundId: string,
    abTestType: AbTestType,
    tx: ITransaction,
  ): Promise<AbTestRoundDayMetrics[]>;
}
