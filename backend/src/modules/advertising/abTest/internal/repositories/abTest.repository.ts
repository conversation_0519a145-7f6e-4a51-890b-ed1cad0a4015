import { eq } from "drizzle-orm";
import { err, ok, Result } from "neverthrow";

import { db, Transaction } from "../../../../../database/db";
import { linkedInAdAccountTable } from "../../../../../database/schemas/advertising/linkedInAdAccount.table";
import { AbTest } from "../domain/abTest.entity";
import { AbTestType } from "../domain/abTestType.valueObject";
import { IAbTestRepository } from "./abTest.repository.interface";
import { abTestTableRegistry } from "./abTestTables.registry";

export class AbTestRepository implements IAbTestRepository {
  async createOne(
    input: AbTest,
    tx: Transaction,
  ): Promise<
    Result<
      AbTest,
      { type: "STAGE_NOT_FOUND" | "AB_TEST_ALREADY_EXISTS_FOR_STAGE" }
    >
  > {
    const invoker = tx ?? db;
    const abTestTable = abTestTableRegistry[input.type];
    try {
      await invoker.insert(abTestTable.abTestTable).values(input);
      return ok(input);
    } catch (error) {
      const errObject = error as {
        code: string;
      };
      if (errObject.code === "23505") {
        return err({
          type: "AB_TEST_ALREADY_EXISTS_FOR_STAGE",
        });
      }
      if (errObject.code === "23503") {
        return err({
          type: "STAGE_NOT_FOUND",
        });
      }
      throw error;
    }
  }

  async updateOne(input: AbTest, tx: Transaction): Promise<void> {
    const invoker = tx ?? db;
    const abTestTable = abTestTableRegistry[input.type].abTestTable;
    await invoker
      .update(abTestTable)
      .set(input)
      .where(eq(abTestTable.stageId, input.stageId));
  }

  async getOne(
    stageId: string,
    type: AbTestType,
    tx?: Transaction,
  ): Promise<AbTest | null> {
    const invoker = tx ?? db;
    const abTestTable = abTestTableRegistry[type].abTestTable;
    const res = await invoker
      .select()
      .from(abTestTable)
      .where(eq(abTestTable.stageId, stageId));
    if (!res[0]) {
      return null;
    }

    if (
      res[0].status !== "COMPLETED" &&
      res[0].status !== "AUTO_RESOLVED" &&
      res[0].status !== "USER_RESOLVED"
    ) {
      return AbTest({
        stageId: res[0].stageId,
        type: type,
        status: res[0].status,
        winnerId: null,
      });
    } else {
      if (!res[0].winnerId) {
        throw new Error("WinnerId is null");
      }
      return AbTest({
        stageId: res[0].stageId,
        type: type,
        status: res[0].status,
        winnerId: res[0].winnerId,
      });
    }
  }
}
