import { err, ok, Result } from "neverthrow";

import { AbTestRoundDomain } from "../../domain/abTestRound.domain";
import { AbTestRound } from "../../domain/abTestRound.entity";
import { IAbTestRoundRepository } from "../../repositories/abTestRound.repository.interface";
import { EndAbTestRoundCommand } from "./endAbTestRound.command.interface";

interface EndAbTestRoundError {
  type: "AB_TEST_ROUND_NOT_FOUND" | "AB_TEST_ROUND_NOT_IN_PROGRESS";
}

export class EndAbTestRoundCommandHandler {
  constructor(
    private readonly ctx: {
      abTestRoundRepository: IAbTestRoundRepository;
    },
  ) {}

  async execute(
    command: EndAbTestRoundCommand,
  ): Promise<Result<AbTestRound, EndAbTestRoundError>> {
    const abTestRound = await this.ctx.abTestRoundRepository.getOne(
      command.abTestRoundId,
      command.abTestType,
      command.tx,
    );

    if (!abTestRound) {
      return err({ type: "AB_TEST_ROUND_NOT_FOUND" });
    }

    if (abTestRound.status !== "IN_PROGRESS") {
      return err({ type: "AB_TEST_ROUND_NOT_IN_PROGRESS" });
    }

    const completedRound = AbTestRoundDomain.endAbTestRound({
      abTestRound,
      winner: command.winner,
    });

    if (completedRound.isErr()) {
      return err(completedRound.error);
    }

    await this.ctx.abTestRoundRepository.update(
      completedRound.value,
      command.abTestType,
      command.tx,
    );

    return ok(completedRound.value);
  }
}
