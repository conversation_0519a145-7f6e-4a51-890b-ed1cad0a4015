import { err, ok, Result } from "neverthrow";

import { AbTestRoundDomain } from "../../domain/abTestRound.domain";
import { AbTestRound } from "../../domain/abTestRound.entity";
import { AbTestType } from "../../domain/abTestType.valueObject";
import { IAbTestRepository } from "../../repositories/abTest.repository.interface";
import { IAbTestRoundRepository } from "../../repositories/abTestRound.repository.interface";
import { StartNextAbTestRoundCommand } from "./startNextAbTestRound.command.interface";

interface StartNextAbTestRoundError {
  type:
    | "AB_TEST_NOT_FOUND"
    | "NO_ROUNDS_FOUND"
    | "AB_TEST_NOT_IN_PROGRESS"
    | "NO_ROUNDS_TO_START"
    | "ROUND_ALREADY_IN_PROGRESS";
}

export class StartNextAbTestRoundCommandHandler {
  constructor(
    private readonly ctx: {
      abTestRepository: IAbTestRepository;
      abTestRoundRepository: IAbTestRoundRepository;
    },
  ) {}
  async execute(
    command: StartNextAbTestRoundCommand,
  ): Promise<Result<AbTestRound, StartNextAbTestRoundError>> {
    const abTest = await this.ctx.abTestRepository.getOne(
      command.abTestId,
      command.abTestType,
      command.tx,
    );
    if (!abTest) {
      return err({ type: "AB_TEST_NOT_FOUND" });
    }
    if (abTest.status !== "IN_PROGRESS") {
      return err({ type: "AB_TEST_NOT_IN_PROGRESS" });
    }

    const roundsInAbTest = await this.ctx.abTestRoundRepository.getAllForAbTest(
      command.abTestId,
      command.abTestType,
      command.tx,
    );
    const startNextRoundResult = AbTestRoundDomain.selectAndStartNextRound({
      abTest: abTest,
      roundsInAbTest: roundsInAbTest,
    });
    if (startNextRoundResult.isErr()) {
      return err(startNextRoundResult.error);
    }
    await this.ctx.abTestRoundRepository.update(
      startNextRoundResult.value.startedRound,
      command.abTestType,
      command.tx,
    );
    return ok(startNextRoundResult.value.startedRound);
  }
}
