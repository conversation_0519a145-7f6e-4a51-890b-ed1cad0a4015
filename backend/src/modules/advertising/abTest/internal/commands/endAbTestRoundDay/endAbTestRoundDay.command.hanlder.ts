import { err, ok, Result } from "neverthrow";

import { LinkedInAdProgramRepositoryInterface } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdProgram.repository.interface";
import { LinkedInStateOrchestratorService } from "../../../../linkedInStateOrchestrator/application/linkedInStateOrchestrator.service";
import { AbTestRoundDayDomain } from "../../domain/abTestRoundDay.domain";
import { AbTestRoundDay } from "../../domain/abTestRoundDay.entity";
import { AbTestRoundDayMetricsDomain } from "../../domain/abTestRoundDayMetrics.domain";
import { DecisionEngine } from "../../domain/decisionEngine.domain";
import { IAbTestRepository } from "../../repositories/abTest.repository.interface";
import { IAbTestRoundRepository } from "../../repositories/abTestRound.repository.interface";
import { IAbTestRoundDayRepository } from "../../repositories/abTestRoundDay.repository.interface";
import { IAbTestRoundDayMetricsRepository } from "../../repositories/abTestRoundDayMetrics.repository.interface";
import { DataProviderService } from "../../services/abTestDataProviders/dataProvider.service";
import { EndAbTestRoundDayCommand } from "./endAbTestRoundDay.command.interface";

interface EndAbTestRoundDayError {
  type:
    | "AB_TEST_ROUND_DAY_NOT_FOUND"
    | "AB_TEST_ROUND_DAY_NOT_IN_PROGRESS"
    | "COULD_NOT_STOP_LINKEDIN_STATE"
    | "COULD_NOT_GET_LINKEDIN_STATE_METRICS"
    | "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE"
    | "COULD_NOT_GET_DECISION"
    | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
}

export class EndAbTestRoundDayCommandHandler {
  constructor(
    private readonly ctx: {
      abTestRoundRepository: IAbTestRoundRepository;
      abTestRoundDayRepository: IAbTestRoundDayRepository;
      linkedInStateOrchestrator: LinkedInStateOrchestratorService;
      dataProviderService: DataProviderService;
      decisionEngine: DecisionEngine;
      abTestRoundDayMetricsRepository: IAbTestRoundDayMetricsRepository;
    },
  ) {}

  async execute(
    command: EndAbTestRoundDayCommand,
  ): Promise<Result<AbTestRoundDay, EndAbTestRoundDayError>> {
    const abTestRoundDay = await this.ctx.abTestRoundDayRepository.getOne(
      command.abTestRoundDayId,
      command.abTestType,
      command.tx,
    );
    if (!abTestRoundDay) {
      return err({ type: "AB_TEST_ROUND_DAY_NOT_FOUND" });
    }
    if (abTestRoundDay.status !== "IN_PROGRESS") {
      return err({ type: "AB_TEST_ROUND_DAY_NOT_IN_PROGRESS" });
    }

    const abTestRound = await this.ctx.abTestRoundRepository.getOne(
      abTestRoundDay.abTestRoundId,
      command.abTestType,
      command.tx,
    );
    if (!abTestRound) {
      throw new Error("Ab test round not found");
    }

    const stopLinkedInStateResult =
      await this.ctx.linkedInStateOrchestrator.stopLinkedInState(
        abTestRoundDay.deploymentConfigId,
        command.tx,
      );
    if (stopLinkedInStateResult.isErr()) {
      return err({ type: "COULD_NOT_STOP_LINKEDIN_STATE" });
    }

    const metrics =
      await this.ctx.linkedInStateOrchestrator.getLinkedInStateMetrics(
        abTestRoundDay.deploymentConfigId,
        command.tx,
      );
    if (metrics.isErr()) {
      return err({ type: "COULD_NOT_GET_LINKEDIN_STATE_METRICS" });
    }

    const metricsForDecisionEngine =
      await this.ctx.dataProviderService.getMetricsForDecisionEngine({
        currentBestVariantId: abTestRound.currentBestId,
        contenderVariantId: abTestRound.contenderId,
        linkedInStateOutput: metrics.value,
        abTestType: command.abTestType,
      });

    if (metricsForDecisionEngine.isErr()) {
      return err(metricsForDecisionEngine.error);
    }
    const metricsForRound =
      await this.ctx.abTestRoundDayMetricsRepository.getAllForAbTestRound(
        abTestRoundDay.abTestRoundId,
        command.abTestType,
        command.tx,
      );

    const currentBestMetrics: {
      primaryMetric: number;
      secondaryMetric: number;
    }[] = [
      {
        primaryMetric:
          metricsForDecisionEngine.value.currentBestMetrics.primaryMetric,
        secondaryMetric:
          metricsForDecisionEngine.value.currentBestMetrics.secondaryMetric,
      },
      ...metricsForRound.map((r) => ({
        primaryMetric: r.currentBestMetrics.primaryMetric.forDay,
        secondaryMetric: r.currentBestMetrics.secondaryMetric.forDay,
      })),
    ];
    const contenderPrimaryMetrics: {
      primaryMetric: number;
      secondaryMetric: number;
    }[] = [
      {
        primaryMetric:
          metricsForDecisionEngine.value.contenderMetrics.primaryMetric,
        secondaryMetric:
          metricsForDecisionEngine.value.contenderMetrics.secondaryMetric,
      },
      ...metricsForRound.map((r) => ({
        primaryMetric: r.contenderMetrics.primaryMetric.forDay,
        secondaryMetric: r.contenderMetrics.secondaryMetric.forDay,
      })),
    ];

    const decision = this.ctx.decisionEngine.getDecision({
      firstVariant: currentBestMetrics,
      secondVariant: contenderPrimaryMetrics,
    });
    if (decision.isErr()) {
      return err({ type: "COULD_NOT_GET_DECISION" });
    }

    const completedAbTestRoundDay = AbTestRoundDayDomain.endRoundDay({
      abTestRoundDay,
      winner:
        decision.value.decision === "FIRST_VARIANT"
          ? "CURRENT_BEST"
          : decision.value.decision === "SECOND_VARIANT"
            ? "CONTENDER"
            : "NO_DECISION",
      currentBestResult: Math.round(
        metricsForDecisionEngine.value.currentBestMetrics.primaryMetric,
      ),
      contenderResult: Math.round(
        metricsForDecisionEngine.value.contenderMetrics.primaryMetric,
      ),
    });
    if (completedAbTestRoundDay.isErr()) {
      return err(completedAbTestRoundDay.error);
    }

    await this.ctx.abTestRoundDayRepository.updateOne(
      completedAbTestRoundDay.value,
      command.abTestType,
      command.tx,
    );

    const completedAbTestRoundDayMetrics = AbTestRoundDayMetricsDomain.create({
      abTestRoundDayId: completedAbTestRoundDay.value.id,
      abTestRoundId: completedAbTestRoundDay.value.abTestRoundId,
      dayIndex: completedAbTestRoundDay.value.dayIndex,
      createdAt: new Date(),
      currentBestMetrics: {
        primaryMetric: {
          forDay:
            metricsForDecisionEngine.value.currentBestMetrics.primaryMetric,
          sum: decision.value.currentBest.primaryMetric.sum,
          mean: decision.value.currentBest.primaryMetric.mean,
        },
        secondaryMetric: {
          forDay:
            metricsForDecisionEngine.value.currentBestMetrics.secondaryMetric,
          sum: decision.value.currentBest.secondaryMetric.sum,
          mean: decision.value.currentBest.secondaryMetric.mean,
        },
      },
      contenderMetrics: {
        primaryMetric: {
          forDay: metricsForDecisionEngine.value.contenderMetrics.primaryMetric,
          sum: decision.value.contender.primaryMetric.sum,
          mean: decision.value.contender.primaryMetric.mean,
        },
        secondaryMetric: {
          forDay:
            metricsForDecisionEngine.value.contenderMetrics.secondaryMetric,
          sum: decision.value.contender.secondaryMetric.sum,
          mean: decision.value.contender.secondaryMetric.mean,
        },
      },
      primaryMetricTTestResult: decision.value.primaryMetricTTestResult,
      secondaryMetricTTestResult: decision.value.secondaryMetricTTestResult,
      decision:
        decision.value.decision === "FIRST_VARIANT"
          ? "CURRENT_BEST"
          : decision.value.decision === "SECOND_VARIANT"
            ? "CONTENDER"
            : "NO_DECISION",
      decisionType: decision.value.decisionType,
    });

    await this.ctx.abTestRoundDayMetricsRepository.createOne(
      completedAbTestRoundDayMetrics,
      command.abTestType,
      command.tx,
    );

    return ok(completedAbTestRoundDay.value);
  }
}
