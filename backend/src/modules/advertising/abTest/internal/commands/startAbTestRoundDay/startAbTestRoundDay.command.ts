import { err, ok, Result } from "neverthrow";

import { LinkedInStateOrchestratorService } from "../../../../linkedInStateOrchestrator/application/linkedInStateOrchestrator.service";
import { AbTestRoundDayDomain } from "../../domain/abTestRoundDay.domain";
import { IAbTestRoundDayRepository } from "../../repositories/abTestRoundDay.repository.interface";
import { StartAbTestRoundDayCommand } from "./startAbTestRoundDay.command.interface";

interface StartAbTestRoundDayError {
  type:
    | "AB_TEST_ROUND_DAY_NOT_FOUND"
    | "STATE_CONFIG_NOT_FOUND"
    | "AD_SEGMENT_NOT_FOUND"
    | "AD_PROGRAM_NOT_FOUND"
    | "DEPLOYMENT_CONFIG_ID_DOES_NOT_MATCH"
    | "DEPLOYMENT_CONFIG_NOT_RUNNING"
    | "AB_TEST_ROUND_DAY_ALREADY_STARTED";
}

export class StartAbTestRoundDayCommandHandler {
  constructor(
    private readonly abTestRoundDayRepository: IAbTestRoundDayRepository,
    private readonly linkedInStateOrchestrator: LinkedInStateOrchestratorService,
  ) {}
  async execute(
    command: StartAbTestRoundDayCommand,
  ): Promise<Result<void, StartAbTestRoundDayError>> {
    const abTestRoundDay = await this.abTestRoundDayRepository.getOne(
      command.abTestRoundDayId,
      command.type,
      command.tx,
    );
    if (!abTestRoundDay) {
      return err({ type: "AB_TEST_ROUND_DAY_NOT_FOUND" });
    }
    if (abTestRoundDay.status !== "NOT_STARTED") {
      return err({ type: "AB_TEST_ROUND_DAY_ALREADY_STARTED" });
    }

    const res = await this.linkedInStateOrchestrator.startLinkedInState(
      abTestRoundDay.deploymentConfigId,
      command.tx,
    );
    if (res.isErr()) {
      return err(res.error);
    }

    const inProgressRoundDay = AbTestRoundDayDomain.startRoundDay({
      abTestRoundDay,
      deploymentConfig: res.value,
    });
    if (inProgressRoundDay.isErr()) {
      return err({ type: inProgressRoundDay.error.type });
    }

    await this.abTestRoundDayRepository.updateOne(
      inProgressRoundDay.value,
      command.type,
      command.tx,
    );

    return ok();
  }
}
