import { err, ok, Result } from "neverthrow";

import { AbTestRoundDomain } from "../../domain/abTestRound.domain";
import { IAbTestRepository } from "../../repositories/abTest.repository.interface";
import { IAbTestRoundRepository } from "../../repositories/abTestRound.repository.interface";
import { SetCurrentBestVariantCommand } from "./setCurrentBestVariantForAbTest.command.interface";

interface SetCurrentBestVariantError {
  type:
    | "AB_TEST_NOT_FOUND"
    | "VARIANT_NOT_FOUND_IN_ROUNDS"
    | "A_ROUND_IS_CURRENTLY_IN_PROGRESS";
}

export class SetCurrentBestVariantForAbTestHandler {
  constructor(
    private readonly abTestRepository: IAbTestRepository,
    private readonly abTestRoundRepository: IAbTestRoundRepository,
  ) {}

  async execute(
    command: SetCurrentBestVariantCommand,
  ): Promise<Result<void, SetCurrentBestVariantError>> {
    const abTest = await this.abTestRepository.getOne(
      command.abTestId,
      command.abTestType,
      command.tx,
    );
    if (!abTest) {
      return err({ type: "AB_TEST_NOT_FOUND" });
    }

    const abTestRounds = await this.abTestRoundRepository.getAllForAbTest(
      command.abTestId,
      command.abTestType,
      command.tx,
    );
    const result =
      await AbTestRoundDomain.checkIfVariantIdCanBeSetAsCurrentBestForNonStartedRoundsInAbTest(
        {
          abTestRounds,
          variantIdToSetAsCurrentBest: command.variantId,
        },
      );
    if (result.isErr()) {
      return err(result.error);
    }
    await this.abTestRoundRepository.updateCurrentBestIdForNonStartedRoundsForAbTest(
      command.abTestId,
      command.variantId,
      command.abTestType,
      command.tx,
    );
    return ok();
  }
}
