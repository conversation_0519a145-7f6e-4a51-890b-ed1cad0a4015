import { z } from "zod";

import { abTestTypeSchema } from "./abTestType.valueObject";

export const adSegmentBestVariantSchema = z.object({
  type: abTestTypeSchema,
  id: z.string().uuid(),
  adSegmentId: z.string().uuid(),
  variantId: z.string().uuid(),
});

export const AdSegmentBestVariant = (
  input: z.infer<typeof adSegmentBestVariantSchema>,
) => {
  return adSegmentBestVariantSchema.parse(input);
};

export type AdSegmentBestVariant = z.infer<typeof adSegmentBestVariantSchema>;
