import { z } from "zod";

import { createEntity } from "../../../../../helpers/entity";

const sponsoredContentVarientsMap = z.object({
  adFormatType: z.literal("SPONSORED_CONTENT"),
  audienceId: z.string().uuid(),
  audienceType: z.string(),
  valuePropId: z.string().uuid(),
  valuePropType: z.string(),
  adProgramCreativeId: z.string().uuid(),
  adProgramCreativeType: z.string(),
  socialPostBodyCopyId: z.string().uuid(),
  socialPostBodyCopyType: z.string(),
  socialPostCallToActionId: z.string().uuid(),
  socialPostCallToActionType: z.string(),
});

const sponsoredInmailVarientsMap = z.object({
  adFormatType: z.literal("SPONSORED_INMAIL"),
  audienceId: z.string().uuid(),
  audienceType: z.string(),
  valuePropId: z.string().uuid(),
  valuePropType: z.string(),
  conversationSubjectCopyId: z.string().uuid(),
  conversationSubjectCopyType: z.string(),
  conversationMessageCopyId: z.string().uuid(),
  conversationMessageCopyType: z.string(),
  conversationCallToActionId: z.string().uuid(),
  conversationCallToActionType: z.string(),
});

const varientsMap = z.discriminatedUnion("adFormatType", [
  sponsoredContentVarientsMap,
  sponsoredInmailVarientsMap,
]);

export const abTestRoundMetadataSchema = z.object({
  abTestRoundId: z.string().uuid(),
  currentBest: varientsMap,
  contender: varientsMap,
});

export const AbTestRoundMetadata = createEntity(abTestRoundMetadataSchema);
export type AbTestRoundMetadata = z.infer<typeof abTestRoundMetadataSchema>;
