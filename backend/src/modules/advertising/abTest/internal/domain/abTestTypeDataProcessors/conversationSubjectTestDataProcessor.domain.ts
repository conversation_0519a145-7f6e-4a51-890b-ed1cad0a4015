import { err, ok, Result } from "neverthrow";

import { AdProgram } from "../../../../domain/entites/adProgram";
import { AdSegmentValueProp } from "../../../../domain/entites/adSegmentValueProp";
import { ConversationSubjectCopy } from "../../../../domain/entites/conversationSubjectCopy";
import { LinkedInCampaign } from "../../../../domain/entites/linkedInCampaign";
import { ValuePropCreative } from "../../../../domain/entites/valuePropCreative";
import { SponsoredContentAdFormat } from "../../../../domain/valueObjects/linkedinAdFormat";
import { LinkedInStateInput } from "../../../../linkedInStateOrchestrator/domain/linkedInStateInput.valueObject";
import { LinkedInStateOutput } from "../../../../linkedInStateOrchestrator/domain/linkedInStateOutput.entity";

export const ConversationSubjectAbTestDomain = {
  constructResourceIdsList: {
    forSponsoredInmail: (input: {
      conversationSubjectCopies: ConversationSubjectCopy[];
    }): string[] => {
      const conversationSubjectsTypesSet = new Set<string>();
      const uniqueConversationSubjectIds: string[] = [];

      for (const conversationSubjectCopy of input.conversationSubjectCopies) {
        if (conversationSubjectsTypesSet.has(conversationSubjectCopy.type)) {
          continue;
        }
        conversationSubjectsTypesSet.add(conversationSubjectCopy.type);
        uniqueConversationSubjectIds.push(conversationSubjectCopy.id);
      }

      return uniqueConversationSubjectIds;
    },
  },
  constructLinkerInStateOrchestratorInput: {
    forSponsoredInmail: (input: {
      adSegmentId: string;
      audienceId: string;
      currentBestConversationSubject: ConversationSubjectCopy;
      contenderConversationSubject: ConversationSubjectCopy;
      bestConversationMessageCopyType: string | null;
      bestConversationCallToActionCopyType: string | null;
    }) => {
      const stateInput: LinkedInStateInput = {
        adFormatType: "SPONSORED_INMAIL",
        adSegmentId: input.adSegmentId,
        data: {
          campaigns: [
            {
              campaignId: input.audienceId,
              ads: [
                {
                  valuePropId: input.currentBestConversationSubject.valuePropId,
                  subjectCopyType: input.currentBestConversationSubject.type,
                  messageCopyType:
                    input.bestConversationMessageCopyType ?? "standard",
                  callToActionCopyType:
                    input.bestConversationCallToActionCopyType ?? "standard",
                },
                {
                  valuePropId: input.contenderConversationSubject.valuePropId,
                  subjectCopyType: input.contenderConversationSubject.type,
                  messageCopyType:
                    input.bestConversationMessageCopyType ?? "standard",
                  callToActionCopyType:
                    input.bestConversationCallToActionCopyType ?? "standard",
                },
              ],
            },
          ],
        },
      };
      return stateInput;
    },
  },
  constructMetricsForDecisionEngine: {
    forSponsoredInmail: (input: {
      adProgram: AdProgram;
      linkedInStateOutput: LinkedInStateOutput;
      currentBestConversationSubject: ConversationSubjectCopy;
      contenderConversationSubject: ConversationSubjectCopy;
    }): Result<
      {
        currentBestMetrics: { primaryMetric: number; secondaryMetric: number };
        contenderMetrics: { primaryMetric: number; secondaryMetric: number };
      },
      {
        type:
          | "CURRENT_BEST_VALUE_PROP_NOT_FOUND"
          | "CONTENDER_VALUE_PROP_NOT_FOUND"
          | "NO_CAMPAIGN_FOUND";
      }
    > => {
      if (
        input.adProgram.adFormat.type !== "SPONSORED_INMAIL" ||
        input.linkedInStateOutput.adFormatType !== "SPONSORED_INMAIL"
      ) {
        throw new Error("Ad program is not a sponsored inmail");
      }
      const campaign = input.linkedInStateOutput.data.campaigns[0];
      if (!campaign) {
        return err({ type: "NO_CAMPAIGN_FOUND" });
      }

      const currentBestConversationSubject = campaign.ads.find(
        (ad) =>
          ad.valuePropId === input.currentBestConversationSubject.valuePropId &&
          ad.subjectCopyType === input.currentBestConversationSubject.type,
      );
      const contenderConversationSubjectMetrics = campaign.ads.find(
        (ad) =>
          ad.valuePropId === input.contenderConversationSubject.valuePropId &&
          ad.subjectCopyType === input.contenderConversationSubject.type,
      );
      if (
        !currentBestConversationSubject ||
        !contenderConversationSubjectMetrics
      ) {
        return err({
          type: !currentBestConversationSubject
            ? "CURRENT_BEST_VALUE_PROP_NOT_FOUND"
            : "CONTENDER_VALUE_PROP_NOT_FOUND",
        });
      }
      if (input.adProgram.adFormat.type !== "SPONSORED_INMAIL") {
        throw new Error("Ad program is not a sponsored inmail");
      }
      let currentBestPrimaryMetric: number | null = null;
      let contenderPrimaryMetric: number | null = null;

      if (input.adProgram.objectiveType == "LEAD_GENERATION") {
        currentBestPrimaryMetric =
          currentBestConversationSubject.metrics.leads ?? 0;
        contenderPrimaryMetric =
          contenderConversationSubjectMetrics.metrics.leads ?? 0;
      } else {
        currentBestPrimaryMetric =
          currentBestConversationSubject.metrics.totalEngagements ?? 0;
        contenderPrimaryMetric =
          contenderConversationSubjectMetrics.metrics.totalEngagements ?? 0;
      }

      if (input.adProgram.adFormat.type !== "SPONSORED_INMAIL") {
        throw new Error("Ad program is not a sponsored inmail");
      }

      const currentBestSecondaryMetric =
        (currentBestConversationSubject.metrics.opens ?? 0) /
        (currentBestConversationSubject.metrics.sends ?? 1);
      const contenderSecondaryMetric =
        (contenderConversationSubjectMetrics.metrics.opens ?? 0) /
        (contenderConversationSubjectMetrics.metrics.sends ?? 1);

      return ok({
        currentBestMetrics: {
          primaryMetric: currentBestPrimaryMetric,
          secondaryMetric: currentBestSecondaryMetric,
        },
        contenderMetrics: {
          primaryMetric: contenderPrimaryMetric,
          secondaryMetric: contenderSecondaryMetric,
        },
      });
    },
  },
};
