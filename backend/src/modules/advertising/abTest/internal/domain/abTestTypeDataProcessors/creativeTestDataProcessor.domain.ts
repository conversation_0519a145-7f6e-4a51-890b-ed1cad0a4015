import { err, ok, Result } from "neverthrow";

import { AdProgram } from "../../../../domain/entites/adProgram";
import { AdSegmentValueProp } from "../../../../domain/entites/adSegmentValueProp";
import { ConversationSubjectCopy } from "../../../../domain/entites/conversationSubjectCopy";
import { LinkedInCampaign } from "../../../../domain/entites/linkedInCampaign";
import { ValuePropCreative } from "../../../../domain/entites/valuePropCreative";
import { SponsoredContentAdFormat } from "../../../../domain/valueObjects/linkedinAdFormat";
import { LinkedInStateInput } from "../../../../linkedInStateOrchestrator/domain/linkedInStateInput.valueObject";
import { LinkedInStateOutput } from "../../../../linkedInStateOrchestrator/domain/linkedInStateOutput.entity";

export const CreativeAbTestDomain = {
  constructResourceIdsList: {
    forSponsoredContent: (input: {
      adSegmentValuePropCreatives: ValuePropCreative[];
    }): string[] => {
      const creativeIdsSet = new Set<string>();
      for (const valuePropCreative of input.adSegmentValuePropCreatives) {
        creativeIdsSet.add(valuePropCreative.adProgramCreativeId);
      }
      const creativeIds = Array.from(creativeIdsSet);
      return creativeIds;
    },
  },
  constructLinkerInStateOrchestratorInput: {
    forSponsoredContent: (input: {
      adSegmentId: string;
      audienceId: string;
      adSegmentValuePropCreatives: ValuePropCreative[];
      currentBestCreativeId: string;
      contenderCreativeId: string;
      bestSocialPostBodyCopyType: string | null;
      bestSocialPostCallToActionType: string | null;
      adProgram: AdProgram;
    }) => {
      let adFormat: "SINGLE_IMAGE" | "SINGLE_VIDEO" | "DOCUMENT" | undefined =
        undefined;
      if (input.adProgram.adFormat.type !== "SPONSORED_CONTENT") {
        throw new Error("Ad program is not a sponsored content");
      }
      if (input.adProgram.adFormat.format === "SINGLE_IMAGE") {
        adFormat = "SINGLE_IMAGE";
      } else if (input.adProgram.adFormat.format === "VIDEO") {
        adFormat = "SINGLE_VIDEO";
      } else if (input.adProgram.adFormat.format === "DOCUMENT") {
        adFormat = "DOCUMENT";
      }
      if (!adFormat) {
        throw new Error("Ad format is not supported");
      }

      const valuePropForCurrentBestCreativeId =
        input.adSegmentValuePropCreatives.find(
          (valuePropCreative) =>
            valuePropCreative.adProgramCreativeId ===
            input.currentBestCreativeId,
        )?.adSegmentValuePropId;
      if (!valuePropForCurrentBestCreativeId) {
        throw new Error("Current best creative not found");
      }
      let contenderBestCreativeId: string | undefined = undefined;

      contenderBestCreativeId =
        input.adSegmentValuePropCreatives.find(
          (valuePropCreative) =>
            valuePropCreative.adProgramCreativeId ===
              input.contenderCreativeId &&
            valuePropCreative.adSegmentValuePropId ===
              valuePropForCurrentBestCreativeId,
        )?.adProgramCreativeId ??
        input.adSegmentValuePropCreatives.find(
          (valuePropCreative) =>
            valuePropCreative.adProgramCreativeId === input.contenderCreativeId,
        )?.adProgramCreativeId;
      if (!contenderBestCreativeId) {
        throw new Error("Contender creative not found");
      }

      const stateInput: LinkedInStateInput = {
        adFormatType: "SPONSORED_CONTENT",
        adSegmentId: input.adSegmentId,
        adFormat: adFormat,
        data: {
          campaigns: [
            {
              campaignId: input.audienceId,
              ads: [
                {
                  adCreativeId: input.currentBestCreativeId,
                  valuePropId: valuePropForCurrentBestCreativeId,
                  bodyCopyType: input.bestSocialPostBodyCopyType ?? "standard",
                  callToActionCopyType:
                    input.bestSocialPostCallToActionType ?? "Standard",
                },
                {
                  adCreativeId: contenderBestCreativeId,
                  valuePropId: valuePropForCurrentBestCreativeId,
                  bodyCopyType: input.bestSocialPostBodyCopyType ?? "standard",
                  callToActionCopyType:
                    input.bestSocialPostCallToActionType ?? "Standard",
                },
              ],
            },
          ],
        },
      };
      return stateInput;
    },
  },
  constructMetricsForDecisionEngine: {
    forSponsoredContent: (input: {
      adProgram: AdProgram;
      linkedInStateOutput: LinkedInStateOutput;
      currentBestCreativeId: string;
      contenderCreativeId: string;
    }): Result<
      {
        currentBestMetrics: { primaryMetric: number; secondaryMetric: number };
        contenderMetrics: { primaryMetric: number; secondaryMetric: number };
      },
      {
        type:
          | "CURRENT_BEST_CREATIVE_NOT_FOUND"
          | "CONTENDER_CREATIVE_NOT_FOUND"
          | "NO_CAMPAIGN_FOUND";
      }
    > => {
      if (
        input.adProgram.adFormat.type !== "SPONSORED_CONTENT" ||
        input.linkedInStateOutput.adFormatType !== "SPONSORED_CONTENT"
      ) {
        throw new Error("Ad program is not a sponsored content");
      }
      const campaign = input.linkedInStateOutput.data.campaigns[0];
      if (!campaign) {
        return err({ type: "NO_CAMPAIGN_FOUND" });
      }

      const currentBestCreative = campaign.ads.find(
        (ad) => ad.adCreativeId === input.currentBestCreativeId,
      );
      const contenderCreative = campaign.ads.find(
        (ad) => ad.adCreativeId === input.contenderCreativeId,
      );
      if (!currentBestCreative || !contenderCreative) {
        return err({
          type: !currentBestCreative
            ? "CURRENT_BEST_CREATIVE_NOT_FOUND"
            : "CONTENDER_CREATIVE_NOT_FOUND",
        });
      }
      let currentBestPrimaryMetric: number | null = null;
      let currentBestSecondaryMetric: number | null = null;
      let contenderPrimaryMetric: number | null = null;
      let contenderSecondaryMetric: number | null = null;

      if (input.adProgram.objectiveType === "LEAD_GENERATION") {
        currentBestPrimaryMetric = currentBestCreative.metrics.leads ?? 0;
        contenderPrimaryMetric = contenderCreative.metrics.leads ?? 0;
      } else {
        currentBestPrimaryMetric =
          currentBestCreative.metrics.totalEngagements ?? 0;
        contenderPrimaryMetric =
          contenderCreative.metrics.totalEngagements ?? 0;
      }
      if (input.adProgram.adFormat.type !== "SPONSORED_CONTENT") {
        throw new Error("Ad program is not a sponsored content");
      }
      if (input.adProgram.adFormat.format === "VIDEO") {
        currentBestSecondaryMetric =
          (currentBestCreative.metrics.videoCompletions ?? 0) /
          (currentBestCreative.metrics.videoViews &&
          currentBestCreative.metrics.videoViews > 0
            ? currentBestCreative.metrics.videoViews
            : 1);
        contenderSecondaryMetric =
          (contenderCreative.metrics.videoCompletions ?? 0) /
          (contenderCreative.metrics.videoViews &&
          contenderCreative.metrics.videoViews > 0
            ? contenderCreative.metrics.videoViews
            : 1);
      } else {
        currentBestPrimaryMetric =
          (currentBestCreative.metrics.videoCompletions ?? 0) /
          (currentBestCreative.metrics.videoViews &&
          currentBestCreative.metrics.videoViews > 0
            ? currentBestCreative.metrics.videoViews
            : 1);
        contenderPrimaryMetric =
          (contenderCreative.metrics.videoCompletions ?? 0) /
          (contenderCreative.metrics.videoViews &&
          contenderCreative.metrics.videoViews > 0
            ? contenderCreative.metrics.videoViews
            : 1);
      }
      return ok({
        currentBestMetrics: {
          primaryMetric: currentBestPrimaryMetric ?? 0,
          secondaryMetric: currentBestSecondaryMetric ?? 0,
        },
        contenderMetrics: {
          primaryMetric: contenderPrimaryMetric ?? 0,
          secondaryMetric: contenderSecondaryMetric ?? 0,
        },
      });
    },
  },
};
