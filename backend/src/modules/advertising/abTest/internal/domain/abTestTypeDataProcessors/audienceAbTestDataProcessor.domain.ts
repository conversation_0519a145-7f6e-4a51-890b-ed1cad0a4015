import { err, ok, Result } from "neverthrow";

import { AdProgram } from "../../../../domain/entites/adProgram";
import { LinkedInCampaign } from "../../../../domain/entites/linkedInCampaign";
import { LinkedInStateInput } from "../../../../linkedInStateOrchestrator/domain/linkedInStateInput.valueObject";
import {
  LinkedInStateOutput,
  linkedInStateOutputSchema,
} from "../../../../linkedInStateOrchestrator/domain/linkedInStateOutput.entity";

export const AudienceAbTestDomain = {
  constructResourceIdsList: (input: {
    campaigns: LinkedInCampaign[];
  }): string[] => {
    return input.campaigns.map((campaign) => campaign.linkedInAudienceId);
  },
  constructLinkerInStateOrchestratorInput: {
    forSponsoredContent: (input: {
      adSegmentId: string;
      currentBestAudienceId: string;
      contenderAudienceId: string;
      creativeId: string;
      valuePropId: string;
      bestSocialPostBodyCopyType: string | null;
      bestSocialPostCallToActionType: string | null;
      adProgram: AdProgram;
    }) => {
      let adFormat: "SINGLE_IMAGE" | "SINGLE_VIDEO" | "DOCUMENT" | undefined =
        undefined;
      if (input.adProgram.adFormat.type !== "SPONSORED_CONTENT") {
        throw new Error("Ad program is not a sponsored content");
      }
      if (input.adProgram.adFormat.format === "SINGLE_IMAGE") {
        adFormat = "SINGLE_IMAGE";
      } else if (input.adProgram.adFormat.format === "VIDEO") {
        adFormat = "SINGLE_VIDEO";
      } else if (input.adProgram.adFormat.format === "DOCUMENT") {
        adFormat = "DOCUMENT";
      }
      if (!adFormat) {
        throw new Error("Ad format is not supported");
      }

      const stateInput: LinkedInStateInput = {
        adFormatType: "SPONSORED_CONTENT",
        adFormat: adFormat,
        adSegmentId: input.adSegmentId,
        data: {
          campaigns: [
            {
              campaignId: input.currentBestAudienceId,
              ads: [
                {
                  adCreativeId: input.creativeId,
                  valuePropId: input.valuePropId,
                  bodyCopyType: input.bestSocialPostBodyCopyType ?? "standard",
                  callToActionCopyType:
                    input.bestSocialPostCallToActionType ?? "Standard",
                },
              ],
            },
            {
              campaignId: input.contenderAudienceId,
              ads: [
                {
                  adCreativeId: input.creativeId,
                  valuePropId: input.valuePropId,
                  bodyCopyType: input.bestSocialPostBodyCopyType ?? "standard",
                  callToActionCopyType:
                    input.bestSocialPostCallToActionType ?? "Standard",
                },
              ],
            },
          ],
        },
      };
      return stateInput;
    },
    forSponsoredInmail: (input: {
      adSegmentId: string;
      currentBestAudienceId: string;
      contenderAudienceId: string;
      valuePropId: string;
      conversationSubjectType: string;
      conversationMessageType: string | null;
      conversationCallToActionType: string | null;
    }) => {
      const stateInput: LinkedInStateInput = {
        adFormatType: "SPONSORED_INMAIL",
        adSegmentId: input.adSegmentId,
        data: {
          campaigns: [
            {
              campaignId: input.currentBestAudienceId,
              ads: [
                {
                  valuePropId: input.valuePropId,
                  subjectCopyType: input.conversationSubjectType,
                  messageCopyType: input.conversationMessageType ?? "standard",
                  callToActionCopyType:
                    input.conversationCallToActionType ?? "standard",
                },
              ],
            },
            {
              campaignId: input.contenderAudienceId,
              ads: [
                {
                  valuePropId: input.valuePropId,
                  subjectCopyType: input.conversationSubjectType,
                  messageCopyType: input.conversationMessageType ?? "standard",
                  callToActionCopyType:
                    input.conversationCallToActionType ?? "standard",
                },
              ],
            },
          ],
        },
      };
      return stateInput;
    },
  },
  constructMetricsForDecisionEngine: {
    forSponsoredContent(input: {
      adProgram: AdProgram;
      linkedInStateOutput: LinkedInStateOutput;
      currentBestAudienceId: string;
      contenderAudienceId: string;
    }): Result<
      {
        currentBestMetrics: {
          primaryMetric: number;
          secondaryMetric: number;
        };
        contenderMetrics: {
          primaryMetric: number;
          secondaryMetric: number;
        };
      },
      {
        type:
          | "CURRENT_BEST_CAMPAIGN_NOT_FOUND"
          | "CONTENDER_CAMPAIGN_NOT_FOUND";
      }
    > {
      const currentBestCampaignMetrics =
        input.linkedInStateOutput.data.campaigns.find(
          (c) => c.campaignId == input.currentBestAudienceId,
        );
      const contenderCampaignMetrics =
        input.linkedInStateOutput.data.campaigns.find(
          (c) => c.campaignId == input.contenderAudienceId,
        );
      if (!currentBestCampaignMetrics || !contenderCampaignMetrics) {
        return err({
          type:
            !currentBestCampaignMetrics && !contenderCampaignMetrics
              ? "CURRENT_BEST_CAMPAIGN_NOT_FOUND"
              : "CONTENDER_CAMPAIGN_NOT_FOUND",
        });
      }
      let currentBestPrimaryMetric: number | null = null;
      let currentBestSecondaryMetric: number | null = null;
      let contenderPrimaryMetric: number | null = null;
      let contenderSecondaryMetric: number | null = null;

      if (input.adProgram.objectiveType == "LEAD_GENERATION") {
        currentBestPrimaryMetric =
          currentBestCampaignMetrics.metrics.leads ?? 0;
        contenderPrimaryMetric = contenderCampaignMetrics.metrics.leads ?? 0;
      } else {
        currentBestPrimaryMetric =
          currentBestCampaignMetrics.metrics.totalEngagements ?? 0;
        contenderPrimaryMetric =
          contenderCampaignMetrics.metrics.totalEngagements ?? 0;
      }

      if (input.adProgram.adFormat.type !== "SPONSORED_CONTENT") {
        throw new Error("Ad program is not a sponsored content");
      }

      if (input.adProgram.adFormat.format !== "VIDEO") {
        currentBestSecondaryMetric =
          currentBestCampaignMetrics.metrics.videoViews ?? 0;
        contenderSecondaryMetric =
          contenderCampaignMetrics.metrics.videoViews ?? 0;
      } else {
        currentBestSecondaryMetric =
          currentBestCampaignMetrics.metrics.clicks ?? 0;
        contenderSecondaryMetric = contenderCampaignMetrics.metrics.clicks ?? 0;
      }

      return ok({
        currentBestMetrics: {
          primaryMetric: currentBestPrimaryMetric,
          secondaryMetric: currentBestSecondaryMetric,
        },
        contenderMetrics: {
          primaryMetric: contenderPrimaryMetric,
          secondaryMetric: contenderSecondaryMetric,
        },
      });
    },
    forSponsoredInmail: (input: {
      adProgram: AdProgram;
      linkedInStateOutput: LinkedInStateOutput;
      currentBestAudienceId: string;
      contenderAudienceId: string;
    }): Result<
      {
        currentBestMetrics: { primaryMetric: number; secondaryMetric: number };
        contenderMetrics: { primaryMetric: number; secondaryMetric: number };
      },
      {
        type:
          | "CURRENT_BEST_CAMPAIGN_NOT_FOUND"
          | "CONTENDER_CAMPAIGN_NOT_FOUND";
      }
    > => {
      const currentBestCampaignMetrics =
        input.linkedInStateOutput.data.campaigns.find(
          (c) => c.campaignId == input.currentBestAudienceId,
        );
      const contenderCampaignMetrics =
        input.linkedInStateOutput.data.campaigns.find(
          (c) => c.campaignId == input.contenderAudienceId,
        );
      if (!currentBestCampaignMetrics || !contenderCampaignMetrics) {
        return err({
          type:
            !currentBestCampaignMetrics && !contenderCampaignMetrics
              ? "CURRENT_BEST_CAMPAIGN_NOT_FOUND"
              : "CONTENDER_CAMPAIGN_NOT_FOUND",
        });
      }
      let currentBestPrimaryMetric: number | null = null;
      let contenderPrimaryMetric: number | null = null;

      if (input.adProgram.objectiveType == "LEAD_GENERATION") {
        currentBestPrimaryMetric =
          currentBestCampaignMetrics.metrics.leads ?? 0;
        contenderPrimaryMetric = contenderCampaignMetrics.metrics.leads ?? 0;
      } else {
        currentBestPrimaryMetric =
          currentBestCampaignMetrics.metrics.totalEngagements ?? 0;
        contenderPrimaryMetric =
          contenderCampaignMetrics.metrics.totalEngagements ?? 0;
      }

      if (input.adProgram.adFormat.type !== "SPONSORED_INMAIL") {
        throw new Error("Ad program is not a sponsored inmail");
      }

      const currentBestSecondaryMetric =
        currentBestCampaignMetrics.metrics.opens ?? 0;
      const contenderSecondaryMetric =
        contenderCampaignMetrics.metrics.opens ?? 0;

      return ok({
        currentBestMetrics: {
          primaryMetric: currentBestPrimaryMetric,
          secondaryMetric: currentBestSecondaryMetric,
        },
        contenderMetrics: {
          primaryMetric: contenderPrimaryMetric,
          secondaryMetric: contenderSecondaryMetric,
        },
      });
    },
  },
};
