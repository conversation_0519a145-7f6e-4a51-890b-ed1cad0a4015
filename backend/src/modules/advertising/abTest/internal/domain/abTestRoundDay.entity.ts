import { z } from "zod";

import { createEntity } from "../../../../../helpers/entity";

export const baseAbTestRoundDaySchema = z.object({
  id: z.string().uuid(),
  abTestRoundId: z.string().uuid(),
  dayIndex: z.number(),
  deploymentConfigId: z.string().uuid(),
});

export const abTestRoundDaySchema = z.discriminatedUnion("status", [
  baseAbTestRoundDaySchema.extend({
    status: z.enum(["NOT_STARTED", "IN_PROGRESS", "FAILED", "CANCELLED"]),
    winner: z.null(),
    currentBestResult: z.null(),
    contenderResult: z.null(),
  }),
  baseAbTestRoundDaySchema.extend({
    status: z.enum(["COMPLETED", "AUTO_RESOLVED", "USER_RESOLVED"]),
    winner: z.enum(["CURRENT_BEST", "CONTENDER", "NO_DECISION"]),
    currentBestResult: z.number(),
    contenderResult: z.number(),
  }),
]);

export const AbTestRoundDay = createEntity(abTestRoundDaySchema);
export type AbTestRoundDay = z.infer<typeof abTestRoundDaySchema>;
