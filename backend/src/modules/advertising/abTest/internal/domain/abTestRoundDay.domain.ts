import { err, ok, Result } from "neverthrow";

import { createUuid } from "../../../../core/utils/uuid";
import { LinkedInStateConfig } from "../../../linkedInStateOrchestrator/domain/linkedInStateConfig.entity";
import { AbTestRound } from "./abTestRound.entity";
import { AbTestRoundDay } from "./abTestRoundDay.entity";

const setAbTestRoundDayToInProgress = (
  abTestRoundDay: AbTestRoundDay,
): Result<AbTestRoundDay, { type: "AB_TEST_ROUND_DAY_ALREADY_STARTED" }> => {
  if (abTestRoundDay.status !== "NOT_STARTED") {
    return err({ type: "AB_TEST_ROUND_DAY_ALREADY_STARTED" });
  }
  return ok(
    AbTestRoundDay({
      ...abTestRoundDay,
      status: "IN_PROGRESS",
    }),
  );
};

const setAbTestRoundDayToCompleted = (input: {
  abTestRoundDay: AbTestRoundDay;
  currentBestResult: number;
  contenderResult: number;
  winner: "CURRENT_BEST" | "CONTENDER" | "NO_DECISION";
}): Result<AbTestRoundDay, { type: "AB_TEST_ROUND_DAY_NOT_IN_PROGRESS" }> => {
  if (input.abTestRoundDay.status !== "IN_PROGRESS") {
    return err({ type: "AB_TEST_ROUND_DAY_NOT_IN_PROGRESS" });
  }
  return ok(
    AbTestRoundDay({
      ...input.abTestRoundDay,
      status: "COMPLETED",
      winner: input.winner,
      currentBestResult: input.currentBestResult,
      contenderResult: input.contenderResult,
    }),
  );
};

export const AbTestRoundDayDomain = {
  create: (input: {
    abTestRound: AbTestRound;
    deploymentConfigId: string;
    existingAbTestRoundDays: AbTestRoundDay[];
  }): Result<
    AbTestRoundDay,
    {
      type:
        | "AB_TEST_ROUND_NOT_RUNNING"
        | "UNCOMPLETED_AB_TEST_ROUND_DAYS_ALREADY_EXIST";
    }
  > => {
    if (input.abTestRound.status !== "IN_PROGRESS") {
      return err({ type: "AB_TEST_ROUND_NOT_RUNNING" });
    }

    if (
      input.existingAbTestRoundDays.some(
        (day) =>
          day.status !== "COMPLETED" &&
          day.status !== "AUTO_RESOLVED" &&
          day.status !== "USER_RESOLVED",
      )
    ) {
      return err({ type: "UNCOMPLETED_AB_TEST_ROUND_DAYS_ALREADY_EXIST" });
    }

    const dayIndex =
      input.existingAbTestRoundDays.sort((a, b) => a.dayIndex - b.dayIndex)[
        input.existingAbTestRoundDays.length - 1
      ]?.dayIndex ?? 0;

    const abTestRoundDay = AbTestRoundDay({
      id: createUuid(),
      abTestRoundId: input.abTestRound.id,
      dayIndex: dayIndex + 1,
      deploymentConfigId: input.deploymentConfigId,
      status: "NOT_STARTED",
      winner: null,
      currentBestResult: null,
      contenderResult: null,
    });

    return ok(abTestRoundDay);
  },

  startRoundDay: (input: {
    abTestRoundDay: AbTestRoundDay;
    deploymentConfig: LinkedInStateConfig;
  }): Result<
    AbTestRoundDay,
    {
      type:
        | "AB_TEST_ROUND_DAY_ALREADY_STARTED"
        | "DEPLOYMENT_CONFIG_ID_DOES_NOT_MATCH"
        | "DEPLOYMENT_CONFIG_NOT_RUNNING";
    }
  > => {
    if (input.abTestRoundDay.status !== "NOT_STARTED") {
      return err({ type: "AB_TEST_ROUND_DAY_ALREADY_STARTED" });
    }

    if (input.abTestRoundDay.deploymentConfigId !== input.deploymentConfig.id) {
      return err({ type: "DEPLOYMENT_CONFIG_ID_DOES_NOT_MATCH" });
    }

    if (input.deploymentConfig.status !== "RUNNING") {
      return err({ type: "DEPLOYMENT_CONFIG_NOT_RUNNING" });
    }

    const inProgressAbTestRoundDay = setAbTestRoundDayToInProgress(
      input.abTestRoundDay,
    );

    if (inProgressAbTestRoundDay.isErr()) {
      return err(inProgressAbTestRoundDay.error);
    }
    return ok(inProgressAbTestRoundDay.value);
  },

  endRoundDay: (input: {
    abTestRoundDay: AbTestRoundDay;
    winner: "CURRENT_BEST" | "CONTENDER" | "NO_DECISION";
    currentBestResult: number;
    contenderResult: number;
  }): Result<AbTestRoundDay, { type: "AB_TEST_ROUND_DAY_NOT_IN_PROGRESS" }> => {
    if (input.abTestRoundDay.status !== "IN_PROGRESS") {
      return err({ type: "AB_TEST_ROUND_DAY_NOT_IN_PROGRESS" });
    }
    const completedAbTestRoundDay = setAbTestRoundDayToCompleted(input);
    return completedAbTestRoundDay;
  },
};
