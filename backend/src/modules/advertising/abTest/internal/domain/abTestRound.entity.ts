import { z } from "zod";

import { createEntity } from "../../../../../helpers/entity";

export const baseAbTestRoundSchema = z.object({
  id: z.string().uuid(),
  abTestId: z.string().uuid(),
  currentBestId: z.string().uuid(),
  contenderId: z.string().uuid(),
  roundIndex: z.number(),
});

export const abTestRoundSchema = z.discriminatedUnion("status", [
  baseAbTestRoundSchema.extend({
    status: z.enum(["NOT_STARTED", "IN_PROGRESS", "FAILED", "CANCELLED"]),
    winner: z.null(),
  }),
  baseAbTestRoundSchema.extend({
    status: z.enum(["COMPLETED", "AUTO_RESOLVED", "USER_RESOLVED"]),
    winner: z.enum(["CURRENT_BEST", "CONTENDER"]),
  }),
]);

export const AbTestRound = createEntity(abTestRoundSchema);

export type AbTestRound = z.infer<typeof abTestRoundSchema>;
