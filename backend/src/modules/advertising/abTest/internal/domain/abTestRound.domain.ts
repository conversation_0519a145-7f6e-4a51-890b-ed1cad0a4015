import { err, ok, Result } from "neverthrow";

import { createUuid } from "../../../../core/utils/uuid";
import { AbTest } from "./abTest.entity";
import { AbTestRound } from "./abTestRound.entity";

function createAbTestRound(input: {
  abTestId: string;
  variantA: string;
  variantB: string;
  roundIndex: number;
}): Result<AbTestRound, { type: "VARIANTS_ARE_EQUAL" }> {
  if (input.variantA === input.variantB) {
    return err({ type: "VARIANTS_ARE_EQUAL" });
  }
  const abTestRound = AbTestRound({
    id: createUuid(),
    abTestId: input.abTestId,
    contenderId: input.variantA,
    currentBestId: input.variantB,
    roundIndex: input.roundIndex,
    status: "NOT_STARTED",
    winner: null,
  });
  return ok(abTestRound);
}

function setRoundToInProgress(
  round: AbTestRound,
): Result<AbTestRound, { type: "ROUND_ALREADY_STARTED" }> {
  if (round.status !== "NOT_STARTED") {
    return err({ type: "ROUND_ALREADY_STARTED" });
  }
  return ok(
    AbTestRound({
      ...round,
      status: "IN_PROGRESS",
      winner: null,
    }),
  );
}

function setRoundToCompleted(
  round: AbTestRound,
  winner: "CURRENT_BEST" | "CONTENDER",
): Result<AbTestRound, { type: "AB_TEST_ROUND_NOT_IN_PROGRESS" }> {
  if (round.status !== "IN_PROGRESS") {
    return err({ type: "AB_TEST_ROUND_NOT_IN_PROGRESS" });
  }
  return ok(
    AbTestRound({
      ...round,
      status: "COMPLETED",
      winner: winner,
    }),
  );
}

export const AbTestRoundDomain = {
  setupInitialRounds: (input: {
    abTest: AbTest;
    variants: string[];
  }): Result<
    AbTestRound[],
    {
      type:
        | "EMPTY_VARIENTS_LIST_PROVIDED"
        | "VARIENTS_LIST_WITH_ONLY_ONE_VARIENT_PROVIDED"
        | "DUPLICATES_EXIST_IN_VARIENTS_LIST"
        | "ERROR_CREATING_ROUNDS";
    }
  > => {
    const rounds: AbTestRound[] = [];

    if (input.variants.length === 0) {
      return err({ type: "EMPTY_VARIENTS_LIST_PROVIDED" });
    }
    if (input.variants.length === 1) {
      return err({ type: "VARIENTS_LIST_WITH_ONLY_ONE_VARIENT_PROVIDED" });
    }

    const uniqueVariants = new Set(input.variants);
    if (uniqueVariants.size !== input.variants.length) {
      return err({ type: "DUPLICATES_EXIST_IN_VARIENTS_LIST" });
    }

    const currentBestId = input.variants[0]!;

    let roundIndex = 1;
    for (const contenderId of input.variants.slice(1)) {
      const round = createAbTestRound({
        abTestId: input.abTest.stageId,
        variantA: currentBestId,
        variantB: contenderId,
        roundIndex: roundIndex,
      });
      roundIndex++;
      if (round.isErr()) {
        return err({ type: "ERROR_CREATING_ROUNDS" });
      }
      rounds.push(round.value);
    }
    return ok(rounds);
  },

  selectAndStartNextRound: (input: {
    abTest: AbTest;
    roundsInAbTest: AbTestRound[];
  }): Result<
    {
      startedRound: AbTestRound;
    },
    {
      type:
        | "NO_ROUNDS_FOUND"
        | "AB_TEST_NOT_IN_PROGRESS"
        | "NO_ROUNDS_TO_START"
        | "ROUND_ALREADY_IN_PROGRESS";
    }
  > => {
    if (input.abTest.status !== "IN_PROGRESS") {
      return err({ type: "AB_TEST_NOT_IN_PROGRESS" });
    }
    if (input.roundsInAbTest.length === 0) {
      return err({ type: "NO_ROUNDS_FOUND" });
    }
    const runningRounds = input.roundsInAbTest.filter(
      (round) => round.status === "IN_PROGRESS",
    );
    if (runningRounds.length > 0) {
      return err({ type: "ROUND_ALREADY_IN_PROGRESS" });
    }
    const notStartedRounds = input.roundsInAbTest.filter(
      (round) => round.status === "NOT_STARTED",
    );
    if (notStartedRounds.length === 0) {
      return err({ type: "NO_ROUNDS_TO_START" });
    }
    const roundToStart = notStartedRounds[0]!;
    const inProgressRound = setRoundToInProgress(roundToStart);
    if (inProgressRound.isErr()) {
      throw new Error("Failed to set round to in progress");
    }
    return ok({ startedRound: inProgressRound.value });
  },

  endAbTestRound(input: {
    abTestRound: AbTestRound;
    winner: "CURRENT_BEST" | "CONTENDER";
  }): Result<AbTestRound, { type: "AB_TEST_ROUND_NOT_IN_PROGRESS" }> {
    if (input.abTestRound.status !== "IN_PROGRESS") {
      return err({ type: "AB_TEST_ROUND_NOT_IN_PROGRESS" });
    }
    const completedRound = setRoundToCompleted(input.abTestRound, input.winner);
    return completedRound;
  },

  checkIfVariantIdCanBeSetAsCurrentBestForNonStartedRoundsInAbTest(input: {
    abTestRounds: AbTestRound[];
    variantIdToSetAsCurrentBest: string;
  }): Result<
    void,
    { type: "VARIANT_NOT_FOUND_IN_ROUNDS" | "A_ROUND_IS_CURRENTLY_IN_PROGRESS" }
  > {
    const variantsSet = new Set<string>();
    for (const round of input.abTestRounds) {
      variantsSet.add(round.currentBestId);
      variantsSet.add(round.contenderId);
    }
    const variants = Array.from(variantsSet);
    if (!variants.includes(input.variantIdToSetAsCurrentBest)) {
      return err({ type: "VARIANT_NOT_FOUND_IN_ROUNDS" });
    }
    const runningRounds = input.abTestRounds.filter(
      (round) => round.status === "IN_PROGRESS",
    );
    if (runningRounds.length > 0) {
      return err({ type: "A_ROUND_IS_CURRENTLY_IN_PROGRESS" });
    }
    return ok();
  },
};
