import { z } from "zod";

import { createEntity } from "../../../../../helpers/entity";

export const abTestRoundDayMetricsSchema = z.object({
  abTestRoundDayId: z.string().uuid(),
  abTestRoundId: z.string().uuid(),
  dayIndex: z.number(),
  createdAt: z.date(),
  currentBestMetrics: z.object({
    primaryMetric: z.object({
      forDay: z.number(),
      sum: z.number(),
      mean: z.number(),
    }),
    secondaryMetric: z.object({
      forDay: z.number(),
      sum: z.number(),
      mean: z.number(),
    }),
  }),
  contenderMetrics: z.object({
    primaryMetric: z.object({
      forDay: z.number(),
      sum: z.number(),
      mean: z.number(),
    }),
    secondaryMetric: z.object({
      forDay: z.number(),
      sum: z.number(),
      mean: z.number(),
    }),
  }),
  primaryMetricTTestResult: z.number().nullable(),
  secondaryMetricTTestResult: z.number().nullable(),
  decision: z.enum(["CURRENT_BEST", "CONTENDER", "NO_DECISION"]),
  decisionType: z.enum([
    "PRIMARY_METRIC_T_TEST",
    "SECONDARY_METRIC_T_TEST",
    "PRIMARY_METRIC_SUM",
    "SECONDARY_METRIC_SUM",
    "DATA_LENGTH_IS_ONE",
    "NO_STATISTICAL_DIFFERENCE_IN_PRIMARY_AND_SECONDARY_METRICS_BEFORE_5_DAYS",
    "RANDOM",
  ]),
});

export const AbTestRoundDayMetrics = createEntity(abTestRoundDayMetricsSchema);
export type AbTestRoundDayMetrics = z.infer<typeof abTestRoundDayMetricsSchema>;
