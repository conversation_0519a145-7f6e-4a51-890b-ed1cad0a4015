import { z } from "zod";

import { abTestSchema } from "../../../domain/entites/abTest";
import { deploymentConfigSchema } from "../../../domain/entites/deploymentConfig";
import { abTestRoundSchema } from "./abTestRound.entity";
import { abTestRoundDaySchema } from "./abTestRoundDay.entity";

export const abTestAggregateSchema = z.object({
  abTest: abTestSchema,
  rounds: z.array(
    z.object({
      abTestRound: abTestRoundSchema,
    }),
  ),
});
