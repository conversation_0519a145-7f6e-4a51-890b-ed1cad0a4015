import { z } from "zod";

import { createEntity } from "../../../../../helpers/entity";
import { abTestTypeSchema } from "./abTestType.valueObject";

export const baseAbTestSchema = z.object({
  stageId: z.string().uuid(),
  type: abTestTypeSchema,
});

const abTestSchema = z.discriminatedUnion("status", [
  baseAbTestSchema.extend({
    status: z.enum(["NOT_STARTED", "IN_PROGRESS", "FAILED", "CANCELLED"]),
    winnerId: z.null(),
  }),
  baseAbTestSchema.extend({
    status: z.enum(["COMPLETED", "AUTO_RESOLVED", "USER_RESOLVED"]),
    winnerId: z.string().uuid(),
  }),
]);

export const AbTest = createEntity(abTestSchema);
export type AbTest = z.infer<typeof abTestSchema>;
