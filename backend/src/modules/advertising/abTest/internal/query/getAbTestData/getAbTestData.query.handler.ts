import { IAbTestRepository } from "../../repositories/abTest.repository.interface";
import { GetAbTestDataCommand } from "./getAbTestData.query.interface";

export class GetAbTestDataQueryHandler {
  constructor(private readonly abTestRepository: IAbTestRepository) {}
  async execute(command: GetAbTestDataCommand) {
    const abTest = await this.abTestRepository.getOne(
      command.stageId,
      command.type,
      command.tx,
    );
    if (!abTest) {
      return null;
    }
    return abTest;
  }
}
