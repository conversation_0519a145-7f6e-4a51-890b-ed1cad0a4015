import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { LinkedInStateInput } from "../domain/linkedInStateInput.valueObject";
import { SetupLinkedInStateOrchestratorCommandHandler } from "./commands/setupLinkedInStateOrchestrator/setupLinkedInStateOrchestrator.command.handler";
import { StartLinkedInStateOrchestratorCommandHandler } from "./commands/startLinkedInStateOrchestrator/startLinkedInStateOrchestrator.command.handler";
import { StopLinkedInStateOrchestratorCommandHandler } from "./commands/stopLinkedInStateOrchestrator/stopLinkedInStateOrchestrator.command.handler";
import { GetLinkedInStateOrhestratorMeticsQueryHandler } from "./queries/getLinkedInStateOrhestratorMetics.query.handler";

export class LinkedInStateOrchestratorService {
  constructor(
    private readonly ctx: {
      setupLinkedInStateOrchestratorCommandHandler: SetupLinkedInStateOrchestratorCommandHandler;
      startLinkedInStateOrchestratorCommandHandler: StartLinkedInStateOrchestratorCommandHandler;
      stopLinkedInStateOrchestratorCommandHandler: StopLinkedInStateOrchestratorCommandHandler;
      getLinkedInStateOrchestratorCommandHandler: GetLinkedInStateOrhestratorMeticsQueryHandler;
    },
  ) {}

  async startLinkedInState(stateConfigId: string, tx: ITransaction) {
    const res =
      await this.ctx.startLinkedInStateOrchestratorCommandHandler.execute({
        stateConfigId,
        tx,
      });
    return res;
  }

  async setupLinkedInState(input: LinkedInStateInput, tx: ITransaction) {
    const res =
      await this.ctx.setupLinkedInStateOrchestratorCommandHandler.execute({
        input,
        tx,
      });
    return res;
  }

  async stopLinkedInState(stateConfigId: string, tx: ITransaction) {
    const res =
      await this.ctx.stopLinkedInStateOrchestratorCommandHandler.execute({
        stateConfigId,
        tx,
      });
    return res;
  }

  async getLinkedInStateMetrics(stateConfigId: string, tx: ITransaction) {
    const res =
      await this.ctx.getLinkedInStateOrchestratorCommandHandler.execute({
        stateConfigId: stateConfigId,
        tx: tx,
      });
    return res;
  }
}
