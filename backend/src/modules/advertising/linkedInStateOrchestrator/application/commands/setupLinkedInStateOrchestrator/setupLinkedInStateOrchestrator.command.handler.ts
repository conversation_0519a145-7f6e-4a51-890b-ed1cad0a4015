import { IConversationCallToActionCopyRepository } from "../../../../application/interfaces/infrastructure/repositories/conversationCallToActionCopy.repository.interface";
import { ILinkedInAdAccountRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdAccount.repository.interface";
import { LinkedInAdProgramRepositoryInterface } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdProgram.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../../../application/interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { ILinkedInPostRepositoryInterface } from "../../../../application/interfaces/infrastructure/repositories/linkedInPost.repository.interface";
import { ILinkedInSponsoredCreativeRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInSponsoredCreative.repository.interface";
import { ILinkedInService } from "../../../../application/interfaces/infrastructure/services/thirdPartyApis/linkedInApi/linkedIn.service.interface";
import {
  CarouselImageLinkedInPost,
  DocumentLinkedInPost,
  SingleImageLinkedInPost,
  SingleVideoLinkedInPost,
} from "../../../../domain/entites/linkedInPost";
import { LinkedInSponsoredCreative } from "../../../../domain/entites/linkedInSponsoredCreative";
import { linkedInStateConfigDomain } from "../../../domain/linkedInStateConfig.domain";
import {
  LinkedInStateInput,
  SponsoredInmailLinkedInStateInput,
  SponsoredPostLinkedInStateInput,
} from "../../../domain/linkedInStateInput.valueObject";
import { mapLinkedInStateInputToCampaigns } from "../../../domain/mapLinkedInStateInputToCampaigns.domain";
import { mapLinkedInStateInputToSponsoredCreatives } from "../../../domain/mapLinkedInStateInputToSponsoredCreatives.domain";
import { ILinkedInStateConfigRepository } from "../../../repository/linkedInStateConfig.repository.interface";
import { LinkedInStateOrchestratorService } from "../../linkedInStateOrchestrator.service";
import { SetupLinkedInStateOrchestratorCommand } from "./setupLinkedInStateOrchestrator.command.interface";

export class SetupLinkedInStateOrchestratorCommandHandler {
  constructor(
    private readonly ctx: {
      linkedInPostRepository: ILinkedInPostRepositoryInterface;
      linkedInSponsoredCreativeRepository: ILinkedInSponsoredCreativeRepository;
      linkedInCampaignRepository: ILinkedInCampaignRepositoryInterface;
      linkedInConversationCallToActionRepository: IConversationCallToActionCopyRepository;
      linkedInStateConfigRepository: ILinkedInStateConfigRepository;
      linkedInService: ILinkedInService;
      linkedInAdAccountRepository: ILinkedInAdAccountRepository;
      adSegmentRepository: ILinkedInAdSegmentRepository;
      adProgramRepository: LinkedInAdProgramRepositoryInterface;
    },
  ) {}

  async execute(command: SetupLinkedInStateOrchestratorCommand) {
    const mappedLinkedInCampaigns = await this.setupLinkedInCampaigns(
      command.input,
    );

    let mappedSponsoredCreatives: LinkedInSponsoredCreative[] | null = null;

    if (command.input.adFormatType == "SPONSORED_CONTENT") {
      mappedSponsoredCreatives =
        await this.setupLinkedInStateForSponsoredContent(command.input);
    } else if (command.input.adFormatType == "SPONSORED_INMAIL") {
      mappedSponsoredCreatives =
        await this.setupLinkedInStateForSponsoredInmail(command.input);
    }
    if (!mappedSponsoredCreatives) {
      throw new Error("Invalid ad format");
    }

    const linkedInStateConfig =
      linkedInStateConfigDomain.setupLinkedInStateConfig({
        adSegmentId: command.input.adSegmentId,
        campaignIds: mappedLinkedInCampaigns.map(
          (each) => each.linkedInAudienceId,
        ),
        sponsoredCreativeIds: mappedSponsoredCreatives.map((each) => each.id),
      });

    await this.ctx.linkedInStateConfigRepository.createOne(
      linkedInStateConfig,
      command.tx,
    );
    return linkedInStateConfig;
  }

  private async setupLinkedInCampaigns(input: LinkedInStateInput) {
    const campaigns =
      await this.ctx.linkedInCampaignRepository.getManyForAdSegment(
        input.adSegmentId,
      );

    const mappedLinkedInCampaigns = mapLinkedInStateInputToCampaigns(
      input,
      campaigns,
    );
    if (mappedLinkedInCampaigns.isErr()) {
      throw new Error("Error mapping linkedInCampaigns");
    }

    return mappedLinkedInCampaigns.value;
  }

  private async setupLinkedInStateForSponsoredInmail(
    input: SponsoredInmailLinkedInStateInput,
  ) {
    const conversationCallToActions =
      await this.ctx.linkedInConversationCallToActionRepository.getAllForAdSegment(
        input.adSegmentId,
      );
    const sponsoredCreatives =
      await this.ctx.linkedInSponsoredCreativeRepository.getManyForAdSegment(
        input.adSegmentId,
      );

    const mappedLinkedInStateForSponsoredInmail =
      mapLinkedInStateInputToSponsoredCreatives.forSponsoredInmail({
        linkedInStateInput: input,
        conversationCallToAction: conversationCallToActions,
        sponsoredCreatives: sponsoredCreatives,
      });
    if (mappedLinkedInStateForSponsoredInmail.isErr()) {
      throw new Error("Error mapping linkedInStateForSponsoredInmail");
    }

    return mappedLinkedInStateForSponsoredInmail.value;
  }

  private async setupLinkedInStateForSponsoredContent(
    input: SponsoredPostLinkedInStateInput,
  ) {
    let linkedInPosts:
      | {
          type: "SINGLE_IMAGE";
          content: SingleImageLinkedInPost[];
        }
      | {
          type: "SINGLE_VIDEO";
          content: SingleVideoLinkedInPost[];
        }
      | {
          type: "CAROUSEL_IMAGE";
          content: CarouselImageLinkedInPost[];
        }
      | {
          type: "DOCUMENT";
          content: DocumentLinkedInPost[];
        }
      | null = null;

    switch (input.adFormat) {
      case "SINGLE_IMAGE":
        linkedInPosts = {
          type: "SINGLE_IMAGE",
          content:
            await this.ctx.linkedInPostRepository.getSingleImagePostsForAdSegment(
              {
                adSegmentId: input.adSegmentId,
              },
            ),
        };
        break;
      case "SINGLE_VIDEO":
        linkedInPosts = {
          type: "SINGLE_VIDEO",
          content:
            await this.ctx.linkedInPostRepository.getSingleVideoPostsForAdSegment(
              {
                adSegmentId: input.adSegmentId,
              },
            ),
        };
        break;
      case "DOCUMENT":
        linkedInPosts = {
          type: "DOCUMENT",
          content:
            await this.ctx.linkedInPostRepository.getSingleDocumentPostsForAdSegment(
              {
                adSegmentId: input.adSegmentId,
              },
            ),
        };
        break;
      default:
        throw new Error("Invalid ad format");
    }
    const linkedInSponsoredCreatives =
      await this.ctx.linkedInSponsoredCreativeRepository.getManyForAdSegment(
        input.adSegmentId,
      );

    const mappedLinkedInSponsoredCreatives =
      mapLinkedInStateInputToSponsoredCreatives.forSponsoredContent({
        linkedInPosts: linkedInPosts.content,
        linkedInStateInput: input,
        sponsoredCreatives: linkedInSponsoredCreatives,
      });

    if (mappedLinkedInSponsoredCreatives.isErr()) {
      throw new Error("Error mapping linkedInSponsoredCreatives");
    }

    return mappedLinkedInSponsoredCreatives.value;
  }
}
