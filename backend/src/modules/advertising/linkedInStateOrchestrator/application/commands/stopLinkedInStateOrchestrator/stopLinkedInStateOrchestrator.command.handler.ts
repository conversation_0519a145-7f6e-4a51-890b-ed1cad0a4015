import { err, ok, Result } from "neverthrow";

import { ILinkedInAdAccountRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdAccount.repository.interface";
import { LinkedInAdProgramRepositoryInterface } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdProgram.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../../../application/interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { ILinkedInSponsoredCreativeRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInSponsoredCreative.repository.interface";
import { ILinkedInService } from "../../../../application/interfaces/infrastructure/services/thirdPartyApis/linkedInApi/linkedIn.service.interface";
import { linkedInStateConfigDomain } from "../../../domain/linkedInStateConfig.domain";
import { LinkedInStateOutput } from "../../../domain/linkedInStateOutput.entity";
import { ILinkedInStateConfigRepository } from "../../../repository/linkedInStateConfig.repository.interface";
import { StopLinkedInStateOrchestratorCommand } from "./stopLinkedInStateOrchestrator.command.interface";

export class StopLinkedInStateOrchestratorCommandHandler {
  constructor(
    private readonly ctx: {
      linkedInStateConfigRepository: ILinkedInStateConfigRepository;
      linkedInService: ILinkedInService;
      adSegmentRepository: ILinkedInAdSegmentRepository;
      adProgramRepository: LinkedInAdProgramRepositoryInterface;
      linkedInAdAccountRepository: ILinkedInAdAccountRepository;
      campaignRepository: ILinkedInCampaignRepositoryInterface;
      linkedInCampaignAnalyticsRepository: ILinkedInSponsoredCreativeRepository;
    },
  ) {}

  async execute(
    command: StopLinkedInStateOrchestratorCommand,
  ): Promise<Result<void, { type: "STATE_CONFIG_NOT_FOUND" }>> {
    const stateConfig = await this.ctx.linkedInStateConfigRepository.getOne(
      command.stateConfigId,
      command.tx,
    );

    if (!stateConfig) {
      return err({
        type: "STATE_CONFIG_NOT_FOUND",
      });
    }

    const adSegment = await this.ctx.adSegmentRepository.getOne(
      stateConfig.adSegmentId,
      command.tx,
    );

    if (!adSegment) {
      throw new Error("Ad segment not found");
    }

    const adProgram = await this.ctx.adProgramRepository.getOne(
      adSegment.linkedInAdProgramId,
    );

    if (!adProgram) {
      throw new Error("Ad program not found");
    }

    const adAccount = await this.ctx.linkedInAdAccountRepository.getOneById(
      adProgram.linkedInAdAccountId,
      command.tx,
    );

    if (!adAccount) {
      throw new Error("Ad account not found");
    }

    const campaigns = await this.ctx.campaignRepository.getManyForAdSegment(
      adSegment.id,
      command.tx,
    );

    const startDate = stateConfig.startDatetime;
    if (!startDate) {
      throw new Error("Start date not found");
    }

    const endDate = new Date(startDate);

    const campaignsWithUrns: {
      campaignId: string;
      urn: string;
    }[] = [];

    for (const campaign of stateConfig.campaigns) {
      const urn = campaigns.find(
        (each) => each.linkedInAudienceId === campaign.campaignId,
      )?.linkedInCampaignUrn;
      if (!urn) {
        throw new Error("Campaign not found");
      }

      campaignsWithUrns.push({
        campaignId: campaign.campaignId,
        urn: urn,
      });
    }

    const sponsoredCreativesWithUrns: {
      campaignId: string;
      urn: string;
    }[] = [];

    const sponsoredCreatives =
      await this.ctx.linkedInCampaignAnalyticsRepository.getManyForAdSegment(
        stateConfig.adSegmentId,
        command.tx,
      );

    for (const sponsoredCreative of stateConfig.sponsoredCreatives) {
      const urn = sponsoredCreatives.find(
        (each) => each.id === sponsoredCreative.sponsoredCreativeId,
      )?.linkedInSponseredCreativeUrn;
      if (!urn) {
        throw new Error("Sponsored creative not found");
      }

      sponsoredCreativesWithUrns.push({
        campaignId: sponsoredCreative.sponsoredCreativeId,
        urn: urn,
      });
    }

    const campaignAnalytics =
      await this.ctx.linkedInService.getAnalyticsForCampaigns({
        campaignUrns: campaignsWithUrns.map((each) => each.urn),
        startDate: startDate,
        endDate: endDate,
        timeGranularity: "DAILY",
      });

    const sponsoredCreativeAnalytics =
      await this.ctx.linkedInService.getAnalyticsForCreatives({
        sponsoredCreativeUrns: sponsoredCreativesWithUrns.map(
          (each) => each.urn,
        ),
        startDate: startDate,
        endDate: endDate,
        timeGranularity: "DAILY",
      });

    const campaignMetrics = [];

    for (const campaignWithUrn of campaignsWithUrns) {
      const campaignAnalytic = campaignAnalytics.find(
        (each) => each.campaignUrn === campaignWithUrn.urn,
      );
      if (!campaignAnalytic) {
        campaignMetrics.push({
          campaignId: campaignWithUrn.campaignId,
          metrics: null,
        });
      } else {
        campaignMetrics.push({
          campaignId: campaignWithUrn.campaignId,
          metrics: {
            impressions: campaignAnalytic.impressions,
            clicks: campaignAnalytic.clicks,
            conversions: campaignAnalytic.externalWebsiteConversions,
            leads: campaignAnalytic.oneClickLeads,
            cost: campaignAnalytic.costInUsd,
            videoViews: campaignAnalytic.videoViews,
            sends: campaignAnalytic.sends,
            opens: campaignAnalytic.opens,
            landingPageClicks: campaignAnalytic.landingPageClicks,
            videoCompletions: campaignAnalytic.videoCompletions,
            videoFirstQuartileCompletions:
              campaignAnalytic.videoFirstQuartileCompletions,
            videoMidpointCompletions: campaignAnalytic.videoMidpointCompletions,
            videoThirdQuartileCompletions:
              campaignAnalytic.videoThirdQuartileCompletions,
            videoStarts: campaignAnalytic.videoStarts,
            oneClickLeadFormOpens: campaignAnalytic.oneClickLeadFormOpens,
            totalEngagements: campaignAnalytic.totalEngagements,
            actionClicks: campaignAnalytic.actionClicks,
            externalWebsiteConversions:
              campaignAnalytic.externalWebsiteConversions,
          },
        });
      }
    }

    const sponsoredCreativeMetrics = [];

    for (const sponsoredCreativeWithUrn of sponsoredCreativesWithUrns) {
      const sponsoredCreativeAnalytic = sponsoredCreativeAnalytics.find(
        (each) => each.sponsoredCreatieUrn === sponsoredCreativeWithUrn.urn,
      );

      if (!sponsoredCreativeAnalytic) {
        sponsoredCreativeMetrics.push({
          sponsoredCreativeId: sponsoredCreativeWithUrn.campaignId,
          metrics: null,
        });
      } else {
        sponsoredCreativeMetrics.push({
          sponsoredCreativeId: sponsoredCreativeWithUrn.campaignId,
          metrics: {
            impressions: sponsoredCreativeAnalytic.impressions,
            clicks: sponsoredCreativeAnalytic.clicks,
            conversions: sponsoredCreativeAnalytic.externalWebsiteConversions,
            leads: sponsoredCreativeAnalytic.oneClickLeads,
            cost: sponsoredCreativeAnalytic.costInUsd,
            videoViews: sponsoredCreativeAnalytic.videoViews,
            sends: sponsoredCreativeAnalytic.sends,
            opens: sponsoredCreativeAnalytic.opens,
            landingPageClicks: sponsoredCreativeAnalytic.landingPageClicks,
            videoCompletions: sponsoredCreativeAnalytic.videoCompletions,
            videoFirstQuartileCompletions:
              sponsoredCreativeAnalytic.videoFirstQuartileCompletions,
            videoMidpointCompletions:
              sponsoredCreativeAnalytic.videoMidpointCompletions,
            videoThirdQuartileCompletions:
              sponsoredCreativeAnalytic.videoThirdQuartileCompletions,
            videoStarts: sponsoredCreativeAnalytic.videoStarts,
            oneClickLeadFormOpens:
              sponsoredCreativeAnalytic.oneClickLeadFormOpens,
            totalEngagements: sponsoredCreativeAnalytic.totalEngagements,
            actionClicks: sponsoredCreativeAnalytic.actionClicks,
            externalWebsiteConversions:
              sponsoredCreativeAnalytic.externalWebsiteConversions,
          },
        });
      }
    }

    const linkedInStateConfig =
      linkedInStateConfigDomain.stopLinkedInStateConfig(
        stateConfig,
        campaignMetrics,
        sponsoredCreativeMetrics,
      );

    await this.ctx.linkedInStateConfigRepository.updateOne(
      linkedInStateConfig,
      command.tx,
    );

    return ok();
  }
}
