import { err, ok, Result } from "neverthrow";

import { ILinkedInAdAccountRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdAccount.repository.interface";
import { LinkedInAdProgramRepositoryInterface } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdProgram.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../../../application/interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { ILinkedInSponsoredCreativeRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInSponsoredCreative.repository.interface";
import { ILinkedInService } from "../../../../application/interfaces/infrastructure/services/thirdPartyApis/linkedInApi/linkedIn.service.interface";
import { getLinkedInDesiredStateFromStateConfig } from "../../../domain/getLinkedInDesiredStateFromStateConfig.domain";
import { linkedInStateConfigDomain } from "../../../domain/linkedInStateConfig.domain";
import { LinkedInStateConfig } from "../../../domain/linkedInStateConfig.entity";
import { ILinkedInStateConfigRepository } from "../../../repository/linkedInStateConfig.repository.interface";
import { LinkedInStateOrchestratorService } from "../../linkedInStateOrchestrator.service";
import { StartLinkedInStateOrchestratorCommand } from "./startLinkedInStateOrchestrator.command.interface";

export class StartLinkedInStateOrchestratorCommandHandler {
  constructor(
    private readonly ctx: {
      linkedInStateConfigRepository: ILinkedInStateConfigRepository;
      linkedInCampaignRepository: ILinkedInCampaignRepositoryInterface;
      linkedInSponsoredCreativeRepository: ILinkedInSponsoredCreativeRepository;
      adSegmentRepository: ILinkedInAdSegmentRepository;
      adProgramRepository: LinkedInAdProgramRepositoryInterface;
      linkedInAdAccountRepository: ILinkedInAdAccountRepository;
      linkedInService: ILinkedInService;
    },
  ) {}

  async execute(command: StartLinkedInStateOrchestratorCommand): Promise<
    Result<
      LinkedInStateConfig,
      {
        type:
          | "STATE_CONFIG_NOT_FOUND"
          | "AD_SEGMENT_NOT_FOUND"
          | "AD_PROGRAM_NOT_FOUND";
      }
    >
  > {
    const stateConfig = await this.ctx.linkedInStateConfigRepository.getOne(
      command.stateConfigId,
      command.tx,
    );
    if (!stateConfig) {
      return err({
        type: "STATE_CONFIG_NOT_FOUND",
      });
    }

    const [campaigns, sponsoredCreatives] = await Promise.all([
      this.ctx.linkedInCampaignRepository.getManyForAdSegment(
        stateConfig.adSegmentId,
        command.tx,
      ),
      this.ctx.linkedInSponsoredCreativeRepository.getManyForAdSegment(
        stateConfig.adSegmentId,
        command.tx,
      ),
    ]);

    const desiredState = getLinkedInDesiredStateFromStateConfig({
      stateConfig,
      campaigns,
      sponsoredCreatives,
    });

    const adSegment = await this.ctx.adSegmentRepository.getOne(
      stateConfig.adSegmentId,
      command.tx,
    );
    if (!adSegment) {
      return err({
        type: "AD_SEGMENT_NOT_FOUND",
      });
    }

    const adProgram = await this.ctx.adProgramRepository.getOne(
      adSegment.linkedInAdProgramId,
    );

    if (!adProgram) {
      return err({
        type: "AD_PROGRAM_NOT_FOUND",
      });
    }

    const adAccount = await this.ctx.linkedInAdAccountRepository.getOneById(
      adProgram.linkedInAdAccountId,
      command.tx,
    );
    if (!adAccount) {
      throw new Error("Ad account not found");
    }

    for (const campaign of desiredState.campaigns) {
      await this.ctx.linkedInService.updateCampaignStatus({
        adAccountUrn: adAccount.linkedInAdAccountUrn,
        campaignUrn: campaign.campaignUrn,
        status: campaign.status,
      });
      await this.ctx.linkedInCampaignRepository.updateStatusById(
        campaign.campaignId,
        campaign.status,
      );
    }

    for (const sponsoredCreative of desiredState.sponsoredCreatives) {
      await this.ctx.linkedInService.updateSponsoredCreativeStatus({
        adAccountUrn: adAccount.linkedInAdAccountUrn,
        adUrn: sponsoredCreative.sponsoredCreativeUrn,
        status: sponsoredCreative.status,
      });
      await this.ctx.linkedInSponsoredCreativeRepository.updateStatusById(
        sponsoredCreative.sponsoredCreativeId,
        sponsoredCreative.status,
      );
    }

    const updatedLinkedInStateConfig =
      linkedInStateConfigDomain.startLinkedInStateConfig(stateConfig);

    await this.ctx.linkedInStateConfigRepository.updateOne(
      updatedLinkedInStateConfig,
      command.tx,
    );

    return ok(updatedLinkedInStateConfig);
  }
}
