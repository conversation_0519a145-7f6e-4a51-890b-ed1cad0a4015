import { err, ok, Result } from "neverthrow";

import { IConversationCallToActionCopyRepository } from "../../../application/interfaces/infrastructure/repositories/conversationCallToActionCopy.repository.interface";
import { LinkedInAdProgramRepositoryInterface } from "../../../application/interfaces/infrastructure/repositories/linkedInAdProgram.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../../application/interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { ILinkedInPostRepositoryInterface } from "../../../application/interfaces/infrastructure/repositories/linkedInPost.repository.interface";
import { ILinkedInSponsoredCreativeRepository } from "../../../application/interfaces/infrastructure/repositories/linkedInSponsoredCreative.repository.interface";
import {
  CarouselImageLinkedInPost,
  DocumentLinkedInPost,
  SingleImageLinkedInPost,
  SingleVideoLinkedInPost,
} from "../../../domain/entites/linkedInPost";
import { LinkedInAdProgramRepository } from "../../../infrastructure/repositories/linkedInAdProgram.repository";
import { getLinkedInStateConfigMetrics } from "../../domain/getLinkedInStateConfigMetrics.domain";
import { LinkedInStateOutput } from "../../domain/linkedInStateOutput.entity";
import { ILinkedInStateConfigRepository } from "../../repository/linkedInStateConfig.repository.interface";
import { GetLinkedInStateOrhestratorMeticsQuery } from "./getLinkedInStateOrhestratorMetics.query.interface";

export class GetLinkedInStateOrhestratorMeticsQueryHandler {
  constructor(
    private readonly linkedInStateConfigRepository: ILinkedInStateConfigRepository,
    private readonly adSegmentRepository: ILinkedInAdSegmentRepository,
    private readonly adProgramRepository: LinkedInAdProgramRepositoryInterface,
    private readonly postRepository: ILinkedInPostRepositoryInterface,
    private readonly linkedInSponsoredCreativeRepository: ILinkedInSponsoredCreativeRepository,
    private readonly conversationCallToActionCopyRepository: IConversationCallToActionCopyRepository,
  ) {}

  async execute(
    query: GetLinkedInStateOrhestratorMeticsQuery,
  ): Promise<Result<LinkedInStateOutput, { type: "STAGE_CONFIG_NOT_FOUND" }>> {
    const stateConfig = await this.linkedInStateConfigRepository.getOne(
      query.stateConfigId,
      query.tx,
    );
    if (!stateConfig) {
      return err({ type: "STAGE_CONFIG_NOT_FOUND" });
    }
    const stageConfig = await this.linkedInStateConfigRepository.getOne(
      query.stateConfigId,
      query.tx,
    );
    if (!stageConfig) {
      return err({ type: "STAGE_CONFIG_NOT_FOUND" });
    }

    const adSegment = await this.adSegmentRepository.getOne(
      stageConfig.adSegmentId,
      query.tx,
    );
    if (!adSegment) {
      throw new Error("Ad segment not found");
    }

    const adProgram = await this.adProgramRepository.getOne(
      adSegment.linkedInAdProgramId,
    );
    if (!adProgram) {
      throw new Error("Ad program not found");
    }

    const sponsoredCreatives =
      await this.linkedInSponsoredCreativeRepository.getManyForAdSegment(
        adSegment.id,
      );

    if (adProgram.adFormat.type == "SPONSORED_CONTENT") {
      let linkedInPosts:
        | {
            type: "SINGLE_IMAGE";
            content: SingleImageLinkedInPost[];
          }
        | {
            type: "SINGLE_VIDEO";
            content: SingleVideoLinkedInPost[];
          }
        | {
            type: "CAROUSEL_IMAGE";
            content: CarouselImageLinkedInPost[];
          }
        | {
            type: "DOCUMENT";
            content: DocumentLinkedInPost[];
          }
        | null = null;

      switch (adProgram.adFormat.format) {
        case "SINGLE_IMAGE":
          linkedInPosts = {
            type: "SINGLE_IMAGE",
            content: await this.postRepository.getSingleImagePostsForAdSegment({
              adSegmentId: adSegment.id,
            }),
          };
          break;
        case "VIDEO":
          linkedInPosts = {
            type: "SINGLE_VIDEO",
            content: await this.postRepository.getSingleVideoPostsForAdSegment({
              adSegmentId: adSegment.id,
            }),
          };
          break;
        case "DOCUMENT":
          linkedInPosts = {
            type: "DOCUMENT",
            content:
              await this.postRepository.getSingleDocumentPostsForAdSegment({
                adSegmentId: adSegment.id,
              }),
          };
          break;
        default:
          throw new Error("Invalid ad format");
      }

      const linkedInStateOutput =
        await getLinkedInStateConfigMetrics.forSponsoredContent({
          adProgram: adProgram,
          posts: linkedInPosts.content,
          linkedInStateConfig: stateConfig,
          sponsoredCreatives: sponsoredCreatives,
        });
      if (linkedInStateOutput.isErr()) {
        throw new Error("Invalid ad format");
      }
      return ok(linkedInStateOutput.value);
    } else if (adProgram.adFormat.type == "SPONSORED_INMAIL") {
      const conversationCallToActionCopies =
        await this.conversationCallToActionCopyRepository.getAllForAdSegment(
          adSegment.id,
        );
      const linkedInStateOutput =
        await getLinkedInStateConfigMetrics.forSponsoredInmail({
          adProgram: adProgram,
          linkedInStateConfig: stateConfig,
          conversationCallToActionCopies: conversationCallToActionCopies,
          sponsoredCreatives: sponsoredCreatives,
        });
      if (linkedInStateOutput.isErr()) {
        throw new Error("Invalid ad format");
      }
      return ok(linkedInStateOutput.value);
    }

    throw new Error("Invalid ad format");
  }
}
