import { Transaction } from "../../../../database/dbTransactionType";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { LinkedInStateConfig } from "../domain/linkedInStateConfig.entity";

export interface ILinkedInStateConfigRepository {
  createOne(
    input: LinkedInStateConfig,
    tx: ITransaction,
  ): Promise<LinkedInStateConfig>;
  getOne(id: string, tx: ITransaction): Promise<LinkedInStateConfig | null>;
  updateOne(
    input: LinkedInStateConfig,
    tx: ITransaction,
  ): Promise<LinkedInStateConfig>;
}
