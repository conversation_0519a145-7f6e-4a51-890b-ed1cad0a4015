import { eq } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { linkedInDeploymentConfigTable } from "../../../../database/schemas/advertising/linkedInDeploymentConfig.table";
import { linkedInDeploymentConfigCampaign } from "../../../../database/schemas/advertising/linkedInDeploymentConfigCampaign.table";
import { linkedInDeploymentConfigSponsoredCreative } from "../../../../database/schemas/advertising/linkedInDeploymentConfigSponsoredCreative.table";
import { createUuid } from "../../../core/utils/uuid";
import { LinkedInStateConfig } from "../domain/linkedInStateConfig.entity";
import { ILinkedInStateConfigRepository } from "./linkedInStateConfig.repository.interface";

export class LinkedInStateConfigRepository
  implements ILinkedInStateConfigRepository
{
  async getOne(
    stepId: string,
    tx: Transaction,
  ): Promise<LinkedInStateConfig | null> {
    const invoker = tx ?? db;
    const dbRes = await invoker
      .select()
      .from(linkedInDeploymentConfigTable)
      .where(eq(linkedInDeploymentConfigTable.id, stepId))
      .leftJoin(
        linkedInDeploymentConfigCampaign,
        eq(
          linkedInDeploymentConfigTable.id,
          linkedInDeploymentConfigCampaign.linkedInDeploymentConfigId,
        ),
      )
      .leftJoin(
        linkedInDeploymentConfigSponsoredCreative,
        eq(
          linkedInDeploymentConfigTable.id,
          linkedInDeploymentConfigSponsoredCreative.linkedInDeploymentConfigId,
        ),
      );

    if (dbRes.length === 0) {
      return null;
    }

    const linkedInStateConfigMap = dbRes.reduce<
      Record<string, LinkedInStateConfig>
    >((acc, row) => {
      let current = acc[row.linkedin_deployment_config.id];
      if (!current) {
        current = {
          id: row.linkedin_deployment_config.id,
          status: row.linkedin_deployment_config.status,
          adSegmentId: row.linkedin_deployment_config.linkedInAdSegmentId,
          campaigns: [],
          sponsoredCreatives: [],
          startDatetime: row.linkedin_deployment_config.startDatetime,
          endDatetime: row.linkedin_deployment_config.endDatetime,
        };
      }
      if (row.linkedin_deployment_config_campaign) {
        current.campaigns.push({
          campaignId: row.linkedin_deployment_config_campaign.campaignId,
          metrics: {
            impressions:
              row.linkedin_deployment_config_campaign.impressions ?? null,
            clicks: row.linkedin_deployment_config_campaign.clicks ?? null,
            conversions:
              row.linkedin_deployment_config_campaign.conversions ?? null,
            cost:
              row.linkedin_deployment_config_campaign.cost == null
                ? null
                : parseFloat(row.linkedin_deployment_config_campaign.cost),
            leads: row.linkedin_deployment_config_campaign.leads ?? null,
            videoViews:
              row.linkedin_deployment_config_campaign.videoViews ?? null,
            sends: row.linkedin_deployment_config_campaign.sends ?? null,
            opens: row.linkedin_deployment_config_campaign.opens ?? null,
            actionClicks:
              row.linkedin_deployment_config_campaign.actionClicks ?? null,
            totalEngagements:
              row.linkedin_deployment_config_campaign.totalEngagements ?? null,
            oneClickLeadFormOpens:
              row.linkedin_deployment_config_campaign.oneClickLeadFormOpens ??
              null,
            landingPageClicks:
              row.linkedin_deployment_config_campaign.landingPageClicks ?? null,
            videoCompletions:
              row.linkedin_deployment_config_campaign.videoCompletions ?? null,
            videoFirstQuartileCompletions:
              row.linkedin_deployment_config_campaign
                .videoFirstQuartileCompletions ?? null,
            videoMidpointCompletions:
              row.linkedin_deployment_config_campaign
                .videoMidpointCompletions ?? null,
            videoThirdQuartileCompletions:
              row.linkedin_deployment_config_campaign
                .videoThirdQuartileCompletions ?? null,
            videoStarts:
              row.linkedin_deployment_config_campaign.videoStarts ?? null,
            externalWebsiteConversions:
              row.linkedin_deployment_config_campaign
                .externalWebsiteConversions ?? null,
          },
        });
      }
      if (row.linkedin_deployment_config_sponsored_creative) {
        current.sponsoredCreatives.push({
          sponsoredCreativeId:
            row.linkedin_deployment_config_sponsored_creative
              .sponsoredCreativeId,
          metrics: {
            impressions:
              row.linkedin_deployment_config_sponsored_creative.impressions ??
              null,
            clicks:
              row.linkedin_deployment_config_sponsored_creative.clicks ?? null,
            conversions:
              row.linkedin_deployment_config_sponsored_creative.conversions ??
              null,
            cost:
              row.linkedin_deployment_config_sponsored_creative.cost == null
                ? null
                : parseFloat(
                    row.linkedin_deployment_config_sponsored_creative.cost,
                  ),
            leads:
              row.linkedin_deployment_config_sponsored_creative.leads ?? null,
            videoViews:
              row.linkedin_deployment_config_sponsored_creative.videoViews ??
              null,
            sends:
              row.linkedin_deployment_config_sponsored_creative.sends ?? null,
            opens:
              row.linkedin_deployment_config_sponsored_creative.opens ?? null,
            actionClicks:
              row.linkedin_deployment_config_sponsored_creative.actionClicks ??
              null,
            totalEngagements:
              row.linkedin_deployment_config_sponsored_creative
                .totalEngagements ?? null,
            oneClickLeadFormOpens:
              row.linkedin_deployment_config_sponsored_creative
                .oneClickLeadFormOpens ?? null,
            landingPageClicks:
              row.linkedin_deployment_config_sponsored_creative
                .landingPageClicks ?? null,
            videoCompletions:
              row.linkedin_deployment_config_sponsored_creative
                .videoCompletions ?? null,
            videoFirstQuartileCompletions:
              row.linkedin_deployment_config_sponsored_creative
                .videoFirstQuartileCompletions ?? null,
            videoMidpointCompletions:
              row.linkedin_deployment_config_sponsored_creative
                .videoMidpointCompletions ?? null,
            videoThirdQuartileCompletions:
              row.linkedin_deployment_config_sponsored_creative
                .videoThirdQuartileCompletions ?? null,
            videoStarts:
              row.linkedin_deployment_config_sponsored_creative.videoStarts ??
              null,
            externalWebsiteConversions:
              row.linkedin_deployment_config_sponsored_creative
                .externalWebsiteConversions ?? null,
          },
        });
      }
      acc[row.linkedin_deployment_config.id] = current;
      return acc;
    }, {});

    const linkedInStateConfig = Object.values(linkedInStateConfigMap)[0];
    if (!linkedInStateConfig) {
      return null;
    }
    return linkedInStateConfig;
  }

  async createOne(
    linkedInStateConfig: LinkedInStateConfig,
    tx: Transaction,
  ): Promise<LinkedInStateConfig> {
    const invoker = tx ?? db;
    await invoker.insert(linkedInDeploymentConfigTable).values({
      id: linkedInStateConfig.id,
      linkedInAdSegmentId: linkedInStateConfig.adSegmentId,
      status: linkedInStateConfig.status,
      startDatetime: linkedInStateConfig.startDatetime,
      endDatetime: linkedInStateConfig.endDatetime,
    });

    if (linkedInStateConfig.campaigns.length > 0) {
      const res = await invoker.insert(linkedInDeploymentConfigCampaign).values(
        linkedInStateConfig.campaigns.map((c) => ({
          id: createUuid(),
          linkedInDeploymentConfigId: linkedInStateConfig.id,
          campaignId: c.campaignId,
        })),
      );
    }

    if (linkedInStateConfig.sponsoredCreatives.length > 0) {
      await invoker.insert(linkedInDeploymentConfigSponsoredCreative).values(
        linkedInStateConfig.sponsoredCreatives.map((c) => ({
          id: createUuid(),
          linkedInDeploymentConfigId: linkedInStateConfig.id,
          sponsoredCreativeId: c.sponsoredCreativeId,
        })),
      );
    }

    return linkedInStateConfig;
  }

  async updateOne(
    input: LinkedInStateConfig,
    tx: Transaction,
  ): Promise<LinkedInStateConfig> {
    const invoker = tx;
    await invoker
      .update(linkedInDeploymentConfigTable)
      .set({
        status: input.status,
        startDatetime: input.startDatetime,
        endDatetime: input.endDatetime,
      })
      .where(eq(linkedInDeploymentConfigTable.id, input.id));

    await invoker
      .delete(linkedInDeploymentConfigCampaign)
      .where(
        eq(
          linkedInDeploymentConfigCampaign.linkedInDeploymentConfigId,
          input.id,
        ),
      );

    await invoker
      .delete(linkedInDeploymentConfigSponsoredCreative)
      .where(
        eq(
          linkedInDeploymentConfigSponsoredCreative.linkedInDeploymentConfigId,
          input.id,
        ),
      );

    await invoker.insert(linkedInDeploymentConfigCampaign).values(
      input.campaigns.map((c) => ({
        id: createUuid(),
        linkedInDeploymentConfigId: input.id,
        campaignId: c.campaignId,
        impressions: c.metrics?.impressions ?? null,
        clicks: c.metrics?.clicks ?? null,
        conversions: c.metrics?.conversions ?? null,
        videoViews: c.metrics?.videoViews ?? null,
        cost: c.metrics?.cost ? Math.round(c.metrics.cost).toString() : null,
        sends: c.metrics?.sends ?? null,
        opens: c.metrics?.opens ?? null,
        leads: c.metrics?.leads ?? null,
        actionClicks: c.metrics?.actionClicks ?? null,
        totalEngagements: c.metrics?.totalEngagements ?? null,
        oneClickLeadFormOpens: c.metrics?.oneClickLeadFormOpens ?? null,
        landingPageClicks: c.metrics?.landingPageClicks ?? null,
        videoCompletions: c.metrics?.videoCompletions ?? null,
        videoFirstQuartileCompletions:
          c.metrics?.videoFirstQuartileCompletions ?? null,
        videoMidpointCompletions: c.metrics?.videoMidpointCompletions ?? null,
        videoThirdQuartileCompletions:
          c.metrics?.videoThirdQuartileCompletions ?? null,
        videoStarts: c.metrics?.videoStarts ?? null,
        externalWebsiteConversions:
          c.metrics?.externalWebsiteConversions ?? null,
      })),
    );

    await invoker.insert(linkedInDeploymentConfigSponsoredCreative).values(
      input.sponsoredCreatives.map((c) => ({
        id: createUuid(),
        linkedInDeploymentConfigId: input.id,
        sponsoredCreativeId: c.sponsoredCreativeId,
        impressions: c.metrics?.impressions ?? null,
        clicks: c.metrics?.clicks ?? null,
        conversions: c.metrics?.conversions ?? null,
        cost: c.metrics?.cost ? Math.round(c.metrics.cost).toString() : null,
        videoViews: c.metrics?.videoViews ?? null,
        sends: c.metrics?.sends ?? null,
        opens: c.metrics?.opens ?? null,
        leads: c.metrics?.leads ?? null,
        actionClicks: c.metrics?.actionClicks ?? null,
        totalEngagements: c.metrics?.totalEngagements ?? null,
        oneClickLeadFormOpens: c.metrics?.oneClickLeadFormOpens ?? null,
        landingPageClicks: c.metrics?.landingPageClicks ?? null,
        videoCompletions: c.metrics?.videoCompletions ?? null,
        videoFirstQuartileCompletions:
          c.metrics?.videoFirstQuartileCompletions ?? null,
        videoMidpointCompletions: c.metrics?.videoMidpointCompletions ?? null,
        videoThirdQuartileCompletions:
          c.metrics?.videoThirdQuartileCompletions ?? null,
        videoStarts: c.metrics?.videoStarts ?? null,
        externalWebsiteConversions:
          c.metrics?.externalWebsiteConversions ?? null,
      })),
    );

    return input;
  }
}
