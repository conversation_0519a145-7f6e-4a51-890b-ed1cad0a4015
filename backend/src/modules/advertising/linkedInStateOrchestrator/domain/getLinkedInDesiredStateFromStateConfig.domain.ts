import { LinkedInCampaign } from "../../domain/entites/linkedInCampaign";
import { LinkedInSponsoredCreative } from "../../domain/entites/linkedInSponsoredCreative";
import { LinkedInStateConfig } from "./linkedInStateConfig.entity";

const getCampaignDesiredStateFromStateConfig = (input: {
  campaigns: LinkedInCampaign[];
  stateConfig: LinkedInStateConfig;
}) => {
  const campaignsWithDesiredState: {
    campaignId: string;
    campaignUrn: string;
    status: "ACTIVE" | "PAUSED";
  }[] = [];

  for (const campaign of input.campaigns) {
    if (
      input.stateConfig.campaigns.find(
        (c) => c.campaignId === campaign.linkedInAudienceId,
      )
    ) {
      campaignsWithDesiredState.push({
        campaignId: campaign.linkedInAudienceId,
        campaignUrn: campaign.linkedInCampaignUrn,
        status: "ACTIVE",
      });
    } else {
      campaignsWithDesiredState.push({
        campaignId: campaign.linkedInAudienceId,
        campaignUrn: campaign.linkedInCampaignUrn,
        status: "PAUSED",
      });
    }
  }

  return campaignsWithDesiredState;
};

const getSponsoredCreativeDesiredStateFromStateConfig = (input: {
  sponsoredCreatives: LinkedInSponsoredCreative[];
  stateConfig: LinkedInStateConfig;
}) => {
  const sponsoredCreativesWithDesiredState: {
    sponsoredCreativeId: string;
    sponsoredCreativeUrn: string;
    status: "ACTIVE" | "PAUSED";
  }[] = [];

  for (const sponsoredCreative of input.sponsoredCreatives) {
    if (
      input.stateConfig.sponsoredCreatives.find(
        (c) => c.sponsoredCreativeId === sponsoredCreative.id,
      )
    ) {
      sponsoredCreativesWithDesiredState.push({
        sponsoredCreativeId: sponsoredCreative.id,
        sponsoredCreativeUrn: sponsoredCreative.linkedInSponseredCreativeUrn,
        status: "ACTIVE",
      });
    } else {
      sponsoredCreativesWithDesiredState.push({
        sponsoredCreativeId: sponsoredCreative.id,
        sponsoredCreativeUrn: sponsoredCreative.linkedInSponseredCreativeUrn,
        status: "PAUSED",
      });
    }
  }

  return sponsoredCreativesWithDesiredState;
};

export const getLinkedInDesiredStateFromStateConfig = (input: {
  stateConfig: LinkedInStateConfig;
  campaigns: LinkedInCampaign[];
  sponsoredCreatives: LinkedInSponsoredCreative[];
}) => {
  const campaignsWithDesiredState =
    getCampaignDesiredStateFromStateConfig(input);

  const sponsoredCreativesWithDesiredState =
    getSponsoredCreativeDesiredStateFromStateConfig(input);
  return {
    campaigns: campaignsWithDesiredState,
    sponsoredCreatives: sponsoredCreativesWithDesiredState,
  };
};
