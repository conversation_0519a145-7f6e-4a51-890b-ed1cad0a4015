import { z } from "zod";

import { createEntity } from "../../../../helpers/entity";

export const linkedInStateConfigSchema = z.object({
  id: z.string().uuid(),
  adSegmentId: z.string().uuid(),
  status: z.enum([
    "NOT_STARTED",
    "RUNNING",
    "CANCELLED",
    "INTERRUPTED",
    "FAILED",
    "COMPLETED",
  ]),
  campaigns: z.array(
    z.object({
      campaignId: z.string(),
      metrics: z
        .object({
          impressions: z.number().nullable(),
          clicks: z.number().nullable(),
          conversions: z.number().nullable(),
          leads: z.number().nullable(),
          cost: z.number().nullable(),
          videoViews: z.number().nullable(),
          sends: z.number().nullable(),
          opens: z.number().nullable(),
          actionClicks: z.number().nullable(),
          totalEngagements: z.number().nullable(),
          oneClickLeadFormOpens: z.number().nullable(),
          landingPageClicks: z.number().nullable(),
          videoCompletions: z.number().nullable(),
          videoFirstQuartileCompletions: z.number().nullable(),
          videoMidpointCompletions: z.number().nullable(),
          videoThirdQuartileCompletions: z.number().nullable(),
          videoStarts: z.number().nullable(),
          externalWebsiteConversions: z.number().nullable(),
        })
        .nullable(),
    }),
  ),
  sponsoredCreatives: z.array(
    z.object({
      sponsoredCreativeId: z.string(),
      metrics: z
        .object({
          impressions: z.number().nullable(),
          clicks: z.number().nullable(),
          conversions: z.number().nullable(),
          leads: z.number().nullable(),
          cost: z.number().nullable(),
          videoViews: z.number().nullable(),
          sends: z.number().nullable(),
          opens: z.number().nullable(),
          actionClicks: z.number().nullable(),
          totalEngagements: z.number().nullable(),
          oneClickLeadFormOpens: z.number().nullable(),
          landingPageClicks: z.number().nullable(),
          videoCompletions: z.number().nullable(),
          videoFirstQuartileCompletions: z.number().nullable(),
          videoMidpointCompletions: z.number().nullable(),
          videoThirdQuartileCompletions: z.number().nullable(),
          videoStarts: z.number().nullable(),
          externalWebsiteConversions: z.number().nullable(),
        })
        .nullable(),
    }),
  ),
  startDatetime: z.coerce.date().nullable(),
  endDatetime: z.coerce.date().nullable(),
});

export const LinkedInStateConfig = createEntity(linkedInStateConfigSchema);
export type LinkedInStateConfig = z.infer<typeof linkedInStateConfigSchema>;
