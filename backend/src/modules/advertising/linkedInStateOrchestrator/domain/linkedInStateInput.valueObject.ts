import { z } from "zod";

import type { AdProgram } from "../../domain/entites/adProgram";

export const sponsoredPostLinkedInStateInputSchema = z.object({
  adSegmentId: z.string().uuid(),
  adFormatType: z.literal("SPONSORED_CONTENT"),
  adFormat: z.enum(["SINGLE_IMAGE", "SINGLE_VIDEO", "DOCUMENT"]),
  data: injectAds(
    z.object({
      adCreativeId: z.string(),
      valuePropId: z.string(),
      bodyCopyType: z.string(),
      callToActionCopyType: z.string(),
    }),
  ),
});

export type SponsoredPostLinkedInStateInput = z.infer<
  typeof sponsoredPostLinkedInStateInputSchema
>;

export const sponsoredInmailLinkedInStateInputSchema = z.object({
  adSegmentId: z.string().uuid(),
  adFormatType: z.literal("SPONSORED_INMAIL"),
  data: injectAds(
    z.object({
      valuePropId: z.string(),
      subjectCopyType: z.string(),
      messageCopyType: z.string(),
      callToActionCopyType: z.string(),
    }),
  ),
});

export type SponsoredInmailLinkedInStateInput = z.infer<
  typeof sponsoredInmailLinkedInStateInputSchema
>;

export const linkedInStateInputSchema = z.discriminatedUnion("adFormatType", [
  sponsoredPostLinkedInStateInputSchema,
  sponsoredInmailLinkedInStateInputSchema,
]);

export type LinkedInStateInput = z.infer<typeof linkedInStateInputSchema>;

function injectAds<T extends z.ZodType>(input: T) {
  return z.object({
    campaigns: z.array(
      z.object({
        campaignId: z.string().uuid(),
        ads: z.array(input),
      }),
    ),
  });
}
