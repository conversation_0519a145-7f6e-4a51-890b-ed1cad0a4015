import { err, ok, Result } from "neverthrow";

import { AdProgram } from "../../domain/entites/adProgram";
import { ConversationCallToActionCopy } from "../../domain/entites/conversationCallToActionCopy";
import { LinkedInPost } from "../../domain/entites/linkedInPost";
import { LinkedInSponsoredCreative } from "../../domain/entites/linkedInSponsoredCreative";
import { LinkedInStateConfig } from "./linkedInStateConfig.entity";
import { LinkedInStateOutput } from "./linkedInStateOutput.entity";

export const getLinkedInStateConfigMetrics = {
  forSponsoredContent: (input: {
    adProgram: AdProgram;
    linkedInStateConfig: LinkedInStateConfig;
    posts: LinkedInPost[];
    sponsoredCreatives: LinkedInSponsoredCreative[];
  }): Result<
    LinkedInStateOutput,
    { type: "STATE_CONFIG_NOT_FOUND" | "AD_PROGRAM_IS_NOT_SPONSORED_CONTENT" }
  > => {
    if (input.adProgram.adFormat.type !== "SPONSORED_CONTENT") {
      throw new Error("Invalid ad format");
    }
    let adFormat: "SINGLE_IMAGE" | "SINGLE_VIDEO" | "DOCUMENT" = "SINGLE_IMAGE";
    if (input.adProgram.adFormat.format === "SINGLE_IMAGE") {
      adFormat = "SINGLE_IMAGE";
    } else if (input.adProgram.adFormat.format === "VIDEO") {
      adFormat = "SINGLE_VIDEO";
    } else if (input.adProgram.adFormat.format === "DOCUMENT") {
      adFormat = "DOCUMENT";
    }
    if (!adFormat) {
      throw new Error("Invalid ad format");
    }

    const linkedInStateOutput: LinkedInStateOutput = {
      adFormatType: "SPONSORED_CONTENT",
      adSegmentId: input.linkedInStateConfig.adSegmentId,
      adFormat: adFormat,
      data: {
        campaigns: [],
      },
    };

    for (const campaign of input.linkedInStateConfig.campaigns) {
      if (!campaign.metrics) {
        throw new Error("Campaign metrics are not set");
      }
      linkedInStateOutput.data.campaigns.push({
        campaignId: campaign.campaignId,
        ads: [],
        metrics: campaign.metrics,
      });
    }

    for (const sponsoredCreative of input.linkedInStateConfig
      .sponsoredCreatives) {
      const sc = input.sponsoredCreatives.find(
        (each) =>
          each.id === sponsoredCreative.sponsoredCreativeId &&
          each.content.type == "SPONSORED_CONTENT",
      );
      if (!sc) {
        throw new Error("Sponsored creative not found");
      }
      if (sc.content.type !== "SPONSORED_CONTENT") {
        throw new Error("Sponsored creative is not a sponsored content");
      }
      const postId = sc.content.postId;
      const post = input.posts.find(
        (post) =>
          post.id === postId &&
          (post.content.type == "SINGLE_IMAGE" ||
            post.content.type == "SINGLE_VIDEO" ||
            post.content.type == "DOCUMENT"),
      );
      if (
        !post ||
        (post.content.type !== "SINGLE_IMAGE" &&
          post.content.type !== "SINGLE_VIDEO" &&
          post.content.type !== "DOCUMENT")
      ) {
        throw new Error("Post not found");
      }
      const stateOutputCampaign = linkedInStateOutput.data.campaigns.find(
        (campaign) => campaign.campaignId === sc.cmapaignId,
      );
      if (!stateOutputCampaign) {
        throw new Error("Campaign not found");
      }
      if (!sponsoredCreative.metrics) {
        throw new Error("Sponsored creative metrics are not set");
      }
      stateOutputCampaign.ads.push({
        adCreativeId: post.content.adCreativeId,
        valuePropId: post.content.linkedInAdSegmentValuePropId,
        bodyCopyType: post.content.socialPostCopyType,
        callToActionCopyType: post.content.socialPostCallToActionType,
        metrics: sponsoredCreative.metrics,
      });
    }
    return ok(linkedInStateOutput);
  },
  forSponsoredInmail: (input: {
    adProgram: AdProgram;
    linkedInStateConfig: LinkedInStateConfig;
    conversationCallToActionCopies: ConversationCallToActionCopy[];
    sponsoredCreatives: LinkedInSponsoredCreative[];
  }): Result<
    LinkedInStateOutput,
    { type: "STATE_CONFIG_NOT_FOUND" | "AD_PROGRAM_IS_NOT_SPONSORED_INMAIL" }
  > => {
    if (input.adProgram.adFormat.type !== "SPONSORED_INMAIL") {
      throw new Error("Invalid ad format");
    }
    const linkedInStateOutput: LinkedInStateOutput = {
      adFormatType: "SPONSORED_INMAIL",
      adSegmentId: input.linkedInStateConfig.adSegmentId,
      data: {
        campaigns: [],
      },
    };

    for (const campaign of input.linkedInStateConfig.campaigns) {
      if (!campaign.metrics) {
        throw new Error("Campaign metrics are not set");
      }
      linkedInStateOutput.data.campaigns.push({
        campaignId: campaign.campaignId,
        ads: [],
        metrics: campaign.metrics,
      });
    }

    for (const sponsoredCreative of input.linkedInStateConfig
      .sponsoredCreatives) {
      const sc = input.sponsoredCreatives.find(
        (each) =>
          each.id === sponsoredCreative.sponsoredCreativeId &&
          each.content.type == "SPONSORED_INMAIL",
      );
      if (!sc) {
        throw new Error("Sponsored creative not found");
      }
      if (sc.content.type !== "SPONSORED_INMAIL") {
        throw new Error("Sponsored creative is not a sponsored inmail");
      }

      const conversationCallToActionId = sc.content.conversationCallToActionId;
      const conversationCallToActionCopy =
        input.conversationCallToActionCopies.find(
          (each) => each.id === conversationCallToActionId,
        );
      if (!conversationCallToActionCopy) {
        throw new Error("Conversation call to action copy not found");
      }
      const stateOutputCampaign = linkedInStateOutput.data.campaigns.find(
        (campaign) => campaign.campaignId === sc.cmapaignId,
      );
      if (!stateOutputCampaign) {
        throw new Error("Campaign not found");
      }
      if (!sponsoredCreative.metrics) {
        throw new Error("Sponsored creative metrics are not set");
      }
      stateOutputCampaign.ads.push({
        valuePropId: conversationCallToActionCopy.valuePropId,
        messageCopyType: conversationCallToActionCopy.messageType,
        subjectCopyType: conversationCallToActionCopy.subjectType,
        callToActionCopyType: conversationCallToActionCopy.type,
        metrics: sponsoredCreative.metrics,
      });
    }
    return ok(linkedInStateOutput);
  },
};
