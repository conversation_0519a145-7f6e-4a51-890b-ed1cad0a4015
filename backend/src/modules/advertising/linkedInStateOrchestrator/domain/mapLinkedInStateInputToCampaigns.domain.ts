import { err, ok, Result } from "neverthrow";

import { LinkedInCampaign } from "../../domain/entites/linkedInCampaign";
import { LinkedInStateInput } from "./linkedInStateInput.valueObject";

export const mapLinkedInStateInputToCampaigns = (
  input: LinkedInStateInput,
  campaigns: LinkedInCampaign[],
): Result<LinkedInCampaign[], { type: "SOME_CAMPAIGNS_NOT_FOUND" }> => {
  const campaignIdsFromLinkedInStateInput = input.data.campaigns.map(
    (campaign) => campaign.campaignId,
  );
  const foundCampaigns = campaigns.filter((campaign) =>
    campaignIdsFromLinkedInStateInput.includes(campaign.linkedInAudienceId),
  );
  if (foundCampaigns.length !== campaignIdsFromLinkedInStateInput.length) {
    return err({ type: "SOME_CAMPAIGNS_NOT_FOUND" });
  }
  return ok(foundCampaigns);
};
