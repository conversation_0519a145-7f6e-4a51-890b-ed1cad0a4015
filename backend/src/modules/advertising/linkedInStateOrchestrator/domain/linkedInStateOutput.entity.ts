import { z } from "zod";

export const sponsoredPostLinkedInStateOutputSchema = z.object({
  adSegmentId: z.string().uuid(),
  adFormatType: z.literal("SPONSORED_CONTENT"),
  adFormat: z.enum(["SINGLE_IMAGE", "SINGLE_VIDEO", "DOCUMENT"]),
  data: injectAds(
    z.object({
      adCreativeId: z.string(),
      valuePropId: z.string(),
      bodyCopyType: z.string(),
      callToActionCopyType: z.string(),
      metrics: z.object({
        impressions: z.number().nullable(),
        clicks: z.number().nullable(),
        conversions: z.number().nullable(),
        leads: z.number().nullable(),
        videoViews: z.number().nullable(),
        sends: z.number().nullable(),
        opens: z.number().nullable(),
        cost: z.number().nullable(),
        actionClicks: z.number().nullable(),
        totalEngagements: z.number().nullable(),
        oneClickLeadFormOpens: z.number().nullable(),
        landingPageClicks: z.number().nullable(),
        videoCompletions: z.number().nullable(),
        videoFirstQuartileCompletions: z.number().nullable(),
        videoMidpointCompletions: z.number().nullable(),
        videoThirdQuartileCompletions: z.number().nullable(),
        videoStarts: z.number().nullable(),
        externalWebsiteConversions: z.number().nullable(),
      }),
    }),
  ),
});

export type SponsoredPostLinkedInStateOutput = z.infer<
  typeof sponsoredPostLinkedInStateOutputSchema
>;

export const sponsoredInmailLinkedInStateOutputSchema = z.object({
  adSegmentId: z.string().uuid(),
  adFormatType: z.literal("SPONSORED_INMAIL"),
  data: injectAds(
    z.object({
      valuePropId: z.string(),
      subjectCopyType: z.string(),
      messageCopyType: z.string(),
      callToActionCopyType: z.string(),
      metrics: z.object({
        impressions: z.number().nullable(),
        clicks: z.number().nullable(),
        conversions: z.number().nullable(),
        leads: z.number().nullable(),
        videoViews: z.number().nullable(),
        sends: z.number().nullable(),
        opens: z.number().nullable(),
        cost: z.number().nullable(),
        actionClicks: z.number().nullable(),
        totalEngagements: z.number().nullable(),
        oneClickLeadFormOpens: z.number().nullable(),
        landingPageClicks: z.number().nullable(),
        videoCompletions: z.number().nullable(),
        videoFirstQuartileCompletions: z.number().nullable(),
        videoMidpointCompletions: z.number().nullable(),
        videoThirdQuartileCompletions: z.number().nullable(),
        videoStarts: z.number().nullable(),
        externalWebsiteConversions: z.number().nullable(),
      }),
    }),
  ),
});

export type SponsoredInmailLinkedInStateOutput = z.infer<
  typeof sponsoredInmailLinkedInStateOutputSchema
>;

export const linkedInStateOutputSchema = z.discriminatedUnion("adFormatType", [
  sponsoredPostLinkedInStateOutputSchema,
  sponsoredInmailLinkedInStateOutputSchema,
]);

export type LinkedInStateOutput = z.infer<typeof linkedInStateOutputSchema>;

function injectAds<T extends z.ZodType>(input: T) {
  return z.object({
    campaigns: z.array(
      z.object({
        campaignId: z.string().uuid(),
        ads: z.array(input),
        metrics: z.object({
          impressions: z.number().nullable(),
          clicks: z.number().nullable(),
          conversions: z.number().nullable(),
          leads: z.number().nullable(),
          videoViews: z.number().nullable(),
          sends: z.number().nullable(),
          opens: z.number().nullable(),
          cost: z.number().nullable(),
          actionClicks: z.number().nullable(),
          totalEngagements: z.number().nullable(),
          oneClickLeadFormOpens: z.number().nullable(),
          landingPageClicks: z.number().nullable(),
          videoCompletions: z.number().nullable(),
          videoFirstQuartileCompletions: z.number().nullable(),
          videoMidpointCompletions: z.number().nullable(),
          videoThirdQuartileCompletions: z.number().nullable(),
          videoStarts: z.number().nullable(),
          externalWebsiteConversions: z.number().nullable(),
        }),
      }),
    ),
  });
}
