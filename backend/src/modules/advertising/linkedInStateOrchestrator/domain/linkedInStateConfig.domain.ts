import { createUuid } from "../../../core/utils/uuid";
import { LinkedInStateConfig } from "./linkedInStateConfig.entity";

export const linkedInStateConfigDomain = {
  setupLinkedInStateConfig: (input: {
    adSegmentId: string;
    campaignIds: string[];
    sponsoredCreativeIds: string[];
  }) => {
    const linkedInStateConfig: LinkedInStateConfig = {
      id: createUuid(),
      adSegmentId: input.adSegmentId,
      status: "NOT_STARTED",
      campaigns: input.campaignIds.map((campaignId) => ({
        campaignId,
        metrics: null,
      })),
      sponsoredCreatives: input.sponsoredCreativeIds.map(
        (sponsoredCreativeId) => ({
          sponsoredCreativeId,
          metrics: null,
        }),
      ),
      startDatetime: null,
      endDatetime: null,
    };
    return linkedInStateConfig;
  },

  startLinkedInStateConfig: (input: LinkedInStateConfig) => {
    const linkedInStateConfig: LinkedInStateConfig = {
      ...input,
      status: "RUNNING",
      startDatetime: new Date(),
      endDatetime: null,
    };
    return linkedInStateConfig;
  },

  stopLinkedInStateConfig: (
    input: LinkedInStateConfig,
    campaignMetrics: {
      campaignId: string;
      metrics: {
        impressions: number;
        clicks: number;
        conversions: number;
        leads: number;
        cost: number;
        videoViews: number;
        sends: number;
        opens: number;
        landingPageClicks: number;
        videoCompletions: number;
        videoFirstQuartileCompletions: number;
        videoMidpointCompletions: number;
        videoThirdQuartileCompletions: number;
        videoStarts: number;
        externalWebsiteConversions: number;
        oneClickLeadFormOpens: number;
        totalEngagements: number;
        actionClicks: number;
      } | null;
    }[],
    sponsoredCreativeMetrics: {
      sponsoredCreativeId: string;
      metrics: {
        impressions: number;
        clicks: number;
        conversions: number;
        leads: number;
        cost: number;
        videoViews: number;
        sends: number;
        opens: number;
        landingPageClicks: number;
        videoCompletions: number;
        videoFirstQuartileCompletions: number;
        videoMidpointCompletions: number;
        videoThirdQuartileCompletions: number;
        videoStarts: number;
        externalWebsiteConversions: number;
        oneClickLeadFormOpens: number;
        totalEngagements: number;
        actionClicks: number;
      } | null;
    }[],
  ) => {
    const linkedInStateConfig: LinkedInStateConfig = {
      ...input,
      status: "COMPLETED",
      endDatetime: new Date(),
      campaigns: input.campaigns.map((campaign) => ({
        campaignId: campaign.campaignId,
        metrics:
          campaignMetrics.find(
            (each) => each.campaignId === campaign.campaignId,
          )?.metrics ?? null,
      })),
      sponsoredCreatives: input.sponsoredCreatives.map((sponsoredCreative) => ({
        sponsoredCreativeId: sponsoredCreative.sponsoredCreativeId,
        metrics:
          sponsoredCreativeMetrics.find(
            (each) =>
              each.sponsoredCreativeId ===
              sponsoredCreative.sponsoredCreativeId,
          )?.metrics ?? null,
      })),
    };
    return linkedInStateConfig;
  },
};
