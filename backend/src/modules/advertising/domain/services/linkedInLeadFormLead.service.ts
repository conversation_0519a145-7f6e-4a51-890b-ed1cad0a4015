import type { ILinkedInLeadFormLeadRepositoryInterface } from "../../application/interfaces/infrastructure/repositories/linkedInLeadFormLead.repository.interface";
import { LinkedInLeadFormLead } from "../entites/linkedInLeadFormLead";

export class LinkedInLeadFormLeadService {
  constructor(
    private linkedInLeadFormLeadRepository: ILinkedInLeadFormLeadRepositoryInterface,
  ) {}

  buildLeadFormLead(input: LinkedInLeadFormLead) {
    const leadFormLead = LinkedInLeadFormLead(input);
    return leadFormLead;
  }

  async createOne(input: LinkedInLeadFormLead) {
    const leadFormLead = this.buildLeadFormLead(input);
    await this.linkedInLeadFormLeadRepository.createOne(leadFormLead);
    return leadFormLead;
  }

  async getOneByLinkedInLeadFormResponseId(linkedInLeadFormResponseId: string) {
    const res =
      await this.linkedInLeadFormLeadRepository.getOneByLinkedInLeadFormResponseId(
        linkedInLeadFormResponseId,
      );
    if (!res) {
      return null;
    }
    return this.buildLeadFormLead(res);
  }

  async getOneById(id: string) {
    const res = await this.linkedInLeadFormLeadRepository.getOneById(id);
    if (!res) {
      return null;
    }
    return this.buildLeadFormLead(res);
  }

  async getAllByLeadFormId(leadFormId: string) {
    const results =
      await this.linkedInLeadFormLeadRepository.getAllByLeadFormId(leadFormId);
    return results.map((result) => this.buildLeadFormLead(result));
  }

  async getOneByNameAndLeadCreatedAt(
    firstName: string,
    lastName: string,
    leadCreatedAt: Date,
  ) {
    const res =
      await this.linkedInLeadFormLeadRepository.getOneByNameAndLeadCreatedAt(
        firstName,
        lastName,
        leadCreatedAt,
      );
    if (!res) {
      return null;
    }
    return this.buildLeadFormLead(res);
  }

  // async getAllLeadsAndOpportunitiesByAccountId(accountId: string, startDateFilter?: Date, endDateFilter?: Date) {
  //   const results = await this.linkedInLeadFormLeadRepository.getAllLeadsAndOpportunitiesByAccountId(accountId, startDateFilter, endDateFilter);
  //   return results.map((result) => this.buildLeadFormLead(result));
  // }

  async createMany(leads: LinkedInLeadFormLead[]): Promise<string[]> {
    return this.linkedInLeadFormLeadRepository.createMany(leads);
  }

  async filterOutExistingLeads(
    leads: Array<{
      id: string;
      linkedInLeadFormId: string;
      linkedInLeadFormResponseId: string;
      linkedinCampaignUrn: string;
      linkedInAdAccountId: string;
      firstName: string | null;
      lastName: string | null;
      email: string | null;
      phoneNumber: string | null;
      city: string | null;
      state: string | null;
      country: string | null;
      zipCode: string | null;
      companyName: string | null;
      jobTitle: string | null;
      jobFunction: string | null;
      industry: string | null;
      seniority: string | null;
      companySize: string | null;
      degree: string | null;
      fieldOfStudy: string | null;
      school: string | null;
      startDate: Date | null;
      graduationDate: Date | null;
      gender: string | null;
      workEmail: string | null;
      linkedinProfileLink: string | null;
      workPhoneNumber: string | null;
      leadType: string;
      testLead: boolean;
      leadCreatedAt: Date;
    }>,
  ): Promise<Array<(typeof leads)[0]>> {
    if (leads.length === 0) return [];

    console.log(
      `[LinkedInLeadFormLeadService] Filtering ${leads.length} leads to remove existing ones`,
    );

    // Get the response IDs of all leads
    const responseIds = leads.map((lead) => lead.linkedInLeadFormResponseId);

    // Check which leads already exist
    const existingLeads =
      await this.linkedInLeadFormLeadRepository.getExistingLeadsByResponseIds(
        responseIds,
      );

    // Create a set of existing response IDs for fast lookup
    const existingResponseIdSet = new Set(
      existingLeads.map((lead) => lead.linkedInLeadFormResponseId),
    );

    // Filter out leads that already exist
    const newLeads = leads.filter(
      (lead) => !existingResponseIdSet.has(lead.linkedInLeadFormResponseId),
    );

    console.log(
      `[LinkedInLeadFormLeadService] Filtered out ${leads.length - newLeads.length} existing leads, ${newLeads.length} new leads remaining`,
    );

    return newLeads;
  }
}
