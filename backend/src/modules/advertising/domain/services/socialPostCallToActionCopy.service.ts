import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { ISocialPostCallToActionCopyRepository } from "../../application/interfaces/infrastructure/repositories/socialPostCallToActionCopy.repository.interface";
import { SocialPostCallToActionCopy } from "../entites/socialPostCallToActionCopy";

export class SocialPostCallToActionCopyService {
  constructor(
    private readonly socialPostCallToActionCopyRepository: ISocialPostCallToActionCopyRepository,
  ) {}

  async createOneOrUpdateOneIfExists(
    input: SocialPostCallToActionCopy,
    tx?: ITransaction,
  ) {
    await this.socialPostCallToActionCopyRepository.createOneOrUpdateOneIfExists(
      input,
      tx,
    );
  }

  async deleteManyForLinkedInAdSegmentValueProps(
    input: {
      linkedInAdSegmentValuePropIds: string[];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ) {
    if (input.linkedInAdSegmentValuePropIds.length > 0) {
      await this.socialPostCallToActionCopyRepository.deleteManyForLinkedInAdSegmentValueProps(
        input,
        tx,
      );
    }
  }

  async getOne(
    input: {
      valuePropId: string;
      socialPostCopyType: string;
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ) {
    return await this.socialPostCallToActionCopyRepository.getOne(input, tx);
  }

  async getManyForLinkedInAdSegmentValueProp(
    input: {
      linkedInAdSegmentValuePropId: string;
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ) {
    return await this.socialPostCallToActionCopyRepository.getManyForLinkedInAdSegmentValueProp(
      input,
      tx,
    );
  }

  async updateManyToActive(ids: string[], tx?: ITransaction) {
    await this.socialPostCallToActionCopyRepository.updateManyToActive(ids, tx);
  }

  async deleteManyForLinkedInAdSegmentValuePropAndTypes(
    input: {
      linkedInAdSegmentValuePropId: string;
      types: string[];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ) {
    await this.socialPostCallToActionCopyRepository.deleteManyForLinkedInAdSegmentValuePropAndTypes(
      input,
      tx,
    );
  }
}
