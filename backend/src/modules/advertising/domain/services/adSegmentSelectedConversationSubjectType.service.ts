import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { createUuid } from "../../../core/utils/uuid";
import { IAdSegmentSelectedConversationSubjectTypeRepository } from "../../application/interfaces/infrastructure/repositories/adSegmentSelectedConversationSubjectType.repository.interface";
import { AdSegmentSelectedConversationSubjectType } from "../entites/adSegmentSelectedConversationSubjectType";
import { AdSegmentValuePropService } from "./adSegmentValueProp.service";
import { ConversationSubjectCopyService } from "./conversationSubjectCopy.service";

export class AdSegmentSelectedConversationSubjectTypeService {
  constructor(
    private readonly adSegmentSelectedConversationSubjectTypeRepository: IAdSegmentSelectedConversationSubjectTypeRepository,
  ) {}

  async getAllForAdSegment(
    adSegmentId: string,
    transaction?: ITransaction,
  ): Promise<AdSegmentSelectedConversationSubjectType[]> {
    return this.adSegmentSelectedConversationSubjectTypeRepository.getAllForAdSegment(
      adSegmentId,
      transaction,
    );
  }

  async deleteAllForAdSegment(
    adSegmentId: string,
    transaction?: ITransaction,
  ): Promise<void> {
    await this.adSegmentSelectedConversationSubjectTypeRepository.deleteAllForAdSegment(
      adSegmentId,
      transaction,
    );
  }

  async setForAdSegment(
    adSegmentId: string,
    conversationSubjectCopyTypes: string[],
    conversationSubjectService: ConversationSubjectCopyService,
    adSegmentValuePropService: AdSegmentValuePropService,
    transaction?: ITransaction,
  ) {
    const existing = await this.getAllForAdSegment(adSegmentId, transaction);
    const idsToDelete = existing
      .filter((i) => !conversationSubjectCopyTypes.includes(i.type))
      .map((i) => i.id);

    const typesToDelete = existing
      .filter((i) => !conversationSubjectCopyTypes.includes(i.type))
      .map((i) => i.type);

    await this.adSegmentSelectedConversationSubjectTypeRepository.deleteManyById(
      idsToDelete,
      transaction,
    );

    const adSegmentValueProps =
      await adSegmentValuePropService.getManyForAdSegment(
        adSegmentId,
        "DRAFT",
        transaction,
      );

    await conversationSubjectService.deleteManyForAdSegmentByType(
      {
        adSegmentValuePropIds: adSegmentValueProps.map((i) => i.id),
        conversationCopyTypes: typesToDelete,
        status: "DRAFT",
      },
      transaction,
    );

    const idsToCreate = conversationSubjectCopyTypes.filter(
      (i) => !existing.some((e) => e.type === i),
    );

    await this.adSegmentSelectedConversationSubjectTypeRepository.createMany(
      idsToCreate.map((i) =>
        AdSegmentSelectedConversationSubjectType({
          id: createUuid(),
          adSegmentId: adSegmentId,
          type: i,
        }),
      ),
      transaction,
    );
  }

  async createOne(
    input: AdSegmentSelectedConversationSubjectType,
    transaction?: ITransaction,
  ): Promise<void> {
    await this.adSegmentSelectedConversationSubjectTypeRepository.createOne(
      input,
      transaction,
    );
  }

  async deleteOne(id: string, transaction?: ITransaction): Promise<void> {
    await this.adSegmentSelectedConversationSubjectTypeRepository.deleteOne(
      id,
      transaction,
    );
  }
}
