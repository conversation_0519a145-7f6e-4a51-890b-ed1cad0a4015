import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { createUuid } from "../../../core/utils/uuid";
import { IAdSegmentSelectedSocialPostCallToActionTypeRepository } from "../../application/interfaces/infrastructure/repositories/adSegmentSelectedSocialPostCallToActionType.repository.interface";
import { AdSegmentSelectedSocialPostCallToActionType } from "../entites/adSegmentSelectedSocialPostCallToActionType";
import { AdSegmentValuePropService } from "./adSegmentValueProp.service";
import { SocialPostCallToActionCopyService } from "./socialPostCallToActionCopy.service";

export class AdSegmentSelectedSocialPostCallToActionTypeService {
  constructor(
    private readonly adSegmentSelectedSocialPostCallToActionTypeRepository: IAdSegmentSelectedSocialPostCallToActionTypeRepository,
  ) {}

  async getAllForAdSegment(
    adSegmentId: string,
    transaction?: ITransaction,
  ): Promise<AdSegmentSelectedSocialPostCallToActionType[]> {
    return this.adSegmentSelectedSocialPostCallToActionTypeRepository.getAllForAdSegment(
      adSegmentId,
      transaction,
    );
  }

  async deleteAllForAdSegment(
    adSegmentId: string,
    transaction?: ITransaction,
  ): Promise<void> {
    await this.adSegmentSelectedSocialPostCallToActionTypeRepository.deleteAllForAdSegment(
      adSegmentId,
      transaction,
    );
  }

  async setForAdSegment(
    adSegmentId: string,
    valuePropId: string,
    socialPostCallToActionTypes: string[],
    socialPostCallToActionCopyService: SocialPostCallToActionCopyService,
    adSegmentValuePropService: AdSegmentValuePropService,
    transaction?: ITransaction,
  ) {
    const existing = await this.getAllForAdSegment(adSegmentId, transaction);
    const idsToDelete = existing
      .filter((i) => !socialPostCallToActionTypes.includes(i.type))
      .map((i) => i.id);

    const typesToDelete = existing
      .filter((i) => !socialPostCallToActionTypes.includes(i.type))
      .map((i) => i.type);

    await this.adSegmentSelectedSocialPostCallToActionTypeRepository.deleteManyById(
      idsToDelete,
      transaction,
    );

    const adSegmentValueProps =
      await adSegmentValuePropService.getManyForAdSegment(
        adSegmentId,
        "DRAFT",
        transaction,
      );

    await socialPostCallToActionCopyService.deleteManyForLinkedInAdSegmentValuePropAndTypes(
      {
        linkedInAdSegmentValuePropId: valuePropId,
        types: typesToDelete,
        status: "DRAFT",
      },
      transaction,
    );

    const idsToCreate = socialPostCallToActionTypes.filter(
      (i) => !existing.some((e) => e.type === i),
    );

    await this.adSegmentSelectedSocialPostCallToActionTypeRepository.createMany(
      idsToCreate.map((i) =>
        AdSegmentSelectedSocialPostCallToActionType({
          id: createUuid(),
          adSegmentId: adSegmentId,
          type: i,
        }),
      ),
      transaction,
    );
  }

  async createOne(
    input: AdSegmentSelectedSocialPostCallToActionType,
    transaction?: ITransaction,
  ): Promise<void> {
    await this.adSegmentSelectedSocialPostCallToActionTypeRepository.createOne(
      input,
      transaction,
    );
  }

  async deleteOne(id: string, transaction?: ITransaction): Promise<void> {
    await this.adSegmentSelectedSocialPostCallToActionTypeRepository.deleteOne(
      id,
      transaction,
    );
  }
}
