import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { createUuid } from "../../../core/utils/uuid";
import { IAdSegmentSelectedSocialPostBodyTypeRepository } from "../../application/interfaces/infrastructure/repositories/adSegmentSelectedSocialPostBodyType.repository.interface";
import { AdSegmentSelectedSocialPostBodyType } from "../entites/adSegmentSelectedSocialPostBodyType";
import { AdSegmentValuePropService } from "./adSegmentValueProp.service";
import { SocialPostAdCopyService } from "./socialPostAdCopy.service";

export class AdSegmentSelectedSocialPostBodyTypeService {
  constructor(
    private readonly adSegmentSelectedSocialPostBodyTypeRepository: IAdSegmentSelectedSocialPostBodyTypeRepository,
  ) {}

  async getAllForAdSegment(
    adSegmentId: string,
    transaction?: ITransaction,
  ): Promise<AdSegmentSelectedSocialPostBodyType[]> {
    return this.adSegmentSelectedSocialPostBodyTypeRepository.getAllForAdSegment(
      adSegmentId,
      transaction,
    );
  }

  async deleteAllForAdSegment(
    adSegmentId: string,
    transaction?: ITransaction,
  ): Promise<void> {
    await this.adSegmentSelectedSocialPostBodyTypeRepository.deleteAllForAdSegment(
      adSegmentId,
      transaction,
    );
  }

  async setForAdSegment(
    adSegmentId: string,
    adSegmentValuePropId: string,
    socialPostBodyTypes: string[],
    socialPostAdCopyService: SocialPostAdCopyService,
    adSegmentValuePropService: AdSegmentValuePropService,
    transaction?: ITransaction,
  ) {
    const existing = await this.getAllForAdSegment(adSegmentId, transaction);
    const idsToDelete = existing
      .filter((i) => !socialPostBodyTypes.includes(i.type))
      .map((i) => i.id);

    const typesToDelete = existing
      .filter((i) => !socialPostBodyTypes.includes(i.type))
      .map((i) => i.type);

    await this.adSegmentSelectedSocialPostBodyTypeRepository.deleteManyById(
      idsToDelete,
      transaction,
    );

    console.log("typesToDelete", {
      adSegmentValuePropId,
      typesToDelete,
    });
    await socialPostAdCopyService.deleteManyByTypeForValueProp(
      {
        valuePropId: adSegmentValuePropId,
        types: typesToDelete,
        status: "DRAFT",
      },
      transaction,
    );

    const idsToCreate = socialPostBodyTypes.filter(
      (i) => !existing.some((e) => e.type === i),
    );

    console.log("idsToCreate", {
      adSegmentValuePropId,
      idsToCreate,
    });

    await this.adSegmentSelectedSocialPostBodyTypeRepository.createMany(
      idsToCreate.map((i) =>
        AdSegmentSelectedSocialPostBodyType({
          id: createUuid(),
          adSegmentId: adSegmentId,
          type: i,
        }),
      ),
      transaction,
    );
  }

  async createOne(
    input: AdSegmentSelectedSocialPostBodyType,
    transaction?: ITransaction,
  ): Promise<void> {
    await this.adSegmentSelectedSocialPostBodyTypeRepository.createOne(
      input,
      transaction,
    );
  }

  async deleteOne(id: string, transaction?: ITransaction): Promise<void> {
    await this.adSegmentSelectedSocialPostBodyTypeRepository.deleteOne(
      id,
      transaction,
    );
  }
}
