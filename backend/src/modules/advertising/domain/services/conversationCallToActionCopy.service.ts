import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { createUuid } from "../../../core/utils/uuid";
import { IConversationCallToActionCopyRepository } from "../../application/interfaces/infrastructure/repositories/conversationCallToActionCopy.repository.interface";
import { ConversationCallToActionCopy } from "../entites/conversationCallToActionCopy";
import { ConversationCallToActionCopyType } from "../valueObjects/conversationCallToActionCopyType";
import { ConversationMessageCopyType } from "../valueObjects/conversationMessageCopyType";
import { ConversationSubjectCopyType } from "../valueObjects/conversationSubjectCopyType";

export class ConversationCallToActionCopyService {
  constructor(
    private readonly conversationCallToActionCopyRepository: IConversationCallToActionCopyRepository,
  ) {}

  async createOrUpdateOneIfExists(
    conversationCallToActionCopy: Omit<
      ConversationCallToActionCopy,
      "conversationMessageCopyId" | "conversationSubjectCopyId" | "id"
    >,
    tx?: ITransaction,
  ) {
    const id = createUuid();
    const res =
      await this.conversationCallToActionCopyRepository.createOrUpdateOneIfExists(
        {
          id: id,
          content: conversationCallToActionCopy.content,
          type: conversationCallToActionCopy.type,
          subjectType: conversationCallToActionCopy.subjectType,
          valuePropId: conversationCallToActionCopy.valuePropId,
          messageType: conversationCallToActionCopy.messageType,
          status: conversationCallToActionCopy.status,
        },
      );
    return ConversationCallToActionCopy({
      id: res.id,
      conversationMessageCopyId: res.conversationMessageCopyId,
      conversationSubjectCopyId: res.conversationSubjectCopyId,
      content: res.content,
      type: res.type,
      subjectType: res.subjectType,
      valuePropId: res.valuePropId,
      messageType: res.messageType,
      status: res.status,
    });
  }

  async getOne(
    input: {
      valuePropId: string;
      conversationMessageCopyType: string;
      conversationSubjectCopyType: string;
      conversationCallToActionCopyType: string;
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ) {
    const res = await this.conversationCallToActionCopyRepository.getOne(input);
    return res;
  }

  async deleteManyForLinkedInAdSegmentValueProps(
    input: {
      valuePropIds: string[];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ) {
    await this.conversationCallToActionCopyRepository.deleteManyForLinkedInAdSegmentValueProps(
      input,
    );
  }

  async getAllForValueProp(
    valuePropId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
  ) {
    const res =
      await this.conversationCallToActionCopyRepository.getAllForValueProp(
        valuePropId,
        status,
      );
    return res;
  }

  async getAllForValuePropWithMessageAndSubjectCopies(
    valuePropId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ) {
    const res =
      await this.conversationCallToActionCopyRepository.getAllForValuePropWithMessageAndSubjectCopies(
        valuePropId,
        status,
        tx,
      );
    return res;
  }

  async updateManyToActive(
    input: {
      ids: string[];
    },
    tx?: ITransaction,
  ) {
    await this.conversationCallToActionCopyRepository.updateManyToActive(
      input,
      tx,
    );
  }

  async deleteManyForValuePropSubjectTypeAndManyMessageTypeAndCallToActionTypes(
    valuePropId: string,
    subjectType: ConversationSubjectCopyType,
    messageType: ConversationMessageCopyType,
    callToActionTypes: ConversationCallToActionCopyType[],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ) {
    await this.conversationCallToActionCopyRepository.deleteManyForValuePropSubjectTypeAndManyMessageTypeAndCallToActionTypes(
      valuePropId,
      subjectType,
      messageType,
      callToActionTypes,
      status,
      tx,
    );
  }

  async deleteManyForValuePropSubjectTypeAndManyMessageTypes(
    valuePropId: string,
    subjectType: ConversationSubjectCopyType,
    messageTypes: ConversationMessageCopyType[],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ) {
    await this.conversationCallToActionCopyRepository.deleteManyForValuePropSubjectTypeAndManyMessageTypes(
      valuePropId,
      subjectType,
      messageTypes,
      status,
      tx,
    );
  }
}
