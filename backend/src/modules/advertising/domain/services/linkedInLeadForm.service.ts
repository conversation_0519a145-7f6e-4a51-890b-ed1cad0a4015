import type { ILinkedInLeadFormRepositoryInterface } from "../../application/interfaces/infrastructure/repositories/linkedInLeadForm.repository.interface";
import { LinkedInLeadForm } from "../entites/linkedInLeadForm";

export class LinkedInLeadFormService {
  constructor(
    private linkedInLeadFormRepository: ILinkedInLeadFormRepositoryInterface,
  ) {}

  buildLeadForm(input: LinkedInLeadForm) {
    const leadForm = LinkedInLeadForm(input);
    return leadForm;
  }

  async createOne(input: LinkedInLeadForm) {
    const leadForm = this.buildLeadForm(input);
    await this.linkedInLeadFormRepository.createOne(leadForm);
    return leadForm;
  }

  async getOneById(id: string) {
    const res = await this.linkedInLeadFormRepository.getOneById(id);
    if (!res) {
      return null;
    }
    return this.buildLeadForm(res);
  }

  async getAllByLinkedInAdAccountId(linkedInAdAccountId: string) {
    const res =
      await this.linkedInLeadFormRepository.getAllByLinkedInAdAccountId(
        linkedInAdAccountId,
      );
    return res;
  }

  async getOneByLinkedInUrn(linkedInUrn: string) {
    const res =
      await this.linkedInLeadFormRepository.getOneByLinkedInUrn(linkedInUrn);
    if (!res) {
      return null;
    }
    return this.buildLeadForm(res);
  }

  // Add to LinkedInLeadFormService.ts
  async createMany(forms: LinkedInLeadForm[]): Promise<string[]> {
    return this.linkedInLeadFormRepository.createMany(forms);
  }

  async filterOutExistingForms(
    forms: LinkedInLeadForm[],
  ): Promise<Array<(typeof forms)[0]>> {
    if (forms.length === 0) return [];

    console.log(
      `[LinkedInLeadFormService] Filtering ${forms.length} lead forms to remove existing ones`,
    );

    // Get the URNs of all forms
    const urns = forms.map((form) => form.leadGenFormUrn);

    // Check which forms already exist
    const existingForms =
      await this.linkedInLeadFormRepository.getExistingFormsByUrns(urns);

    // Create a set of existing URNs for fast lookup
    const existingUrnSet = new Set(
      existingForms.map((form) => form.leadGenFormUrn),
    );

    // Filter out forms that already exist
    const newForms = forms.filter(
      (form) => !existingUrnSet.has(form.leadGenFormUrn),
    );

    console.log(
      `[LinkedInLeadFormService] Filtered out ${forms.length - newForms.length} existing forms, ${newForms.length} new forms remaining`,
    );

    return newForms;
  }
}
