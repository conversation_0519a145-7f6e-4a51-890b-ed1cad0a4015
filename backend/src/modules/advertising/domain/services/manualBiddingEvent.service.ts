import { createUuid } from "../../../core/utils/uuid";
import { IManualBiddingEventRepository } from "../../application/interfaces/infrastructure/repositories/manualBiddingEvent.repository.interface";
import { ManualBiddingEvent } from "../entites/manualBiddingEvent";

export class ManualBiddingEventService {
  constructor(
    private readonly ctx: {
      manualBiddingEventRepository: IManualBiddingEventRepository;
    },
  ) {}

  async createOne(input: {
    linkedInCampaignId: string;
    newBid: number;
    budgetUsedPercentage: number;
    timeElapsedPercentage: number;
    timestamp: Date;
    budget: number;
    dailyOrTotalBudget: "monthly" | "total";
    originalBid: number;
    timeElapsed: number;
    budgetUsed: number;
    minBid: number;
    maxBid: number;
    minSuggestedBid: number;
    maxSuggestedBid: number;
    suggestedBid: number;
    decision: string;
    bidType: string;
  }) {
    return this.ctx.manualBiddingEventRepository.createOne(
      ManualBiddingEvent({
        id: createUuid(),
        originalBid: input.originalBid,
        timeElapsed: input.timeElapsed,
        budgetUsed: input.budgetUsed,
        budgetUsedPercentage: input.budgetUsedPercentage,
        timeElapsedPercentage: input.timeElapsedPercentage,
        budget: input.budget,
        dailyOrTotalBudget: input.dailyOrTotalBudget,
        timestamp: input.timestamp,
        linkedInCampaignId: input.linkedInCampaignId,
        newBid: input.newBid,
        minBid: input.minBid,
        maxBid: input.maxBid,
        minSuggestedBid: input.minSuggestedBid,
        maxSuggestedBid: input.maxSuggestedBid,
        suggestedBid: input.suggestedBid,
        decision: input.decision,
        bidType: input.bidType,
      }),
    );
  }

  async getForCampaign(campaignId: string) {
    return this.ctx.manualBiddingEventRepository.getForCampaign(campaignId);
  }

  async getOne(id: string) {
    return this.ctx.manualBiddingEventRepository.getOne(id);
  }
}
