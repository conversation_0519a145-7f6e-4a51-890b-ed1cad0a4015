import { Transaction } from "../../../../database/dbTransactionType";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { createUuid } from "../../../core/utils/uuid";
import { IConversationMessageCopyRepository } from "../../application/interfaces/infrastructure/repositories/conversationMessageCopy.repository.interface";
import { ConversationMessageCopy } from "../entites/conversationMessageCopy";
import { ConversationSubjectCopy } from "../entites/conversationSubjectCopy";
import { ConversationCallToActionCopyService } from "./conversationCallToActionCopy.service";

export class ConversationMessageCopyService {
  constructor(
    private readonly conversationMessageCopyRepository: IConversationMessageCopyRepository,
  ) {}

  async createOneOrUpdateOneIfExists(
    input: Omit<ConversationMessageCopy, "subjectCopyId" | "id">,
    tx?: ITransaction,
  ) {
    const id = createUuid();
    const data: Omit<ConversationMessageCopy, "subjectCopyId"> = {
      id: id,
      content: input.content,
      type: input.type,
      subjectType: input.subjectType,
      valuePropId: input.valuePropId,
      status: input.status,
    };

    const res = await this.conversationMessageCopyRepository.upsertOne(
      data,
      tx,
    );
    return ConversationMessageCopy({
      ...res,
      subjectCopyId: res.subjectCopyId,
    });
  }

  async getOne(
    input: {
      valuePropId: string;
      messageType: ConversationMessageCopy["type"];
      subjectType: ConversationSubjectCopy["type"];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: ITransaction,
  ) {
    const res =
      this.conversationMessageCopyRepository.getOneByValuePropMessageTypeAndSubjectType(
        input.valuePropId,
        input.messageType,
        input.subjectType,
        input.status,
        tx,
      );
    return res;
  }

  async getOneById(
    id: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ) {
    return this.conversationMessageCopyRepository.getOneById(id, status, tx);
  }

  async getAllForValueProp(
    valuePropId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ) {
    return this.conversationMessageCopyRepository.getAllForValueProp(
      valuePropId,
      status,
      tx,
    );
  }

  async deleteOne(
    id: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ) {
    await this.conversationMessageCopyRepository.deleteOneById(id, status, tx);
  }

  async deleteManyForValueProp(
    valuePropId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: ITransaction,
  ) {
    await this.conversationMessageCopyRepository.deleteAllForValueProp(
      valuePropId,
      status,
      tx,
    );
  }

  async deleteManyForManyValueProps(
    valuePropIds: string[],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    ctx: {
      conversationCallToActionCopyService: ConversationCallToActionCopyService;
    },
    tx?: ITransaction,
  ) {
    await ctx.conversationCallToActionCopyService.deleteManyForLinkedInAdSegmentValueProps(
      {
        valuePropIds,
        status,
      },
      tx,
    );

    await this.conversationMessageCopyRepository.deleteAllForManyValueProps(
      valuePropIds,
      status,
      tx,
    );
  }

  async updateManyToActive(ids: string[], tx?: ITransaction) {
    await this.conversationMessageCopyRepository.updateManyToActive(
      { ids },
      tx,
    );
  }

  async deleteManyForValuePropSubjectTypeAndManyMessageTypes(
    valuePropId: string,
    subjectType: ConversationSubjectCopy["type"],
    messageTypes: ConversationMessageCopy["type"][],
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    conversationCallToActionCopyService: ConversationCallToActionCopyService,
    tx?: ITransaction,
  ) {
    await conversationCallToActionCopyService.deleteManyForValuePropSubjectTypeAndManyMessageTypes(
      valuePropId,
      subjectType,
      messageTypes,
      status,
      tx,
    );
    await this.conversationMessageCopyRepository.deleteManyForValuePropSubjectTypeAndManyMessageTypes(
      valuePropId,
      subjectType,
      messageTypes,
      status,
      tx,
    );
  }
}
