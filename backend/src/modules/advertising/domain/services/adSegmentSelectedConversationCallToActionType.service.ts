import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { createUuid } from "../../../core/utils/uuid";
import { IAdSegmentSelectedConversationCallToActionTypeRepository } from "../../application/interfaces/infrastructure/repositories/adSegmentSelectedConversationCallToActionType.repository.interface";
import { AdSegmentSelectedConversationCallToActionType } from "../entites/adSegmentSelectedConversationCallToActionType";
import { AdSegmentValuePropService } from "./adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "./conversationCallToActionCopy.service";

export class AdSegmentSelectedConversationCallToActionTypeService {
  constructor(
    private readonly adSegmentSelectedConversationCallToActionTypeRepository: IAdSegmentSelectedConversationCallToActionTypeRepository,
  ) {}

  async getAllForAdSegment(
    adSegmentId: string,
    transaction?: ITransaction,
  ): Promise<AdSegmentSelectedConversationCallToActionType[]> {
    return this.adSegmentSelectedConversationCallToActionTypeRepository.getAllForAdSegment(
      adSegmentId,
      transaction,
    );
  }

  async deleteAllForAdSegment(
    adSegmentId: string,
    transaction?: ITransaction,
  ): Promise<void> {
    await this.adSegmentSelectedConversationCallToActionTypeRepository.deleteAllForAdSegment(
      adSegmentId,
      transaction,
    );
  }

  async setForAdSegment(
    adSegmentId: string,
    valuePropId: string,
    conversationSubjectCopyType: string,
    conversationMessageCopyType: string,
    conversationCallToActionCopyTypes: string[],
    conversationCallToActionCopyService: ConversationCallToActionCopyService,
    adSegmentValuePropService: AdSegmentValuePropService,
    transaction?: ITransaction,
  ) {
    const existing = await this.getAllForAdSegment(adSegmentId, transaction);
    const idsToDelete = existing
      .filter((i) => !conversationCallToActionCopyTypes.includes(i.type))
      .map((i) => i.id);

    const typesToDelete = existing
      .filter((i) => !conversationCallToActionCopyTypes.includes(i.type))
      .map((i) => i.type);

    await this.adSegmentSelectedConversationCallToActionTypeRepository.deleteManyById(
      idsToDelete,
      transaction,
    );

    const adSegmentValueProps =
      await adSegmentValuePropService.getManyForAdSegment(
        adSegmentId,
        "DRAFT",
        transaction,
      );

    console.log({
      valuePropId,
      conversationSubjectCopyType,
      conversationMessageCopyType,
      typesToDelete,
    });

    await conversationCallToActionCopyService.deleteManyForValuePropSubjectTypeAndManyMessageTypeAndCallToActionTypes(
      valuePropId,
      conversationSubjectCopyType,
      conversationMessageCopyType,
      typesToDelete,
      "DRAFT",
      transaction,
    );

    const idsToCreate = conversationCallToActionCopyTypes.filter(
      (i) => !existing.some((e) => e.type === i),
    );

    await this.adSegmentSelectedConversationCallToActionTypeRepository.createMany(
      idsToCreate.map((i) =>
        AdSegmentSelectedConversationCallToActionType({
          id: createUuid(),
          adSegmentId: adSegmentId,
          type: i,
        }),
      ),
      transaction,
    );
  }

  async createOne(
    input: AdSegmentSelectedConversationCallToActionType,
    transaction?: ITransaction,
  ): Promise<void> {
    await this.adSegmentSelectedConversationCallToActionTypeRepository.createOne(
      input,
      transaction,
    );
  }

  async deleteOne(id: string, transaction?: ITransaction): Promise<void> {
    await this.adSegmentSelectedConversationCallToActionTypeRepository.deleteOne(
      id,
      transaction,
    );
  }
}
