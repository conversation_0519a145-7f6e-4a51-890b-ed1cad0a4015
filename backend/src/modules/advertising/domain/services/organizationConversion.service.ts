import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { IOrganizationConversionRepository } from "../../application/interfaces/infrastructure/repositories/organizationConversation.repository.interface";
import { OrganizationConversion } from "../entites/organizationConversion";

export class OrganizationConversionService {
  constructor(
    private readonly organizationConversionRepository: IOrganizationConversionRepository,
  ) {}

  async createOne(
    organizationConversion: OrganizationConversion,
  ): Promise<OrganizationConversion> {
    return this.organizationConversionRepository.createOne(
      organizationConversion,
    );
  }

  async getForOrganization(
    organizationId: number,
  ): Promise<OrganizationConversion[]> {
    const organizationConversions =
      await this.organizationConversionRepository.getOneForOrganization(
        organizationId,
      );
    return organizationConversions;
  }

  async getOne(id: string): Promise<OrganizationConversion | null> {
    return this.organizationConversionRepository.getOne(id);
  }

  async deleteOne(id: string, tx?: ITransaction): Promise<void> {
    await this.organizationConversionRepository.deleteOne(id, tx);
  }
}
