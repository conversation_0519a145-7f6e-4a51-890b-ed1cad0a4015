import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { createUuid } from "../../../core/utils/uuid";
import { IAdSegmentSelectedConversationMessageTypeRepository } from "../../application/interfaces/infrastructure/repositories/adSegmentSelectedConversationMessageType.repository.interface";
import { AdSegmentSelectedConversationMessageType } from "../entites/adSegmentSelectedConversationMessageType";
import { AdSegmentValuePropService } from "./adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "./conversationCallToActionCopy.service";
import { ConversationMessageCopyService } from "./conversationMessageCopy.service";

export class AdSegmentSelectedConversationMessageTypeService {
  constructor(
    private readonly adSegmentSelectedConversationMessageTypeRepository: IAdSegmentSelectedConversationMessageTypeRepository,
  ) {}

  async getAllForAdSegment(
    adSegmentId: string,
    transaction?: ITransaction,
  ): Promise<AdSegmentSelectedConversationMessageType[]> {
    return this.adSegmentSelectedConversationMessageTypeRepository.getAllForAdSegment(
      adSegmentId,
      transaction,
    );
  }

  async deleteAllForAdSegment(
    adSegmentId: string,
    transaction?: ITransaction,
  ): Promise<void> {
    await this.adSegmentSelectedConversationMessageTypeRepository.deleteAllForAdSegment(
      adSegmentId,
      transaction,
    );
  }

  async setForAdSegment(
    adSegmentId: string,
    valuePropId: string,
    conversationSubjectCopyType: string,
    conversationMessageCopyTypes: string[],
    conversationMessageCopyService: ConversationMessageCopyService,
    conversationCallToActionCopyService: ConversationCallToActionCopyService,
    adSegmentValuePropService: AdSegmentValuePropService,
    transaction?: ITransaction,
  ) {
    const existing = await this.getAllForAdSegment(adSegmentId, transaction);
    const idsToDelete = existing
      .filter((i) => !conversationMessageCopyTypes.includes(i.type))
      .map((i) => i.id);

    const typesToDelete = existing
      .filter((i) => !conversationMessageCopyTypes.includes(i.type))
      .map((i) => i.type);

    await this.adSegmentSelectedConversationMessageTypeRepository.deleteManyById(
      idsToDelete,
      transaction,
    );

    await conversationMessageCopyService.deleteManyForValuePropSubjectTypeAndManyMessageTypes(
      valuePropId,
      conversationSubjectCopyType,
      typesToDelete,
      "DRAFT",
      conversationCallToActionCopyService,
      transaction,
    );

    const idsToCreate = conversationMessageCopyTypes.filter(
      (i) => !existing.some((e) => e.type === i),
    );

    await this.adSegmentSelectedConversationMessageTypeRepository.createMany(
      idsToCreate.map((i) =>
        AdSegmentSelectedConversationMessageType({
          id: createUuid(),
          adSegmentId: adSegmentId,
          type: i,
        }),
      ),
      transaction,
    );
  }

  async createOne(
    input: AdSegmentSelectedConversationMessageType,
    transaction?: ITransaction,
  ): Promise<void> {
    await this.adSegmentSelectedConversationMessageTypeRepository.createOne(
      input,
      transaction,
    );
  }

  async deleteOne(id: string, transaction?: ITransaction): Promise<void> {
    await this.adSegmentSelectedConversationMessageTypeRepository.deleteOne(
      id,
      transaction,
    );
  }
}
