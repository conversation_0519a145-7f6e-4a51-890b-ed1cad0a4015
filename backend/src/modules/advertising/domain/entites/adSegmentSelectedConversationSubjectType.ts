import { z } from "zod";

export const adSegmentConversationSubjectType = z.object({
  id: z.string().uuid(),
  adSegmentId: z.string(),
  type: z.string(),
});

export const AdSegmentSelectedConversationSubjectType = (
  input: z.infer<typeof adSegmentConversationSubjectType>,
) => {
  return adSegmentConversationSubjectType.parse(input);
};

export type AdSegmentSelectedConversationSubjectType = z.infer<
  typeof adSegmentConversationSubjectType
>;
