import { z } from "zod";

import { Entity } from "../../../../helpers/entity";

export const CampaignGroupMetricsSchema = z.object({
  campaignGroupId: z.string(),
  campaignGroupName: z.string(),
  leads: z.number().optional(),
  engagements: z.number().optional(),
  clicks: z.number().optional(),
  impressions: z.number().optional(),
  videoViews: z.number().optional(),
  spend: z.number(),
  learnings: z.string().optional(),
  bestPerformingAd: z.string().optional(),
  worstPerformingAd: z.string().optional(),
  performanceImprovement: z.number().optional(),
});

export const TotalMetricsSchema = z.object({
  totalLeads: z.number().optional(),
  totalEngagements: z.number().optional(),
  totalClicks: z.number().optional(),
  totalImpressions: z.number().optional(),
  totalVideoViews: z.number().optional(),
  totalSpend: z.number(),
});

export const SlackNotificationSchema = z.object({
  id: z.string().uuid(),
  organizationId: z.number().positive(),
  notificationType: z.enum(['scheduled', 'ad_hoc_insight']),
  weekStartDate: z.date(),
  weekEndDate: z.date(),
  campaignGroupMetrics: z.array(CampaignGroupMetricsSchema),
  totalMetrics: TotalMetricsSchema,
  slackWebhookUrl: z.string().url(),
});

export const SlackNotification = Entity(SlackNotificationSchema);

export type SlackNotification = z.infer<typeof SlackNotificationSchema>;
export type CampaignGroupMetrics = z.infer<typeof CampaignGroupMetricsSchema>;
export type TotalMetrics = z.infer<typeof TotalMetricsSchema>; 