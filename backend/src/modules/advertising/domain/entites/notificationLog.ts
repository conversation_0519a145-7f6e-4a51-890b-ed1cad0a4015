import { z } from "zod";

import { Entity } from "../../../../helpers/entity";

export const NotificationLogSchema = z.object({
  id: z.string().uuid(),
  organizationId: z.number().positive(),
  notificationType: z.enum(['scheduled', 'ad_hoc_insight']),
  weekStartDate: z.date(),
  weekEndDate: z.date(),
  sentAt: z.date(),
  status: z.enum(['sent', 'failed']),
  slackMessageTs: z.string().optional(),
  campaignData: z.record(z.any()).optional(),
  createdAt: z.date(),
});

export const NotificationLog = Entity(NotificationLogSchema);

export type NotificationLog = z.infer<typeof NotificationLogSchema>; 