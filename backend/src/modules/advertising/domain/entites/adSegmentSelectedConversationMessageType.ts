import { z } from "zod";

export const adSegmentSelectedConversationMessageType = z.object({
  id: z.string().uuid(),
  adSegmentId: z.string().uuid(),
  type: z.string(),
});

export const AdSegmentSelectedConversationMessageType = (
  input: z.infer<typeof adSegmentSelectedConversationMessageType>,
) => {
  return adSegmentSelectedConversationMessageType.parse(input);
};

export type AdSegmentSelectedConversationMessageType = z.infer<
  typeof adSegmentSelectedConversationMessageType
>;
