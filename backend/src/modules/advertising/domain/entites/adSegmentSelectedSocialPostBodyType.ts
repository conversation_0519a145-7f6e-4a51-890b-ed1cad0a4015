import { z } from "zod";

export const adSegmentSelectedSocialPostBodyType = z.object({
  id: z.string().uuid(),
  adSegmentId: z.string().uuid(),
  type: z.string(),
});

export const AdSegmentSelectedSocialPostBodyType = (
  input: z.infer<typeof adSegmentSelectedSocialPostBodyType>,
) => {
  return adSegmentSelectedSocialPostBodyType.parse(input);
};

export type AdSegmentSelectedSocialPostBodyType = z.infer<
  typeof adSegmentSelectedSocialPostBodyType
>;
