import { z } from "zod";

import { Entity } from "../../../../helpers/entity";

export const LinkedInLeadFormLead = Entity(
  z.object({
    id: z.string().uuid(),
    linkedInLeadFormId: z.string().uuid(),
    linkedInLeadFormResponseId: z.string(),
    linkedinCampaignUrn: z.string().nullable(),
    linkedInAdAccountId: z.string().uuid(),

    // Personal information
    firstName: z.string().nullable(),
    lastName: z.string().nullable(),
    phoneNumber: z.string().nullable(),
    email: z.string().nullable(),

    // Location information
    city: z.string().nullable(),
    state: z.string().nullable(),
    country: z.string().nullable(),
    zipCode: z.string().nullable(),

    // Professional information
    jobTitle: z.string().nullable(),
    jobFunction: z.string().nullable(),
    seniority: z.string().nullable(),
    companyName: z.string().nullable(),
    companySize: z.string().nullable(),
    industry: z.string().nullable(),

    // Education information
    degree: z.string().nullable(),
    fieldOfStudy: z.string().nullable(),
    school: z.string().nullable(),
    startDate: z.date().nullable(),
    graduationDate: z.date().nullable(),

    // Additional information
    gender: z.string().nullable(),
    workEmail: z.string().nullable(),
    linkedinProfileLink: z.string().nullable(),
    workPhoneNumber: z.string().nullable(),
    leadType: z.string().nullable(),
    testLead: z.boolean().default(false),

    // Timestamps
    leadCreatedAt: z.date().nullable(),
  }),
);

export type LinkedInLeadFormLead = ReturnType<typeof LinkedInLeadFormLead>;
