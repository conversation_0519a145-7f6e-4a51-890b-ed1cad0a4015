import { z } from "zod";

export const adSegmentSelectedConversationCallToActionType = z.object({
  id: z.string().uuid(),
  adSegmentId: z.string().uuid(),
  type: z.string(),
});

export const AdSegmentSelectedConversationCallToActionType = (
  input: z.infer<typeof adSegmentSelectedConversationCallToActionType>,
) => {
  return adSegmentSelectedConversationCallToActionType.parse(input);
};

export type AdSegmentSelectedConversationCallToActionType = z.infer<
  typeof adSegmentSelectedConversationCallToActionType
>;
