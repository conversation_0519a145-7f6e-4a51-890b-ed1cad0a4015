import { z } from "zod";

import { Entity } from "../../../../helpers/entity";

export const LinkedInLeadFormAndLead = Entity(
  z.object({
    leadFormLeads: z.object({
      id: z.string().uuid(),
      linkedInLeadFormId: z.string().uuid(),
      linkedinCampaignUrn: z.string().nullable(),
      linkedInAdAccountId: z.string().uuid(),

      // Personal information
      firstName: z.string().nullable(),
      lastName: z.string().nullable(),

      // Location information

      // Professional information
      jobTitle: z.string().nullable(),
      jobFunction: z.string().nullable(),
      seniority: z.string().nullable(),
      companyName: z.string().nullable(),

      // Additional information
      workEmail: z.string().nullable(),
      linkedinProfileLink: z.string().nullable(),

      // Timestamps
      leadCreatedAt: z.date().nullable(),
    }),
    leadForm: z.object({
      id: z.string().uuid(),
      linkedInAdAccountId: z.string().uuid(),
      name: z.string(),
    }),
  }),
);

export type LinkedInLeadFormAndLead = ReturnType<
  typeof LinkedInLeadFormAndLead
>;
