import { z } from "zod";

import { conversationMessageCopyTypeSchema } from "../valueObjects/conversationMessageCopyType";
import { conversationSubjectCopyTypeSchema } from "../valueObjects/conversationSubjectCopyType";

export const conversationMessageCopySchema = z.object({
  id: z.string().uuid(),
  subjectCopyId: z.string().uuid(),
  content: z.string(),
  subjectType: z.string(),
  valuePropId: z.string().uuid(),
  type: z.string(),
  status: z.enum(["DRAFT", "ACTIVE", "ARCHIVED"]),
});

export const ConversationMessageCopy = (
  data: z.infer<typeof conversationMessageCopySchema>,
) => {
  return conversationMessageCopySchema.parse(data);
};

export type ConversationMessageCopy = z.infer<
  typeof conversationMessageCopySchema
>;

/*
Value Prop
Subject Type
Message Type
*/
