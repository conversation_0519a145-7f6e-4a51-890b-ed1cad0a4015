import { z } from "zod";

export const createNewConversationValuePropStateSchema = z.object({
  id: z.string(),
  adSegmentId: z.string(),
  valueProp: z.string(),
  conversationSubjectCopyType: z.string(),
  conversationSubjectCopy: z.string(),
  conversationMessageCopyType: z.string(),
  conversationMessageCopy: z.string(),
  conversationCallToActionCopyType: z.string(),
  conversationCallToActionCopy: z.string(),
  toBeUsed: z.boolean(),
});

export type CreateNewConversationValuePropState = z.infer<
  typeof createNewConversationValuePropStateSchema
>;
