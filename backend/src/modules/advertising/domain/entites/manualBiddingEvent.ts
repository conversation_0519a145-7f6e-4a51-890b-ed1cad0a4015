import { z } from "zod";

export const manualBiddingEventSchema = z.object({
  id: z.string().uuid(),
  timestamp: z.coerce.date(),
  linkedInCampaignId: z.string(),
  originalBid: z.number(),
  newBid: z.number(),
  timeElapsed: z.number(),
  budgetUsed: z.number(),
  budget: z.number(),
  dailyOrTotalBudget: z.enum(["monthly", "total"]),
  budgetUsedPercentage: z.number(),
  timeElapsedPercentage: z.number(),
  minBid: z.number(),
  maxBid: z.number(),
  minSuggestedBid: z.number(),
  maxSuggestedBid: z.number(),
  suggestedBid: z.number(),
  decision: z.string(),
  bidType: z.string(),
});
export const ManualBiddingEvent = (
  input: z.infer<typeof manualBiddingEventSchema>,
) => {
  return manualBiddingEventSchema.parse(input);
};

export type ManualBiddingEvent = z.infer<typeof manualBiddingEventSchema>;
