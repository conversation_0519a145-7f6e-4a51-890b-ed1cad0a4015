import { z } from "zod";

import { conversationCallToActionCopyTypeSchema } from "../valueObjects/conversationCallToActionCopyType";
import { conversationMessageCopyTypeSchema } from "../valueObjects/conversationMessageCopyType";
import { conversationSubjectCopyTypeSchema } from "../valueObjects/conversationSubjectCopyType";

export const conversationCallToActionCopySchema = z.object({
  id: z.string().uuid(),
  conversationMessageCopyId: z.string().uuid(),
  conversationSubjectCopyId: z.string().uuid(),
  content: z.string(),
  type: z.string(),
  subjectType: z.string(),
  valuePropId: z.string(),
  messageType: z.string(),
  status: z.enum(["DRAFT", "ACTIVE", "ARCHIVED"]),
});

export const ConversationCallToActionCopy = (
  data: z.infer<typeof conversationCallToActionCopySchema>,
) => {
  return conversationCallToActionCopySchema.parse(data);
};

export type ConversationCallToActionCopy = z.infer<
  typeof conversationCallToActionCopySchema
>;
