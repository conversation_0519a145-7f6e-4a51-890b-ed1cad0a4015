import { z } from "zod";

export const socialPostCallToActionCopyShema = z.object({
  id: z.string().uuid(),
  adSegmentValuePropId: z.string().uuid(),
  callToAction: z.string(),
  type: z.string(),
  status: z.enum(["DRAFT", "ACTIVE", "ARCHIVED"]),
});

export const SocialPostCallToActionCopy = (
  input: z.infer<typeof socialPostCallToActionCopyShema>,
) => {
  return socialPostCallToActionCopyShema.parse(input);
};

export type SocialPostCallToActionCopy = z.infer<
  typeof socialPostCallToActionCopyShema
>;
