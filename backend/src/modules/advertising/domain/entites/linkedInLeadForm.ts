import { z } from "zod";

import { Entity } from "../../../../helpers/entity";
import { linkedInVersionedLeadGenFormUrnSchema } from "../valueObjects/linkedInUrns/linkedInLeadGenFormUrn";

export const LinkedInLeadForm = Entity(
  z.object({
    id: z.string().uuid(),
    linkedInAdAccountId: z.string().uuid(),
    leadGenFormUrn: linkedInVersionedLeadGenFormUrnSchema,
    name: z.string(),
    state: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED"]),
    version: z.number().optional(),
  }),
);

export type LinkedInLeadForm = ReturnType<typeof LinkedInLeadForm>;
