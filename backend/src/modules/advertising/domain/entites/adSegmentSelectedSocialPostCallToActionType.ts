import { z } from "zod";

export const adSegmentSelectedSocialPostCallToActionType = z.object({
  id: z.string().uuid(),
  adSegmentId: z.string().uuid(),
  type: z.string(),
});

export const AdSegmentSelectedSocialPostCallToActionType = (
  input: z.infer<typeof adSegmentSelectedSocialPostCallToActionType>,
) => {
  return adSegmentSelectedSocialPostCallToActionType.parse(input);
};

export type AdSegmentSelectedSocialPostCallToActionType = z.infer<
  typeof adSegmentSelectedSocialPostCallToActionType
>;
