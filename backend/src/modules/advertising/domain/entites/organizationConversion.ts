import { z } from "zod";

export const organizationConversionSchema = z.object({
  id: z.string().uuid(),
  organizationId: z.number().int().positive(),
  conversionUrn: z.string(),
  name: z.string(),
});

export const OrganizationConversion = (
  organizationConversion: z.infer<typeof organizationConversionSchema>,
) => {
  return organizationConversionSchema.parse(organizationConversion);
};

export type OrganizationConversion = z.infer<
  typeof organizationConversionSchema
>;
