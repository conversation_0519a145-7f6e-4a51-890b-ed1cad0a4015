import { BatchNotificationsUseCase } from "../../application/useCase/notifications/batchNotifications.useCase";
import { SendNotificationUseCase } from "../../application/useCase/notifications/sendNotification.useCase";
import { OrganizationRepository } from "../../../core/infrastructure/repositories/organization.repository";
import { CampaignMetricsAggregationService } from "../../infrastructure/services/campaignMetricsAggregation.service";
import { SlackApiService } from "../../infrastructure/services/slack.service";
import { LinkedInAdAccountRepository } from "../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInCampaignGroupRepository } from "../../infrastructure/repositories/linkedInCampaignGroup.repository";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";

export const scheduledSlackNotifications = advertisingInngestClient.createFunction(
  { id: "scheduled-slack-notifications" },
  [
    { event: "slack/scheduled-notifications" },
    { cron: "0 * * * *" }, // Every hour - checks for organizations due for hourly, daily, weekly, biweekly, or monthly notifications
  ],
  async ({ step, event }) => {
    const result = await step.run("send-scheduled-notifications", async () => {
      // Initialize repositories and services
      const organizationRepository = new OrganizationRepository();
      const linkedInAdAccountRepository = new LinkedInAdAccountRepository();
      const linkedInCampaignGroupRepository = new LinkedInCampaignGroupRepository();
      
      const slackApiService = new SlackApiService();
      const campaignMetricsAggregationService = new CampaignMetricsAggregationService(
        linkedInAdAccountRepository,
        linkedInCampaignGroupRepository
      );
      
      const sendNotificationUseCase = new SendNotificationUseCase(
        organizationRepository,
        slackApiService,
        campaignMetricsAggregationService
      );
      
      const batchUseCase = new BatchNotificationsUseCase(
        organizationRepository,
        sendNotificationUseCase
      );

      // Execute batch processing
      const batchResult = await batchUseCase.execute();
      
      console.log(`Scheduled notifications batch completed:`, batchResult);
      
      return batchResult;
    });

    return result;
  }
);

// Legacy alias for backward compatibility
export const dailySlackNotifications = scheduledSlackNotifications; 