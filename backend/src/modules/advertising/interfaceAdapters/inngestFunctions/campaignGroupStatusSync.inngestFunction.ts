import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

import { LinkedInAdAccount } from "../../domain/entites/linkedInAdAccount";
import { LinkedInCampaignGroup } from "../../domain/entites/linkedInCampaignGroup";
import { LinkedInAdAccountRepository } from "../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInCampaignGroupRepository } from "../../infrastructure/repositories/linkedInCampaignGroup.repository";
import { LinkedInService } from "../../infrastructure/services/linkedIn.service";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";

export const campaignGroupSync = advertisingInngestClient.createFunction(
  { id: "campaign-group-sync" },
  [
    { event: "campaignGroupSync/all-customers" },
    ...(process.env.NODE_ENV === "production"
      ? [{ cron: "*/15 * * * *" }]
      : []),
  ],
  async ({ step, event }) => {
    const batchSize = 50;
    let offset = 0;

    let linkedInAdAccounts: LinkedInAdAccount[] | null = [];
    do {
      linkedInAdAccounts = await step.run("get-li-ad-accounts", async () => {
        const linkedInAdAccountRepository = new LinkedInAdAccountRepository();

        return await linkedInAdAccountRepository.getAll({
          offset: offset,
          limit: batchSize,
        });
      });

      if (linkedInAdAccounts?.length === 0) {
        return;
      }

      await step.run("run-adaccounts-batches", async () => {
        const linkedInCampaignGroupRepository =
          new LinkedInCampaignGroupRepository();
        if (!linkedInAdAccounts) {
          return;
        }

        for (const linkedInAdAccount of linkedInAdAccounts) {
          let client;
          try {
            client = await getLinkedInApiClientFromOrganizationId(
              linkedInAdAccount.organizationId,
            );
          } catch (e) {
            console.error(
              "[campaignGroupStatusSync.inngest] Client not retrieved for account id",
              linkedInAdAccount.id,
            );
            continue;
          }

          if (!client) {
            console.error(
              "[campaignGroupStatusSync.inngest] No linkedin client for org:",
              linkedInAdAccount.organizationId,
            );
            continue;
          }
          const linkedInService = new LinkedInService(client);

          const kalosCampaignGroupMap: Record<string, LinkedInCampaignGroup> =
            {};

          const kalosCampaignGroups =
            await linkedInCampaignGroupRepository.getAllForLinkedInAdAccountId(
              linkedInAdAccount.id,
            );

          const campaignGroupUrns = kalosCampaignGroups.map((campaignGroup) => {
            kalosCampaignGroupMap[campaignGroup.linkedInCampaignGroupUrn] = {
              ...campaignGroup,
            };

            return campaignGroup.linkedInCampaignGroupUrn;
          });

          // not db id, but id form account urn
          const linkedInAdAccountId = linkedInAdAccount.linkedInAdAccountUrn
            .split(":")
            .pop();

          if (!linkedInAdAccountId) {
            console.error(
              "[campaignGroupStatusSync.inngest] No linkedin ad account id for:",
              linkedInAdAccount.id,
            );
            continue;
          }

          const linkedInCampaignGroups =
            await linkedInService.getCampaignGroups({
              linkedInAdAccountId: linkedInAdAccountId,
              campaignGroupUrns: campaignGroupUrns,
            });

          // Check to see if linkedinCampaign group status is different from kalos db campaign group status
          for (const linkedInCampaignGroup of linkedInCampaignGroups.elements) {
            const campaignGroupUrn =
              "urn:li:sponsoredCampaignGroup:" + linkedInCampaignGroup.id;

            const kalosCampaignGroup = kalosCampaignGroupMap[campaignGroupUrn];

            if (!kalosCampaignGroup) {
              // campaign group doesn't exist in kalos db
              continue;
            }
            if (linkedInCampaignGroup.status !== kalosCampaignGroup.status) {
              console.log(
                "[campaignGroupStatusSync.inngest] Updating Status",
                linkedInCampaignGroup.id,
                linkedInCampaignGroup.status,
              );
              // update status
              await linkedInCampaignGroupRepository.updateStatusByLinkedInUrn(
                campaignGroupUrn,
                linkedInCampaignGroup.status,
              );
            }
          }
        }
      });
      offset += batchSize;
    } while (linkedInAdAccounts?.length === batchSize);
  },
);
