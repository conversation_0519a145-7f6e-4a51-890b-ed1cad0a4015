// crmProcessOrg.ts
import { sourceEnum } from "../../../../database/schemas/crm/crmCommon.enums"; // Full Relative path

import { Engagement } from "../../../../database/schemas/crm/engagement.table";
// Full Relative path
import { OrganizationRepository } from "../../../core/infrastructure/repositories/organization.repository";
import { createUuid } from "../../../core/utils/uuid";
import { Contact } from "../../../crm/domain/entities/contact";
import { HubspotProvider } from "../../../crm/infrastructure/externalCrms/hubspot.externalCrm.provider";
import { SalesforceClientNew } from "../../../crm/infrastructure/externalCrms/salesforce.externalCrm.provider";
import { contactRepository } from "../../../crm/infrastructure/repositories/contact.repository";
import { CrmCredentialRepository } from "../../../crm/infrastructure/repositories/crmCredential.repository";
import { crmMappingRepository } from "../../../crm/infrastructure/repositories/crmMapping.repository"; // Corrected import
import { EngagementRepository } from "../../../crm/infrastructure/repositories/engagement.repository"; // Relative path import

import { mappingDefinitionRepository } from "../../../crm/infrastructure/repositories/mappingDefinition.repository"; // Added import
import { LinkedInCampaignService } from "../../domain/services/linkedInCampaign.service";
import { LinkedInCampaignGroupService } from "../../domain/services/linkedInCampaignGroup.service";
import { LinkedInSponsoredCreativeService } from "../../domain/services/linkedInSponsoredCreative.service";
import { LinkedInCampaignRepository } from "../../infrastructure/repositories/linkedInCampaign.repository";
import { LinkedInCampaignGroupRepository } from "../../infrastructure/repositories/linkedInCampaignGroup.repository";
import { LinkedInLeadFormLeadRepository } from "../../infrastructure/repositories/linkedInLeadFormLead.repository"; // Added import
import { LinkedInSponsoredCreativeRepository } from "../../infrastructure/repositories/linkedInSponsoredCreative.repository";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";

const checkIfLeadInKalos = async (
  sponsoredCreativeId?: string,
  campaignGroupId?: string,
  campaignId?: string,
): Promise<boolean> => {
  if (sponsoredCreativeId && !isNaN(Number(sponsoredCreativeId))) {
    const sponsoredCreativeUrn = `urn:li:sponsoredCreative:${sponsoredCreativeId}`;

    const linkedInSponsoredCreativeRepository =
      new LinkedInSponsoredCreativeRepository();
    const linkedInSponsoredCreativeService =
      new LinkedInSponsoredCreativeService(linkedInSponsoredCreativeRepository);

    const sponsoredCreativeExists =
      await linkedInSponsoredCreativeService.getOneBySponsoredCreativeUrn({
        linkedInSponsoredCreativeUrn: sponsoredCreativeUrn,
      });

    if (sponsoredCreativeExists) {
      return true;
    }
  }

  if (campaignGroupId && !isNaN(Number(campaignGroupId))) {
    const campaignGroupUrn = `urn:li:sponsoredCampaignGroup:${campaignGroupId}`;

    const linkedInCampaignGroupRepository =
      new LinkedInCampaignGroupRepository();
    const linkedInCampaignGroupService = new LinkedInCampaignGroupService(
      linkedInCampaignGroupRepository,
    );

    const campaignGroupExists =
      await linkedInCampaignGroupService.getOneByLinkedInUrn(campaignGroupUrn);

    if (campaignGroupExists) {
      return true;
    }
  }

  if (campaignId && !isNaN(Number(campaignId))) {
    const campaignUrn = `urn:li:sponsoredCampaign:${campaignId}`;

    const linkedInCampaignRepository = new LinkedInCampaignRepository();
    const linkedInCampaignService = new LinkedInCampaignService(
      linkedInCampaignRepository,
    );

    const campaginExists =
      await linkedInCampaignService.getOneByLinkedInCampaignUrn(campaignUrn);
    if (campaginExists) {
      return true;
    }
  }

  return false;
};

export const processOrgCrmImport = advertisingInngestClient.createFunction(
  { id: "crm-process-org-crm-import" },
  { event: "crm/process-org-crm-import" },
  async ({ event, step }) => {
    const { organizationId } = event.data;

    const crmCredentialRepository = new CrmCredentialRepository();
    // Instantiate Repositories
    const linkedInLeadFormLeadRepository = new LinkedInLeadFormLeadRepository(); // Instantiated repo
    const engagementRepository = new EngagementRepository(); // Instantiated Engagement repo

    const credentials =
      await crmCredentialRepository.getAllByOrganizationId(organizationId);

    const organizationRepository = new OrganizationRepository();
    const organization = await organizationRepository.getOne(organizationId);

    for (const crmCredential of credentials) {
      const credential = crmCredential.credential;

      // Declare variables that need to be shared between steps (only for salesforce)
      let firstMeetingMapping: {
        entity: string;
        field: string;
        type: string;
        operator: string;
      } | null = null;

      const getLeadsStep = await step.run("Get Leads", async () => {
        // These arrays need to be returned from this step to be shared
        const contactIds: Array<{
          salesforceContactId: string;
          internalContactId: string;
        }> = [];
        const opportunityIds: Array<{
          salesforceOpportunityId: string;
          internalContactId: string;
        }> = [];
        if (!credential.refreshToken) {
          return;
        }
        if (credential.crmType === "hubspot") {
          const hubspotProvider = await HubspotProvider.createFactory({
            organizationId: credential.organizationId,
            organizationUserId: credential.organizationUserId,
            refreshToken: credential.refreshToken,
          });

          // await hubspotProvider.getMeetings();
          // return;

          const contactExists =
            await contactRepository.getOneForOrganizationId(organizationId);

          const contactsGenerator = hubspotProvider.getLeads(
            organization?.createdAt ?? new Date("2024-08-01T00:00:00.000Z"),
            new Date(),
            !contactExists, // Full pull if no existing data
            // true,
          );

          // Go through each contact
          // Check if mapped data from utm fields have creativeid, campaigngroupid, campaignid
          // if any of those (urns) exist in our db, ingest
          for await (const batch of contactsGenerator) {
            const contacts: Contact[] = [];

            for (const contact of batch) {
              const isLeadFromKalos = await checkIfLeadInKalos(
                contact.creativeId ?? undefined,
                contact.campaignGroupId ?? undefined,
                contact.campaignId ?? undefined,
              );

              if (!isLeadFromKalos) {
                continue;
              }

              contacts.push({
                id: createUuid(),
                organizationId,
                crmId: contact.id,
                firstName: contact.firstName,
                lastName: contact.lastName,
                email: contact.email,
                phoneNumber: contact.phoneNumber,
                title: contact.title,
                lifecycleStage: contact.lifecycleStage,
                linkedinCampaignGroupUrn: !isNaN(
                  Number(contact.campaignGroupId),
                )
                  ? `urn:li:sponsoredCampaignGroup:${contact.campaignGroupId}`
                  : null,
                linkedinSponsoredCreativeUrn: !isNaN(Number(contact.creativeId))
                  ? `urn:li:sponsoredCreative:${contact.creativeId}`
                  : null,
                utmParams: contact.utmParams,
                leadCreatedAt: contact.createDate,
                companyName: contact.companyName,
                source: "hubspot",
                pageTracking: contact.pageTracking,
              });
            }
            // --- Save the batch of contacts ---
            if (contacts.length > 0) {
              // Use the existing save method
              await contactRepository.createOrUpdateIfExistsMany(contacts);

              // --- Attempt to link saved contacts to LinkedIn leads ---
              for (const savedContact of contacts) {
                if (savedContact.email) {
                  try {
                    await linkedInLeadFormLeadRepository.linkContactIdByEmailAndOrg(
                      {
                        workEmail: savedContact.email,
                        organizationId: savedContact.organizationId, // Convert number to string
                        contactId: savedContact.id,
                      },
                    );
                  } catch (linkError) {
                    console.error(
                      `Failed to link contact ${savedContact.id} (${savedContact.email}) to LinkedIn lead:`,
                      linkError,
                    );
                    // Decide if we should continue or handle error differently
                  }
                }
              }
            }
          }
        } else if (credential.crmType === "salesforce") {
          const salesforceConnection = crmCredential.connection;
          if (!salesforceConnection) {
            return;
          }
          //salesforce
          const salesforceProvider = new SalesforceClientNew(
            salesforceConnection.instanceUrl,
            credential.refreshToken,
          );

          // await salesforceProvider.getLeadandContactchema();
          // return;
          const contactExists =
            await contactRepository.getOneForOrganizationId(organizationId);

          // Fetch dynamic UTM mappings from the database

          // 1. Fetch the 'utm' mapping definition
          const utmMappingDefinition =
            await mappingDefinitionRepository.getLatestMappingDefinitionByType(
              "utm",
            );

          let actualFieldMappings: Record<string, string> | null = null;

          if (utmMappingDefinition) {
            // 2. Fetch the CRM-specific mapping instance using the definition ID
            const fetchedCrmMapping =
              await crmMappingRepository.getCrmMappingByOrgAndType(
                organizationId,
                "salesforce",
                utmMappingDefinition.id, // Use the ID from the fetched definition
              );
            if (fetchedCrmMapping && fetchedCrmMapping.mappings) {
              actualFieldMappings = fetchedCrmMapping.mappings as Record<
                string,
                string
              >; // Assuming mappings is Record<string, string>
            }
          } else {
            console.warn(
              `No UTM mapping definition found for organization ${organizationId}. Falling back to defaults or no UTM processing.`,
            );
            // Optionally, log an error or send a notification if a definition is expected
          }

          // 3. Use fetched mappings, or fallback to a default if none are found
          const customUtmFieldMappings: Record<string, string> =
            actualFieldMappings && Object.keys(actualFieldMappings).length > 0
              ? actualFieldMappings
              : {
                  // Fallback default mappings
                  utm_source: "UTM_Source__c",
                  utm_medium: "UTM_Medium__c",
                  utm_term: "UTM_Term__c",
                  utm_campaign: "UTM_Campaign__c",
                  utm_content: "UTM_Content__c",
                };

          // Filter out empty values and ensure consistent casing
          const customUtmFields = Object.values(customUtmFieldMappings)
            .filter((field) => field && field.trim() !== "")
            .map((field) => field.trim());

          // Fetch the 'first_meeting' mapping definition to get the mapping definition ID
          const firstMeetingMappingDefinition =
            await mappingDefinitionRepository.getLatestMappingDefinitionByType(
              "first_meeting",
            );

          if (firstMeetingMappingDefinition) {
            // Get the actual first meeting mapping for this organization
            const crmMapping =
              await crmMappingRepository.getCrmMappingByOrgAndType(
                credential.organizationId,
                credential.crmType,
                firstMeetingMappingDefinition.id,
              );

            if (crmMapping && crmMapping.mappings) {
              // The mappings should contain the actual first meeting configuration
              // Structure: {"type": "date", "field": "Date_BQ__c", "entity": "contact", "operator": "exists"}
              const mapping = crmMapping.mappings as any;
              if (
                mapping.entity &&
                mapping.field &&
                mapping.type &&
                mapping.operator
              ) {
                firstMeetingMapping = {
                  entity: mapping.entity,
                  field: mapping.field,
                  type: mapping.type,
                  operator: mapping.operator,
                };
              }
            }
          }

          // Add first meeting field to custom fields if we have a mapping and it's for leads
          const allCustomFields = [...customUtmFields];
          if (firstMeetingMapping && firstMeetingMapping.entity === "lead") {
            allCustomFields.push(firstMeetingMapping.field);
          }

          // await salesforceProvider.getLeadandContactchema();
          const leadsGenerator = await salesforceProvider.getLeads(
            !contactExists,
            organization?.createdAt ?? new Date("2025-01-15T00:00:00.000Z"),
            allCustomFields,
          );

          for await (const batch of leadsGenerator) {
            const sfLeads: Contact[] = [];
            const leadsWithFirstMeetings: Array<{
              leadId: string;
              contactId: string;
              firstMeetingValue: string;
              leadData: any;
            }> = [];

            for (const lead of batch) {
              const utmParams: Record<string, string> = {};

              // Map custom utm fields to normalized (standard) utm fields
              Object.entries(customUtmFieldMappings).forEach(
                ([utmField, customField]) => {
                  const value = lead[customField];

                  if (value) {
                    utmParams[utmField] = value;
                  }
                },
              );
              const isLeadFromKalos = await checkIfLeadInKalos(
                utmParams.utm_term ?? undefined, //creative
                utmParams.utm_content ?? undefined, // campaigngroup
                utmParams.utm_campaign ?? undefined, //campaign
              );

              if (!isLeadFromKalos) {
                continue;
              }

              // Create the contact object first so we have the internal contact ID
              const contactId = createUuid();
              const contact = {
                id: contactId,
                organizationId: organizationId,
                crmId: lead.Id,
                firstName: lead.FirstName,
                lastName: lead.LastName,
                email: lead.Email,
                phoneNumber: lead.Phone,
                title: lead.Title,
                companyName: lead.Company,
                leadCreatedAt: new Date(lead.CreatedDate),
                lifecycleStage: lead.IsConverted
                  ? ("marketingqualifiedlead" as const)
                  : ("lead" as const),
                source: "salesforce" as const,
                linkedinSponsoredCreativeUrn: !isNaN(Number(utmParams.utm_term))
                  ? `urn:li:sponsoredCreative:${utmParams.utm_term}`
                  : null,
                linkedinCampaignGroupUrn: !isNaN(Number(utmParams.utm_content))
                  ? `urn:li:sponsoredCampaignGroup:${utmParams.utm_content}`
                  : null,
                pageTracking: {}, // no page tracking in salesforce unless custom
                utmParams: utmParams,
              };

              // Process first meeting data directly if we have it for leads
              if (
                firstMeetingMapping &&
                firstMeetingMapping.entity === "lead"
              ) {
                const firstMeetingValue = lead[firstMeetingMapping.field];

                if (
                  firstMeetingValue &&
                  firstMeetingMapping.operator === "exists"
                ) {
                  console.log(
                    `Lead ${lead.Id} has first meeting scheduled: ${firstMeetingValue}`,
                  );

                  // Store the first meeting data with the contact ID we already have
                  leadsWithFirstMeetings.push({
                    leadId: lead.Id,
                    contactId: contactId, // Use the contact ID we just created
                    firstMeetingValue: firstMeetingValue,
                    leadData: lead,
                  });
                }
              }

              // Collect contact IDs for first meeting processing (for contacts and opportunities)
              if (
                firstMeetingMapping &&
                firstMeetingMapping.entity !== "lead"
              ) {
                switch (firstMeetingMapping.entity) {
                  case "contact":
                    if (lead.ConvertedContactId) {
                      // Store both the Salesforce contact ID and our internal contact ID
                      contactIds.push({
                        salesforceContactId: lead.ConvertedContactId,
                        internalContactId: contactId,
                      });
                    }
                    break;
                  case "opportunity":
                    if (lead.ConvertedOpportunityId) {
                      // Store both the Salesforce opportunity ID and our internal contact ID
                      opportunityIds.push({
                        salesforceOpportunityId: lead.ConvertedOpportunityId,
                        internalContactId: contactId,
                      });
                    }
                    break;
                }
              }

              sfLeads.push(contact);
            }
            if (sfLeads.length > 0) {
              // Use the existing save method
              await contactRepository.createOrUpdateIfExistsMany(sfLeads);

              // --- Create first meeting engagements for leads ---
              if (leadsWithFirstMeetings.length > 0) {
                const engagementsToCreate = [];

                for (const leadWithMeeting of leadsWithFirstMeetings) {
                  const firstMeetingValue =
                    leadWithMeeting.leadData[firstMeetingMapping?.field ?? ""];

                  let meetingStartDate = null;

                  if (firstMeetingMapping?.type === "date") {
                    if (firstMeetingValue) {
                      meetingStartDate = new Date(firstMeetingValue);
                    }
                  } else if (firstMeetingMapping?.type === "boolean") {
                    if (firstMeetingValue === false) {
                      continue;
                    }
                  }

                  // Use the contact ID we already have (no lookup needed!)
                  const engagement = {
                    id: createUuid(),
                    organizationId: credential.organizationId,
                    contactId: leadWithMeeting.contactId,
                    source: "salesforce" as const,
                    externalId: `first_meeting_${leadWithMeeting.leadId}`,
                    type: "meeting" as const,
                    subject: "First Meeting Scheduled",
                    startTime: meetingStartDate,
                    endTime: null,
                    status: "scheduled" as const,
                    metadata: {
                      firstMeetingField: firstMeetingMapping?.field,
                      leadData: leadWithMeeting.leadData,
                    },
                    createdAt: new Date(),
                    updatedAt: new Date(),
                  };

                  engagementsToCreate.push(engagement);
                }

                if (engagementsToCreate.length > 0) {
                  await engagementRepository.createOrUpdateIfExistsMany(
                    engagementsToCreate,
                  );
                  console.log(
                    `Created ${engagementsToCreate.length} first meeting engagements for leads`,
                  );
                }
              }

              // --- Attempt to link saved contacts to LinkedIn leads ---
              for (const savedContact of sfLeads) {
                if (savedContact.email) {
                  try {
                    await linkedInLeadFormLeadRepository.linkContactIdByEmailAndOrg(
                      {
                        workEmail: savedContact.email,
                        organizationId: savedContact.organizationId, // Convert number to string
                        contactId: savedContact.id,
                      },
                    );
                  } catch (linkError) {
                    console.error(
                      `Failed to link contact ${savedContact.id} (${savedContact.email}) to LinkedIn lead:`,
                      linkError,
                    );
                    // Decide if we should continue or handle error differently
                  }
                }
              }
            }
          }
        }

        // Return the data that needs to be shared with the next step
        return {
          firstMeetingMapping,
          contactIds,
          opportunityIds,
        };
      });

      const getMeetingsStep = await step.run("Get Meetings", async () => {
        // Process first meetings for contacts and opportunities (leads are handled during lead processing)

        // Get the data from the previous step, for salesforce
        const { firstMeetingMapping, contactIds, opportunityIds } =
          getLeadsStep || {};

        if (!credential.refreshToken) {
          return;
        }
        if (credential.crmType === "hubspot") {
          const hubspotProvider = await HubspotProvider.createFactory({
            organizationId: credential.organizationId,
            organizationUserId: credential.organizationUserId,
            refreshToken: credential.refreshToken,
          });

          // Check if we already have engagements for this organization
          const engagementsInDb =
            await engagementRepository.getByOrganizationId(
              credential.organizationId,
            );

          const engagementExists = engagementsInDb.length > 0;

          // Get the meetings generator - do a full pull if no engagements exist
          const meetingsGenerator =
            hubspotProvider.getMeetings(!engagementExists);

          // Process meetings in batches as they come in
          for await (const meetingsBatch of meetingsGenerator) {
            console.log(`Processing batch of ${meetingsBatch.length} meetings`);

            if (meetingsBatch.length === 0) continue;

            // Extract all contact IDs from the meetings
            const allContactIds = new Set<string>();
            meetingsBatch.forEach((meeting) => {
              meeting.contactIds.forEach((contactId) => {
                allContactIds.add(contactId);
              });
            });

            if (allContactIds.size === 0) {
              console.log(
                "No contact IDs found in this batch of meetings, skipping",
              );
              continue;
            }

            console.log(
              `Found ${allContactIds.size} unique contact IDs in meetings`,
            );

            // Find which contacts exist in our database
            const existingContacts = await contactRepository.getManyByCrmIds(
              Array.from(allContactIds),
              credential.organizationId,
            );

            if (existingContacts.length === 0) {
              console.log(
                "None of the contacts from meetings exist in our database, skipping",
              );
              continue;
            }

            console.log(
              `Found ${existingContacts.length} contacts in our database`,
            );

            // Create a map for quick lookup of internal contact IDs by CRM ID
            const contactMap = new Map<string, string>();
            existingContacts.forEach((contact) => {
              contactMap.set(contact.crmId, contact.id);
            });

            // Create engagements for meetings with contacts that exist in our database
            const engagementsToCreate: Engagement[] = [];

            for (const meeting of meetingsBatch) {
              for (const contactCrmId of meeting.contactIds) {
                const contactId = contactMap.get(contactCrmId);
                if (contactId) {
                  engagementsToCreate.push({
                    id: createUuid(),
                    organizationId: credential.organizationId,
                    contactId: contactId,
                    source: "hubspot",
                    externalId: meeting.id,
                    type: "meeting",
                    subject: meeting.title ?? null,
                    startTime: meeting.startTime
                      ? new Date(meeting.startTime)
                      : null,
                    endTime: meeting.endTime ? new Date(meeting.endTime) : null,
                    status: meeting.status ?? null, // Assuming all fetched meetings are completed
                    metadata: meeting.rawProperties,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                  });
                }
              }
            }

            if (engagementsToCreate.length > 0) {
              console.log(`Creating ${engagementsToCreate.length} engagements`);
              console.log("engagements", engagementsToCreate);
              await engagementRepository.createOrUpdateIfExistsMany(
                engagementsToCreate,
              );
              console.log(`Successfully created engagements`);
            } else {
              console.log("No engagements to create for this batch");
            }
          }

          console.log("Finished processing all meetings");
        } else if (credential.crmType === "salesforce") {
          if (
            !firstMeetingMapping ||
            (!contactIds?.length && !opportunityIds?.length)
          ) {
            console.log(
              "No first meeting mapping or contact/opportunity IDs found, skipping first meeting processing",
            );
            return;
          }

          console.log("Processing Salesforce first meetings...");
          const salesforceConnection = crmCredential.connection;
          if (!salesforceConnection || !credential.refreshToken) {
            console.log(
              "No Salesforce connection or refresh token found, skipping first meeting processing",
            );
            return;
          }

          // Initialize Salesforce provider
          const salesforceProvider = new SalesforceClientNew(
            salesforceConnection.instanceUrl,
            credential.refreshToken,
          );

          // Process first meetings based on entity type
          if (
            firstMeetingMapping.entity === "contact" &&
            contactIds &&
            contactIds.length > 0
          ) {
            console.log(
              `Processing ${contactIds.length} contacts for first meetings`,
            );

            for await (const batch of salesforceProvider.getFirstMeetings(
              "contact",
              firstMeetingMapping.field,
              contactIds.map((c) => c.salesforceContactId),
            )) {
              console.log(
                `Processing batch of ${batch.length} contacts with first meeting data`,
              );

              // Process each contact and check if they have a first meeting scheduled
              for (const contact of batch) {
                const firstMeetingValue = contact[firstMeetingMapping.field];

                if (
                  firstMeetingValue &&
                  firstMeetingMapping.operator === "exists"
                ) {
                  console.log(
                    `Contact ${contact.Id} has first meeting scheduled: ${firstMeetingValue}`,
                  );

                  // Find the internal contact ID using our mapping
                  const contactMapping = contactIds.find(
                    (c) => c.salesforceContactId === contact.Id,
                  );

                  if (contactMapping) {
                    let meetingStartDate = null;

                    if (firstMeetingMapping.type === "date") {
                      meetingStartDate = new Date(firstMeetingValue);
                    } else if (firstMeetingMapping.type === "boolean") {
                      if (firstMeetingValue === false) {
                        continue;
                      }
                    }

                    // Create an engagement for the first meeting
                    const engagement = {
                      id: createUuid(),
                      organizationId: credential.organizationId,
                      contactId: contactMapping.internalContactId,
                      source: "salesforce" as const,
                      externalId: `first_meeting_${contact.Id}`,
                      type: "meeting" as const,
                      subject: "First Meeting Scheduled",
                      startTime: meetingStartDate,
                      endTime: null,
                      status: "scheduled" as const,
                      metadata: {
                        firstMeetingField: firstMeetingMapping.field,
                        contactData: contact,
                      },
                      createdAt: new Date(),
                      updatedAt: new Date(),
                    };

                    await engagementRepository.createOrUpdateIfExistsMany([
                      engagement,
                    ]);
                    console.log(
                      `Created first meeting engagement for contact ${contactMapping.internalContactId}`,
                    );
                  }
                }
              }
            }
          }

          if (
            firstMeetingMapping.entity === "opportunity" &&
            opportunityIds &&
            opportunityIds.length > 0
          ) {
            console.log(
              `Processing ${opportunityIds.length} opportunities for first meetings`,
            );

            for await (const batch of salesforceProvider.getFirstMeetings(
              "opportunity",
              firstMeetingMapping.field,
              opportunityIds.map((o) => o.salesforceOpportunityId),
            )) {
              console.log(
                `Processing batch of ${batch.length} opportunities with first meeting data`,
              );

              // Process each opportunity and check if they have a first meeting scheduled
              for (const opportunity of batch) {
                const firstMeetingValue =
                  opportunity[firstMeetingMapping.field];

                if (
                  firstMeetingValue &&
                  firstMeetingMapping.operator === "exists"
                ) {
                  console.log(
                    `Opportunity ${opportunity.Id} has first meeting scheduled: ${firstMeetingValue}`,
                  );

                  // Find the internal contact ID using our mapping
                  const opportunityMapping = opportunityIds.find(
                    (o) => o.salesforceOpportunityId === opportunity.Id,
                  );

                  if (opportunityMapping) {
                    let meetingStartDate = null;

                    if (firstMeetingMapping.type === "date") {
                      meetingStartDate = new Date(firstMeetingValue);
                    } else if (firstMeetingMapping.type === "boolean") {
                      if (firstMeetingValue === false) {
                        continue;
                      }
                    }

                    // Create an engagement for the first meeting
                    const engagement = {
                      id: createUuid(),
                      organizationId: credential.organizationId,
                      contactId: opportunityMapping.internalContactId,
                      source: "salesforce" as const,
                      externalId: `first_meeting_${opportunity.Id}`,
                      type: "meeting" as const,
                      subject: "First Meeting Scheduled",
                      startTime: meetingStartDate,
                      endTime: null,
                      status: "scheduled" as const,
                      metadata: {
                        firstMeetingField: firstMeetingMapping.field,
                        opportunityData: opportunity,
                      },
                      createdAt: new Date(),
                      updatedAt: new Date(),
                    };

                    await engagementRepository.createOrUpdateIfExistsMany([
                      engagement,
                    ]);
                    console.log(
                      `Created first meeting engagement for opportunity ${opportunityMapping.internalContactId}`,
                    );
                  }
                }
              }
            }
          }

          // Note: Lead first meetings are processed directly during lead processing for efficiency

          console.log("Finished processing first meetings");
        }
      });
    }
  },
);
