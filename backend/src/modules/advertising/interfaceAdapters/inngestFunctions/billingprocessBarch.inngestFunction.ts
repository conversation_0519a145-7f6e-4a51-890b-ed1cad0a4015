import { Stripe } from "stripe";

import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

import { GetSpendInTimeRnageUseCase } from "../../application/useCase/linkedInCampaignGroup/getSpendInTimeRange.useCase";
import { LinkedInCampaignGroupRepository } from "../../infrastructure/repositories/linkedInCampaignGroup.repository";
import { LinkedInService } from "../../infrastructure/services/linkedIn.service";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";

export const billingProcessBatch = advertisingInngestClient.createFunction(
  { id: "billing-process-batch" },
  { event: "billing/process-ad-spend-batch" },
  async ({ step, event }) => {
    const { organizations } = event.data as {
      organizations: {
        organizationId: number;
        stripeCustomerId: string;
      }[];
    };
    for (const organization of organizations) {
      await step.run("process-ad-spend", async () => {
        const stripe = new Stripe(process.env.STRIPE_SECRET_KEY ?? "");
        const client = await getLinkedInApiClientFromOrganizationId(
          organization.organizationId,
        );
        if (!client) {
          throw new Error("No linkedin client found");
        }

        const linkedInService = new LinkedInService(client);
        const useCase = new GetSpendInTimeRnageUseCase({
          linkedInService,
          linkedInCampaignGroupRepository:
            new LinkedInCampaignGroupRepository(),
        });
        const res = await useCase.execute({
          organizationId: organization.organizationId,
          timeRange: { startDate: new Date("2025-03-01"), endDate: new Date() },
        });
        if (res > 0) {
          await stripe.billing.meterEvents.create({
            event_name: "linkedin_spend_meter",
            payload: {
              value: Math.floor(res).toString(),
              stripe_customer_id: organization.stripeCustomerId,
            },
          });
        }
      });
    }
  },
);
