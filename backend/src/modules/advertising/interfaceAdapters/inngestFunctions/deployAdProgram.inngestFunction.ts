import { z } from "zod";

import { getLinkedInFromUserId } from "../../../../../../packages/linkedInApi/src/linkedInClient";
import { SegmentService } from "../../../core/domain/services/segment.service";
import { segmentRepository } from "../../../core/infrastructure/repositories/segment.repository";
import { TransactionManagerService } from "../../../core/infrastructure/services/transcationManager.service";
import { deployLinkedInAdProgramRequestDto } from "../../application/dtos/controllerDtos/linkedInAdProgram/deployLinkedInAdProgram.dto";
import { DeployLinkedInAdProgramUseCase } from "../../application/useCase/linkedInAdProgram/deployLinkedInAdProgram.useCase";
import { DeploySponsoredConversationAdProgramUseCase } from "../../application/useCase/linkedInAdProgram/deploySponsoredConversationAdProgram.useCase";
import { AdAudienceService } from "../../domain/services/adAudience.service";
import { AdCreativeService } from "../../domain/services/adCreative.service";
import { AdSegmentService } from "../../domain/services/adSegment.service";
import { AdSegmentValuePropService } from "../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../domain/services/conversationCallToActionCopy.service";
import { ConversationCopySerivce } from "../../domain/services/conversationCopy.service";
import { ConversationSubjectCopyService } from "../../domain/services/conversationSubjectCopy.service";
import { LinkedInAdAccountService } from "../../domain/services/linkedInAdAccount.service";
import { LinkedInAdProgramService } from "../../domain/services/linkedInAdProgram.service";
import { LinkedInAdProgramAdCreativeService } from "../../domain/services/linkedInAdProgramAdCreative.service";
import { LinkedInCampaignService } from "../../domain/services/linkedInCampaign.service";
import { LinkedInCampaignGroupService } from "../../domain/services/linkedInCampaignGroup.service";
import { LinkedInPostService } from "../../domain/services/linkedInPost.service";
import { LinkedInSponsoredCreativeService } from "../../domain/services/linkedInSponsoredCreative.service";
import { OrganizationConversionService } from "../../domain/services/organizationConversion.service";
import { SocialPostAdCopyService } from "../../domain/services/socialPostAdCopy.service";
import { AdCreativeRepository } from "../../infrastructure/repositories/adCreative.repository";
import { AdSegmentValuePropRepository } from "../../infrastructure/repositories/adSegmentValueProp.repository";
import { AdSegmentValuePropCreativeRepository } from "../../infrastructure/repositories/adSegmentValuePropCreative.repository";
import { ConversationCallToActionCopyRepository } from "../../infrastructure/repositories/conversationCallToActionCopy.repository";
import { ConversationCopyRepository } from "../../infrastructure/repositories/conversationCopy.repository";
import { ConversationMessageCopyRepository } from "../../infrastructure/repositories/conversationMessageCopy.repository";
import { ConversationSubjectCopyRepository } from "../../infrastructure/repositories/conversationSubjectCopy.repository";
import { LinkedInAdAccountRepository } from "../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInAdAudienceRepository } from "../../infrastructure/repositories/linkedInAdAudience.repository";
import { LinkedInAdProgramRepository } from "../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInAdProgramCreativeRepository } from "../../infrastructure/repositories/linkedInAdProgramCreative.repository";
import { LinkedInAdSegmentRepository } from "../../infrastructure/repositories/linkedInAdSegment.repository";
import { LinkedInCampaignRepository } from "../../infrastructure/repositories/linkedInCampaign.repository";
import { LinkedInCampaignGroupRepository } from "../../infrastructure/repositories/linkedInCampaignGroup.repository";
import { LinkedInPostRepository } from "../../infrastructure/repositories/linkedInPost.repository";
import { LinkedInSponsoredCreativeRepository } from "../../infrastructure/repositories/linkedInSponsoredCreative.repository";
import { OrganizationConversionRepository } from "../../infrastructure/repositories/organizationConversion.repository";
import { SocialPostCallToActionCopyRepository } from "../../infrastructure/repositories/socialPostCallToActionCopy.repository";
import { SocialPostCopyRepository } from "../../infrastructure/repositories/socialPostCopy.repository";
import { AdCreativeS3StorageService } from "../../infrastructure/services/adCreativeS3Storage.service";
import { LinkedInService } from "../../infrastructure/services/linkedIn.service";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";

const eventDataSchema = z.object({
  input: deployLinkedInAdProgramRequestDto,
  ctx: z.object({
    organizationId: z.number(),
    userId: z.string(),
  }),
});

export const deployAdProgram = advertisingInngestClient.createFunction(
  { id: "deployAdProgram", retries: 0 },
  { event: "linkedin/deployAdProgram" },
  async ({ step, event }) => {
    const { input, ctx } = eventDataSchema.parse(event.data);
    const linkedInAdProgramRepository = new LinkedInAdProgramRepository();
    const linkedInAdProgramService = new LinkedInAdProgramService(
      linkedInAdProgramRepository,
    );
    const linkedInAdAccountRepository = new LinkedInAdAccountRepository();
    const linkedInAdAccountService = new LinkedInAdAccountService(
      linkedInAdAccountRepository,
    );
    const adSegmentRepository = new LinkedInAdSegmentRepository();
    const adSegmentService = new AdSegmentService(adSegmentRepository);
    const adSegmentValuePropRepository = new AdSegmentValuePropRepository();
    const adSegmentValuePropService = new AdSegmentValuePropService(
      adSegmentValuePropRepository,
    );
    const linkedInClient = await getLinkedInFromUserId(ctx.userId);
    if (!linkedInClient) {
      throw new Error("LinkedIn client not found");
    }
    const linkedInService = new LinkedInService(linkedInClient);
    const segmentService = new SegmentService(segmentRepository);
    const linkedInCampaignGroupRepository =
      new LinkedInCampaignGroupRepository();
    const linkedInCampaignGroupService = new LinkedInCampaignGroupService(
      linkedInCampaignGroupRepository,
    );
    const adAudienceRepository = new LinkedInAdAudienceRepository();
    const adAudienceService = new AdAudienceService(adAudienceRepository);
    const linkedInCampaignRepository = new LinkedInCampaignRepository();
    const linkedInCampaignService = new LinkedInCampaignService(
      linkedInCampaignRepository,
    );
    const linkedInPostRepository = new LinkedInPostRepository();
    const linkedInPostService = new LinkedInPostService(linkedInPostRepository);
    const socialPostAdCopyRepository = new SocialPostCopyRepository();
    const socialPostAdCopyService = new SocialPostAdCopyService(
      socialPostAdCopyRepository,
    );
    const adProgramCreativeRepository =
      new LinkedInAdProgramCreativeRepository();
    const adProgramCreativeService = new LinkedInAdProgramAdCreativeService(
      adProgramCreativeRepository,
    );
    const adCreativeRepository = new AdCreativeRepository();
    const adCreativeStorageService = new AdCreativeS3StorageService();
    const adCreativeService = new AdCreativeService(
      adCreativeRepository,
      adCreativeStorageService,
    );
    const linkedInSponsoredCreativeRepository =
      new LinkedInSponsoredCreativeRepository();
    const linkedInSponsoredCreativeService =
      new LinkedInSponsoredCreativeService(linkedInSponsoredCreativeRepository);

    const conversationCopyRepository = new ConversationCopyRepository();
    const conversationCopyService = new ConversationCopySerivce(
      conversationCopyRepository,
    );

    const organizationConversionRepository =
      new OrganizationConversionRepository();
    const organizationConversionService = new OrganizationConversionService(
      organizationConversionRepository,
    );

    const adSegmentValuePropCreativeRepository =
      new AdSegmentValuePropCreativeRepository();

    const socialPostCallToActionCopyRepository =
      new SocialPostCallToActionCopyRepository();

    const adProgram = await linkedInAdProgramService.getOne(input.adProgramId);
    if (!adProgram) {
      throw new Error("Ad program not found");
    }

    if (adProgram.adFormat.type == "SPONSORED_CONTENT") {
      const useCase = new DeployLinkedInAdProgramUseCase({
        linkedInAdProgramService: linkedInAdProgramService,
        adSegmentService: adSegmentService,
        linkedInService: linkedInService,
        adAccountService: linkedInAdAccountService,
        segmentService: segmentService,
        organizationId: ctx.organizationId,
        linkedInCampaignGroupService: linkedInCampaignGroupService,
        adAudienceService: adAudienceService,
        linkedInCampaignService: linkedInCampaignService,
        linkedInPostService: linkedInPostService,
        adSegmentValuePropService: adSegmentValuePropService,
        socialPostCopySerice: socialPostAdCopyService,
        adProgramCreativeService: adProgramCreativeService,
        adCreativeService: adCreativeService,
        adCreativeStorageService: adCreativeStorageService,
        linkedInSponsoredCreativeService: linkedInSponsoredCreativeService,
        organizationConversionService: organizationConversionService,
        adSegmentValuePropCreativeRepository:
          adSegmentValuePropCreativeRepository,
        socialPostCallToActionCopyRepository:
          socialPostCallToActionCopyRepository,
      });

      const transactionManager = new TransactionManagerService();
      await transactionManager.startTransaction(async (tx) => {
        await useCase.execute(input, tx);
      });

      const adSegmentsInAdProgram = await adSegmentService.getAllForAdProgram(
        input.adProgramId,
      );
      for (const adSegment of adSegmentsInAdProgram) {
        await advertisingInngestClient.send({
          name: "linkedin/poll-ad-status",
          data: {
            adSegmentId: adSegment.id,
            organizationId: ctx.organizationId,
            runStageAfterApproval: true,
          },
        });
      }
    } else if (adProgram.adFormat.type == "SPONSORED_INMAIL") {
      const conversationCallToActionCopyRepository =
        new ConversationCallToActionCopyRepository();
      const conversationCallToActionCopyService =
        new ConversationCallToActionCopyService(
          conversationCallToActionCopyRepository,
        );

      const conversationSubjectCopyRepository =
        new ConversationSubjectCopyRepository();
      const conversationSubjectCopyService = new ConversationSubjectCopyService(
        conversationSubjectCopyRepository,
      );
      const conversationMessageCopyRepository =
        new ConversationMessageCopyRepository();

      const useCase = new DeploySponsoredConversationAdProgramUseCase({
        linkedInAdProgramService: linkedInAdProgramService,
        adSegmentService: adSegmentService,
        linkedInService: linkedInService,
        adAccountService: linkedInAdAccountService,
        segmentService: segmentService,
        organizationId: ctx.organizationId,
        linkedInCampaignGroupService: linkedInCampaignGroupService,
        adAudienceService: adAudienceService,
        linkedInCampaignService: linkedInCampaignService,
        linkedInPostService: linkedInPostService,
        adSegmentValuePropService: adSegmentValuePropService,
        linkedInSponsoredCreativeService: linkedInSponsoredCreativeService,
        conversationCopyService: conversationCopyService,
        conversationCallToActionCopyService:
          conversationCallToActionCopyService,
        organizationConversionService: organizationConversionService,
        conversationSubjectCopyRepository: conversationSubjectCopyRepository,
        conversationMessageCopyRepository: conversationMessageCopyRepository,
      });

      const transactionManager = new TransactionManagerService();
      await transactionManager.startTransaction(async (tx) => {
        await useCase.execute(input, tx);
      });

      const adSegmentsInAdProgram = await adSegmentService.getAllForAdProgram(
        input.adProgramId,
      );
      for (const adSegment of adSegmentsInAdProgram) {
        await advertisingInngestClient.send({
          name: "linkedin/poll-ad-status",
          data: {
            adSegmentId: adSegment.id,
            organizationId: ctx.organizationId,
            runStageAfterApproval: true,
          },
        });
      }
    }
    const adProgramToUpdate = await linkedInAdProgramService.getOne(
      input.adProgramId,
    );
    if (!adProgramToUpdate) {
      throw new Error("Ad program not found");
    }
    await linkedInAdProgramService.updateOne({
      ...adProgramToUpdate,
      status: "ACTIVE",
    });
  },
);
