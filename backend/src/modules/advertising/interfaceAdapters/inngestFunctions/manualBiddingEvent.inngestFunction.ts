import { z } from "zod";

import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

import { ManualBiddingService } from "../../application/services/manualBidding.service";
import { AdAudienceService } from "../../domain/services/adAudience.service";
import { AdSegmentService } from "../../domain/services/adSegment.service";
import { LinkedInAdAccountService } from "../../domain/services/linkedInAdAccount.service";
import { LinkedInAdProgramService } from "../../domain/services/linkedInAdProgram.service";
import { LinkedInCampaignService } from "../../domain/services/linkedInCampaign.service";
import { ManualBiddingEventService } from "../../domain/services/manualBiddingEvent.service";
import { LinkedInAdAccountRepository } from "../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInAdAudienceRepository } from "../../infrastructure/repositories/linkedInAdAudience.repository";
import { LinkedInAdProgramRepository } from "../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInAdSegmentRepository } from "../../infrastructure/repositories/linkedInAdSegment.repository";
import { LinkedInCampaignRepository } from "../../infrastructure/repositories/linkedInCampaign.repository";
import { ManualBiddingEventRepository } from "../../infrastructure/repositories/mannualBiddingEvent.repository";
import { LinkedInService } from "../../infrastructure/services/linkedIn.service";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";

const eventSchema = z.object({
  adSegmentId: z.string(),
});

export const manualBiddingEvent = advertisingInngestClient.createFunction(
  { id: "manual-bidding-event" },
  { event: "manualBidding.event" },
  async ({ step, event }) => {
    const parsedEventData = eventSchema.parse(event.data);

    const adSegment = await step.run("get-ad-segment", async () => {
      const adSegmentService = new AdSegmentService(
        new LinkedInAdSegmentRepository(),
      );
      const res = await adSegmentService.getOne(parsedEventData.adSegmentId);
      if (!res) {
        throw new Error("Ad segment not found");
      }
      return res;
    });

    const adAudiences = await step.run("get-ad-audiences", async () => {
      const adAudienceService = new AdAudienceService(
        new LinkedInAdAudienceRepository(),
      );

      const res = await adAudienceService.getManyForAdSegment(adSegment.id);
      if (!res) {
        throw new Error("Ad audiences not found");
      }
      return res;
    });

    const steps = [];

    for (const adAudience of adAudiences) {
      const currStep = step.run(
        `manual-bidding-for-audience-${adAudience.id}`,
        async () => {
          const campaignService = new LinkedInCampaignService(
            new LinkedInCampaignRepository(),
          );
          const adProgramService = new LinkedInAdProgramService(
            new LinkedInAdProgramRepository(),
          );

          const adAccountService = new LinkedInAdAccountService(
            new LinkedInAdAccountRepository(),
          );

          const adProgram = await adProgramService.getOne(
            adSegment.linkedInAdProgramId,
          );
          if (!adProgram) {
            throw new Error("Ad program not found");
          }

          const linkedInAdAccount = await adAccountService.getOneById(
            adProgram.linkedInAdAccountId,
          );
          if (!linkedInAdAccount) {
            throw new Error("LinkedIn ad account not found");
          }

          const client = await getLinkedInApiClientFromOrganizationId(
            linkedInAdAccount.organizationId,
          );
          if (!client) {
            throw new Error("LinkedIn client not found");
          }

          const res = await campaignService.getOneById(adAudience.id);
          if (!res) {
            return "audience has no campaign (not deployed)";
          }

          const manualBiddingService = new ManualBiddingService({
            linkedinService: new LinkedInService(client),
            linkedInCampaignService: campaignService,
            adProgramService,
            adAccountService: new LinkedInAdAccountService(
              new LinkedInAdAccountRepository(),
            ),
            manualBiddingEventService: new ManualBiddingEventService({
              manualBiddingEventRepository: new ManualBiddingEventRepository(),
            }),
          });

          const result = await manualBiddingService.adjustBidForCampaign(
            res.linkedInAudienceId,
            adProgram,
          );

          return result;
        },
      );

      steps.push(currStep);
    }

    await Promise.all(steps);

    advertisingInngestClient.send({
      name: "manualBidding.wait-for-next-event",
      data: {
        adSegmentId: parsedEventData.adSegmentId,
      },
    });
  },
);
