// crmImportCron.ts
import { Organization } from "../../../core/domain/entites/organization";
import { OrganizationRepository } from "../../../core/infrastructure/repositories/organization.repository";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";

export const crmImportCron = advertisingInngestClient.createFunction(
  { id: "crm-import-cron" },
  [
    { event: "crm/all-customers" },
    ...(process.env.NODE_ENV === "production"
      ? [{ cron: "*/15 * * * *" }]
      : []),
  ],
  async ({ step }) => {
    const batchSize = 50;
    let offset = 0;
    let organizations: Omit<Organization, "createdAt">[] = [];

    do {
      organizations = await step.run("get-organizations", async () => {
        const organizationRepository = new OrganizationRepository();
        return await organizationRepository.getAll({
          offset,
          limit: batchSize,
        });
      });

      await step.run("send-org-events", async () => {
        for (const org of organizations) {
          await advertisingInngestClient.send({
            name: "crm/process-org-crm-import",
            data: {
              organizationId: org.organizationId,
            },
          });
        }
      });

      offset += batchSize;
    } while (organizations.length === batchSize);
  },
);
