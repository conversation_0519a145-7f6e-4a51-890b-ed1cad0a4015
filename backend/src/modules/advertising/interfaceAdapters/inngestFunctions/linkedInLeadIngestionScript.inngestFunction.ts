import crypto from "crypto";
import { Inngest } from "inngest";

import {
  getLinkedInApiClientFromOrganizationId,
  getLinkedInFromUserId,
} from "../../../../../../packages/linkedInApi/src/linkedInClient";
import { createUuid } from "../../../core/utils/uuid";
import { LinkedInAdAccountService } from "../../domain/services/linkedInAdAccount.service";
import { LinkedInLeadFormService } from "../../domain/services/linkedInLeadForm.service";
import { LinkedInLeadFormLeadService } from "../../domain/services/linkedInLeadFormLead.service";
import { LinkedInAdAccountRepository } from "../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInLeadFormRepository } from "../../infrastructure/repositories/linkedInLeadForm.repository";
import { LinkedInLeadFormLeadRepository } from "../../infrastructure/repositories/linkedInLeadFormLead.repository";
import { LinkedInService } from "../../infrastructure/services/linkedIn.service";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";
import { linkedInUrnToId } from "../../utils/linkedInUrnUtils";

// Define the shape of the LinkedIn lead form submission payload based on LinkedIn docs
interface LinkedInLeadFormSubmission {
  firstName?: string;
  lastName?: string;
  emailAddress?: string;
  phoneNumber?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  company?: string;
  jobTitle?: string;
  jobFunction?: string;
  industry?: string;
  seniority?: string;
  companySize?: string;
  degree?: string;
  fieldOfStudy?: string;
  school?: string;
  startDate?: string;
  graduationDate?: string;
  [key: string]: any; // Allow for additional fields
}

interface LinkedInWebhookData {
  type: string;
  leadGenFormResponse: string;
  leadGenForm: string;
  owner: { organization?: string; sponsoredAccount?: string };
  associatedEntity: { event: string };
  leadType: "SPONSORED" | "EVENT" | "COMPANY" | "ORGANIZATION_PRODUCT";
  leadAction: string;
  occuredAt: number;
}

type PredefinedField =
  | "FIRST_NAME"
  | "LAST_NAME"
  | "EMAIL"
  | "PHONE_NUMBER"
  | "CITY"
  | "STATE"
  | "COUNTRY"
  | "ZIP_CODE"
  | "JOB_TITLE"
  | "JOB_FUNCTION"
  | "SENIORITY" /* add all your field names */;

interface IPredefinedFieldMapping {
  FIRST_NAME: string;
  LAST_NAME: string;
  EMAIL: string;
  PHONE_NUMBER: string;
  CITY: string;
  STATE: string;
  COUNTRY: string;
  ZIP_CODE: string;
  JOB_TITLE: string;
  JOB_FUNCTION: string;
  SENIORITY: string;
  COMPANY_NAME: string;
  COMPANY_SIZE: string;
  INDUSTRY: string;
  DEGREE: string;
  FIELD_OF_STUDY: string;
  SCHOOL: string;
  START_DATE: string;
  GRADUATION_DATE: string;
  GENDER: string;
  WORK_EMAIL: string;
  LINKEDIN_PROFILE_LINK: string;
  WORK_PHONE_NUMBER: string;
}

const predefinedFieldMapping: IPredefinedFieldMapping = {
  FIRST_NAME: "firstName",
  LAST_NAME: "lastName",
  EMAIL: "emailAddress",
  PHONE_NUMBER: "phoneNumber",
  CITY: "city",
  STATE: "state",
  COUNTRY: "country",
  ZIP_CODE: "zipCode",
  JOB_TITLE: "jobTitle",
  JOB_FUNCTION: "jobFunction",
  SENIORITY: "seniority",
  COMPANY_NAME: "company",
  COMPANY_SIZE: "companySize",
  INDUSTRY: "industry",
  DEGREE: "degree",
  FIELD_OF_STUDY: "fieldOfStudy",
  SCHOOL: "school",
  START_DATE: "startDate",
  GRADUATION_DATE: "graduationDate",
  GENDER: "gender",
  WORK_EMAIL: "workEmail",
  LINKEDIN_PROFILE_LINK: "linkedinProfileLink",
  WORK_PHONE_NUMBER: "workPhoneNumber",
};

interface LinkedInAccountIdPayload {
  linkedInAdAccountId: string;
}

// Create Inngest function to handle LinkedIn lead form webhooks
// Might Require pagination for form retrival - future case
export const linkedInLeadIngestionScript =
  advertisingInngestClient.createFunction(
    {
      id: "lead-ingestion-script",
      name: "linkedin/leads-ingestion-script",
    },
    { event: "linkedin/lead-ingestion.run" },
    async ({ event, step }) => {
      console.log("[LinkedIn Lead Ingestion] Starting lead ingestion process");
      // Extract data from the event
      const { linkedInAdAccountId } = event.data as LinkedInAccountIdPayload;
      console.log(
        `[LinkedIn Lead Ingestion] Processing leads for ad account: ${linkedInAdAccountId}`,
      );

      // Initialize services and repositories
      const linkedInAdAccountService = new LinkedInAdAccountService(
        new LinkedInAdAccountRepository(),
      );
      const leadFormLeadRepository = new LinkedInLeadFormLeadRepository();
      const leadFormLeadService = new LinkedInLeadFormLeadService(
        leadFormLeadRepository,
      );
      const leadFormRepository = new LinkedInLeadFormRepository();
      const leadFormService = new LinkedInLeadFormService(leadFormRepository);

      // Get the LinkedIn ad account
      const linkedInAdAccount =
        await linkedInAdAccountService.getOneById(linkedInAdAccountId);
      if (!linkedInAdAccount) {
        console.error(
          "[LinkedIn Lead Ingestion] LinkedIn ad account not found:",
          linkedInAdAccountId,
        );
        throw new Error("LinkedIn ad account not found");
      }
      console.log(
        `[LinkedIn Lead Ingestion] Found ad account: ${linkedInAdAccount.id}`,
      );

      // Get LinkedIn API client
      const client = await getLinkedInApiClientFromOrganizationId(
        linkedInAdAccount.organizationId,
      );
      if (!client) {
        console.error(
          "[LinkedIn Lead Ingestion] LinkedIn client not found for organization:",
          linkedInAdAccount.organizationId,
        );
        throw new Error("LinkedIn client not found");
      }
      console.log(
        `[LinkedIn Lead Ingestion] LinkedIn API client created for organization: ${linkedInAdAccount.organizationId}`,
      );

      // Initialize LinkedIn service
      const linkedInService = new LinkedInService(client);

      return await step.run("process-lead-forms", async () => {
        try {
          // Get all lead forms from LinkedIn API
          console.log(
            `[LinkedIn Lead Ingestion] Fetching lead forms for ad account: ${linkedInAdAccount.linkedInAdAccountUrn}`,
          );
          const leadForms = await linkedInService.getLeadGenForms({
            adAccountUrn: linkedInAdAccount.linkedInAdAccountUrn,
          });
          console.log(
            `[LinkedIn Lead Ingestion] Found ${leadForms.length} lead forms`,
          );

          if (leadForms.length === 0) {
            console.log(
              "[LinkedIn Lead Ingestion] No lead forms found, ending process",
            );
            return {
              success: true,
              message: "No lead forms found",
              processedForms: 0,
              processedLeads: 0,
            };
          }

          // Prepare the form data
          const formsToCreate = leadForms.map((leadForm) => {
            const versionLeadGenFormUrn = `urn:li:versionedLeadGenForm:(urn:li:leadGenForm:${leadForm.id},${leadForm.versionId})`;
            return {
              id: createUuid(),
              linkedInAdAccountId: linkedInAdAccount.id,
              leadGenFormUrn: versionLeadGenFormUrn,
              name: leadForm.name,
              state: leadForm.state,
              version: leadForm.versionId,
            };
          });

          // Filter out forms that already exist
          const newFormsToCreate =
            await leadFormService.filterOutExistingForms(formsToCreate);

          // Create form ID mapping for all forms (new and existing)
          const formIdRecords: Record<string, string> = {};

          // First, map all new forms we're about to create
          for (const form of newFormsToCreate) {
            formIdRecords[form.leadGenFormUrn] = form.id;
          }

          // Then, get existing forms to add to our mapping
          const existingFormUrns = leadForms.map(
            (form) =>
              `urn:li:versionedLeadGenForm:(urn:li:leadGenForm:${form.id},${form.versionId})`,
          );
          const existingForms =
            await leadFormRepository.getExistingFormsByUrns(existingFormUrns);

          // Add existing forms to our mapping
          for (const form of existingForms) {
            formIdRecords[form.leadGenFormUrn] = form.id;
          }

          // Insert the new forms in batch
          if (newFormsToCreate.length > 0) {
            console.log(
              `[LinkedIn Lead Ingestion] Creating ${newFormsToCreate.length} new lead forms in batch`,
            );
            await leadFormService.createMany(newFormsToCreate);
          } else {
            console.log(
              "[LinkedIn Lead Ingestion] No new lead forms to create",
            );
          }

          // Process leads
          console.log(
            "[LinkedIn Lead Ingestion] Starting to process leads from all forms",
          );
          const allLeadsToCreate = [];
          let totalLeadResponses = 0;

          // Process each form to collect all leads
          for (const leadForm of leadForms) {
            try {
              const versionLeadGenFormUrn = `urn:li:versionedLeadGenForm:(urn:li:leadGenForm:${leadForm.id},${leadForm.versionId})`;

              console.log(
                `[LinkedIn Lead Ingestion] Fetching responses for form: ${leadForm.name}`,
              );
              const leadFormResponses =
                await linkedInService.getLeadFormResponses({
                  adAccountUrn: linkedInAdAccount.linkedInAdAccountUrn,
                  versionedLeadGenFormUrn: versionLeadGenFormUrn,
                });

              totalLeadResponses += leadFormResponses.elements.length;
              console.log(
                `[LinkedIn Lead Ingestion] Found ${leadFormResponses.elements.length} responses for form: ${leadForm.name}`,
              );

              if (leadFormResponses.elements.length === 0) {
                console.log(
                  `[LinkedIn Lead Ingestion] No responses for form: ${leadForm.name}, skipping`,
                );
                continue;
              }

              // Prepare lead data for this form
              const leadsToCreate = leadFormResponses.elements
                .map((leadFormResponse) => {
                  // Process lead data
                  const responses = leadFormResponse.formResponse.answers;
                  const responseQuestionMapping: Record<number, string> = {};

                  // Map response to question ID
                  responses.forEach((response) => {
                    const questionId = response.questionId;
                    const answer = response.answerDetails.textQuestionAnswer;
                    if (answer?.answer) {
                      responseQuestionMapping[questionId] =
                        answer.answer.trim();
                    }
                  });

                  // Build lead data object
                  const leadData: LinkedInLeadFormSubmission = {};
                  leadForm.content?.questions?.forEach((question) => {
                    const predefinedField = question.predefinedField;
                    const questionId = question.questionId;
                    if (!questionId) return;

                    if (predefinedField) {
                      const fieldName =
                        predefinedFieldMapping[
                          predefinedField as PredefinedField
                        ];
                      if (fieldName && responseQuestionMapping[questionId]) {
                        leadData[fieldName] =
                          responseQuestionMapping[questionId];
                      }
                    }
                  });

                  // Get campaign URN and other data
                  const leadCreatedAt = new Date(leadFormResponse.submittedAt);
                  let campaignUrn =
                    leadFormResponse?.leadMetadata?.sponsoredLeadMetadata
                      ?.campaign || "";

                  // Get form ID
                  const linkedInLeadFormId =
                    formIdRecords[versionLeadGenFormUrn];
                  if (!linkedInLeadFormId) {
                    console.error(
                      `[LinkedIn Lead Ingestion] No form ID found for URN: ${versionLeadGenFormUrn}`,
                    );
                    return null;
                  }

                  // Create lead object
                  return {
                    id: createUuid(),
                    linkedInLeadFormId: linkedInLeadFormId,
                    linkedInLeadFormResponseId: leadFormResponse.id,
                    linkedinCampaignUrn: campaignUrn,
                    linkedInAdAccountId: linkedInAdAccount.id,
                    // Personal information
                    firstName: leadData.firstName || null,
                    lastName: leadData.lastName || null,
                    email: leadData.emailAddress || null,
                    phoneNumber: leadData.phoneNumber || null,
                    // Location information
                    city: leadData.city || null,
                    state: leadData.state || null,
                    country: leadData.country || null,
                    zipCode: leadData.postalCode || null,
                    // Professional information
                    companyName: leadData.company || null,
                    jobTitle: leadData.jobTitle || null,
                    jobFunction: leadData.jobFunction || null,
                    industry: leadData.industry || null,
                    seniority: leadData.seniority || null,
                    companySize: leadData.companySize || null,
                    // Education information
                    degree: leadData.degree || null,
                    fieldOfStudy: leadData.fieldOfStudy || null,
                    school: leadData.school || null,
                    startDate: leadData.startDate
                      ? new Date(leadData.startDate)
                      : null,
                    graduationDate: leadData.graduationDate
                      ? new Date(leadData.graduationDate)
                      : null,
                    gender: leadData.gender || null,
                    workEmail: leadData.workEmail || null,
                    linkedinProfileLink: leadData.linkedinProfileLink || null,
                    workPhoneNumber: leadData.workPhoneNumber || null,
                    leadType: leadFormResponse.leadType,
                    testLead: leadFormResponse.testLead,
                    leadCreatedAt: leadCreatedAt,
                  };
                })
                .filter((lead) => lead !== null) as Array<any>; // Filter out any nulls

              // Add leads to our collection
              allLeadsToCreate.push(...leadsToCreate);
            } catch (error) {
              console.error(
                `[LinkedIn Lead Ingestion] Error processing form ${leadForm.name}:`,
                error,
              );
              // Continue with other forms instead of failing completely
            }
          }

          console.log(
            `[LinkedIn Lead Ingestion] Collected ${allLeadsToCreate.length} total leads from ${totalLeadResponses} responses`,
          );

          // Filter out existing leads
          const newLeadsToCreate =
            await leadFormLeadService.filterOutExistingLeads(allLeadsToCreate);

          // Insert the new leads in batch
          let insertedLeadIds: string[] = [];
          if (newLeadsToCreate.length > 0) {
            console.log(
              `[LinkedIn Lead Ingestion] Creating ${newLeadsToCreate.length} new leads in batch`,
            );
            insertedLeadIds =
              await leadFormLeadService.createMany(newLeadsToCreate);
            console.log(
              `[LinkedIn Lead Ingestion] Successfully created ${insertedLeadIds.length} new leads`,
            );
          } else {
            console.log("[LinkedIn Lead Ingestion] No new leads to create");
          }

          // Return summary results
          const result = {
            success: true,
            message: "Lead ingestion completed successfully",
            processedForms: leadForms.length,
            newFormsCreated: newFormsToCreate.length,
            totalLeadResponses: totalLeadResponses,
            newLeadsCreated: insertedLeadIds.length,
            insertedLeadIds: insertedLeadIds,
          };


          return result;
        } catch (error) {
          console.error(
            "[LinkedIn Lead Ingestion] Error in lead ingestion process:",
            error,
          );
          throw error;
        }
      });
    },
  );
