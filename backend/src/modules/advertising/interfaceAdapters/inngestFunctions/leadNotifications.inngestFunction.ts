import { OrganizationRepository } from "../../../core/infrastructure/repositories/organization.repository";
import { SendLeadNotificationUseCase } from "../../application/useCase/notifications/sendLeadNotification.useCase";
import { LinkedInLeadFormLeadService } from "../../domain/services/linkedInLeadFormLead.service";
import { LinkedInLeadFormRepository } from "../../infrastructure/repositories/linkedInLeadForm.repository";
import { LinkedInLeadFormLeadRepository } from "../../infrastructure/repositories/linkedInLeadFormLead.repository";
import { SlackApiService } from "../../infrastructure/services/slack.service";
import { advertisingInngestClient } from "../../utils/advertisingInngestClient";

export const scheduledLeadNotifications =
  advertisingInngestClient.createFunction(
    {
      id: "scheduled-lead-notifications",
      name: "scheduled-lead-notifications",
    },
    { cron: "*/15 * * * *" }, // Run every 15 minutes
    async ({ step }) => {
      return await step.run("check-and-send-lead-notifications", async () => {
        // Initialize repositories and services
        const organizationRepository = new OrganizationRepository();
        const slackApiService = new SlackApiService();
        const leadFormRepository = new LinkedInLeadFormRepository();
        const leadFormLeadRepository = new LinkedInLeadFormLeadRepository();

        const sendLeadNotificationUseCase = new SendLeadNotificationUseCase(
          organizationRepository,
          slackApiService,
          leadFormRepository,
        );

        let totalProcessedOrganizations = 0;
        let totalNotificationsSent = 0;
        let totalLeadsProcessed = 0;

        try {
          // Get all organizations
          const organizations = await organizationRepository.selectAll();

          for (const organization of organizations) {
            // Skip if Slack notifications are not configured or disabled
            if (
              !organization.slackNotificationWebhookUrl ||
              !organization.slackLeadNotificationsEnabled
            ) {
              continue;
            }

            // Skip if lead notifications are disabled
            if (!organization.slackLeadNotificationsEnabled) {
              console.log(
                `Lead notifications disabled for organization ${organization.organizationId}`,
              );
              continue;
            }

            totalProcessedOrganizations++;

            // Always use 15 minutes window
            const timeWindowMinutes = 15;
            const endDate = new Date();
            const startDate = new Date(
              endDate.getTime() - timeWindowMinutes * 60 * 1000,
            );

            // Get leads for this organization in the time window
            const leads =
              await leadFormLeadRepository.getLeadsByOrganizationInTimeWindow(
                organization.organizationId,
                startDate,
                endDate,
              );

            // TODO: Add a call to fetch leads from customer CRM leads table when we have a customer CRM integration implemented

            if (leads.length === 0) {
              console.log(
                `No new leads found for organization ${organization.organizationId} in the last ${timeWindowMinutes} minutes`,
              );
              continue;
            }

            console.log(
              `Found ${leads.length} new leads for organization ${organization.organizationId} in the last ${timeWindowMinutes} minutes`,
            );
            totalLeadsProcessed += leads.length;

            // Send notification for each lead
            for (const lead of leads) {
              try {
                const notificationResult =
                  await sendLeadNotificationUseCase.execute(
                    {
                      leadId: lead.id,
                      organizationId: organization.organizationId,
                    },
                    lead,
                  );

                if (notificationResult.success) {
                  totalNotificationsSent++;
                  console.log(
                    `Successfully sent notification for lead ${lead.id} to organization ${organization.organizationId}`,
                  );
                } else {
                  console.error(
                    `Failed to send notification for lead ${lead.id}: ${notificationResult.error}`,
                  );
                }

                // Add a small delay between notifications to avoid overwhelming Slack
                await new Promise((resolve) => setTimeout(resolve, 200));
              } catch (error) {
                console.error(
                  `Error processing lead notification for lead ${lead.id}:`,
                  error,
                );
              }
            }
          }

          console.log(
            `Scheduled lead notifications completed. Organizations processed: ${totalProcessedOrganizations}, Leads processed: ${totalLeadsProcessed}, Notifications sent: ${totalNotificationsSent}`,
          );

          return {
            success: true,
            processedOrganizations: totalProcessedOrganizations,
            leadsProcessed: totalLeadsProcessed,
            notificationsSent: totalNotificationsSent,
          };
        } catch (error) {
          console.error("Error in scheduled lead notifications:", error);
          return {
            success: false,
            error: String(error),
            processedOrganizations: totalProcessedOrganizations,
            leadsProcessed: totalLeadsProcessed,
            notificationsSent: totalNotificationsSent,
          };
        }
      });
    },
  );
