import { organizationRoute } from "../../../../trpc/trpc";
import { TransactionManagerService } from "../../../core/infrastructure/services/transcationManager.service";
import { createAdSegmentValurPropCreativeRequestDtoSchema } from "../../application/dtos/controllerDtos/adSegmentValuePropCreative/createAdSegmentValuePropCreative.dto";
import { deleteAdSegmentValuePropCreativeRequestDtoSchema } from "../../application/dtos/controllerDtos/adSegmentValuePropCreative/deleteAdSegmentValuePropCreative.dto";
import { getAdSegmentValuePropCreativesForAdProgramCreativeRequestDtoSchema } from "../../application/dtos/controllerDtos/adSegmentValuePropCreative/getAdSegmentValuePropCreativesForAdProgramCreative.dto";
import { getAdSegmentValuePropCreativesForAdSegmentRequestDtoSchema } from "../../application/dtos/controllerDtos/adSegmentValuePropCreative/getAdSegmentValuePropCreativesForAdSegment.dto";
import { getAdSegmentValuePropCreativesForAdSegmentValuePropRequestDtoSchema } from "../../application/dtos/controllerDtos/adSegmentValuePropCreative/getAdSegmentValuePropCreativesForAdSegmentValueProp.dto";
import { CreateAdSegmentValuePropCreativeUseCase } from "../../application/useCase/adSegmentValuePropCreative/createAdSegmentValuePropCreative.useCase";
import { DeleteAdSegmentValuePropCreativeUseCase } from "../../application/useCase/adSegmentValuePropCreative/deleteAdSegmentValuePropCreative.useCase";
import { GetAdSegmentValuePropCreativesForAdProgramCreativeUseCase } from "../../application/useCase/adSegmentValuePropCreative/getAdSegmentValuePropCreativesForAdProgramCreative.useCase";
import { GetAdSegmentValuePropCreativesForAdSegmentUseCase } from "../../application/useCase/adSegmentValuePropCreative/getAdSegmentValuePropCreativesForAdSegment.useCase";
import { GetAdSegmentValuePropCreativesForAdSegmentValuePropUseCase } from "../../application/useCase/adSegmentValuePropCreative/getAdSegmentValuePropCreativesForAdSegmentValueProp.useCase";
import { AdSegmentValuePropCreativeRepository } from "../../infrastructure/repositories/adSegmentValuePropCreative.repository";

export const adSegmentValuePropCreativeController = {
  createOne: organizationRoute
    .input(createAdSegmentValurPropCreativeRequestDtoSchema)
    .mutation(async ({ input, ctx }) => {
      const transactionManager = new TransactionManagerService();
      const x = await transactionManager.startTransaction(async (tx) => {
        const adSegmentValuePropCreativeRepository =
          new AdSegmentValuePropCreativeRepository();
        const createAdSegmentValuePropCreativeUseCase =
          new CreateAdSegmentValuePropCreativeUseCase(
            adSegmentValuePropCreativeRepository,
          );
        await createAdSegmentValuePropCreativeUseCase.execute(input, tx);
      });
    }),
  deleteOne: organizationRoute
    .input(deleteAdSegmentValuePropCreativeRequestDtoSchema)
    .mutation(async ({ input, ctx }) => {
      const transactionManager = new TransactionManagerService();
      const x = await transactionManager.startTransaction(async (tx) => {
        const adSegmentValuePropCreativeRepository =
          new AdSegmentValuePropCreativeRepository();
        const deleteAdSegmentValuePropCreativeUseCase =
          new DeleteAdSegmentValuePropCreativeUseCase(
            adSegmentValuePropCreativeRepository,
          );
        await deleteAdSegmentValuePropCreativeUseCase.execute(input, tx);
      });
    }),
  getForAdProgramCreative: organizationRoute
    .input(getAdSegmentValuePropCreativesForAdProgramCreativeRequestDtoSchema)
    .query(async ({ input, ctx }) => {
      const transactionManager = new TransactionManagerService();
      const x = await transactionManager.startTransaction(async (tx) => {
        const adSegmentValuePropCreativeRepository =
          new AdSegmentValuePropCreativeRepository();
        const getAdSegmentValuePropCreativesForAdProgramCreativeUseCase =
          new GetAdSegmentValuePropCreativesForAdProgramCreativeUseCase(
            adSegmentValuePropCreativeRepository,
          );
        const adSegmentValuePropCreatives =
          await getAdSegmentValuePropCreativesForAdProgramCreativeUseCase.execute(
            input,
            tx,
          );
        return adSegmentValuePropCreatives;
      });
      return x;
    }),
  getForAdSegment: organizationRoute
    .input(getAdSegmentValuePropCreativesForAdSegmentRequestDtoSchema)
    .query(async ({ input, ctx }) => {
      const transactionManager = new TransactionManagerService();
      const result = await transactionManager.startTransaction(async (tx) => {
        const adSegmentValuePropCreativeRepository =
          new AdSegmentValuePropCreativeRepository();
        const getAdSegmentValuePropCreativesForAdSegmentUseCase =
          new GetAdSegmentValuePropCreativesForAdSegmentUseCase(
            adSegmentValuePropCreativeRepository,
          );
        const adSegmentValuePropCreatives =
          await getAdSegmentValuePropCreativesForAdSegmentUseCase.execute(
            input,
            tx,
          );
        return adSegmentValuePropCreatives;
      });
      return result;
    }),
  getForAdSegmentValueProp: organizationRoute
    .input(getAdSegmentValuePropCreativesForAdSegmentValuePropRequestDtoSchema)
    .query(async ({ input, ctx }) => {
      const transactionManager = new TransactionManagerService();
      const result = await transactionManager.startTransaction(async (tx) => {
        const adSegmentValuePropCreativeRepository =
          new AdSegmentValuePropCreativeRepository();
        const getAdSegmentValuePropCreativesForAdSegmentValuePropUseCase =
          new GetAdSegmentValuePropCreativesForAdSegmentValuePropUseCase(
            adSegmentValuePropCreativeRepository,
          );
        const adSegmentValuePropCreatives =
          await getAdSegmentValuePropCreativesForAdSegmentValuePropUseCase.execute(
            input,
            tx,
          );
        return adSegmentValuePropCreatives;
      });
      return result;
    }),
};
