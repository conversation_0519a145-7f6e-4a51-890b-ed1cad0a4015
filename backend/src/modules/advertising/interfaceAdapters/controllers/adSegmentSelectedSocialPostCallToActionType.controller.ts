import { z } from "zod";

import { organizationRoute } from "../../../../trpc/trpc";
import { TransactionManagerService } from "../../../core/infrastructure/services/transcationManager.service";
import { getAdSegmentSelectedSocialPostCallToActionTypesForAdSegmentRequestDto } from "../../application/dtos/controllerDtos/adSegmentSelectedSocialPostCallToActionType/getAdSegmentSelectedSocialPostCallToActionTypesForAdSegment.dto";
import { setAdSegmentSelectedSocialPostCallToActionTypesForAdSegmentRequestDto } from "../../application/dtos/controllerDtos/adSegmentSelectedSocialPostCallToActionType/setAdSegmentSelectedSocialPostCallToActionTypesForAdSegment.dto";
import { setSingleAdSegmentSelectedSocialPostCallToActionTypesRequestDto } from "../../application/dtos/controllerDtos/adSegmentSelectedSocialPostCallToActionType/setSingleAdSegmentSelectedSocialPostCallToActionTypes.dto";
import { GetAdSegmentSelectedSocialPostCallToActionTypesForAdSegmentUseCase } from "../../application/useCase/adSegmentSelectedSocialPostCallToActionType/getAdSegmentSelectedSocialPostCallToActionTypesForAdSegment.useCase";
import { SetAdSegmentSelectedSocialPostCallToActionTypesForAdSegmentUseCase } from "../../application/useCase/adSegmentSelectedSocialPostCallToActionType/setAdSegmentSelectedSocialPostCallToActionTypesForAdSegment.useCase";
import { SetSingleAdSegmentSelectedSocialPostCallToActionTypesUseCase } from "../../application/useCase/adSegmentSelectedSocialPostCallToActionType/setSingleAdSegmentSelectedSocialPostCallToActionTypes.useCase";
import { AdSegmentService } from "../../domain/services/adSegment.service";
import { AdSegmentSelectedSocialPostCallToActionTypeService } from "../../domain/services/adSegmentSelectedSocialPostCallToActionType.service";
import { AdSegmentValuePropService } from "../../domain/services/adSegmentValueProp.service";
import { SocialPostCallToActionCopyService } from "../../domain/services/socialPostCallToActionCopy.service";
import { AdSegmentSelectedSocialPostCallToActionTypeRepository } from "../../infrastructure/repositories/adSegmentSelectedSocialPostCallToActionType.repository";
import { AdSegmentValuePropRepository } from "../../infrastructure/repositories/adSegmentValueProp.repository";
import { LinkedInAdSegmentRepository } from "../../infrastructure/repositories/linkedInAdSegment.repository";
import { SocialPostCallToActionCopyRepository } from "../../infrastructure/repositories/socialPostCallToActionCopy.repository";

export const adSegmentSelectedSocialPostCallToActionTypeController = {
  setAdSegmentSelectedSocialPostCallToActionTypesForAdSegment: organizationRoute
    .input(
      setAdSegmentSelectedSocialPostCallToActionTypesForAdSegmentRequestDto,
    )
    .mutation(async ({ input, ctx }) => {
      const adSegmentSelectedSocialPostCallToActionTypeService =
        new AdSegmentSelectedSocialPostCallToActionTypeService(
          new AdSegmentSelectedSocialPostCallToActionTypeRepository(),
        );

      const transaction = new TransactionManagerService();
      const adSegmentValuePropService = new AdSegmentValuePropService(
        new AdSegmentValuePropRepository(),
      );
      const socialPostCallToActionCopyService =
        new SocialPostCallToActionCopyService(
          new SocialPostCallToActionCopyRepository(),
        );

      await transaction.startTransaction(async (tx) => {
        const useCase =
          new SetAdSegmentSelectedSocialPostCallToActionTypesForAdSegmentUseCase(
            adSegmentSelectedSocialPostCallToActionTypeService,
            adSegmentValuePropService,
            socialPostCallToActionCopyService,
            tx,
          );

        await useCase.execute(input);
      });
    }),

  setSelectedSocialPostCallToActionTypesForSingleAdSegment: organizationRoute
    .input(setSingleAdSegmentSelectedSocialPostCallToActionTypesRequestDto)
    .mutation(async ({ input, ctx }) => {
      const adSegmentSelectedSocialPostCallToActionTypeService =
        new AdSegmentSelectedSocialPostCallToActionTypeService(
          new AdSegmentSelectedSocialPostCallToActionTypeRepository(),
        );

      const transaction = new TransactionManagerService();
      const adSegmentValuePropService = new AdSegmentValuePropService(
        new AdSegmentValuePropRepository(),
      );
      const socialPostCallToActionCopyService =
        new SocialPostCallToActionCopyService(
          new SocialPostCallToActionCopyRepository(),
        );

      await transaction.startTransaction(async (tx) => {
        const useCase =
          new SetSingleAdSegmentSelectedSocialPostCallToActionTypesUseCase(
            adSegmentSelectedSocialPostCallToActionTypeService,
            adSegmentValuePropService,
            socialPostCallToActionCopyService,
            tx,
          );

        await useCase.execute(input);
      });
    }),

  getAdSegmentSelectedSocialPostCallToActionTypesForAdSegment: organizationRoute
    .input(
      getAdSegmentSelectedSocialPostCallToActionTypesForAdSegmentRequestDto,
    )
    .query(async ({ input, ctx }) => {
      const adSegmentSelectedSocialPostCallToActionTypeService =
        new AdSegmentSelectedSocialPostCallToActionTypeService(
          new AdSegmentSelectedSocialPostCallToActionTypeRepository(),
        );

      const transaction = new TransactionManagerService();

      return await transaction.startTransaction(async (tx) => {
        const useCase =
          new GetAdSegmentSelectedSocialPostCallToActionTypesForAdSegmentUseCase(
            adSegmentSelectedSocialPostCallToActionTypeService,
            tx,
          );

        return await useCase.execute(input);
      });
    }),

  getForAdProgram: organizationRoute
    .input(
      z.object({
        adProgramId: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const adSegmentService = new AdSegmentService(
        new LinkedInAdSegmentRepository(),
      );

      const adSegments = await adSegmentService.getAllForAdProgram(
        input.adProgramId,
      );

      const res = [];

      const adSegmentSelectedSocialPostCallToActionTypeService =
        new AdSegmentSelectedSocialPostCallToActionTypeService(
          new AdSegmentSelectedSocialPostCallToActionTypeRepository(),
        );

      for (const adSegment of adSegments) {
        const callToActionTypes =
          await adSegmentSelectedSocialPostCallToActionTypeService.getAllForAdSegment(
            adSegment.id,
          );

        res.push(...callToActionTypes);
      }

      return res;
    }),
};
