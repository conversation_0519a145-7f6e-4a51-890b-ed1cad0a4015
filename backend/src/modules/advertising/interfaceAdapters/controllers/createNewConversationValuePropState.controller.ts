import { z } from "zod";

import { organizationRoute } from "../../../../trpc/trpc";
import { createUuid } from "../../../core/utils/uuid";
import { CreateNewConversationValuePropStateRepository } from "../../infrastructure/repositories/createNewConversationValuePropState.repository";

export const createNewConversationValuePropStateController = {
  upsertOne: organizationRoute
    .input(
      z.object({
        adSegmentId: z.string(),
        valueProp: z.string(),
        toBeUsed: z.boolean(),
        conversationSubjectCopy: z.string(),
        conversationMessageCopy: z.string(),
        conversationCallToActionCopy: z.string(),
        conversationSubjectCopyType: z.string(),
        conversationMessageCopyType: z.string(),
        conversationCallToActionCopyType: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const repository = new CreateNewConversationValuePropStateRepository();
      await repository.upsertOne({
        id: createUuid(),
        adSegmentId: input.adSegmentId,
        valueProp: input.valueProp,
        toBeUsed: input.toBeUsed,
        conversationSubjectCopy: input.conversationSubjectCopy,
        conversationMessageCopy: input.conversationMessageCopy,
        conversationCallToActionCopy: input.conversationCallToActionCopy,
        conversationSubjectCopyType: input.conversationSubjectCopyType,
        conversationMessageCopyType: input.conversationMessageCopyType,
        conversationCallToActionCopyType:
          input.conversationCallToActionCopyType,
      });
    }),
  getForAdSegment: organizationRoute
    .input(
      z.object({
        adSegmentId: z.string(),
      }),
    )
    .query(async ({ input }) => {
      const repository = new CreateNewConversationValuePropStateRepository();
      return await repository.getForAdSegment(input.adSegmentId);
    }),
  getOne: organizationRoute
    .input(
      z.object({
        adSegmentId: z.string(),
        valueProp: z.string(),
      }),
    )
    .query(async ({ input }) => {
      const repository = new CreateNewConversationValuePropStateRepository();
      return await repository.getOne(input.adSegmentId, input.valueProp);
    }),
  setToBeUsed: organizationRoute
    .input(
      z.object({
        adSegmentId: z.string(),
        valueProp: z.string(),
        toBeUsed: z.boolean(),
      }),
    )
    .mutation(async ({ input }) => {
      const repository = new CreateNewConversationValuePropStateRepository();
      await repository.setToBeUsed(
        input.adSegmentId,
        input.valueProp,
        input.toBeUsed,
      );
    }),
};
