import { z } from "zod";

import { organizationRoute } from "../../../../trpc/trpc";
import { getConversationSubjectCopyDto } from "../../application/dtos/controllerDtos/conversationSubjectCopy/getConversationSubjectCopy.dto";
import { GetConversationSubjectCopyUseCase } from "../../application/useCase/conversationSubjectCopy/getConversationSubjectCopy.useCase";
import { ConversationSubjectCopyRepository } from "../../infrastructure/repositories/conversationSubjectCopy.repository";

export const conversationSubjectCopyController = {
  getOne: organizationRoute
    .input(getConversationSubjectCopyDto)
    .query(async ({ input }) => {
      const useCase = new GetConversationSubjectCopyUseCase(
        new ConversationSubjectCopyRepository(),
      );
      const conversationSubjectCopy = await useCase.execute(input);
      return conversationSubjectCopy;
    }),
  getOneByValuePorpIdAndType: organizationRoute
    .input(
      z.object({
        valuePropId: z.string(),
        type: z.string(),
        status: z.enum(["ACTIVE", "DRAFT", "ARCHIVED"]),
      }),
    )
    .query(async ({ input }) => {
      const conversationSubjectCopyRepository =
        new ConversationSubjectCopyRepository();
      const conversationSubjectCopy =
        await conversationSubjectCopyRepository.getOne({
          valuePropId: input.valuePropId,
          conversationCopyType: input.type,
          status: input.status,
        });
      return conversationSubjectCopy;
    }),
};
