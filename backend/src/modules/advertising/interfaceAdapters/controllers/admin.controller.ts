import { TRPCError } from "@trpc/server";

import {
  getLinkedInApiClientFromOrganizationId,
  getLinkedInFromUserId,
} from "../../../../../../packages/linkedInApi/src/linkedInClient";
import { adminRoute } from "../../../../trpc/trpc";
import { adminCreateOrganizationConversionRequestDtoSchema } from "../../application/dtos/controllerDtos/admin/adminCreateOrganizationConversion.dto";
import { adminDeleteOrganizationConversionRequestDtoSchema } from "../../application/dtos/controllerDtos/admin/adminDeleteOrganizationConversion.dto";
import { adminGetConversionsForAdAccountRequestDtoSchema } from "../../application/dtos/controllerDtos/admin/adminGetConversionsForAdAccount.dto";
import { adminGetOrganizationConversionsRequestDtoSchema } from "../../application/dtos/controllerDtos/admin/adminGetOrganizationConversions.dto";
import { GetAudienceCountUseCase } from "../../application/useCase/linkedInApi/getAudieneCount.useCase";
import { GetConversionsForAdAccountUseCase } from "../../application/useCase/linkedInApi/getConversionsForAdAccount.useCase";
import { CreateOrganizationConversionUseCase } from "../../application/useCase/organizationConversion/createOrganizationConversion.useCase";
import { DeleteOrganizationConversionUseCase } from "../../application/useCase/organizationConversion/deleteOrganizationConversion.useCase";
import { GetOrganizationConversionUseCase } from "../../application/useCase/organizationConversion/getOrganizationConversion.useCase";
import { AdAudienceService } from "../../domain/services/adAudience.service";
import { OrganizationConversionService } from "../../domain/services/organizationConversion.service";
import { LinkedInAdAccountRepository } from "../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInAdAudienceRepository } from "../../infrastructure/repositories/linkedInAdAudience.repository";
import { OrganizationConversionRepository } from "../../infrastructure/repositories/organizationConversion.repository";
import { LinkedInService } from "../../infrastructure/services/linkedIn.service";

export const advertisingAdminController = {
  getOrganizationConversions: adminRoute
    .input(adminGetOrganizationConversionsRequestDtoSchema)
    .query(async ({ input }) => {
      const useCase = new GetOrganizationConversionUseCase(
        new OrganizationConversionService(
          new OrganizationConversionRepository(),
        ),
        { organizationId: input.organizationId },
      );
      return useCase.execute();
    }),
  createOrganizationConversion: adminRoute
    .input(adminCreateOrganizationConversionRequestDtoSchema)
    .mutation(async ({ input }) => {
      const useCase = new CreateOrganizationConversionUseCase(
        new OrganizationConversionService(
          new OrganizationConversionRepository(),
        ),
        { organizationId: input.organizationId },
      );
      return useCase.execute(input);
    }),
  deleteOrganizationConversion: adminRoute
    .input(adminDeleteOrganizationConversionRequestDtoSchema)
    .mutation(async ({ input }) => {
      const useCase = new DeleteOrganizationConversionUseCase(
        new OrganizationConversionService(
          new OrganizationConversionRepository(),
        ),
        { organizationId: input.organizationId },
      );
      return useCase.execute(input);
    }),
  getConversionsForAdAccount: adminRoute
    .input(adminGetConversionsForAdAccountRequestDtoSchema)
    .query(async ({ input }) => {
      const adAccountRepository = new LinkedInAdAccountRepository();
      const adAccounts = await adAccountRepository.getForOrganization(
        input.organizationId,
      );
      if (adAccounts.length > 1) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message:
            "Multiple ad accounts found for organization. This is not supported",
        });
      }
      const adAccount = adAccounts[0];
      if (!adAccount) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "No ad account found for organization",
        });
      }
      const linkedInClient = await getLinkedInApiClientFromOrganizationId(
        adAccount.organizationId,
      );
      if (!linkedInClient) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Failed to get LinkedIn authentication",
        });
      }
      const linkedInService = new LinkedInService(linkedInClient);
      const getConversionsForAdAccountUseCase =
        new GetConversionsForAdAccountUseCase(linkedInService);
      return await getConversionsForAdAccountUseCase.execute({
        adAccountId: adAccount.linkedInAdAccountUrn,
      });
    }),
};
