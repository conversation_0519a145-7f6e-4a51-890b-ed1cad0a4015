import { organizationRoute } from "../../../../trpc/trpc";
import {
  getMidCampaignNotificationRequestDto,
  GetMidCampaignNotificationRequestDto,
} from "../../application/dtos/controllerDtos/midCampaignNotification/getMidCampaignNotification.dto";
import { GetMidCampaignNotificationUseCase } from "../../application/useCase/midCampaignNotification/getMidCampaignNotification.useCase";
import { MidCampaignNotificationRepository } from "../../infrastructure/midCampaignNotification.repository";

export const adSegmentMidCampaignNotificationController = {
  getOne: organizationRoute
    .input(getMidCampaignNotificationRequestDto)
    .query(async ({ input, ctx }) => {
      const useCase = new GetMidCampaignNotificationUseCase(
        new MidCampaignNotificationRepository(),
      );
      const result = await useCase.execute(input);
      return result;
    }),
};
