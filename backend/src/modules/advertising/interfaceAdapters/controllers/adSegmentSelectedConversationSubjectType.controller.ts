import { z } from "zod";

import { organizationRoute } from "../../../../trpc/trpc";
import { TransactionManagerService } from "../../../core/infrastructure/services/transcationManager.service";
import { createAdSegmentSelectedConversationSubjectTypeRequestDto } from "../../application/dtos/controllerDtos/adSegmentSelectedConversationSubjectType/create.dto";
import { deleteAdSegmentSelectedConversationSubjectTypesForAdSegmentRequestDto } from "../../application/dtos/controllerDtos/adSegmentSelectedConversationSubjectType/deleteAdSegmentSelectedConversationSubjectTypesForAdSegment.dto";
import { getAdSegmentSelectedConversationSubjectTypesForAdSegmentRequestDto } from "../../application/dtos/controllerDtos/adSegmentSelectedConversationSubjectType/getAdSegmentSelectedConversationSubjectTypesForAdSegment.dto";
import { setAdSegmentSelectedConversationSubjectTypesForAdSegmentRequestDto } from "../../application/dtos/controllerDtos/adSegmentSelectedConversationSubjectType/setAdSegmentSelectedConversationSubjectTypesForAdSegment.dto";
import { CreateAdSegmentSelectedConversationSubjectTypeUseCase } from "../../application/useCase/adSegmentSelectedConversationSubjectType/createAdSegmentSelectedConversationSubjectType.useCase";
import { DeleteAdSegmentSelectedConversationSubjectTypesForAdSegmentUseCase } from "../../application/useCase/adSegmentSelectedConversationSubjectType/deleteAdSegmentSelectedConversationSubjectTypesForAdSegment";
import { GetAdSegmentSelectedConversationSubjectTypesForAdSegmentUseCase } from "../../application/useCase/adSegmentSelectedConversationSubjectType/getAdSegmentSelectedConversationSubjectTypesForAdSegment.useCase";
import { SetAdSegmentSelectedConversationSubjectTypesForAdSegmentUseCase } from "../../application/useCase/adSegmentSelectedConversationSubjectType/setAdSegmentSelectedConversationSubjectTypesForAdSegment.useCase";
import { AdSegmentService } from "../../domain/services/adSegment.service";
import { AdSegmentSelectedConversationSubjectTypeService } from "../../domain/services/adSegmentSelectedConversationSubjectType.service";
import { AdSegmentValuePropService } from "../../domain/services/adSegmentValueProp.service";
import { ConversationSubjectCopyService } from "../../domain/services/conversationSubjectCopy.service";
import { LinkedInAdProgramService } from "../../domain/services/linkedInAdProgram.service";
import { AdSegmentSelectedConversationSubjectTypeRepository } from "../../infrastructure/repositories/adSegmentSelectedConversationSubjectType.repository";
import { AdSegmentValuePropRepository } from "../../infrastructure/repositories/adSegmentValueProp.repository";
import { ConversationSubjectCopyRepository } from "../../infrastructure/repositories/conversationSubjectCopy.repository";
import { LinkedInAdProgramRepository } from "../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInAdSegmentRepository } from "../../infrastructure/repositories/linkedInAdSegment.repository";

export const adSegmentSelectedConversationSubjectTypeController = {
  setAdSegmentSelectedConversationSubjectTypesForAdSegment: organizationRoute
    .input(setAdSegmentSelectedConversationSubjectTypesForAdSegmentRequestDto)
    .mutation(async ({ input, ctx }) => {
      const adSegmentSelectedConversationSubjectTypeService =
        new AdSegmentSelectedConversationSubjectTypeService(
          new AdSegmentSelectedConversationSubjectTypeRepository(),
        );

      const transaction = new TransactionManagerService();
      const adSegmentValuePropService = new AdSegmentValuePropService(
        new AdSegmentValuePropRepository(),
      );
      const conversationSubjectCopyService = new ConversationSubjectCopyService(
        new ConversationSubjectCopyRepository(),
      );

      await transaction.startTransaction(async (tx) => {
        const useCase =
          new SetAdSegmentSelectedConversationSubjectTypesForAdSegmentUseCase(
            adSegmentSelectedConversationSubjectTypeService,
            adSegmentValuePropService,
            conversationSubjectCopyService,
            tx,
          );

        await useCase.execute(input);
      });
    }),
  getAdSegmentSelectedConversationSubjectTypesForAdSegment: organizationRoute
    .input(getAdSegmentSelectedConversationSubjectTypesForAdSegmentRequestDto)
    .query(async ({ input, ctx }) => {
      const adSegmentSelectedConversationSubjectTypeService =
        new AdSegmentSelectedConversationSubjectTypeService(
          new AdSegmentSelectedConversationSubjectTypeRepository(),
        );

      const transaction = new TransactionManagerService();

      return await transaction.startTransaction(async (tx) => {
        const useCase =
          new GetAdSegmentSelectedConversationSubjectTypesForAdSegmentUseCase(
            adSegmentSelectedConversationSubjectTypeService,
            tx,
          );

        return await useCase.execute(input);
      });
    }),
  createAdSegmentSelectedConversationSubjectType: organizationRoute
    .input(createAdSegmentSelectedConversationSubjectTypeRequestDto)
    .mutation(async ({ input, ctx }) => {
      const adSegmentSelectedConversationSubjectTypeService =
        new AdSegmentSelectedConversationSubjectTypeService(
          new AdSegmentSelectedConversationSubjectTypeRepository(),
        );

      const transaction = new TransactionManagerService();

      await transaction.startTransaction(async (tx) => {
        const useCase =
          new CreateAdSegmentSelectedConversationSubjectTypeUseCase(
            adSegmentSelectedConversationSubjectTypeService,
          );

        await useCase.execute(input, tx);
      });
    }),
  deleteAdSegmentSelectedConversationSubjectType: organizationRoute
    .input(
      deleteAdSegmentSelectedConversationSubjectTypesForAdSegmentRequestDto,
    )
    .mutation(async ({ input, ctx }) => {
      const adSegmentSelectedConversationSubjectTypeService =
        new AdSegmentSelectedConversationSubjectTypeService(
          new AdSegmentSelectedConversationSubjectTypeRepository(),
        );

      const transaction = new TransactionManagerService();

      await transaction.startTransaction(async (tx) => {
        const useCase =
          new DeleteAdSegmentSelectedConversationSubjectTypesForAdSegmentUseCase(
            adSegmentSelectedConversationSubjectTypeService,
          );

        await useCase.execute(input, tx);
      });
    }),
  getForAdProgram: organizationRoute
    .input(
      z.object({
        adProgramId: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const adSegmentService = new AdSegmentService(
        new LinkedInAdSegmentRepository(),
      );

      const adSegments = await adSegmentService.getAllForAdProgram(
        input.adProgramId,
      );

      const res = [];

      const adSegmentSelectedConversationSubjectTypeService =
        new AdSegmentSelectedConversationSubjectTypeService(
          new AdSegmentSelectedConversationSubjectTypeRepository(),
        );

      for (const adSegment of adSegments) {
        const subjects =
          await adSegmentSelectedConversationSubjectTypeService.getAllForAdSegment(
            adSegment.id,
          );

        res.push(...subjects);
      }

      return res;
    }),
};
