import { z } from "zod";

import { organizationRoute } from "../../../../trpc/trpc";
import { TransactionManagerService } from "../../../core/infrastructure/services/transcationManager.service";
import { getAdSegmentSelectedConversationCallToActionTypesForAdSegmentRequestDto } from "../../application/dtos/controllerDtos/adSegmentSelectedConversationCallToActionType/getAdSegmentSelectedConversationCallToActionTypesForAdSegment.dto";
import { setAdSegmentSelectedConversationCallToActionTypesForAdSegmentRequestDto } from "../../application/dtos/controllerDtos/adSegmentSelectedConversationCallToActionType/setAdSegmentSelectedConversationCallToActionTypesForAdSegment.dto";
import { setSingleAdSegmentSelectedConversationCallToActionTypesRequestDto } from "../../application/dtos/controllerDtos/adSegmentSelectedConversationCallToActionType/setSingleAdSegmentSelectedConversationCallToActionTypes.dto";
import { GetAdSegmentSelectedConversationCallToActionTypesForAdSegmentUseCase } from "../../application/useCase/adSegmentSelectedConversationCallToActionType/getAdSegmentSelectedConversationCallToActionTypesForAdSegment.useCase";
import { SetAdSegmentSelectedConversationCallToActionTypesForAdSegmentUseCase } from "../../application/useCase/adSegmentSelectedConversationCallToActionType/setAdSegmentSelectedConversationCallToActionTypesForAdSegment.useCase";
import { SetSingleAdSegmentSelectedConversationCallToActionTypesUseCase } from "../../application/useCase/adSegmentSelectedConversationCallToActionType/setSingleAdSegmentSelectedConversationCallToActionTypes.useCase";
import { AdSegmentService } from "../../domain/services/adSegment.service";
import { AdSegmentSelectedConversationCallToActionTypeService } from "../../domain/services/adSegmentSelectedConversationCallToActionType.service";
import { AdSegmentValuePropService } from "../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../domain/services/conversationCallToActionCopy.service";
import { AdSegmentSelectedConversationCallToActionTypeRepository } from "../../infrastructure/repositories/adSegmentSelectedConversationCallToActionType.repository";
import { AdSegmentValuePropRepository } from "../../infrastructure/repositories/adSegmentValueProp.repository";
import { ConversationCallToActionCopyRepository } from "../../infrastructure/repositories/conversationCallToActionCopy.repository";
import { LinkedInAdSegmentRepository } from "../../infrastructure/repositories/linkedInAdSegment.repository";

export const adSegmentSelectedConversationCallToActionTypeController = {
  setAdSegmentSelectedConversationCallToActionTypesForAdSegment:
    organizationRoute
      .input(
        setAdSegmentSelectedConversationCallToActionTypesForAdSegmentRequestDto,
      )
      .mutation(async ({ input, ctx }) => {
        const adSegmentSelectedConversationCallToActionTypeService =
          new AdSegmentSelectedConversationCallToActionTypeService(
            new AdSegmentSelectedConversationCallToActionTypeRepository(),
          );

        const transaction = new TransactionManagerService();
        const adSegmentValuePropService = new AdSegmentValuePropService(
          new AdSegmentValuePropRepository(),
        );
        const conversationCallToActionCopyService =
          new ConversationCallToActionCopyService(
            new ConversationCallToActionCopyRepository(),
          );

        await transaction.startTransaction(async (tx) => {
          const useCase =
            new SetAdSegmentSelectedConversationCallToActionTypesForAdSegmentUseCase(
              adSegmentSelectedConversationCallToActionTypeService,
              adSegmentValuePropService,
              conversationCallToActionCopyService,
              tx,
            );

          await useCase.execute(input);
        });
      }),

  setSelectedConversationCallToActionTypesForSingleAdSegment: organizationRoute
    .input(setSingleAdSegmentSelectedConversationCallToActionTypesRequestDto)
    .mutation(async ({ input, ctx }) => {
      const adSegmentSelectedConversationCallToActionTypeService =
        new AdSegmentSelectedConversationCallToActionTypeService(
          new AdSegmentSelectedConversationCallToActionTypeRepository(),
        );

      const transaction = new TransactionManagerService();
      const adSegmentValuePropService = new AdSegmentValuePropService(
        new AdSegmentValuePropRepository(),
      );
      const conversationCallToActionCopyService =
        new ConversationCallToActionCopyService(
          new ConversationCallToActionCopyRepository(),
        );

      await transaction.startTransaction(async (tx) => {
        const useCase =
          new SetSingleAdSegmentSelectedConversationCallToActionTypesUseCase(
            adSegmentSelectedConversationCallToActionTypeService,
            adSegmentValuePropService,
            conversationCallToActionCopyService,
            tx,
          );

        await useCase.execute(input);
      });
    }),

  getAdSegmentSelectedConversationCallToActionTypesForAdSegment:
    organizationRoute
      .input(
        getAdSegmentSelectedConversationCallToActionTypesForAdSegmentRequestDto,
      )
      .query(async ({ input, ctx }) => {
        const adSegmentSelectedConversationCallToActionTypeService =
          new AdSegmentSelectedConversationCallToActionTypeService(
            new AdSegmentSelectedConversationCallToActionTypeRepository(),
          );

        const transaction = new TransactionManagerService();

        return await transaction.startTransaction(async (tx) => {
          const useCase =
            new GetAdSegmentSelectedConversationCallToActionTypesForAdSegmentUseCase(
              adSegmentSelectedConversationCallToActionTypeService,
              tx,
            );

          return await useCase.execute(input);
        });
      }),

  getForAdProgram: organizationRoute
    .input(
      z.object({
        adProgramId: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const adSegmentService = new AdSegmentService(
        new LinkedInAdSegmentRepository(),
      );

      const adSegments = await adSegmentService.getAllForAdProgram(
        input.adProgramId,
      );

      const res = [];

      const adSegmentSelectedConversationCallToActionTypeService =
        new AdSegmentSelectedConversationCallToActionTypeService(
          new AdSegmentSelectedConversationCallToActionTypeRepository(),
        );

      for (const adSegment of adSegments) {
        const callToActionTypes =
          await adSegmentSelectedConversationCallToActionTypeService.getAllForAdSegment(
            adSegment.id,
          );

        res.push(...callToActionTypes);
      }

      return res;
    }),
};
