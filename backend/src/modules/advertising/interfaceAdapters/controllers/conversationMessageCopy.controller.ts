import { z } from "zod";

import { organizationRoute } from "../../../../trpc/trpc";
import { ConversationMessageCopyRepository } from "../../infrastructure/repositories/conversationMessageCopy.repository";

export const conversationMessageCopyController = {
  getOne: organizationRoute
    .input(
      z.object({
        conversationMessageCopyId: z.string(),
        status: z.enum(["ACTIVE", "DRAFT", "ARCHIVED"]),
      }),
    )
    .query(async ({ input }) => {
      const conversationMessageCopyRepository =
        new ConversationMessageCopyRepository();
      const conversationMessageCopy =
        await conversationMessageCopyRepository.getOneById(
          input.conversationMessageCopyId,
          input.status,
        );
      return conversationMessageCopy;
    }),
  getOneByValuePorpIdAndType: organizationRoute
    .input(
      z.object({
        valuePropId: z.string(),
        messageType: z.string(),
        subjectType: z.string(),
        status: z.enum(["ACTIVE", "DRAFT", "ARCHIVED"]),
      }),
    )
    .query(async ({ input }) => {
      const conversationMessageCopyRepository =
        new ConversationMessageCopyRepository();
      const conversationMessageCopy =
        await conversationMessageCopyRepository.getOneByValuePropMessageTypeAndSubjectType(
          input.valuePropId,
          input.messageType,
          input.subjectType,
          input.status,
        );
      return conversationMessageCopy;
    }),
};
