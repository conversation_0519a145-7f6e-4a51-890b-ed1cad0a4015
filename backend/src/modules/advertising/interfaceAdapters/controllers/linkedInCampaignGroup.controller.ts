import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

import { organizationRoute } from "../../../../trpc/trpc";
import { getFilteredCampaignGroupsDtoSchema } from "../../application/dtos/controllerDtos/linkedInCampaignGroup/getFilteredCampaignGroups.dto";
import {
  getCampaignGroupAnalyticsDtoSchema,
  getOneCampaignGroupAnalyticsDtoSchema,
} from "../../application/dtos/controllerDtos/linkedInCampaignGroup/linkedinCampaignGroup.dto";
import { updateBudgetRequestDtoSchema } from "../../application/dtos/controllerDtos/linkedInCampaignGroup/updateBudget.dto";
import { getCampaignGroupBudgetDtoSchema } from "../../application/dtos/controllerDtos/linkedInCampaignGroup/getCampaignGroupBudget.dto";
import { GetCampaignGroupBatchAnalytics } from "../../application/useCase/linkedInCampaignGroup/getCampaignGroupBatchAnalytics.useCase";
import { GetOneCampaignGroupAnalytics } from "../../application/useCase/linkedInCampaignGroup/getCampainGroupAnalytics.useCase";
import { GetFilteredCampaignGroups } from "../../application/useCase/linkedInCampaignGroup/getFilteredCampaignGroups.useCase";
import { updateBudgetUseCase } from "../../application/useCase/linkedInCampaignGroup/updateBudget.useCase";
import { LinkedInAdAccountService } from "../../domain/services/linkedInAdAccount.service";
import { LinkedInAdProgramService } from "../../domain/services/linkedInAdProgram.service";
import { LinkedInCampaignGroupService } from "../../domain/services/linkedInCampaignGroup.service";
import { LinkedInAdAccountRepository } from "../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInAdProgramRepository } from "../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInCampaignGroupRepository } from "../../infrastructure/repositories/linkedInCampaignGroup.repository";
import { LinkedInCampaignRepository } from "../../infrastructure/repositories/linkedInCampaign.repository";
import { LinkedInAdAudienceRepository } from "../../infrastructure/repositories/linkedInAdAudience.repository";
import { LinkedInService } from "../../infrastructure/services/linkedIn.service";
import { AbTestRepository } from "../../infrastructure/repositories/abTest.repository";
import { StageRepository } from "../../infrastructure/repositories/stage.repository";

export const linkedInCampaignGroupController = {
  getAnalyticsForBatchCampaignGroups: organizationRoute
    .input(getCampaignGroupAnalyticsDtoSchema)
    .query(async ({ input, ctx }) => {
      const linkedInClient = await getLinkedInApiClientFromOrganizationId(
        ctx.organizationId,
      );
      if (!linkedInClient) {
        throw new Error(
          "[linkedInCampaignGroupController] Linkedin Client not found",
        );
      }

      const linkedInService = new LinkedInService(linkedInClient);

      const useCase = new GetCampaignGroupBatchAnalytics({
        linkedInAdAccountRepository: new LinkedInAdAccountRepository(),
        linkedinCampaignGroupRepository: new LinkedInCampaignGroupRepository(),
        linkedInService: linkedInService,
      });

      const res = useCase.execute(input);

      return res;
    }),

  getAnalyticsForOneCampaignGroup: organizationRoute
    .input(getOneCampaignGroupAnalyticsDtoSchema)
    .query(async ({ input, ctx }) => {
      const linkedInClient = await getLinkedInApiClientFromOrganizationId(
        ctx.organizationId,
      );
      if (!linkedInClient) {
        throw new Error(
          "[linkedInCampaignGroupController] Linkedin Client not found",
        );
      }
      const linkedInService = new LinkedInService(linkedInClient);
      const useCase = new GetOneCampaignGroupAnalytics({
        linkedinCampaignGroupRepository: new LinkedInCampaignGroupRepository(),
        linkedInService: linkedInService,
      });
      const res = await useCase.execute(input);
      return res;
    }),

  getFilteredCampaigns: organizationRoute
    .input(getFilteredCampaignGroupsDtoSchema)
    .query(async ({ input, ctx }) => {
      const linkedinCampaignGroupRepository =
        new LinkedInCampaignGroupRepository();
      const linkedInCampaignGroupService = new LinkedInCampaignGroupService(
        linkedinCampaignGroupRepository,
      );

      const useCase = new GetFilteredCampaignGroups({
        linkedInCampaignGroupService: linkedInCampaignGroupService,
      });

      const res = await useCase.execute({
        adAccountId: input.adAccountId,
        status: input.statuses,
      });

      return res;
    }),

  updateBudget: organizationRoute
    .input(updateBudgetRequestDtoSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const linkedInCampaignGroupRepository = new LinkedInCampaignGroupRepository();
        const linkedInCampaignRepository = new LinkedInCampaignRepository();
        const linkedInAdAccountRepository = new LinkedInAdAccountRepository();
        const linkedInAdProgramRepository = new LinkedInAdProgramRepository();
        const abTestRepository = new AbTestRepository();
        const linkedInAdAudienceRepository = new LinkedInAdAudienceRepository();
        const stageRepository = new StageRepository();
        
        const linkedInAdAccountService = new LinkedInAdAccountService(linkedInAdAccountRepository);
        const linkedInAdProgramService = new LinkedInAdProgramService(linkedInAdProgramRepository);

        const dependencies = {
          linkedInCampaignGroupRepository,
          linkedInCampaignRepository,
          linkedInAdAccountService,
          linkedInAdProgramService,
          abTestRepository,
          linkedInAdAudienceRepository,
          stageRepository,
          organizationId: ctx.organizationId,
        };

        await updateBudgetUseCase(dependencies, input);
        
        return { success: true, message: "Budget updated successfully" };
      } catch (error) {
        console.error("Error updating budget:", error);
        throw new Error(error instanceof Error ? error.message : "Failed to update budget");
      }
    }),

  getCampaignGroupBudget: organizationRoute
    .input(getCampaignGroupBudgetDtoSchema)
    .query(async ({ input, ctx }) => {
      const linkedInCampaignGroupRepository = new LinkedInCampaignGroupRepository();
      
      const campaignGroup = await linkedInCampaignGroupRepository.getOneByAdSegmentId(input.adSegmentId);
      
      return {
        totalBudget: campaignGroup?.totalBudget || 0,
        adSegmentId: input.adSegmentId,
      };
    }),
};
