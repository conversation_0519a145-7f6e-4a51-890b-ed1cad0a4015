import { z } from "zod";

import { organizationRoute } from "../../../../trpc/trpc";
import { OrganizationRepository } from "../../../core/infrastructure/repositories/organization.repository";

const updateSlackSettingsInput = z.object({
  webhookUrl: z.string().url().optional().nullable(),
  enabled: z.boolean().optional(),
  dayOfWeek: z.number().int().min(1).max(8).optional(), // 1 = Monday, 7 = Sunday, 8 = Everyday
  frequency: z.enum(["hourly", "daily", "weekly", "biweekly", "monthly"]).optional(),
  leadNotificationsEnabled: z.boolean().optional(),
});

const slackSettingsResponse = z.object({
  slackNotificationWebhookUrl: z.string().url().optional().nullable(),
  slackNotificationsEnabled: z.boolean(),
  slackNotificationDayOfWeek: z.number().int(),
  slackNotificationFrequency: z.enum(["hourly", "daily", "weekly", "biweekly", "monthly"]),
  slackLeadNotificationsEnabled: z.boolean(),
  isConfigured: z.boolean(),
});

export const slackSettingsController = {
  updateSlackSettings: organizationRoute
    .input(updateSlackSettingsInput)
    .mutation(async ({ input, ctx }) => {
      const organizationRepository = new OrganizationRepository();
      
      // Get current organization
      const organization = await organizationRepository.getOne(ctx.organizationId);
      if (!organization) {
        throw new Error("Organization not found");
      }

      // Update the organization with new settings
      await organizationRepository.updateSlackSettings(ctx.organizationId, {
        webhookUrl: input.webhookUrl,
        enabled: input.enabled,
        dayOfWeek: input.dayOfWeek,
        frequency: input.frequency,
        leadNotificationsEnabled: input.leadNotificationsEnabled,
      });

      // Get updated organization data
      const updatedOrganization = await organizationRepository.getOne(ctx.organizationId);
      if (!updatedOrganization) {
        throw new Error("Organization not found after update");
      }

      return {
        success: true,
        slackNotificationWebhookUrl: updatedOrganization.slackNotificationWebhookUrl,
        slackNotificationsEnabled: updatedOrganization.slackNotificationsEnabled ?? true,
        slackNotificationDayOfWeek: updatedOrganization.slackNotificationDayOfWeek ?? 1,
        slackNotificationFrequency: updatedOrganization.slackNotificationFrequency ?? "weekly",
        slackLeadNotificationsEnabled: updatedOrganization.slackLeadNotificationsEnabled ?? true,
        isConfigured: !!updatedOrganization.slackNotificationWebhookUrl,
      };
    }),

  // Keep the legacy webhook update method for backward compatibility
  updateSlackWebhook: organizationRoute
    .input(z.object({ webhookUrl: z.string().url().optional().nullable() }))
    .mutation(async ({ input, ctx }) => {
      const organizationRepository = new OrganizationRepository();
      
      await organizationRepository.updateSlackWebhook(ctx.organizationId, input.webhookUrl || null);

      return {
        success: true,
        slackNotificationWebhookUrl: input.webhookUrl,
        isConfigured: !!input.webhookUrl,
      };
    }),
    
  getSlackSettings: organizationRoute
    .output(slackSettingsResponse)
    .query(async ({ ctx }) => {
      const organizationRepository = new OrganizationRepository();
      
      const organization = await organizationRepository.getOne(ctx.organizationId);
      if (!organization) {
        throw new Error("Organization not found");
      }

      return {
        slackNotificationWebhookUrl: organization.slackNotificationWebhookUrl,
        slackNotificationsEnabled: organization.slackNotificationsEnabled ?? true,
        slackNotificationDayOfWeek: organization.slackNotificationDayOfWeek ?? 1,
        slackNotificationFrequency: organization.slackNotificationFrequency ?? "weekly",
        slackLeadNotificationsEnabled: organization.slackLeadNotificationsEnabled ?? true,
        isConfigured: !!organization.slackNotificationWebhookUrl,
      };
    }),

  testSlackNotification: organizationRoute
    .mutation(async ({ ctx }) => {
      // For testing purposes - sends a test message
      const organizationRepository = new OrganizationRepository();
      
      const organization = await organizationRepository.getOne(ctx.organizationId);
      if (!organization || !organization.slackNotificationWebhookUrl) {
        throw new Error("No Slack webhook configured");
      }

      // Check if notifications are enabled
      if (!organization.slackNotificationsEnabled) {
        throw new Error("Slack notifications are disabled");
      }

      // Send a test message
      const testMessage = {
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: "🧪 *Test Notification*\n\nThis is a test message from Kalos. Your Slack notifications are working correctly!",
            },
          },
        ],
      };

      try {
        const response = await fetch(organization.slackNotificationWebhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(testMessage),
        });

        if (!response.ok) {
          throw new Error(`Failed to send test message: ${response.status}`);
        }

        return { success: true, message: "Test notification sent successfully!" };
      } catch (error) {
        throw new Error(`Failed to send test notification: ${error}`);
      }
    }),
}; 