import { z } from "zod";

import { organizationRoute } from "../../../../trpc/trpc";
import { TransactionManagerService } from "../../../core/infrastructure/services/transcationManager.service";
import { getAdSegmentSelectedConversationMessageTypesForAdSegmentRequestDto } from "../../application/dtos/controllerDtos/adSegmentSelectedConversationMessageType/getAdSegmentSelectedConversationMessageTypesForAdSegment.dto";
import { setAdSegmentSelectedConversationMessageTypesForAdSegmentRequestDto } from "../../application/dtos/controllerDtos/adSegmentSelectedConversationMessageType/setAdSegmentSelectedConversationMessageTypesForAdSegment.dto";
import { setSingleAdSegmentSelectedConversationMessageTypesRequestDto } from "../../application/dtos/controllerDtos/adSegmentSelectedConversationMessageType/setSingleAdSegmentSelectedConversationMessageTypes.dto";
import { GetAdSegmentSelectedConversationMessageTypesForAdSegmentUseCase } from "../../application/useCase/adSegmentSelectedConversationMessageType/getAdSegmentSelectedConversationMessageTypesForAdSegment.useCase";
import { SetAdSegmentSelectedConversationMessageTypesForAdSegmentUseCase } from "../../application/useCase/adSegmentSelectedConversationMessageType/setAdSegmentSelectedConversationMessageTypesForAdSegment.useCase";
import { SetSingleAdSegmentSelectedConversationMessageTypesUseCase } from "../../application/useCase/adSegmentSelectedConversationMessageType/setSingleAdSegmentSelectedConversationMessageTypes.useCase";
import { AdSegmentService } from "../../domain/services/adSegment.service";
import { AdSegmentSelectedConversationMessageTypeService } from "../../domain/services/adSegmentSelectedConversationMessageType.service";
import { AdSegmentValuePropService } from "../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../domain/services/conversationCallToActionCopy.service";
import { ConversationMessageCopyService } from "../../domain/services/conversationMessageCopy.service";
import { AdSegmentSelectedConversationMessageTypeRepository } from "../../infrastructure/repositories/adSegmentSelectedConversationMessageType.repository";
import { AdSegmentValuePropRepository } from "../../infrastructure/repositories/adSegmentValueProp.repository";
import { ConversationCallToActionCopyRepository } from "../../infrastructure/repositories/conversationCallToActionCopy.repository";
import { ConversationMessageCopyRepository } from "../../infrastructure/repositories/conversationMessageCopy.repository";
import { LinkedInAdSegmentRepository } from "../../infrastructure/repositories/linkedInAdSegment.repository";

export const adSegmentSelectedConversationMessageTypeController = {
  setAdSegmentSelectedConversationMessageTypesForAdSegment: organizationRoute
    .input(setAdSegmentSelectedConversationMessageTypesForAdSegmentRequestDto)
    .mutation(async ({ input, ctx }) => {
      const adSegmentSelectedConversationMessageTypeService =
        new AdSegmentSelectedConversationMessageTypeService(
          new AdSegmentSelectedConversationMessageTypeRepository(),
        );

      const transaction = new TransactionManagerService();
      const adSegmentValuePropService = new AdSegmentValuePropService(
        new AdSegmentValuePropRepository(),
      );
      const conversationMessageCopyService = new ConversationMessageCopyService(
        new ConversationMessageCopyRepository(),
      );
      const conversationCallToActionCopyService =
        new ConversationCallToActionCopyService(
          new ConversationCallToActionCopyRepository(),
        );

      await transaction.startTransaction(async (tx) => {
        const useCase =
          new SetAdSegmentSelectedConversationMessageTypesForAdSegmentUseCase(
            adSegmentSelectedConversationMessageTypeService,
            adSegmentValuePropService,
            conversationMessageCopyService,
            conversationCallToActionCopyService,
            tx,
          );

        await useCase.execute(input);
      });
    }),

  setSelectedConversationMessageTypesForSingleAdSegment: organizationRoute
    .input(setSingleAdSegmentSelectedConversationMessageTypesRequestDto)
    .mutation(async ({ input, ctx }) => {
      const adSegmentSelectedConversationMessageTypeService =
        new AdSegmentSelectedConversationMessageTypeService(
          new AdSegmentSelectedConversationMessageTypeRepository(),
        );

      const transaction = new TransactionManagerService();
      const adSegmentValuePropService = new AdSegmentValuePropService(
        new AdSegmentValuePropRepository(),
      );
      const conversationMessageCopyService = new ConversationMessageCopyService(
        new ConversationMessageCopyRepository(),
      );
      const conversationCallToActionCopyService =
        new ConversationCallToActionCopyService(
          new ConversationCallToActionCopyRepository(),
        );

      await transaction.startTransaction(async (tx) => {
        const useCase =
          new SetSingleAdSegmentSelectedConversationMessageTypesUseCase(
            adSegmentSelectedConversationMessageTypeService,
            adSegmentValuePropService,
            conversationMessageCopyService,
            conversationCallToActionCopyService,
            tx,
          );

        await useCase.execute(input);
      });
    }),

  getAdSegmentSelectedConversationMessageTypesForAdSegment: organizationRoute
    .input(getAdSegmentSelectedConversationMessageTypesForAdSegmentRequestDto)
    .query(async ({ input, ctx }) => {
      const adSegmentSelectedConversationMessageTypeService =
        new AdSegmentSelectedConversationMessageTypeService(
          new AdSegmentSelectedConversationMessageTypeRepository(),
        );

      const transaction = new TransactionManagerService();

      return await transaction.startTransaction(async (tx) => {
        const useCase =
          new GetAdSegmentSelectedConversationMessageTypesForAdSegmentUseCase(
            adSegmentSelectedConversationMessageTypeService,
            tx,
          );

        return await useCase.execute(input);
      });
    }),

  getForAdProgram: organizationRoute
    .input(
      z.object({
        adProgramId: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const adSegmentService = new AdSegmentService(
        new LinkedInAdSegmentRepository(),
      );

      const adSegments = await adSegmentService.getAllForAdProgram(
        input.adProgramId,
      );

      const res = [];

      const adSegmentSelectedConversationMessageTypeService =
        new AdSegmentSelectedConversationMessageTypeService(
          new AdSegmentSelectedConversationMessageTypeRepository(),
        );

      for (const adSegment of adSegments) {
        const messageTypes =
          await adSegmentSelectedConversationMessageTypeService.getAllForAdSegment(
            adSegment.id,
          );

        res.push(...messageTypes);
      }

      return res;
    }),
};
