import { z } from "zod";

import { organizationRoute } from "../../../../trpc/trpc";
import { TransactionManagerService } from "../../../core/infrastructure/services/transcationManager.service";
import { getAdSegmentSelectedSocialPostBodyTypesForAdSegmentRequestDto } from "../../application/dtos/controllerDtos/adSegmentSelectedSocialPostBodyType/getAdSegmentSelectedSocialPostBodyTypesForAdSegment.dto";
import { setAdSegmentSelectedSocialPostBodyTypesForAdSegmentRequestDto } from "../../application/dtos/controllerDtos/adSegmentSelectedSocialPostBodyType/setAdSegmentSelectedSocialPostBodyTypesForAdSegment.dto";
import { setSingleAdSegmentSelectedSocialPostBodyTypesRequestDto } from "../../application/dtos/controllerDtos/adSegmentSelectedSocialPostBodyType/setSingleAdSegmentSelectedSocialPostBodyTypes.dto";
import { GetAdSegmentSelectedSocialPostBodyTypesForAdSegmentUseCase } from "../../application/useCase/adSegmentSelectedSocialPostBodyType/getAdSegmentSelectedSocialPostBodyTypesForAdSegment.useCase";
import { SetAdSegmentSelectedSocialPostBodyTypesForAdSegmentUseCase } from "../../application/useCase/adSegmentSelectedSocialPostBodyType/setAdSegmentSelectedSocialPostBodyTypesForAdSegment.useCase";
import { SetSingleAdSegmentSelectedSocialPostBodyTypesUseCase } from "../../application/useCase/adSegmentSelectedSocialPostBodyType/setSingleAdSegmentSelectedSocialPostBodyTypes.useCase";
import { AdSegmentService } from "../../domain/services/adSegment.service";
import { AdSegmentSelectedSocialPostBodyTypeService } from "../../domain/services/adSegmentSelectedSocialPostBodyType.service";
import { AdSegmentValuePropService } from "../../domain/services/adSegmentValueProp.service";
import { SocialPostAdCopyService } from "../../domain/services/socialPostAdCopy.service";
import { AdSegmentSelectedSocialPostBodyTypeRepository } from "../../infrastructure/repositories/adSegmentSelectedSocialPostBodyType.repository";
import { AdSegmentValuePropRepository } from "../../infrastructure/repositories/adSegmentValueProp.repository";
import { LinkedInAdSegmentRepository } from "../../infrastructure/repositories/linkedInAdSegment.repository";
import { SocialPostCopyRepository } from "../../infrastructure/repositories/socialPostCopy.repository";

export const adSegmentSelectedSocialPostBodyTypeController = {
  setAdSegmentSelectedSocialPostBodyTypesForAdSegment: organizationRoute
    .input(setAdSegmentSelectedSocialPostBodyTypesForAdSegmentRequestDto)
    .mutation(async ({ input, ctx }) => {
      const adSegmentSelectedSocialPostBodyTypeService =
        new AdSegmentSelectedSocialPostBodyTypeService(
          new AdSegmentSelectedSocialPostBodyTypeRepository(),
        );

      const transaction = new TransactionManagerService();
      const adSegmentValuePropService = new AdSegmentValuePropService(
        new AdSegmentValuePropRepository(),
      );
      const socialPostAdCopyService = new SocialPostAdCopyService(
        new SocialPostCopyRepository(),
      );

      await transaction.startTransaction(async (tx) => {
        const useCase =
          new SetAdSegmentSelectedSocialPostBodyTypesForAdSegmentUseCase(
            adSegmentSelectedSocialPostBodyTypeService,
            adSegmentValuePropService,
            socialPostAdCopyService,
            tx,
          );

        await useCase.execute(input);
      });
    }),

  setSelectedSocialPostBodyTypesForSingleAdSegment: organizationRoute
    .input(setSingleAdSegmentSelectedSocialPostBodyTypesRequestDto)
    .mutation(async ({ input, ctx }) => {
      const adSegmentSelectedSocialPostBodyTypeService =
        new AdSegmentSelectedSocialPostBodyTypeService(
          new AdSegmentSelectedSocialPostBodyTypeRepository(),
        );

      const transaction = new TransactionManagerService();
      const adSegmentValuePropService = new AdSegmentValuePropService(
        new AdSegmentValuePropRepository(),
      );
      const socialPostAdCopyService = new SocialPostAdCopyService(
        new SocialPostCopyRepository(),
      );

      await transaction.startTransaction(async (tx) => {
        const useCase =
          new SetSingleAdSegmentSelectedSocialPostBodyTypesUseCase(
            adSegmentSelectedSocialPostBodyTypeService,
            adSegmentValuePropService,
            socialPostAdCopyService,
            tx,
          );

        await useCase.execute(input);
      });
    }),

  getAdSegmentSelectedSocialPostBodyTypesForAdSegment: organizationRoute
    .input(getAdSegmentSelectedSocialPostBodyTypesForAdSegmentRequestDto)
    .query(async ({ input, ctx }) => {
      const adSegmentSelectedSocialPostBodyTypeService =
        new AdSegmentSelectedSocialPostBodyTypeService(
          new AdSegmentSelectedSocialPostBodyTypeRepository(),
        );

      const transaction = new TransactionManagerService();

      return await transaction.startTransaction(async (tx) => {
        const useCase =
          new GetAdSegmentSelectedSocialPostBodyTypesForAdSegmentUseCase(
            adSegmentSelectedSocialPostBodyTypeService,
            tx,
          );

        return await useCase.execute(input);
      });
    }),

  getForAdProgram: organizationRoute
    .input(
      z.object({
        adProgramId: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const adSegmentService = new AdSegmentService(
        new LinkedInAdSegmentRepository(),
      );

      const adSegments = await adSegmentService.getAllForAdProgram(
        input.adProgramId,
      );

      const res = [];

      const adSegmentSelectedSocialPostBodyTypeService =
        new AdSegmentSelectedSocialPostBodyTypeService(
          new AdSegmentSelectedSocialPostBodyTypeRepository(),
        );

      for (const adSegment of adSegments) {
        const bodyTypes =
          await adSegmentSelectedSocialPostBodyTypeService.getAllForAdSegment(
            adSegment.id,
          );

        res.push(...bodyTypes);
      }

      return res;
    }),
};
