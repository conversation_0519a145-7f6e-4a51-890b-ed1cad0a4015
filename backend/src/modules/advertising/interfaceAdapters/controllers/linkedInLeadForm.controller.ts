import { get } from "http";
import { TRPCError } from "@trpc/server";

import { organizationRoute } from "../../../../trpc/trpc";
import { createUuid } from "../../../core/utils/uuid";
import { createLeadFormRequestDto } from "../../application/dtos/controllerDtos/linkedInLeadForm/createLeadForm.dto";
import { getLeadFormByIdRequestDto } from "../../application/dtos/controllerDtos/linkedInLeadForm/getLeadFormById.dto";
import { getLeadFormByUrnRequestDto } from "../../application/dtos/controllerDtos/linkedInLeadForm/getLeadFormByUrn.dto";
import { getLeadFormsByLinkedInAccountIdRequestDto } from "../../application/dtos/controllerDtos/linkedInLeadForm/getLeadFormsByLinkedInAccountId.dto";
import { createLeadRequestDto } from "../../application/dtos/controllerDtos/linkedInLeadFormLead/createLead.dto";
import { getLeadByIdRequestDto } from "../../application/dtos/controllerDtos/linkedInLeadFormLead/getLeadById.dto";
import { getLeadsByFormIdRequestDto } from "../../application/dtos/controllerDtos/linkedInLeadFormLead/getLeadsByFormId.dto";
import { LinkedInLeadFormService } from "../../domain/services/linkedInLeadForm.service";
import { LinkedInLeadFormLeadService } from "../../domain/services/linkedInLeadFormLead.service";
import { LinkedInLeadFormRepository } from "../../infrastructure/repositories/linkedInLeadForm.repository";
import { LinkedInLeadFormLeadRepository } from "../../infrastructure/repositories/linkedInLeadFormLead.repository";

export const linkedInLeadFormController = {
  getById: organizationRoute
    .input(getLeadFormByIdRequestDto)
    .query(async ({ input }) => {
      try {
        const leadFormRepository = new LinkedInLeadFormRepository();
        const leadFormService = new LinkedInLeadFormService(leadFormRepository);

        const leadForm = await leadFormService.getOneById(input.id);

        if (!leadForm) {
          throw new Error("Lead form not found");
        }

        return leadForm;
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "An unexpected error occurred";
        throw new TRPCError({
          code: "NOT_FOUND",
          message: errorMessage,
        });
      }
    }),

  getByUrn: organizationRoute
    .input(getLeadFormByUrnRequestDto)
    .query(async ({ input }) => {
      try {
        const leadFormRepository = new LinkedInLeadFormRepository();
        const leadFormService = new LinkedInLeadFormService(leadFormRepository);

        const leadForm = await leadFormService.getOneByLinkedInUrn(input.urn);

        if (!leadForm) {
          throw new Error("Lead form not found");
        }

        return leadForm;
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "An unexpected error occurred";
        throw new TRPCError({
          code: "NOT_FOUND",
          message: errorMessage,
        });
      }
    }),
  getAllByLinkedInAccountId: organizationRoute
    .input(getLeadFormsByLinkedInAccountIdRequestDto)
    .query(async ({ input }) => {
      try {
        const leadFormRepository = new LinkedInLeadFormRepository();
        const leadFormService = new LinkedInLeadFormService(leadFormRepository);

        const leadForms = await leadFormService.getAllByLinkedInAdAccountId(
          input.linkedInAdAccountId,
        );
      } catch (error) {}
    }),

  create: organizationRoute
    .input(createLeadFormRequestDto)
    .mutation(async ({ input }) => {
      try {
        const leadFormRepository = new LinkedInLeadFormRepository();
        const leadFormService = new LinkedInLeadFormService(leadFormRepository);

        const id = createUuid();

        const leadForm = await leadFormService.createOne({
          id,
          linkedInAdAccountId: input.linkedInAdAccountId,
          leadGenFormUrn: input.leadGenFormUrn,
          name: input.name,
          state: input.state,
          version: input.version ?? 1,
        });

        return {
          id: leadForm.id,
          success: true,
        };
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "An unexpected error occurred";
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: errorMessage,
        });
      }
    }),

  leads: {
    getByFormId: organizationRoute
      .input(getLeadsByFormIdRequestDto)
      .query(async ({ input }) => {
        try {
          const leadFormRepository = new LinkedInLeadFormRepository();
          const leadFormService = new LinkedInLeadFormService(
            leadFormRepository,
          );
          const leadFormLeadRepository = new LinkedInLeadFormLeadRepository();

          // Check if the lead form exists
          const leadForm = await leadFormService.getOneById(input.leadFormId);

          if (!leadForm) {
            throw new Error("Lead form not found");
          }

          // Get all leads for this form
          const leads = await leadFormLeadRepository.getAllByLeadFormId(
            input.leadFormId,
          );

          return leads;
        } catch (error) {
          const errorMessage =
            error instanceof Error
              ? error.message
              : "An unexpected error occurred";
          throw new TRPCError({
            code: "NOT_FOUND",
            message: errorMessage,
          });
        }
      }),

    getById: organizationRoute
      .input(getLeadByIdRequestDto)
      .query(async ({ input }) => {
        try {
          const leadFormLeadRepository = new LinkedInLeadFormLeadRepository();

          const lead = await leadFormLeadRepository.getOneById(input.id);

          if (!lead) {
            throw new Error("Lead not found");
          }

          return lead;
        } catch (error) {
          const errorMessage =
            error instanceof Error
              ? error.message
              : "An unexpected error occurred";
          throw new TRPCError({
            code: "NOT_FOUND",
            message: errorMessage,
          });
        }
      }),

    create: organizationRoute
      .input(createLeadRequestDto)
      .mutation(async ({ input }) => {
        try {
          const leadFormRepository = new LinkedInLeadFormRepository();
          const leadFormService = new LinkedInLeadFormService(
            leadFormRepository,
          );
          const leadFormLeadRepository = new LinkedInLeadFormLeadRepository();

          // Check if the lead form exists
          const leadForm = await leadFormService.getOneById(
            input.linkedInLeadFormId,
          );

          if (!leadForm) {
            throw new Error("Lead form not found");
          }

          // Generate a new ID and create the lead
          const id = createUuid();
          const now = new Date();

          const leadId = await leadFormLeadRepository.createOne({
            id,
            linkedInLeadFormId: input.linkedInLeadFormId,
            linkedInLeadFormResponseId: input.linkedInLeadFormResponseId,
            linkedInAdAccountId: input.linkedInAdAccountId,

            firstName: input.firstName,
            lastName: input.lastName,
            email: input.email,
            phoneNumber: input.phoneNumber,
            city: input.city,
            state: input.state,
            country: input.country,
            zipCode: input.zipCode,
            companyName: input.companyName,
            jobTitle: input.jobTitle,
            jobFunction: input.jobFunction,
            industry: input.industry,
            companySize: input.companySize,
            seniority: input.seniority,
            degree: input.degree,
            fieldOfStudy: input.fieldOfStudy,
            school: input.school,
            startDate: input.startDate,
            graduationDate: input.graduationDate,
            gender: input.gender,
            workEmail: input.workEmail,
            linkedinProfileLink: input.linkedinProfileLink,
            workPhoneNumber: input.workPhoneNumber,
            linkedinCampaignUrn: input.linkedinCampaignUrn,
            leadCreatedAt: now,
            leadType: input.leadType,
            testLead: input.testLead || false,
          });

          return {
            id: leadId,
            success: true,
          };
        } catch (error) {
          const errorMessage =
            error instanceof Error
              ? error.message
              : "An unexpected error occurred";
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: errorMessage,
          });
        }
      }),
  },
};
