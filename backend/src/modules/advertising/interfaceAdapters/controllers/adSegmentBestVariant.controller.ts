import { organizationRoute } from "../../../../trpc/trpc";
import { getOneAdSegmentBestVarientDto } from "../../application/dtos/controllerDtos/adSegmentBestVarient/getOneAdSegmentBestVarient.dto";
import { GetOneAdSegmentBestVarientUseCase } from "../../application/useCase/adSegmentBestVarient/getOneAdSegmentBestVarient.useCase";
import { AdSegmentBestVariantRepository } from "../../infrastructure/repositories/adSegmentBestVariant.repository";

export const adSegmentBestVariantController = {
  getOne: organizationRoute
    .input(getOneAdSegmentBestVarientDto)
    .query(async ({ input }) => {
      const useCase = new GetOneAdSegmentBestVarientUseCase(
        new AdSegmentBestVariantRepository(),
      );
      const adSegmentBestVariant = await useCase.execute(input);
      return adSegmentBestVariant;
    }),
};
