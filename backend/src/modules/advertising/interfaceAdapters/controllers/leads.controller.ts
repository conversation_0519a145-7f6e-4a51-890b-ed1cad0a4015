import { TRPCError } from "@trpc/server";

import { organizationRoute } from "../../../../trpc/trpc";
import { createUuid } from "../../../core/utils/uuid";
import { LeadRowWithEngagement } from "../../../crm/domain/types/types";
import { contactRepository } from "../../../crm/infrastructure/repositories/contact.repository";
import { EngagementRepository } from "../../../crm/infrastructure/repositories/engagement.repository";
import { getAllLeadsDtoSchema } from "../../application/dtos/controllerDtos/leads/getAllLeads.dto";
import { getAllLeadsAndOpportunitiesByAccountIdRequestDto } from "../../application/dtos/controllerDtos/linkedInLeadForm/getAllLeadsAndOpportunitiesByAccountId.dto";
import { createLeadRequestDto } from "../../application/dtos/controllerDtos/linkedInLeadFormLead/createLead.dto";
import { getLeadByIdRequestDto } from "../../application/dtos/controllerDtos/linkedInLeadFormLead/getLeadById.dto";
import { getLeadsByFormIdRequestDto } from "../../application/dtos/controllerDtos/linkedInLeadFormLead/getLeadsByFormId.dto";
import { getAllLeadsForKForms } from "../../application/dtos/controllerDtos/linkedInLeadFormLead/getLeadsForKForms.dto";
import { getLeadsAndPipelinesByAccountIdDtoSchema } from "../../application/dtos/controllerDtos/linkedInLeadFormLead/getLinkedInLeadFormLead.dto";
import { GetAllLeadsUseCase } from "../../application/useCase/leads/getAllLeads.useCase";
import { GetAllLeadsMetricsUseCase } from "../../application/useCase/leads/getAllLeadsMetrics.useCase";
import { LinkedInLeadFormService } from "../../domain/services/linkedInLeadForm.service";
import { LinkedInAdAccountRepository } from "../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInLeadFormRepository } from "../../infrastructure/repositories/linkedInLeadForm.repository";
import { LinkedInLeadFormLeadRepository } from "../../infrastructure/repositories/linkedInLeadFormLead.repository";

export const leadsController = {
  getAllLeads: organizationRoute
    .input(getAllLeadsDtoSchema)
    .query(async ({ input }) => {
      const linkedInAdAccountRepository = new LinkedInAdAccountRepository();

      const linkedInAdAccount =
        await linkedInAdAccountRepository.getForOrganization(
          input.organizationId,
        );

      if (!linkedInAdAccount || linkedInAdAccount.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "LinkedIn Ad Account not found",
        });
      }

      const singleAccountId = linkedInAdAccount[0]?.id;

      if (!singleAccountId) {
        throw new TRPCError({
          code: "NOT_FOUND",

          message: "LinkedIn Ad Account ID not found",
        });
      }
      const leadFormLeadRepository = new LinkedInLeadFormLeadRepository();

      const engagementRepository = new EngagementRepository();

      const getAllLeadsUseCase = new GetAllLeadsUseCase({
        linkedInLeadFormLeadRepository: leadFormLeadRepository,
        contactRepository: contactRepository,
        engagementRepository: engagementRepository,
      });

      const res = (await getAllLeadsUseCase.execute({
        ...input,
        linkedInAccountId: singleAccountId,
      })) as LeadRowWithEngagement[];

      return res;
    }),

  getAllLeadsMetrics: organizationRoute
    .input(getAllLeadsDtoSchema)
    .query(async ({ input }) => {
      const linkedInAdAccountRepository = new LinkedInAdAccountRepository();

      const linkedInAdAccount =
        await linkedInAdAccountRepository.getForOrganization(
          input.organizationId,
        );

      if (!linkedInAdAccount || linkedInAdAccount.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "LinkedIn Ad Account not found",
        });
      }

      const singleAccountId = linkedInAdAccount[0]?.id;

      if (!singleAccountId) {
        throw new TRPCError({
          code: "NOT_FOUND",

          message: "LinkedIn Ad Account ID not found",
        });
      }
      const leadFormLeadRepository = new LinkedInLeadFormLeadRepository();
      const engagementRepository = new EngagementRepository();
      const getAllLeadsMetricsUseCase = new GetAllLeadsMetricsUseCase({
        linkedInLeadFormLeadRepository: leadFormLeadRepository,
        contactRepository: contactRepository,
        engagementRepository: engagementRepository,
      });
      const res = await getAllLeadsMetricsUseCase.execute({
        ...input,
        linkedInAccountId: singleAccountId,
      });

      return res;
    }),
};
