import { SalesforceClientNew } from "../../infrastructure/externalCrms/salesforce.externalCrm.provider";
import { CrmCredentialRepository } from "../../infrastructure/repositories/crmCredential.repository";

// Define the standard UTM keys we are looking for
const STANDARD_UTM_KEYS = [
  "utm_source",
  "utm_medium",
  "utm_campaign",
  "utm_content",
  "utm_term",
];

export const SalesforceService = {
  /**
   * Suggests Salesforce Lead field API names for standard UTM parameters.
   * Fetches Lead object fields from Salesforce and tries to find likely matches.
   * @param {number} organizationId - The ID of the organization.
   * @returns {Promise<Record<string, string | null>>} A map of standard UTM keys to suggested Salesforce API names, or null if no match found.
   * @throws {AppError} If Salesforce credentials are not found or invalid, or if the Salesforce API call fails.
   */
  async suggestLeadUtmFields(
    organizationId: number,
  ): Promise<Record<string, string | null>> {
    const crmCredentialRepository = new CrmCredentialRepository();
    // 1. Fetch Salesforce credentials
    const credential = await crmCredentialRepository.getOneForOrganizationId(
      organizationId,
      "salesforce",
    );

    if (!credential || !credential.refreshToken) {
      throw new Error(
        "Salesforce credentials not found or incomplete for this organization.",
      );
    }

    // Ensure connection object and instanceUrl exist (adjust types/structure as needed)
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment

    if (!credential.instanceUrl) {
      throw new Error(
        "Salesforce connection details (instanceUrl) missing or invalid.",
      );
    }
    const instanceUrl = credential.instanceUrl;

    // 2. Instantiate Salesforce Provider
    let salesforceProvider: SalesforceClientNew;
    try {
      salesforceProvider = new SalesforceClientNew(
        instanceUrl,
        credential.refreshToken,
      );
    } catch (initError) {
      console.error("Failed to initialize SalesforceClientNew:", initError);
      throw new Error("Failed to initialize Salesforce connection.");
    }

    // 3. Get Lead Fields from Salesforce
    let leadFields: string[];
    try {
      leadFields = await salesforceProvider.getLeadFields();
    } catch (error) {
      throw new Error(
        `Failed to fetch Lead fields from Salesforce: ${String(error)}`,
      );
    }

    if (leadFields.length === 0) {
      // This might not be an error, could be permissions or an empty schema
      console.warn(
        `No Lead fields returned from Salesforce for org ${organizationId}. Returning no suggestions.`,
      );
      return Object.fromEntries(STANDARD_UTM_KEYS.map((key) => [key, null]));
    }

    // 4. Suggestion Logic (New Algorithm)
    const suggestions: Record<string, string | null> = Object.fromEntries(
      STANDARD_UTM_KEYS.map((key) => [key, null]),
    );

    // Filter for potential custom UTM fields (__c and contains 'utm')
    const possibleFields = leadFields.filter((field) => {
      const lowerCaseField = field.toLowerCase();
      return lowerCaseField.includes("utm") && field.endsWith("__c");
    });

    // If no potential fields found, return the null map
    if (possibleFields.length === 0) {
      console.warn(
        `No custom fields ending in __c and containing 'utm' found in Salesforce Lead object for org ${organizationId}.`,
      );
      return suggestions;
    }

    // Map standard UTM keys to the first matching possible field
    for (const utmKey of STANDARD_UTM_KEYS) {
      const keyParts = utmKey.split("_");
      if (keyParts.length < 2) continue; // Skip if format is not as expected
      const keyPart = keyParts[1]; // "source", "medium", etc.
      if (!keyPart) continue; // Skip if keyPart is undefined
      const lowerCaseKeyPart = keyPart.toLowerCase();

      for (const possibleField of possibleFields) {
        const lowerCasePossibleField = possibleField.toLowerCase();
        // Check if the possible field contains the key part (case-insensitive)
        if (lowerCasePossibleField.includes(lowerCaseKeyPart)) {
          // Assign if the current utmKey hasn't found a match yet
          if (suggestions[utmKey] === null) {
            suggestions[utmKey] = possibleField;
            // Optional: Remove the field from possibleFields to prevent re-matching?
            // Or break here to take the first match for this utmKey?
            // Let's take the first match for this utmKey.
            break; // Move to the next utmKey
          }
        }
      }
    }

    // 5. Return suggestions
    return suggestions;
  },
};
