// Example for IMappingDefinitionRepository interface
import { Transaction } from "../../../../../../database/db";
import { mappingTypeEnum } from "../../../../../../database/schemas/crm/mappingDefinition.table";
import { MappingDefinition } from "../../../../domain/entities/mappingDefinition";

export interface IMappingDefinitionRepository {
  createMappingDefinition(
    definition: Omit<MappingDefinition, "id" | "createdAt" | "updatedAt">,
    tx?: Transaction,
  ): Promise<MappingDefinition>;
  getMappingDefinitionById(
    id: string,
    tx?: Transaction,
  ): Promise<MappingDefinition | null>;
  getLatestMappingDefinitionByType(
    mappingType: (typeof mappingTypeEnum.enumValues)[number],
    tx?: Transaction,
  ): Promise<MappingDefinition | null>;
  getMappingDefinitionByTypeAndVersion(
    mappingType: (typeof mappingTypeEnum.enumValues)[number],
    version: number,
    tx?: Transaction,
  ): Promise<MappingDefinition | null>;
}
