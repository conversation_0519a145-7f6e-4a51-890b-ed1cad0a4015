// Example for ICrmMappingRepository interface
import { Transaction } from "../../../../../../database/db";
import { crmEnum } from "../../../../../../database/schemas/crm/crmMapping.table";
import { CrmMapping } from "../../../../domain/entities/crmMapping";

export interface ICrmMappingRepository {
  createCrmMapping(
    mapping: Omit<CrmMapping, "id" | "createdAt" | "updatedAt">,
    tx?: Transaction,
  ): Promise<CrmMapping>;
  getCrmMappingByOrgAndType(
    organizationId: number,
    crmType: (typeof crmEnum.enumValues)[number],
    mappingDefinitionId: string,
    tx?: Transaction,
  ): Promise<CrmMapping | null>;
  updateCrmMapping(
    id: string,
    updates: Partial<
      Omit<
        CrmMapping,
        | "id"
        | "organizationId"
        | "crmType"
        | "mappingDefinitionId"
        | "createdAt"
      >
    >,
    tx?: Transaction,
  ): Promise<CrmMapping | null>;
  upsertCrmMapping(
    mapping: Omit<CrmMapping, "id" | "createdAt" | "updatedAt">,
    tx?: Transaction,
  ): Promise<CrmMapping>;
}
