import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { CrmCredential } from "../../../../domain/entities/crmCredential";

export interface ISalesforceConnectionRepository {
  createOne(crmCredential: CrmCredential, tx?: ITransaction): Promise<void>;

  getAllByOrganizationId(
    organizationId: number,
    tx?: ITransaction,
  ): Promise<CrmCredential[]>;
  getOneForOrganizationId(
    organizationId: number,
    crmType: "hubspot" | "salesforce",
    tx?: ITransaction,
  ): Promise<CrmCredential | null>;
  getOneById(id: string, tx?: ITransaction): Promise<CrmCredential | null>;
}
