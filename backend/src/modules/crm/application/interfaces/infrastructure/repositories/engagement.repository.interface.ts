import {
  Engagement,
  NewEngagement,
} from "../../../../../../database/schemas/crm/engagement.table";

/**
 * Interface for the Engagement Repository.
 * Defines the contract for data access operations related to Engagements.
 */
export interface IEngagementRepository {
  /**
   * Upserts a batch of engagements.
   * If an engagement with the same externalId, source, and organizationId exists, it updates it.
   * Otherwise, it inserts a new engagement.
   *
   * @param engagements - An array of NewEngagement objects.
   * @returns A promise that resolves when the operation is complete.
   */
  createOrUpdateIfExistsMany(engagements: NewEngagement[]): Promise<void>;

  /**
   * Gets engagements by contact ID.
   *
   * @param contactId - The internal contact UUID.
   * @returns A promise that resolves with an array of Engagement objects.
   */
  getByContactId(contactId: string): Promise<Engagement[]>;

  /**
   * Gets engagements by organization ID.
   *
   * @param organizationId - The organization ID.
   * @returns A promise that resolves with an array of Engagement objects.
   */
  getByOrganizationId(organizationId: number): Promise<Engagement[]>;
}

// Optional: Define an injection token if using a dependency injection framework
// export const ENGAGEMENT_REPOSITORY = Symbol('IEngagementRepository');
