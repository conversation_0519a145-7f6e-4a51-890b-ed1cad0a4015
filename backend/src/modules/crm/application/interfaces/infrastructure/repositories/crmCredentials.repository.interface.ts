import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { CrmCredential } from "../../../../domain/entities/crmCredential";
import { SalesforceConnection } from "../../../../domain/entities/salesforceConnection";

export interface ICrmCredentialRepository {
  createOne(
    crmCredential: CrmCredential,
    tx?: ITransaction,
  ): Promise<CrmCredential>;
  createOneWithSalesforce(
    crmCredential: CrmCredential,
    salesforceConnection: Omit<SalesforceConnection, "crmCredentialId">,
    tx?: ITransaction,
  ): Promise<void>;
  getAllByOrganizationIdLegacy(
    organizationId: number,
    tx?: ITransaction,
  ): Promise<CrmCredential[]>;
  getAllByOrganizationId(
    organizationId: number,
    tx?: ITransaction,
  ): Promise<
    {
      credential: CrmCredential;
      connection: SalesforceConnection | null;
    }[]
  >;
  getOneForOrganizationId(
    organizationId: number,
    crmType: "hubspot" | "salesforce",
    tx?: ITransaction,
  ): Promise<CrmCredential | null>;
  getOneById(id: string, tx?: ITransaction): Promise<CrmCredential | null>;
}
