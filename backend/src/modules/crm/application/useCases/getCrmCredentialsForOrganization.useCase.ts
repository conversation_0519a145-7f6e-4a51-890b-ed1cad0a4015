import { opportunityService } from "../../domain/services/opportunity.service";
import { SearchForOpportunityStatsRequestDto } from "../dtos/searchForOpportunityStats.dto";
import { ICrmCredentialRepository } from "../interfaces/infrastructure/repositories/crmCredentials.repository.interface";
import { IOpportunityRepository } from "../interfaces/infrastructure/repositories/opportunity.repository.interface";

export class GetCrmCredentialsForOrganizationUseCase {
  constructor(
    private readonly ctx: {
      crmCredentialRepository: ICrmCredentialRepository;
    },
  ) {}

  async execute(organizationId: number, crmType: "hubspot" | "salesforce") {
    const crmCredential =
      await this.ctx.crmCredentialRepository.getOneForOrganizationId(
        organizationId,
        crmType,
      );

    return crmCredential;
  }
}
