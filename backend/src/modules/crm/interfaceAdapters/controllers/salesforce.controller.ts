import { z } from "zod";
import { organizationRoute } from "../../../../trpc/trpc"; // Adjusted import
import { SalesforceService } from "../../application/services/salesforce.service";

export const salesforceControllers = {
  /**
   * Suggests Salesforce Lead field API names for standard UTM parameters.
   * Uses organizationRoute to ensure user belongs to an organization.
   */
  suggestLeadUtmFields: organizationRoute
    // No explicit input needed, organizationId comes from context
    .output(z.record(z.string(), z.string().nullable())) // Output schema: { utm_source: string | null, ... }
    .query(async ({ ctx }) => {
      const { organizationId } = ctx; // Get organizationId directly from context

      if (!organizationId) {
        // This check might be redundant with organizationRoute, but good practice
        throw new Error("Organization ID not found in context.");
      }

      try {
        const suggestions = await SalesforceService.suggestLeadUtmFields(
          organizationId,
        );
        return suggestions;
      } catch (error) {
        console.error(
          `Failed to suggest Salesforce UTM fields for org ${organizationId}:`,
          error,
        );
        // Consider mapping to TRPCError for better client-side handling
        throw new Error(
          `Failed to get UTM field suggestions from Salesforce: ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }),
};
