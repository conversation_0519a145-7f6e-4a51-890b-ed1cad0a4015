import { z } from "zod";

import { organizationRoute } from "../../../../trpc/trpc";
import { getCrmCredentialForOrganizationRequestDto } from "../../application/dtos/getCrmCredentialForOrganization.dto";
import { searchForOpportunityStatsRequestDto } from "../../application/dtos/searchForOpportunityStats.dto";
import { GetCrmCredentialsForOrganizationUseCase } from "../../application/useCases/getCrmCredentialsForOrganization.useCase";
import { searchForOpportunityStatsUseCase } from "../../application/useCases/searchForOpportunityStatsUseCase";
import { CrmCredentialRepository } from "../../infrastructure/repositories/crmCredential.repository";
import { opportunityRepository } from "../../infrastructure/repositories/opportunity.repository";

export const crmCredentialController = {
  getCrmCredentialsForOrganization: organizationRoute
    .input(getCrmCredentialForOrganizationRequestDto)
    .query(async ({ input, ctx }) => {
      const crmCredentialRepository = new CrmCredentialRepository();

      const getCrmCredentialUseCase =
        new GetCrmCredentialsForOrganizationUseCase({
          crmCredentialRepository: crmCredentialRepository,
        });
      const crmCredentials = getCrmCredentialUseCase.execute(
        ctx.organizationId,
        input.crmType,
      );
      return crmCredentials;
    }),

  doesOrganizationHaveHubspotCredential: organizationRoute
    // .input(getCrmCredentialForOrganizationRequestDto)
    .query(async ({ ctx }) => {
      const crmCredentialRepository = new CrmCredentialRepository();

      const getCrmCredentialUseCase =
        new GetCrmCredentialsForOrganizationUseCase({
          crmCredentialRepository: crmCredentialRepository,
        });
      const crmCredentials = await getCrmCredentialUseCase.execute(
        ctx.organizationId,
        "hubspot",
      );
      if (!crmCredentials) {
        return { doesOrganizationHaveHubspotCredential: false };
      } else {
        return { doesOrganizationHaveHubspotCredential: true };
      }
    }),
  doesOrganizationHaveSalesforceCredential: organizationRoute.query(
    async ({ ctx }) => {
      const crmCredentialRepository = new CrmCredentialRepository();

      const getCrmCredentialUseCase =
        new GetCrmCredentialsForOrganizationUseCase({
          crmCredentialRepository: crmCredentialRepository,
        });
      const crmCredentials = await getCrmCredentialUseCase.execute(
        ctx.organizationId,
        "salesforce",
      );
      console.log("CRM CREDS", crmCredentials);
      if (!crmCredentials) {
        return { doesOrganizationHaveSalesforceCredential: false };
      } else {
        return { doesOrganizationHaveSalesforceCredential: true };
      }
    },
  ),
};
