import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { crmEnum } from "../../../../database/schemas/crm/crmMapping.table"; // For crmType enum
import { organizationRoute } from "../../../../trpc/trpc"; // Assuming this is your protected route for organization-specific actions
import { crmMappingService } from "../../domain/services/crmMapping.service";

// Input schema for saving UTM mappings
const saveUtmMappingInputSchema = z.object({
  crmType: z.enum(crmEnum.enumValues), // Ensures crmType is one of 'hubspot' or 'salesforce'
  mappings: z.record(z.string()), // e.g., { utm_source: "Custom_UTM_Source__c" }
});

// Input schema for getting UTM mappings
const getUtmMappingInputSchema = z.object({
  crmType: z.enum(crmEnum.enumValues),
});

// Input schema for saving first meeting mappings
const saveFirstMeetingMappingInputSchema = z.object({
  crmType: z.enum(crmEnum.enumValues),
  mapping: z.object({
    type: z.string(),
    field: z.string(),
    entity: z.string(),
    operator: z.string(),
  }),
});

// Input schema for getting first meeting mappings
const getFirstMeetingMappingInputSchema = z.object({
  crmType: z.enum(crmEnum.enumValues),
});

export const crmMappingController = {
  saveUtmMapping: organizationRoute
    .input(saveUtmMappingInputSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const savedMapping = await crmMappingService.saveCrmUtmMapping(
          ctx.organizationId,
          input.crmType,
          input.mappings,
        );
        return savedMapping;
      } catch (error: any) {
        // Log the error for server-side inspection if needed
        console.error("Error in saveUtmMapping:", error);
        // Propagate a tRPC error to the client
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to save UTM mapping.",
          cause: error,
        });
      }
    }),

  getUtmMapping: organizationRoute
    .input(getUtmMappingInputSchema)
    .query(async ({ ctx, input }) => {
      try {
        const mapping = await crmMappingService.getResolvedCrmUtmMapping(
          ctx.organizationId,
          input.crmType,
        );
        if (mapping === null) {
          // You could return an empty object or a specific structure if no mapping is found
          // For now, returning null to indicate not found explicitly
          return null;
        }
        return mapping;
      } catch (error: any) {
        console.error("Error in getUtmMapping:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to retrieve UTM mapping.",
          cause: error,
        });
      }
    }),

  // Endpoint to get the UTM mapping definition (e.g., for dynamically building forms)
  getUtmMappingDefinition: organizationRoute.query(async () => {
    try {
      const definition = await crmMappingService.getUtmMappingDefinition();
      if (!definition) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "UTM mapping definition not found.",
        });
      }
      return definition;
    } catch (error: any) {
      console.error("Error in getUtmMappingDefinition:", error);
      if (error instanceof TRPCError) throw error;
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: error.message || "Failed to retrieve UTM mapping definition.",
        cause: error,
      });
    }
  }),

  // First meeting mapping endpoints
  saveFirstMeetingMapping: organizationRoute
    .input(saveFirstMeetingMappingInputSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const savedMapping = await crmMappingService.saveFirstMeetingMapping(
          ctx.organizationId,
          input.crmType,
          input.mapping,
        );
        return savedMapping;
      } catch (error: any) {
        console.error("Error in saveFirstMeetingMapping:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to save first meeting mapping.",
          cause: error,
        });
      }
    }),

  getFirstMeetingMapping: organizationRoute
    .input(getFirstMeetingMappingInputSchema)
    .query(async ({ ctx, input }) => {
      try {
        const mapping = await crmMappingService.getFirstMeetingMapping(
          ctx.organizationId,
          input.crmType,
        );
        return mapping;
      } catch (error: any) {
        console.error("Error in getFirstMeetingMapping:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to retrieve first meeting mapping.",
          cause: error,
        });
      }
    }),

  getFirstMeetingMappingDefinition: organizationRoute.query(async () => {
    try {
      const definition =
        await crmMappingService.getFirstMeetingMappingDefinition();
      if (!definition) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "First meeting mapping definition not found.",
        });
      }
      return definition;
    } catch (error: any) {
      console.error("Error in getFirstMeetingMappingDefinition:", error);
      if (error instanceof TRPCError) throw error;
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message:
          error.message ||
          "Failed to retrieve first meeting mapping definition.",
        cause: error,
      });
    }
  }),
};

// Remember to add this controller to your main tRPC router
// e.g., in your main router file:
// import { crmMappingController } from '...path to crmMappingController';
// export const appRouter = t.router({
//   ...,
//   crmMapping: crmMappingController, // or spread its procedures if you structure it differently
//   ...,
// });
