export type LifecycleStage =
  | "subscriber"
  | "lead"
  | "marketingqualifiedlead"
  | "salesqualifiedlead"
  | "opportunity"
  | "customer"
  | "evangelist"
  | "other"
  | null
  | undefined;

export type LeadRow = {
  id: string;
  source: string;
  linkedinCampaignUrn: string | null;
  leadCreatedAt: Date | null;
  firstName: string | null;
  lastName: string | null;
  workEmail: string | null;
  companyName: string | null;
  jobTitle: string | null;
  linkedinProfileLink: string | null;
};

export type LeadRowWithEngagement = LeadRow & {
  engagement?: {
    id: string;
    contactId: string;
    subject: string | null;
    startTime: Date | null;
    endTime: Date | null;
    status: string | null;
    type: string | null;
  } | null;
};
