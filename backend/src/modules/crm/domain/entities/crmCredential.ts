import { z } from "zod";

import { organizationUser } from "../../../../../../packages/database/src/schema/organizationUser";
import { Entity } from "../../../../helpers/entity";

export const CrmCredential = Entity(
  z.object({
    id: z.string().uuid(),
    organizationId: z.number().positive(),
    organizationUserId: z.string(),
    crmType: z.enum(["hubspot", "salesforce"]),
    accessToken: z.string().max(255).nullable().optional(),
    refreshToken: z.string().max(255).nullable().optional(),
    accessTokenExpiresAt: z.coerce.date().nullable().optional(),
    refreshTokenExpiresAt: z.coerce.date().nullable().optional(),
    // createdAt: z.coerce.date(),
    // updatedAt: z.coerce.date(),
  }),
);

export type CrmCredential = ReturnType<typeof CrmCredential>;
