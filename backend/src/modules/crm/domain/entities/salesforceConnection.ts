import { z } from "zod";

import { Entity } from "../../../../helpers/entity";

export const SalesforceConnection = Entity(
  z.object({
    id: z.string().uuid(),
    crmCredentialId: z.string().uuid(),
    salesforceOrganizationId: z.string().max(255),
    instanceUrl: z.string().max(255),
    salesforceUserId: z.string().max(255),
    lastSyncedAt: z.coerce.date().nullable().optional(),
    // createdAt: z.coerce.date(),
    // updatedAt: z.coerce.date(),
  }),
);

export type SalesforceConnection = ReturnType<typeof SalesforceConnection>;
