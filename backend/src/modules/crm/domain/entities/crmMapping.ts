import { z } from "zod";

import { Entity } from "../../../../helpers/entity";

// Define schemas for different mapping types
export const utmMappingSchema = z.record(z.string()); // e.g. { utm_source: "UTM_Source__c" }

export const firstMeetingMappingSchema = z.object({
  type: z.string(),
  field: z.string(),
  entity: z.string(),
  operator: z.string(),
});

// Union type for mappings - validation will be done in service layer based on mapping definition type
const mappingsSchema = z.union([utmMappingSchema, firstMeetingMappingSchema]);

export const CrmMapping = Entity(
  z.object({
    id: z.string().uuid(),
    organizationId: z.number().positive(),
    crmType: z.enum(["salesforce", "hubspot"]),
    mappingDefinitionId: z.string().uuid(),
    mappings: mappingsSchema,
    createdAt: z.date().optional(),
    updatedAt: z.date().optional(),
  }),
);

export type CrmMapping = ReturnType<typeof CrmMapping>;

// Type guards for different mapping types
export const isUtmMapping = (
  mappings: any,
): mappings is z.infer<typeof utmMappingSchema> => {
  return utmMappingSchema.safeParse(mappings).success;
};

export const isFirstMeetingMapping = (
  mappings: any,
): mappings is z.infer<typeof firstMeetingMappingSchema> => {
  return firstMeetingMappingSchema.safeParse(mappings).success;
};
