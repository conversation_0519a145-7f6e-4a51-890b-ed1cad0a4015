import { z } from "zod";

import { Entity } from "../../../../helpers/entity";

// Defines fields as a record of arbitrary field names to required/optional metadata
export const mappingDefinitionFieldsSchema = z.record(
  z.object({
    required: z.boolean(),
    description: z.string().optional(),
    metadata: z
      .object({
        options: z.array(z.string()).optional(),
      })
      .optional(),
  }),
);

export const MappingDefinition = Entity(
  z.object({
    id: z.string().uuid(),
    mappingType: z.enum(["utm", "first_meeting"]), // e.g. 'utm'
    version: z.number().int().positive(),
    fields: mappingDefinitionFieldsSchema,
    createdAt: z.date().optional(),
    updatedAt: z.date().optional(),
  }),
);

export type MappingDefinition = ReturnType<typeof MappingDefinition>;
export type MappingDefinitionFields = z.infer<
  typeof mappingDefinitionFieldsSchema
>;
