import type { ICrmCredentialRepository } from "../../application/interfaces/infrastructure/repositories/crmCredentials.repository.interface";
import { createUuid } from "../../../core/utils/uuid";
import { CrmCredential } from "../entities/crmCredential";
import { SalesforceConnection } from "../entities/salesforceConnection";

export class CrmCredentialService {
  constructor(private crmCredentialRepository: ICrmCredentialRepository) {}

  buildCrmCredential(input: CrmCredential) {
    const credential = CrmCredential(input);
    if (!credential) {
      throw Error("Error creating crm credential in service");
    }
    return credential;
  }

  async createOne(input: CrmCredential) {
    const credential = this.buildCrmCredential(input);
    return this.crmCredentialRepository.createOne(credential);
  }

  async createOneWithSalesforce(
    input: Omit<CrmCredential, "id">,
    salesforceConnection: Omit<SalesforceConnection, "id" | "crmCredentialId">,
  ) {
    const credential = this.buildCrmCredential({ ...input, id: createUuid() });
    await this.crmCredentialRepository.createOneWithSalesforce(credential, {
      ...salesforceConnection,
      id: createUuid(),
    });
  }

  async getAllByOrganizationId(organizationId: number) {
    const results =
      await this.crmCredentialRepository.getAllByOrganizationId(organizationId);

    return results.map((row) => ({
      credential: this.buildCrmCredential(row.credential),
      connection: row.connection,
    }));
  }

  async getOneForOrganizationId(
    organizationId: number,
    crmType: "hubspot" | "salesforce",
  ) {
    const res = await this.crmCredentialRepository.getOneForOrganizationId(
      organizationId,
      crmType,
    );
    if (!res) {
      return null;
    }
    return this.buildCrmCredential(res);
  }

  async getOneById(id: string) {
    const res = await this.crmCredentialRepository.getOneById(id);
    if (!res) {
      return null;
    }
    return this.buildCrmCredential(res);
  }
}
