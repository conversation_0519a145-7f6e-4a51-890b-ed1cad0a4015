import { crmEnum } from "../../../../database/schemas/crm/crmMapping.table";
import { crmMappingRepository } from "../../infrastructure/repositories/crmMapping.repository";
import { mappingDefinitionRepository } from "../../infrastructure/repositories/mappingDefinition.repository";
import {
  CrmMapping,
  firstMeetingMappingSchema,
  isFirstMeetingMapping,
} from "../entities/crmMapping";
import { MappingDefinition } from "../entities/mappingDefinition";

export const crmMappingService = {
  async saveCrmUtmMapping(
    organizationId: number,
    crmType: (typeof crmEnum.enumValues)[number],
    userMappings: Record<string, string>, // e.g., { utm_source: "Custom_UTM_Source__c" }
  ): Promise<CrmMapping> {
    const mappingDefinition =
      await mappingDefinitionRepository.getLatestMappingDefinitionByType("utm");

    if (!mappingDefinition) {
      throw new Error("UTM mapping definition not found.");
    }

    // Validate userMappings against the mappingDefinition
    // Ensure all keys in userMappings are defined in mappingDefinition.fields
    for (const key in userMappings) {
      if (!mappingDefinition.fields[key]) {
        throw new Error(
          `Invalid UTM field: ${key}. Not defined in mapping definition.`,
        );
      }
    }

    // Ensure all required fields from mappingDefinition are present in userMappings
    for (const fieldName in mappingDefinition.fields) {
      if (
        mappingDefinition.fields[fieldName]?.required &&
        !userMappings[fieldName]
      ) {
        throw new Error(`Missing required UTM field: ${fieldName}.`);
      }
    }

    const crmMappingData: Omit<CrmMapping, "id" | "createdAt" | "updatedAt"> = {
      organizationId,
      crmType,
      mappingDefinitionId: mappingDefinition.id,
      mappings: userMappings,
    };

    return crmMappingRepository.upsertCrmMapping(crmMappingData);
  },

  async getResolvedCrmUtmMapping(
    organizationId: number,
    crmType: (typeof crmEnum.enumValues)[number],
  ): Promise<Record<string, string> | null> {
    const mappingDefinition =
      await mappingDefinitionRepository.getLatestMappingDefinitionByType("utm");

    if (!mappingDefinition) {
      // Or return a default mapping based on definition fields if preferred
      return null;
    }

    const crmMapping = await crmMappingRepository.getCrmMappingByOrgAndType(
      organizationId,
      crmType,
      mappingDefinition.id,
    );

    if (!crmMapping) {
      // Optionally, return default mappings based on mappingDefinition.fields keys
      // e.g. return Object.fromEntries(Object.keys(mappingDefinition.fields).map(key => [key, key]));
      return null;
    }

    return crmMapping.mappings;
  },

  // Optional: A method to get the definition itself if frontend needs it for form generation
  async getUtmMappingDefinition(): Promise<MappingDefinition | null> {
    return mappingDefinitionRepository.getLatestMappingDefinitionByType("utm");
  },

  async saveFirstMeetingMapping(
    organizationId: number,
    crmType: (typeof crmEnum.enumValues)[number],
    userMapping: {
      type: string;
      field: string;
      entity: string;
      operator: string;
    },
  ): Promise<CrmMapping> {
    const mappingDefinition =
      await mappingDefinitionRepository.getLatestMappingDefinitionByType(
        "first_meeting",
      );

    if (!mappingDefinition) {
      throw new Error("First meeting mapping definition not found.");
    }

    // First validate the structure using the schema
    const schemaValidation = firstMeetingMappingSchema.safeParse(userMapping);
    if (!schemaValidation.success) {
      throw new Error(
        `Invalid first meeting mapping structure: ${schemaValidation.error.message}`,
      );
    }

    // Validate userMapping against the mappingDefinition
    // Check that all required fields are present
    for (const fieldName in mappingDefinition.fields) {
      const fieldDef = mappingDefinition.fields[fieldName];
      if (
        fieldDef?.required &&
        !userMapping[fieldName as keyof typeof userMapping]
      ) {
        throw new Error(`Missing required first meeting field: ${fieldName}.`);
      }
    }

    // Validate values against options (if defined)
    for (const [fieldName, fieldValue] of Object.entries(userMapping)) {
      const fieldDef = mappingDefinition.fields[fieldName];
      if (fieldDef?.metadata?.options) {
        if (!fieldDef.metadata.options.includes(fieldValue)) {
          throw new Error(
            `Invalid value '${fieldValue}' for field '${fieldName}'. Must be one of: ${fieldDef.metadata.options.join(", ")}`,
          );
        }
      }
    }

    const crmMappingData: Omit<CrmMapping, "id" | "createdAt" | "updatedAt"> = {
      organizationId,
      crmType,
      mappingDefinitionId: mappingDefinition.id,
      mappings: userMapping,
    };

    return crmMappingRepository.upsertCrmMapping(crmMappingData);
  },

  async getFirstMeetingMapping(
    organizationId: number,
    crmType: (typeof crmEnum.enumValues)[number],
  ): Promise<{
    type: string;
    field: string;
    entity: string;
    operator: string;
  } | null> {
    const mappingDefinition =
      await mappingDefinitionRepository.getLatestMappingDefinitionByType(
        "first_meeting",
      );

    if (!mappingDefinition) {
      return null;
    }

    const crmMapping = await crmMappingRepository.getCrmMappingByOrgAndType(
      organizationId,
      crmType,
      mappingDefinition.id,
    );

    if (!crmMapping) {
      return null;
    }

    // Validate that it's a first meeting mapping structure
    if (!isFirstMeetingMapping(crmMapping.mappings)) {
      throw new Error("Invalid first meeting mapping structure in database");
    }

    return crmMapping.mappings;
  },

  async getFirstMeetingMappingDefinition(): Promise<MappingDefinition | null> {
    return mappingDefinitionRepository.getLatestMappingDefinitionByType(
      "first_meeting",
    );
  },
};
