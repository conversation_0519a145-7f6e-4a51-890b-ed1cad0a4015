import { and, eq, inArray, sql } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { crmCredentialTable } from "../../../../database/schemas/crm/crmCredential.table";
import { salesforceConnectionTable } from "../../../../database/schemas/crm/salesforceConnection.table";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { ICrmCredentialRepository } from "../../application/interfaces/infrastructure/repositories/crmCredentials.repository.interface";
import { CrmCredential } from "../../domain/entities/crmCredential";
import { SalesforceConnection } from "../../domain/entities/salesforceConnection";

export class CrmCredentialRepository implements ICrmCredentialRepository {
  async createOne(
    crmCredential: CrmCredential,
    tx?: Transaction,
  ): Promise<CrmCredential> {
    const invoker = tx ?? db;
    await invoker.insert(crmCredentialTable).values(crmCredential);
    return crmCredential;
  }

  async createOneWithSalesforce(
    crmCredential: CrmCredential,
    salesforceConnection: Omit<SalesforceConnection, "crmCredentialId">,
    tx?: Transaction,
  ) {
    const invoker = tx ?? db;

    // Insert CRM credential and capture the generated ID
    const [insertedCrmCredential] = await invoker
      .insert(crmCredentialTable)
      .values(crmCredential)
      .returning({ id: crmCredentialTable.id });

    if (!insertedCrmCredential) {
      return;
    }

    // Insert Salesforce connection using the foreign key
    await invoker.insert(salesforceConnectionTable).values({
      ...salesforceConnection,
      crmCredentialId: insertedCrmCredential.id,
    });
  }

  async getAllByOrganizationIdLegacy(
    organizationId: number,
    tx?: Transaction,
  ): Promise<CrmCredential[]> {
    const invoker = tx ?? db;

    const res = await invoker
      .select()
      .from(crmCredentialTable)
      .where(eq(crmCredentialTable.organizationId, organizationId));

    return res;
  }

  async getAllByOrganizationId(
    organizationId: number,
    tx?: Transaction,
  ): Promise<
    {
      credential: CrmCredential;
      connection: SalesforceConnection | null;
    }[]
  > {
    const invoker = tx ?? db;

    const res = await invoker
      .select({
        credential: crmCredentialTable,
        connection: salesforceConnectionTable,
      })
      .from(crmCredentialTable)
      .leftJoin(
        salesforceConnectionTable,
        eq(crmCredentialTable.id, salesforceConnectionTable.crmCredentialId),
      )
      .where(eq(crmCredentialTable.organizationId, organizationId));

    return res.map((row) => ({
      credential: row.credential,
      connection: row.connection,
    }));
  }

  async getOneForOrganizationId(
    organizationId: number,
    crmType: "hubspot" | "salesforce",
    tx?: Transaction,
  ): Promise<{
    id: string;
    organizationId: number;
    organizationUserId: string;
    crmType: "hubspot" | "salesforce";
    accessToken?: string | null | undefined;
    refreshToken?: string | null | undefined;
    accessTokenExpiresAt?: Date | null | undefined;
    refreshTokenExpiresAt?: Date | null | undefined;
    instanceUrl?: string | null | undefined;
  } | null> {
    const invoker = tx ?? db;

    const res = await invoker
      .select({
        credential: crmCredentialTable,
        connection: salesforceConnectionTable,
      })
      .from(crmCredentialTable)
      .leftJoin(
        salesforceConnectionTable,
        eq(crmCredentialTable.id, salesforceConnectionTable.crmCredentialId),
      )
      .where(
        and(
          eq(crmCredentialTable.organizationId, organizationId),
          eq(crmCredentialTable.crmType, crmType),
        ),
      )
      .limit(1);

    if (!res[0]) {
      return null;
    }
    return { ...res[0].credential, ...res[0].connection };
  }

  async getOneById(
    id: string,
    tx?: Transaction,
  ): Promise<CrmCredential | null> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(crmCredentialTable)
      .where(eq(crmCredentialTable.id, id))
      .limit(1);

    if (!res[0]) {
      return null;
    }
    return res[0];
  }
}
