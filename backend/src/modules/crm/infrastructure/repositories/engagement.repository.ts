import { and, count, eq, gte, inArray, lte, sql } from "drizzle-orm";

import { db, Transaction } from "../../../../database/db";
import {
  Engagement,
  engagementTable,
  NewEngagement,
} from "../../../../database/schemas/crm/engagement.table";
import { IEngagementRepository } from "../../application/interfaces/infrastructure/repositories/engagement.repository.interface";

export class EngagementRepository implements IEngagementRepository {
  /**
   * Upserts a batch of engagements.
   * If an engagement with the same externalId, source, and organizationId exists, it updates it.
   * Otherwise, it inserts a new engagement.
   *
   * @param engagements - An array of NewEngagement objects.
   */
  async createOrUpdateIfExistsMany(
    engagements: NewEngagement[],
  ): Promise<void> {
    if (engagements.length === 0) {
      return; // Nothing to do
    }

    // Note: Ensure the unique constraint "engagement_unique_external_id" is defined
    // on (external_id, source, organization_id) in engagement.table.ts
    const result = await db
      .insert(engagementTable)
      .values(engagements)
      .onConflictDoUpdate({
        target: [
          engagementTable.externalId,
          engagementTable.source,
          engagementTable.organizationId,
          engagementTable.contactId,
        ],
        set: {
          contactId: sql`excluded.contact_id`,
          type: sql`excluded.type`,
          subject: sql`excluded.subject`,
          startTime: sql`excluded.start_time`,
          endTime: sql`excluded.end_time`,
          status: sql`excluded.status`,
          metadata: sql`excluded.metadata`,
          updatedAt: new Date(), // Always update the timestamp
        },
      })
      .returning({ id: engagementTable.id }); // Returning ID might be useful for logging

    console.log(`Upserted ${result.length} engagements.`);
  }

  // --- Other potential methods (if needed later) ---

  /**
   * Gets engagements by contact ID.
   *
   * @param contactId - The internal contact UUID.
   * @returns An array of Engagement objects.
   */
  async getByContactId(contactId: string): Promise<Engagement[]> {
    return await db
      .select()
      .from(engagementTable)
      .where(sql`${engagementTable.contactId} = ${contactId}`)
      .orderBy(sql`${engagementTable.startTime} DESC`); // Example ordering
  }

  /**
   * Gets engagements by organization ID.
   *
   * @param organizationId - The organization ID.
   * @returns An array of Engagement objects.
   */
  async getByOrganizationId(organizationId: number): Promise<Engagement[]> {
    return await db
      .select()
      .from(engagementTable)
      .where(sql`${engagementTable.organizationId} = ${organizationId}`)
      .orderBy(sql`${engagementTable.startTime} DESC`); // Example ordering
  }

  // Add a new method to the EngagementRepository class
  async getTotalMeetingsForOrganization(
    organizationId: number,
    startDate?: Date,
    endDate?: Date,
    tx?: Transaction,
  ): Promise<number> {
    const invoker = tx ?? db;

    const result = await invoker
      .select({ count: count() })
      .from(engagementTable)
      .where(
        and(
          eq(engagementTable.organizationId, organizationId),
          eq(engagementTable.type, "meeting"),
          startDate && endDate
            ? gte(engagementTable.startTime, startDate)
            : undefined,
          endDate && endDate
            ? lte(engagementTable.startTime, endDate)
            : undefined,
        ),
      );

    return result[0]?.count ?? 0;
  }

  /**
   * Gets the first (oldest) engagement for each contact ID
   */
  async getFirstEngagementsByContactIds(
    contactIds: string[],
    organizationId: number,
    tx?: Transaction,
  ): Promise<Engagement[]> {
    const invoker = tx ?? db;

    // This query gets the first (oldest) engagement for each contact
    const firstEngagements = await invoker
      .select({
        contactId: engagementTable.contactId,
        minStartTime: sql`MIN(${engagementTable.startTime})`.as(
          "min_start_time",
        ),
      })
      .from(engagementTable)
      .where(
        and(
          inArray(engagementTable.contactId, contactIds),
          eq(engagementTable.organizationId, organizationId),
        ),
      )
      .groupBy(engagementTable.contactId)
      .as("first_engagements");

    // Join back to get the full engagement details
    const engagements = await invoker
      .select({
        id: engagementTable.id,
        contactId: engagementTable.contactId,
        subject: engagementTable.subject,
        startTime: engagementTable.startTime,
        endTime: engagementTable.endTime,
        status: engagementTable.status,
        type: engagementTable.type,
      })
      .from(engagementTable)
      .innerJoin(
        firstEngagements,
        and(
          eq(engagementTable.contactId, firstEngagements.contactId),
          eq(engagementTable.startTime, firstEngagements.minStartTime),
        ),
      )
      .where(eq(engagementTable.organizationId, organizationId));

    return engagements.map((engagement) => ({
      id: engagement.id,
      createdAt: engagement.startTime || new Date(),
      updatedAt: null,
      organizationId,
      source: "hubspot", // Default source, adjust as needed
      contactId: engagement.contactId,
      externalId: engagement.id,
      type: "meeting",
      status: engagement.status,
      subject: engagement.subject,
      startTime: engagement.startTime,
      endTime: engagement.endTime,
      metadata: null,
    })) as Engagement[];
  }
}
