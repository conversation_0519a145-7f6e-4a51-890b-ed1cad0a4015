import { and, desc, eq, sql } from "drizzle-orm";

import { db, Transaction } from "../../../../database/db";
import {
  mappingDefinitionTable,
  mappingTypeEnum,
} from "../../../../database/schemas/crm/mappingDefinition.table";
import { createUuid } from "../../../core/utils/uuid";
import { IMappingDefinitionRepository } from "../../application/interfaces/infrastructure/repositories/mappingDefinition.repository.interface";
import { MappingDefinition } from "../../domain/entities/mappingDefinition";

export const mappingDefinitionRepository: IMappingDefinitionRepository = {
  async createMappingDefinition(
    definition: Omit<MappingDefinition, "id" | "createdAt" | "updatedAt">,
    tx?: Transaction,
  ): Promise<MappingDefinition> {
    const invoker = tx ?? db;
    const newId = createUuid();
    const newDefinition = {
      ...definition,
      id: newId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    await invoker.insert(mappingDefinitionTable).values(newDefinition);
    return newDefinition;
  },

  async getMappingDefinitionById(
    id: string,
    tx?: Transaction,
  ): Promise<MappingDefinition | null> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(mappingDefinitionTable)
      .where(eq(mappingDefinitionTable.id, id));
    if (result.length === 0) {
      return null;
    }
    // Assuming fields is already in the correct JSON format from the DB
    return result[0] as MappingDefinition;
  },

  async getLatestMappingDefinitionByType(
    mappingType: (typeof mappingTypeEnum.enumValues)[number],
    tx?: Transaction,
  ): Promise<MappingDefinition | null> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(mappingDefinitionTable)
      .where(eq(mappingDefinitionTable.mappingType, mappingType))
      .orderBy(desc(mappingDefinitionTable.version))
      .limit(1);

    if (result.length === 0) {
      return null;
    }
    return result[0] as MappingDefinition;
  },

  async getMappingDefinitionByTypeAndVersion(
    mappingType: (typeof mappingTypeEnum.enumValues)[number],
    version: number,
    tx?: Transaction,
  ): Promise<MappingDefinition | null> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(mappingDefinitionTable)
      .where(
        and(
          eq(mappingDefinitionTable.mappingType, mappingType),
          eq(mappingDefinitionTable.version, version),
        ),
      );
    if (result.length === 0) {
      return null;
    }
    return result[0] as MappingDefinition;
  },
};
