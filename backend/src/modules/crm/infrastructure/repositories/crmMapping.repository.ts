import { and, eq, sql } from "drizzle-orm";

import { db, Transaction } from "../../../../database/db";
import {
  crmEnum,
  crmMappingTable,
} from "../../../../database/schemas/crm/crmMapping.table";
import { createUuid } from "../../../core/utils/uuid";
import { ICrmMappingRepository } from "../../application/interfaces/infrastructure/repositories/crmMapping.repository.interface";
import { CrmMapping } from "../../domain/entities/crmMapping"; // Assuming this entity will be created

export const crmMappingRepository: ICrmMappingRepository = {
  async createCrmMapping(
    mapping: Omit<CrmMapping, "id" | "createdAt" | "updatedAt">,
    tx?: Transaction,
  ): Promise<CrmMapping> {
    const invoker = tx ?? db;
    const newId = createUuid();
    const newMapping = {
      ...mapping,
      id: newId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    await invoker.insert(crmMappingTable).values(newMapping);
    return newMapping;
  },

  async getCrmMappingByOrgAndType(
    organizationId: number,
    crmType: (typeof crmEnum.enumValues)[number],
    mappingDefinitionId: string,
    tx?: Transaction,
  ): Promise<CrmMapping | null> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(crmMappingTable)
      .where(
        and(
          eq(crmMappingTable.organizationId, organizationId),
          eq(crmMappingTable.crmType, crmType),
          eq(crmMappingTable.mappingDefinitionId, mappingDefinitionId),
        ),
      );
    if (result.length === 0) {
      return null;
    }
    // Assuming mappings is already in the correct JSON format from the DB
    return result[0] as CrmMapping;
  },

  async updateCrmMapping(
    id: string,
    updates: Partial<
      Omit<
        CrmMapping,
        | "id"
        | "organizationId"
        | "crmType"
        | "mappingDefinitionId"
        | "createdAt"
      >
    >,
    tx?: Transaction,
  ): Promise<CrmMapping | null> {
    const invoker = tx ?? db;
    const updatedFields = {
      ...updates,
      updatedAt: new Date(),
    };
    await invoker
      .update(crmMappingTable)
      .set(updatedFields)
      .where(eq(crmMappingTable.id, id));

    const updatedMapping = await invoker
      .select()
      .from(crmMappingTable)
      .where(eq(crmMappingTable.id, id));

    if (updatedMapping.length === 0) {
      return null;
    }
    return updatedMapping[0] as CrmMapping;
  },

  async upsertCrmMapping(
    mapping: Omit<CrmMapping, "id" | "createdAt" | "updatedAt">,
    tx?: Transaction,
  ): Promise<CrmMapping> {
    const invoker = tx ?? db;
    const newId = createUuid();
    const newMapping = {
      ...mapping,
      id: newId, // id is required for the entity, but might be ignored by onConflict
    };

    const result = await invoker
      .insert(crmMappingTable)
      .values({
        ...newMapping,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .onConflictDoUpdate({
        target: [
          crmMappingTable.organizationId,
          crmMappingTable.crmType,
          crmMappingTable.mappingDefinitionId,
        ],
        set: {
          mappings: sql`EXCLUDED.mappings`,
          updatedAt: sql`now()`,
        },
      })
      .returning();

    if (result.length > 0) {
      return result[0] as CrmMapping;
    }
    // This part should ideally not be reached if upsert is successful
    // but as a fallback, try to fetch if somehow the returning statement didn't work as expected
    // or if there's a very specific concurrency case (though drizzle handles this generally)
    const existing = await this.getCrmMappingByOrgAndType(
      mapping.organizationId,
      mapping.crmType,
      mapping.mappingDefinitionId,
      tx,
    );
    if (existing) return existing;

    // Should not happen with a proper upsert
    throw new Error("Upsert failed to return the mapping.");
  },
};
