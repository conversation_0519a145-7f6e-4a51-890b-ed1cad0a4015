import jsforce, { Connection, OAuth2, QueryResult } from "jsforce";

import {
  OAUTH2_LOGIN_URL,
  url_paths,
} from "../../../../../../packages/salesforce/src/client/urls";

export class SalesforceClient {
  clientId: string;
  clientSecret: string;
  instanceUrl: string;
  refreshToken: string;
  accessToken: string | undefined = undefined;
  constructor(instaceUrl: string, refreshToken: string) {
    if (
      !process.env["SALESFORCE_CLIENT_ID"] ||
      !process.env["SALESFORCE_CLIENT_SECRET"]
    ) {
      throw new Error("Missing Salesforce client ID or client secret");
    }
    this.clientId = process.env["SALESFORCE_CLIENT_ID"];
    this.clientSecret = process.env["SALESFORCE_CLIENT_SECRET"];
    this.instanceUrl = instaceUrl;
    this.refreshToken = refreshToken;
  }

  async authenticate(): Promise<
    { success: true } | { success: false; errorMessage?: string }
  > {
    const urlencoded = new URLSearchParams();
    urlencoded.append("grant_type", "refresh_token");
    urlencoded.append("client_id", this.clientId);
    urlencoded.append("client_secret", this.clientSecret);
    urlencoded.append("refresh_token", this.refreshToken);

    const res = await fetch(OAUTH2_LOGIN_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: urlencoded,
    });
    if (!res.ok) {
      return { success: false, errorMessage: "Failed to authenticate" };
    }
    const data = (await res.json()) as {
      access_token: string;
    };
    this.accessToken = data.access_token;
    return { success: true };
  }

  async callFetchToSalesforceApi(
    path: string | URL | Request,
    init?: RequestInit | undefined,
    attempt: number = 1,
  ): Promise<
    | {
        success: false;
        error: { message: string; errorCode: string }[];
      }
    | { success: true; data: any }
  > {
    const parsedPath = path.toString().startsWith("/") ? path : `/${path}`;
    const res = await fetch(`${this.instanceUrl}${path}`, {
      ...init,
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        "Content-Type": "application/json",
      },
    });

    if (!res.ok) {
      if (res.status === 401 && attempt < 2) {
        await this.authenticate();
        return this.callFetchToSalesforceApi(parsedPath, init, attempt + 1);
      }
      return {
        success: false,
        error: (await res.json()) as { message: string; errorCode: string }[],
      };
    }

    return { success: true, data: await res.json() };
  }

  async query<SObject extends {}>(
    queryString: string,
    getAllBatches: boolean = false,
  ): Promise<
    | {
        success: false;
        error: {
          message: string;
          errorCode: string;
        }[];
      }
    | {
        success: true;
        totalSize: number;
        done: true;
        records: SObject[];
      }
    | {
        success: true;
        totalSize: number;
        done: false;
        records: SObject[];
        nextUrl: string;
      }
  > {
    const encodedUrl = encodeURI(
      `${url_paths.services.query}?q=${queryString}`,
    );
    const res = await this.callFetchToSalesforceApi(encodedUrl);
    if (!res.success) {
      return res;
    }

    const nextUrl = res.data.done ? undefined : res.data.nextRecordsUrl;

    if (getAllBatches && !res.data.done) {
      let allRecords: any[] = res.data.records;
      let currUrl = nextUrl;
      let currDone = res.data.done;
      do {
        const currRes = await this.getNextBatchQuery(currUrl);
        if (!currRes.success) {
          return currRes;
        }
        allRecords.push(...currRes.records);
        currDone = currRes.done;
        if (!currRes.done) {
          currUrl = currRes.nextUrl;
        }
        if (allRecords.length > res.data.totalSize) {
          throw "Total size mismatch between batches. Infinite Loop Depetected.";
        }
      } while (!currDone);

      return {
        success: true,
        totalSize: res.data.totalSize,
        done: currDone,
        records: allRecords,
      };
    }

    return {
      success: true,
      totalSize: res.data.totalSize,
      done: res.data.done,
      records: res.data.records,
      nextUrl: nextUrl,
    };
  }

  async getNextBatchQuery(getNextResultsBatchUrl: string): Promise<
    | {
        success: false;
        error: {
          message: string;
          errorCode: string;
        }[];
      }
    | {
        success: true;
        totalSize: number;
        done: true;
        records: any[];
      }
    | {
        success: true;
        totalSize: number;
        done: false;
        records: any[];
        nextUrl: string;
      }
  > {
    const res = await this.callFetchToSalesforceApi(getNextResultsBatchUrl);
    if (!res.success) {
      return res;
    }

    const nextUrl = res.data.done ? undefined : res.data.nextRecordsUrl;

    return {
      success: true,
      totalSize: res.data.totalSize,
      done: res.data.done,
      records: res.data.records,
      nextUrl: nextUrl,
    };
  }

  async describeSObject(sObjectName: string) {
    const res = await this.callFetchToSalesforceApi(
      url_paths.services.sobjects.sObject(sObjectName).describe,
    );
    return res;
  }

  async basicMetaDataForSObject(sObjectName: string) {
    const res = await this.callFetchToSalesforceApi(
      url_paths.services.sobjects.sObject(sObjectName).base,
    );
    return res;
  }
}

export class SalesforceClientNew {
  conn: Connection | null;
  clientId: string;
  clientSecret: string;
  instanceUrl: string;
  refreshToken: string;
  accessToken: string | undefined = undefined;
  constructor(instaceUrl: string, refreshToken: string) {
    if (
      !process.env["SALESFORCE_CLIENT_ID"] ||
      !process.env["SALESFORCE_CLIENT_SECRET"]
    ) {
      throw new Error("Missing Salesforce client ID or client secret");
    }
    this.clientId = process.env["SALESFORCE_CLIENT_ID"];
    this.clientSecret = process.env["SALESFORCE_CLIENT_SECRET"];
    this.instanceUrl = instaceUrl;
    this.refreshToken = refreshToken;
    this.conn = null;
  }

  async authenticate(): Promise<
    { success: true } | { success: false; errorMessage?: string }
  > {
    if (this.accessToken) {
      return { success: true };
    }
    const urlencoded = new URLSearchParams();
    urlencoded.append("grant_type", "refresh_token");
    urlencoded.append("client_id", this.clientId);
    urlencoded.append("client_secret", this.clientSecret);
    urlencoded.append("refresh_token", this.refreshToken);

    const res = await fetch(OAUTH2_LOGIN_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: urlencoded,
    });
    if (!res.ok) {
      throw new Error("Failed to Authenticate Salesforce");
    }
    console.log("auth res", res);
    const data = (await res.json()) as {
      access_token: string;
    };
    this.accessToken = data.access_token;

    this.conn = new jsforce.Connection({
      instanceUrl: this.instanceUrl,
      accessToken: data.access_token,
    });
    return { success: true };
  }

  async *queryGenerator<T = any>(soql: string): AsyncGenerator<T[]> {
    await this.authenticate();
    if (!this.conn) return;

    let result = await this.conn.query<T>(soql);
    yield result.records;

    while (!result.done && result.nextRecordsUrl) {
      result = await this.conn.queryMore<T>(result.nextRecordsUrl);
      console.log("records", result.records);
      yield result.records;
    }
  }

  async *getLeads(
    doFullDataPull: boolean,
    createdAfter: Date,
    utmFields: string[] = [],
  ): AsyncGenerator<
    {
      Id: string;
      FirstName?: string;
      LastName?: string;
      Email?: string;
      Title?: string;
      Phone?: string;
      Company?: string;
      CreatedDate: string;
      SystemModstamp: string;
      IsConverted?: boolean;
      ConvertedAccountId?: string;
      ConvertedContactId?: string;
      ConvertedOpportunityId?: string;
      IsArchived?: boolean;
      [key: string]: any; // For custom utm fields
    }[]
  > {
    const baseFields = [
      "Id",
      "FirstName",
      "LastName",
      "Email",
      "Title",
      "Phone",
      "IsConverted",
      "Company",
      "CreatedDate",
      "SystemModstamp",
      "ConvertedAccountId",
      "ConvertedContactId",
      "ConvertedOpportunityId",
    ];

    const allFields = [...baseFields, ...utmFields];
    console.log(allFields);
    let soql = `SELECT ${allFields.join(", ")} FROM Lead`;

    const dateToUse = doFullDataPull
      ? createdAfter
      : new Date(Date.now() - 4 * 60 * 60 * 1000);
    const field = doFullDataPull ? "CreatedDate" : "SystemModstamp";
    const formattedDate = dateToUse.toISOString().replace(/\.000Z$/, "Z");

    soql += ` WHERE ${field} > ${formattedDate} AND (IsConverted = true OR IsConverted = false)`;
    console.log("SOQL", soql);
    yield* this.queryGenerator(soql);
  }

  async *getContacts(
    doFullDataPull: boolean,
    createdAfter: Date,
    utmFields: string[] = [],
  ): AsyncGenerator<
    {
      Id: string;
      FirstName?: string;
      LastName?: string;
      Email?: string;
      Title?: string;
      Phone?: string;
      AccountName?: {
        attributes: {
          type: string;
          url: string; // salesforce url path of Account object
        };
        Name: string;
      };
      CreatedDate: string;
      SystemModstamp: string;
      [key: string]: any; // For custom utm fields
    }[]
  > {
    const baseFields = [
      "Id",
      "FirstName",
      "LastName",
      "Email",
      "Title",
      "Phone",
      "AccountId",
      "Account.Name",
      "CreatedDate",
      "SystemModstamp",
    ];

    const allFields = [...baseFields, ...utmFields];
    let soql = `SELECT ${allFields.join(", ")} FROM Contact`;

    const dateToUse = createdAfter;
    const field = doFullDataPull ? "CreatedDate" : "SystemModstamp";
    const formattedDate = dateToUse.toISOString().replace(/\.000Z$/, "Z");

    soql += ` WHERE ${field} > ${formattedDate}`;

    yield* this.queryGenerator(soql);
  }

  /**
   * Fetches a map of converted contact IDs to their original lead IDs
   * This is used to map events associated with contacts back to their original leads
   *
   * @returns A Map where the key is the converted contact ID and the value is the original lead ID
   */
  async getConvertedContactToLeadMap(): Promise<Map<string, string>> {
    await this.authenticate();

    const soql = `SELECT Id, ConvertedContactId FROM Lead WHERE IsConverted = true AND ConvertedContactId != null`;

    console.log("Fetching converted lead to contact mapping with query:", soql);

    const contactToLeadMap = new Map<string, string>();

    // Use the queryGenerator to handle pagination
    for await (const batch of this.queryGenerator(soql)) {
      for (const lead of batch) {
        if (lead.ConvertedContactId) {
          contactToLeadMap.set(lead.ConvertedContactId, lead.Id);
        }
      }
    }

    console.log(
      `Found ${contactToLeadMap.size} converted contacts with original lead IDs`,
    );
    return contactToLeadMap;
  }

  /**
   * Fetches events from Salesforce for unconverted leads
   *
   * @param doFullDataPull - If true, fetches all events since createdAfter date; if false, only events modified in the last 4 hours
   * @param createdAfter - The date to start fetching events from when doing a full data pull
   * @returns AsyncGenerator that yields batches of raw Salesforce event objects
   */
  async *getEventsForUnconvertedLeads(
    doFullDataPull: boolean,
    createdAfter: Date = new Date("2024-08-01T00:00:00.000Z"),
  ): AsyncGenerator<any[]> {
    await this.authenticate();

    // Define fields to fetch
    const fields = [
      "Id",
      "Subject",
      "EventSubtype",
      "Type",
      "LastModifiedDate",
      "CreatedDate",
      "WhoId",
      "Who.Type",
      "WhatId",
      "StartDateTime",
      "EndDateTime",
      "IsAllDayEvent",
      "Description",
    ];

    // Determine date filter based on doFullDataPull
    const dateToUse = doFullDataPull
      ? createdAfter
      : new Date(Date.now() - 4 * 60 * 60 * 1000); // 4 hours ago

    // Use CreatedDate for full pull, LastModifiedDate for recent events
    const field = doFullDataPull ? "CreatedDate" : "LastModifiedDate";
    const formattedDate = dateToUse.toISOString().replace(/\.000Z$/, "Z");

    // Build the SOQL query for events associated with unconverted leads
    const soql = `SELECT ${fields.join(", ")} FROM Event
      WHERE ${field} > ${formattedDate}
      AND WhoId IN (SELECT Id FROM Lead WHERE IsConverted = false)`;

    console.log("Fetching events for unconverted leads with query:", soql);

    // Use the queryGenerator to handle pagination
    yield* this.queryGenerator(soql);
  }

  /**
   * Fetches events from Salesforce for converted leads (now contacts)
   *
   * @param doFullDataPull - If true, fetches all events since createdAfter date; if false, only events modified in the last 4 hours
   * @param createdAfter - The date to start fetching events from when doing a full data pull
   * @returns AsyncGenerator that yields batches of raw Salesforce event objects
   */
  async *getEventsForConvertedLeads(
    doFullDataPull: boolean,
    createdAfter: Date = new Date("2024-08-01T00:00:00.000Z"),
  ): AsyncGenerator<
    {
      Id: string;
      Subject: string;
      EventSubtype: string;
      Type: string;
      LastModifiedDate: string;
      CreatedDate: string;
      WhoId: string;
      Who: {
        attributes: {
          type: string;
          url: string; // salesforce url path of Account object
        };
        Name: string;
      };
      WhatId: string;
      StartDateTime: string;
      EndDateTime: string;
      IsAllDayEvent: boolean;
      Description: string;
    }[]
  > {
    await this.authenticate();

    // Define fields to fetch
    const fields = [
      "Id",
      "Subject",
      "EventSubtype",
      "Type",
      "LastModifiedDate",
      "CreatedDate",
      "WhoId",
      "Who.Type",
      "WhatId",
      "StartDateTime",
      "EndDateTime",
      "IsAllDayEvent",
      "Description",
    ];

    // Determine date filter based on doFullDataPull
    const dateToUse = doFullDataPull
      ? createdAfter
      : new Date(Date.now() - 4 * 60 * 60 * 1000); // 4 hours ago

    // Use CreatedDate for full pull, LastModifiedDate for recent events
    const field = doFullDataPull ? "CreatedDate" : "LastModifiedDate";
    const formattedDate = dateToUse.toISOString().replace(/\.000Z$/, "Z");

    // Build the SOQL query for events associated with converted leads (now contacts)
    const soql = `SELECT ${fields.join(", ")} FROM Event
      WHERE ${field} > ${formattedDate}
      AND WhoId IN (SELECT ConvertedContactId FROM Lead WHERE IsConverted = true AND ConvertedContactId != null)`;

    // Use the queryGenerator to handle pagination
    yield* this.queryGenerator(soql);
  }
  // {"Id": "00UPM00000RjhsY2AR", "Who": {"Type": "Contact", "attributes": {"url": "/services/data/v42.0/sobjects/Contact/003PM00000HO0OdYAL", "type": "Name"}}, "Type": null, "WhoId": "003PM00000HO0OdYAL", "WhatId": "006PM00000PUzUHYA1", "Subject": "Source.ag<> Superhuman for Teams", "attributes": {"url": "/services/data/v42.0/sobjects/Event/00UPM00000RjhsY2AR", "type": "Event"}, "CreatedDate": "2025-04-30T07:44:29.000+0000", "Description": "Looking forward to connecting! \n\nIf you need to reschedule, please use the link: https://superhuman.chilipiper.com/reschedule/38aa031b-a4ca-45d3-9a73-95cb68a6a54d", "EndDateTime": "2025-05-07T16:30:00.000+0000", "EventSubtype": "Event", "IsAllDayEvent": false, "StartDateTime": "2025-05-07T16:00:00.000+0000", "originalLeadId": "00QPM00000NPNkr2AH", "LastModifiedDate": "2025-05-02T13:38:35.000+0000", "isConvertedContact": true}
  async getSpecificMeeting() {
    await this.authenticate();

    const oppDescribe = await this.conn?.sobject("Opportunity").describe();
    oppDescribe?.fields.forEach((field) => {
      console.log(field.name);
    });

    const contactDescribe = await this.conn?.sobject("Contact").describe();
    contactDescribe?.fields.forEach((field) => {
      console.log(field.name);
    });
    const leadDescribe = await this.conn?.sobject("Lead").describe();
    leadDescribe?.fields.forEach((field) => {
      console.log(field.name);
    });
    const soql = `SELECT Id, Subject, EventSubtype, Type, LastModifiedDate, CreatedDate, WhoId, Who.Type, WhatId, StartDateTime, EndDateTime, IsAllDayEvent, Description FROM Event WHERE WhoId = '003PM00000dmmogYAA'`;
    const result = await this.conn?.query(soql);
    console.log("result", result);

    const soql2 = `SELECT Id, FirstName, LastName, UTM_Source__c, UTM_Medium__c, UTM_Campaign__c, UTM_Term__c, UTM_Content__c, Email, ConvertedContactId, ConvertedOpportunityId FROM Lead WHERE Id = '00QPM00000NhDny2AF'`;
    const result2 = await this.conn?.query(soql2);
    console.log("result2", result2);

    const soqlcontact = `SELECT Id,FirstName,LastName FROM Contact WHERE Id = '003PM00000dmmogYAA'  `;
    const result4 = await this.conn?.query(soqlcontact);
    console.log("result4", result4);

    const soqlopp = `SELECT Id,Name,Source_Contact__c, DC_Scheduled_Date__c, DC_Target_Date__c, DC_Occurred_Date__c, DC_Impact__c, Opp_Source_SDR_Input__c, DC_Owner__c, Clay_Employee_Count__c, DC_Status__c FROM Opportunity WHERE Id = '006PM00000PyyNtYAJ'`;
    const result3 = await this.conn?.query(soqlopp);
    console.log("result3", result3);
  }

  /**
   * Fetches events from Salesforce, either all events since a given date or just recent ones.
   * This method combines events for both unconverted leads and converted leads (now contacts).
   *
   * @param doFullDataPull - If true, fetches all events since createdAfter date; if false, only events modified in the last 4 hours
   * @param createdAfter - The date to start fetching events from when doing a full data pull
   * @returns AsyncGenerator that yields batches of raw Salesforce event objects with additional originalLeadId for converted contacts
   */
  async *getEvents(
    doFullDataPull: boolean,
    createdAfter: Date = new Date("2024-08-01T00:00:00.000Z"),
  ): AsyncGenerator<
    {
      Id: string;
      Subject: string;
      EventSubtype: string;
      Type: string;
      LastModifiedDate: string;
      CreatedDate: string;
      WhoId: string;
      Who: {
        attributes: {
          type: string;
          url: string; // salesforce url path of Account object
        };
        Name: string;
      };
      WhatId: string;
      StartDateTime: string;
      EndDateTime: string;
      IsAllDayEvent: boolean;
      Description: string;
      originalLeadId?: string;
    }[]
  > {
    // First, get the mapping of converted contact IDs to original lead IDs
    const contactToLeadMap = await this.getConvertedContactToLeadMap();

    // Fetch events for unconverted leads
    for await (const batch of this.getEventsForUnconvertedLeads(
      doFullDataPull,
      createdAfter,
    )) {
      console.log("UNCONVERTED LEADS", batch);
      // These events already have the correct WhoId (lead ID), so we can yield them as is
      yield batch;
    }

    // Fetch events for converted leads (now contacts)
    for await (const batch of this.getEventsForConvertedLeads(
      doFullDataPull,
      createdAfter,
    )) {
      // For these events, we need to add the original lead ID
      const enhancedBatch = batch.map((event) => {
        // If this event is associated with a contact that was converted from a lead,
        // add the original lead ID to the event
        console.log("event.WhoId", event.Who);
        if (event.WhoId && contactToLeadMap.has(event.WhoId)) {
          return {
            ...event,
            originalLeadId: contactToLeadMap.get(event.WhoId),
            isConvertedContact: true,
          };
        }
        return event;
      });
      yield enhancedBatch;
    }
  }

  /**
   * Fetches the metadata for the Lead object and returns a list of field API names.
   * @returns {Promise<string[]>} A promise that resolves to an array of field names.
   * @throws {Error} If the Salesforce API call fails or returns unexpected data.
   */
  async getLeadFields(): Promise<string[]> {
    await this.authenticate(); // Ensure authentication
    if (!this.conn) {
      throw new Error("Salesforce connection not initialized.");
    }
    try {
      const leadDescribe = await this.conn.sobject("Lead").describe();
      if (!leadDescribe || !leadDescribe.fields) {
        console.error("Failed to describe Lead object or no fields found.");
        // Throw an error as the caller likely expects fields
        throw new Error(
          "Failed to describe Salesforce Lead object or no fields found.",
        );
      }
      // Extract the API name (field name) from each field description
      return leadDescribe.fields.map((field) => field.name);
    } catch (error) {
      console.error("Error describing Salesforce Lead object:", error);
      // Re-throw the error to be handled by the caller
      throw new Error(
        `Error describing Salesforce Lead object: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  async getLeadandContactchema() {
    await this.authenticate();
    const metadata = await this.conn?.sobject("Lead").describe();
    console.log("HERE", metadata);
    metadata?.fields.forEach((f) => {
      console.log(f.name);
    });
    const metadata2 = await this.conn?.sobject("Contact").describe();

    console.log("\n \n \n");
    metadata2?.fields.forEach((f) => {
      console.log(f.name);
    });
  }

  /**
   * Fetches first meeting data from Salesforce based on entity type and field
   *
   * @param entity - The Salesforce entity type ("contact", "opportunity", "lead")
   * @param field - The field name to retrieve (e.g., "Date_BQ__c")
   * @param entityIds - Array of entity IDs to query
   * @returns AsyncGenerator that yields batches of records with the specified field
   */
  async *getFirstMeetings(
    entity: "contact" | "opportunity" | "lead",
    field: string,
    entityIds: string[],
  ): AsyncGenerator<any[]> {
    await this.authenticate();

    if (entityIds.length === 0) {
      console.log("No entity IDs provided for getFirstMeetings");
      return;
    }

    // Define base fields for each entity type
    let baseFields: string[];
    let entityName: string;

    switch (entity) {
      case "contact":
        baseFields = ["Id", "FirstName", "LastName", "Email"];
        entityName = "Contact";
        break;
      case "opportunity":
        baseFields = ["Id", "Name", "StageName", "CloseDate"];
        entityName = "Opportunity";
        break;
      case "lead": // Should not happen
        baseFields = ["Id", "FirstName", "LastName", "Email", "Company"];
        entityName = "Lead";
        break;
      default:
        throw new Error(`Unsupported entity type: ${entity}`);
    }

    // Add the custom field to the query
    const allFields = [...baseFields, field];

    // Build the SOQL query with IN clause for the entity IDs
    const idList = entityIds.map((id) => `'${id}'`).join(", ");
    const soql = `SELECT ${allFields.join(", ")} FROM ${entityName} WHERE Id IN (${idList})`;

    console.log(`Fetching first meeting data for ${entity} with query:`, soql);

    // Use the queryGenerator to handle pagination
    yield* this.queryGenerator(soql);
  }
}
