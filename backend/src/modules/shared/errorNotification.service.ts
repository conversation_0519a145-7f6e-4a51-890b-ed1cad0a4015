import { SlackApiService } from "../advertising/infrastructure/services/slack.service";
import { OrganizationRepository } from "../core/infrastructure/repositories/organization.repository";
import { ErrorLogRepository } from "./infrastructure/repositories/errorLog.repository";
import { createUuid } from "../core/utils/uuid";

export enum ErrorType {
  LINKEDIN_API_ERROR = "LINKEDIN_API_ERROR",
  LINKEDIN_AUTH_ERROR = "LINKEDIN_AUTH_ERROR",
}

export interface ErrorNotificationData {
  errorType: ErrorType;
  organizationId?: number;
  title: string;
  description: string;
  metadata?: Record<string, any>;
  timestamp?: Date;
  endpoint?: string;
  method?: string;
  statusCode?: string;
  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

export class ErrorNotificationService {
  private slackApiService: SlackApiService;
  private organizationRepository: OrganizationRepository;
  private errorLogRepository: ErrorLogRepository;
  private internalWebhookUrl: string | undefined;

  constructor() {
    this.slackApiService = new SlackApiService();
    this.organizationRepository = new OrganizationRepository();
    this.errorLogRepository = new ErrorLogRepository();
    this.internalWebhookUrl = process.env.KALOS_INTERNAL_ALERTS_WEBHOOK_URL;
  }

  async storeError(errorData: ErrorNotificationData): Promise<{ success: boolean; error?: string }> {
    try {
      // Truncate endpoint to fit database constraint (200 chars)
      const truncatedEndpoint = errorData.endpoint 
        ? errorData.endpoint.length > 200 
          ? errorData.endpoint.substring(0, 197) + "..." 
          : errorData.endpoint
        : undefined;

      // If endpoint was truncated, store the full version in metadata
      const enhancedMetadata = errorData.endpoint && errorData.endpoint.length > 200
        ? {
            ...errorData.metadata,
            fullEndpoint: errorData.endpoint
          }
        : errorData.metadata;

      await this.errorLogRepository.create({
        id: createUuid(),
        organizationId: errorData.organizationId,
        errorType: errorData.errorType,
        title: errorData.title,
        description: errorData.description,
        endpoint: truncatedEndpoint,
        method: errorData.method,
        statusCode: errorData.statusCode,
        metadata: enhancedMetadata,
        severity: errorData.severity || 'MEDIUM',
        isProcessed: false,
        createdAt: errorData.timestamp || new Date(),
      });

      console.log(`[ErrorNotification] Stored error - ${errorData.errorType} for organization ${errorData.organizationId || 'system'}`);
      return { success: true };

    } catch (error) {
      console.error(`[ErrorNotification] Failed to store error:`, error);
      return { success: false, error: "Failed to store error in database" };
    }
  }

  // Keep the old method for backward compatibility but route to storeError
  async sendErrorNotification(errorData: ErrorNotificationData): Promise<{ success: boolean; error?: string }> {
    return this.storeError(errorData);
  }

  // Method for sending aggregated Slack notifications (called by Inngest function)
  async sendAggregatedNotification(summary: Array<{
    errorType: string;
    count: number;
    severity: string;
    organizationIds: number[];
    statusCode?: string;
    endpoint?: string;
  }>): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.internalWebhookUrl) {
        console.warn(`[ErrorNotification] No internal alerts webhook URL configured`);
        return { success: false, error: "Internal alerts webhook not configured" };
      }

      // Filter to only include HIGH and CRITICAL errors
      const highPriorityErrors = summary.filter(error => 
        error.severity === 'CRITICAL' || error.severity === 'HIGH'
      );
      
      if (highPriorityErrors.length === 0) {
        console.log(`[ErrorNotification] No high priority errors to report in this period`);
        return { success: true };
      }

      const slackMessage = this.formatAggregatedMessage(highPriorityErrors);
      
      const slackResponse = await this.slackApiService.sendMessage(
        this.internalWebhookUrl,
        slackMessage
      );

      if (!slackResponse.ok) {
        console.error(`[ErrorNotification] Failed to send aggregated notification:`, slackResponse.error);
        return { success: false, error: slackResponse.error };
      }

      console.log(`[ErrorNotification] Successfully sent high priority error notification for ${highPriorityErrors.length} error types`);
      return { success: true };

    } catch (error) {
      console.error(`[ErrorNotification] Error sending aggregated notification:`, error);
      return { success: false, error: "Failed to send aggregated notification" };
    }
  }

  private formatAggregatedMessage(summary: Array<{
    errorType: string;
    count: number;
    severity: string;
    organizationIds: number[];
    statusCode?: string;
    endpoint?: string;
  }>): any {
    const timestamp = new Date();
    const totalErrors = summary.reduce((sum, item) => sum + item.count, 0);
    
    // Group errors by organization
    const orgErrorMap = new Map<number | 'system', Array<{
      errorType: string;
      count: number;
      severity: string;
      statusCode?: string;
      endpoint?: string;
    }>>();

    summary.forEach(error => {
      if (error.organizationIds.length === 0) {
        // System-level error
        const systemErrors = orgErrorMap.get('system') || [];
        systemErrors.push({
          errorType: error.errorType,
          count: error.count,
          severity: error.severity,
          statusCode: error.statusCode,
          endpoint: error.endpoint
        });
        orgErrorMap.set('system', systemErrors);
      } else {
        // Organization-specific errors
        error.organizationIds.forEach(orgId => {
          const orgErrors = orgErrorMap.get(orgId) || [];
          orgErrors.push({
            errorType: error.errorType,
            count: error.count,
            severity: error.severity,
            statusCode: error.statusCode,
            endpoint: error.endpoint
          });
          orgErrorMap.set(orgId, orgErrors);
        });
      }
    });

    // Format error breakdown by organization
    let errorBreakdown = "";
    
    Array.from(orgErrorMap.entries()).forEach(([orgKey, errors]) => {
      const orgName = orgKey === 'system' ? 'System Errors' : `Organization ${orgKey}`;
      errorBreakdown += `\n\n*${orgName}:*\n`;
      errors.forEach(error => {
        let errorDetails = `• ${error.count} error${error.count === 1 ? '' : 's'} : \`${error.severity}\` - \`${error.errorType}\` - \`Code ${error.statusCode || 'Unknown'}\``;
        errorBreakdown += `${errorDetails}\n`;
      });
    });

    const timePeriodStart = new Date(timestamp.getTime() - 5 * 60 * 1000);

    return {
      blocks: [
        {
          type: "header",
          text: {
            type: "plain_text",
            text: `Priority Errors - Last 5 Minutes`
          }
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `*Total Priority Errors:* ${totalErrors}\n*Time Period:* ${timePeriodStart.toLocaleTimeString()} - ${timestamp.toLocaleTimeString()}${errorBreakdown}`
          }
        },
        {
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `Check the admin dashboard for more details at /admin/error-logs`
            }
          ]
        }
      ]
    };
  }

  // LinkedIn-specific error notification methods
  async notifyLinkedInAuthFailure(
    organizationId: number | undefined,
    errorContext: string,
    errorDetails: string,
    statusCode: number,
    metadata?: Record<string, any>
  ): Promise<{ success: boolean; error?: string }> {
    return this.storeError({
      errorType: ErrorType.LINKEDIN_AUTH_ERROR,
      organizationId,
      title: "🔐 LinkedIn Authentication Failed",
      description: `LinkedIn authentication failed during ${errorContext}. ${errorDetails}`,
      metadata: {
        ...metadata,
        errorContext,
        requires_action: true,
      },
      severity: 'CRITICAL',
      statusCode: statusCode.toString(), // expecting 401
    });
  }

  async notifyLinkedInRateLimitExceeded(
    organizationId: number | undefined,
    endpoint: string,
    statusCode: number,
    metadata?: Record<string, any>
  ): Promise<{ success: boolean; error?: string }> {
    const title = "⚡ LinkedIn Rate Limit Exceeded";     
    const description = `LinkedIn API rate limit exceeded for endpoint: ${endpoint}. The system will automatically retry, but this may indicate heavy usage.`;

    return this.storeError({
      errorType: ErrorType.LINKEDIN_API_ERROR,
      organizationId,
      title,
      description,
      endpoint,
      statusCode: statusCode.toString(),
      metadata: {
        ...metadata,
        requires_action: true,
      },
      severity: 'HIGH', // expecting 429
    });
  }

  async notifyLinkedInApiFailure(
    organizationId: number | undefined,
    endpoint: string,
    error: any,
    statusCode: number,
    metadata?: Record<string, any>
  ): Promise<{ success: boolean; error?: string }> {
    let errorMessage = "Unknown error";
    let errorCode: string | undefined;

    // Extract error details based on the LinkedIn API error format
    if (error?.error && Array.isArray(error.error)) {
      const firstError = error.error[0];
      errorMessage = firstError?.message || "LinkedIn API error";
      errorCode = firstError?.errorCode;
    } else if (typeof error === "string") {
      errorMessage = error;
    } else if (error?.message) {
      errorMessage = error.message;
    }

    return this.storeError({
      errorType: ErrorType.LINKEDIN_API_ERROR,
      organizationId,
      title: "LinkedIn API Operation Failed",
      description: `LinkedIn API call to ${endpoint} failed: ${errorMessage}`,
      endpoint,
      statusCode: statusCode.toString(),
      metadata: {
        ...metadata,
        errorMessage,
        errorCode,
      },
      severity: (statusCode === 401 || statusCode === 429) ? 'CRITICAL' : 'MEDIUM',
    });
  }

  // Method for cleaning up error logs from last day (called at end of day)
  async cleanupErrorLogs(): Promise<{ success: boolean; deletedCount?: number; error?: string }> {
    try {
      console.log(`[ErrorNotification] Starting end-of-day error log cleanup (last 24 hours)`);
      
      const result = await this.errorLogRepository.deleteFromLastDay();
      
      console.log(`[ErrorNotification] Successfully completed error log cleanup - deleted ${result.deletedCount} records from last 24 hours`);
      return { success: true, deletedCount: result.deletedCount };

    } catch (error) {
      console.error(`[ErrorNotification] Error during cleanup:`, error);
      return { success: false, error: "Failed to cleanup error logs" };
    }
  }
} 