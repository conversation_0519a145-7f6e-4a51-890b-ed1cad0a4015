import { eq, gte, lte, and, desc, asc, inArray } from "drizzle-orm";
import { db } from "../../../../database/db";
import { errorLogTable, type ErrorLog, type NewErrorLog } from "../../../../database/schemas/shared/errorLog.table";

export interface ErrorLogFilters {
  organizationId?: number;
  errorType?: string;
  severity?: string;
  statusCode?: string;
  startDate?: Date;
  endDate?: Date;
  isProcessed?: boolean;
}

export interface ErrorLogPagination {
  offset?: number;
  limit?: number;
}

export class ErrorLogRepository {
  async create(errorLog: NewErrorLog): Promise<ErrorLog> {
    const [created] = await db
      .insert(errorLogTable)
      .values(errorLog)
      .returning();
    return created!;
  }

  async findMany(
    filters: ErrorLogFilters = {},
    pagination: ErrorLogPagination = {}
  ): Promise<ErrorLog[]> {
    const { offset = 0, limit = 100 } = pagination;
    
    const conditions = [];
    
    if (filters.organizationId !== undefined) {
      conditions.push(eq(errorLogTable.organizationId, filters.organizationId));
    }
    
    if (filters.errorType) {
      conditions.push(eq(errorLogTable.errorType, filters.errorType));
    }
    
    if (filters.severity) {
      conditions.push(eq(errorLogTable.severity, filters.severity));
    }
    
    if (filters.statusCode) {
      conditions.push(eq(errorLogTable.statusCode, filters.statusCode));
    }
    
    if (filters.startDate) {
      conditions.push(gte(errorLogTable.createdAt, filters.startDate));
    }
    
    if (filters.endDate) {
      conditions.push(lte(errorLogTable.createdAt, filters.endDate));
    }
    
    if (filters.isProcessed !== undefined) {
      conditions.push(eq(errorLogTable.isProcessed, filters.isProcessed));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    return await db
      .select()
      .from(errorLogTable)
      .where(whereClause)
      .orderBy(desc(errorLogTable.createdAt))
      .limit(limit)
      .offset(offset);
  }

  async findUnprocessedSince(since: Date): Promise<ErrorLog[]> {
    return await db
      .select()
      .from(errorLogTable)
      .where(
        and(
          eq(errorLogTable.isProcessed, false),
          gte(errorLogTable.createdAt, since)
        )
      )
      .orderBy(asc(errorLogTable.createdAt));
  }

  async markAsProcessed(ids: string[]): Promise<void> {
    if (ids.length === 0) return;
    
    await db
      .update(errorLogTable)
      .set({ isProcessed: true })
      .where(inArray(errorLogTable.id, ids));
  }

  async getErrorSummary(
    startDate: Date,
    endDate: Date,
    organizationId?: number
  ): Promise<Array<{
    errorType: string;
    count: number;
    severity: string;
  }>> {
    const conditions = [
      gte(errorLogTable.createdAt, startDate),
      lte(errorLogTable.createdAt, endDate)
    ];
    
    if (organizationId) {
      conditions.push(eq(errorLogTable.organizationId, organizationId));
    }

    const errors = await db
      .select()
      .from(errorLogTable)
      .where(and(...conditions));

    // Group by errorType and count
    const summary = errors.reduce((acc, error) => {
      const key = `${error.errorType}-${error.severity}`;
      if (!acc[key]) {
        acc[key] = {
          errorType: error.errorType,
          count: 0,
          severity: error.severity || 'MEDIUM'
        };
      }
      acc[key].count++;
      return acc;
    }, {} as Record<string, { errorType: string; count: number; severity: string }>);

    return Object.values(summary);
  }

  async deleteFromLastDay(): Promise<{ deletedCount: number }> {
    try {
      // Calculate 24 hours ago
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      
      // Get count of records to be deleted (from last 24 hours)
      const recordsToDelete = await db
        .select()
        .from(errorLogTable)
        .where(gte(errorLogTable.createdAt, twentyFourHoursAgo));
      
      const deletedCount = recordsToDelete.length;
      
      // Delete records from the last 24 hours
      await db
        .delete(errorLogTable)
        .where(gte(errorLogTable.createdAt, twentyFourHoursAgo));
      
      console.log(`[ErrorLogRepository] Deleted ${deletedCount} error log records from the last 24 hours`);
      return { deletedCount };
    } catch (error) {
      console.error(`[ErrorLogRepository] Failed to delete error logs from last day:`, error);
      throw error;
    }
  }
} 