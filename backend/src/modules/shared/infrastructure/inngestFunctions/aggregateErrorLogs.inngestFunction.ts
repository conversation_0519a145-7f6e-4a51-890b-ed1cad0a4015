import { advertisingInngestClient } from "../../../advertising/utils/advertisingInngestClient";
import { ErrorLogRepository } from "../repositories/errorLog.repository";
import { ErrorNotificationService } from "../../errorNotification.service";

export const aggregateErrorLogs = advertisingInngestClient.createFunction(
  { id: "aggregate-error-logs" },
  [
    { event: "error-logs/aggregate" },
    { cron: "*/5 * * * *" }, // Every 5 minutes
  ],
  async ({ step, event }) => {
    const result = await step.run("aggregate-and-send-error-notifications", async () => {
      const errorLogRepository = new ErrorLogRepository();
      const errorNotificationService = new ErrorNotificationService();

      console.log("[ErrorAggregation] Starting 5-minute error aggregation");

      // Get errors from the last 5 minutes that haven't been processed
      const fifteenMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      const unprocessedErrors = await errorLogRepository.findUnprocessedSince(fifteenMinutesAgo);

      if (unprocessedErrors.length === 0) {
        console.log("[ErrorAggregation] No unprocessed errors found in the last 5 minutes");
        return { processedCount: 0, notificationSent: false };
      }

      console.log(`[ErrorAggregation] Found ${unprocessedErrors.length} unprocessed errors`);

      // Group errors by type, severity, statusCode, and endpoint, and collect organization IDs
      const errorSummary = unprocessedErrors.reduce((acc, error) => {
        // Create a more detailed key that includes statusCode and endpoint for better grouping
        const statusPart = error.statusCode ? `-${error.statusCode}` : '';
        const endpointPart = error.endpoint ? `-${error.endpoint.replace(/[^a-zA-Z0-9]/g, '_')}` : '';
        const key = `${error.errorType}-${error.severity || 'MEDIUM'}${statusPart}${endpointPart}`;
        
        if (!acc[key]) {
          acc[key] = {
            errorType: error.errorType,
            count: 0,
            severity: error.severity || 'MEDIUM',
            statusCode: error.statusCode || undefined,
            endpoint: error.endpoint || undefined,
            organizationIds: new Set<number>(),
          };
        }
        
        acc[key].count++;
        
        if (error.organizationId) {
          acc[key].organizationIds.add(error.organizationId);
        }
        
        return acc;
      }, {} as Record<string, {
        errorType: string;
        count: number;
        severity: string;
        statusCode?: string;
        endpoint?: string;
        organizationIds: Set<number>;
      }>);

      // Convert Set to Array for the notification service
      const summaryForNotification = Object.values(errorSummary).map(item => ({
        ...item,
        organizationIds: Array.from(item.organizationIds),
      }));

      // Send aggregated notification
      let notificationSent = false;
      try {
        const notificationResult = await errorNotificationService.sendAggregatedNotification(summaryForNotification);
        notificationSent = notificationResult.success;
        
        if (!notificationResult.success) {
          console.error("[ErrorAggregation] Failed to send aggregated notification:", notificationResult.error);
        } else {
          console.log("[ErrorAggregation] Successfully sent aggregated notification");
        }
      } catch (error) {
        console.error("[ErrorAggregation] Error sending aggregated notification:", error);
      }

      // Mark errors as processed
      try {
        const errorIds = unprocessedErrors.map(error => error.id);
        await errorLogRepository.markAsProcessed(errorIds);
        console.log(`[ErrorAggregation] Marked ${errorIds.length} errors as processed`);
      } catch (error) {
        console.error("[ErrorAggregation] Failed to mark errors as processed:", error);
      }

      return {
        processedCount: unprocessedErrors.length,
        notificationSent,
        errorSummary: summaryForNotification,
      };
    });

    // End-of-day cleanup logic
    const cleanupResult = await step.run("check-and-run-eod-cleanup", async () => {
      const now = new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();
      
      // Check if it's past 11:45 PM (23:45)
      const isEndOfDay = currentHour === 23 && currentMinute >= 45;
      
      if (isEndOfDay) {
        console.log("[ErrorAggregation] End-of-day detected, starting error log cleanup (last 24 hours)");
        
        const errorNotificationService = new ErrorNotificationService();
        const cleanupResult = await errorNotificationService.cleanupErrorLogs();
        
        if (cleanupResult.success) {
          console.log(`[ErrorAggregation] End-of-day cleanup completed successfully - deleted ${cleanupResult.deletedCount} records from last 24 hours`);
        } else {
          console.error("[ErrorAggregation] End-of-day cleanup failed:", cleanupResult.error);
        }
        
        return {
          cleanupTriggered: true,
          cleanupSuccess: cleanupResult.success,
          deletedCount: cleanupResult.deletedCount,
          cleanupError: cleanupResult.error,
        };
      } else {
        console.log(`[ErrorAggregation] Not end-of-day yet (${currentHour}:${currentMinute.toString().padStart(2, '0')}), skipping cleanup`);
        return {
          cleanupTriggered: false,
          cleanupSuccess: null,
          deletedCount: 0,
        };
      }
    });

    console.log(`[ErrorAggregation] Completed aggregation:`, { ...result, cleanup: cleanupResult });
    return { ...result, cleanup: cleanupResult };
  }
); 