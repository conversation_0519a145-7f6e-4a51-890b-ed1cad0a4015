import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "../../../../trpc/trpc";
import { ErrorLogRepository } from "../../infrastructure/repositories/errorLog.repository";

const errorLogRepository = new ErrorLogRepository();

export const errorLogController = createTRPCRouter({
  getErrorLogs: publicProcedure
    .input(
      z.object({
        organizationId: z.number().optional(),
        errorType: z.string().optional(),
        severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).optional(),
        statusCode: z.string().optional(),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        isProcessed: z.boolean().optional(),
        offset: z.number().default(0),
        limit: z.number().default(50),
      })
    )
    .query(async ({ input }) => {
      try {
        console.log('[ErrorLogs] getErrorLogs called with input:', input);
        
        const { offset, limit, ...filters } = input;
        
        const errorLogs = await errorLogRepository.findMany(
          filters,
          { offset, limit }
        );
        
        console.log('[ErrorLogs] Found', errorLogs.length, 'error logs');
        
        return {
          errorLogs,
          pagination: {
            offset,
            limit,
            hasMore: errorLogs.length === limit,
          },
        };
      } catch (error) {
        console.error('[ErrorLogs] Error in getErrorLogs:', error);
        throw error;
      }
    }),

  getErrorSummary: publicProcedure
    .input(
      z.object({
        startDate: z.date(),
        endDate: z.date(),
        organizationId: z.number().optional(),
      })
    )
    .query(async ({ input }) => {
      const summary = await errorLogRepository.getErrorSummary(
        input.startDate,
        input.endDate,
        input.organizationId
      );
      
      return summary;
    }),

  getErrorStats: publicProcedure
    .input(
      z.object({
        timeRange: z.enum(['1h', '6h', '24h', '7d', '30d']).default('24h'),
        organizationId: z.number().optional(),
      })
    )
    .query(async ({ input }) => {
      const now = new Date();
      let startDate: Date;
      
      switch (input.timeRange) {
        case '1h':
          startDate = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case '6h':
          startDate = new Date(now.getTime() - 6 * 60 * 60 * 1000);
          break;
        case '24h':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      }
      
      const filters = {
        startDate,
        endDate: now,
        organizationId: input.organizationId,
      };
      
      const [allErrors, summary] = await Promise.all([
        errorLogRepository.findMany(filters),
        errorLogRepository.getErrorSummary(startDate, now, input.organizationId),
      ]);
      
      // Calculate stats
      const totalErrors = allErrors.length;
      const criticalErrors = allErrors.filter(e => e.severity === 'CRITICAL').length;
      const highErrors = allErrors.filter(e => e.severity === 'HIGH').length;
      const mediumErrors = allErrors.filter(e => e.severity === 'MEDIUM').length;
      const lowErrors = allErrors.filter(e => e.severity === 'LOW').length;
      
      // Group by error type
      const errorsByType = allErrors.reduce((acc, error) => {
        acc[error.errorType] = (acc[error.errorType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      // Group by organization
      const errorsByOrg = allErrors.reduce((acc, error) => {
        const orgId = error.organizationId || 'system';
        acc[orgId] = (acc[orgId] || 0) + 1;
        return acc;
      }, {} as Record<string | number, number>);
      
      return {
        totalErrors,
        severityBreakdown: {
          critical: criticalErrors,
          high: highErrors,
          medium: mediumErrors,
          low: lowErrors,
        },
        errorsByType,
        errorsByOrg,
        summary,
        timeRange: input.timeRange,
      };
    }),
}); 