import { z } from "zod";

import { abTestTypeSchema } from "../advertising/abTest/internal/domain/abTestType.valueObject";

export function defineJobTrigger<
  const Name extends string,
  DataSchema extends z.ZodTypeAny,
>(name: Name, dataSchema: DataSchema) {
  type Data = z.infer<DataSchema>;
  type Trigger = { name: Name; data: Data };

  return {
    NAME: name,
    schema: dataSchema,
    build: (data: Data): Trigger => ({ name, data }),
  };
}

export type JobTrigger = ReturnType<typeof defineJobTrigger>;
