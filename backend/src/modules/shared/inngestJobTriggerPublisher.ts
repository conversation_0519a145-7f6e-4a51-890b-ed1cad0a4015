import { Inngest } from "inngest";

import { advertisingInngestClient } from "../advertising/utils/advertisingInngestClient";
import { JobTrigger } from "./jobTrigger.interface";
import { IJobTriggerPublisher } from "./jobTriggerPublisher.interface";

export const InngestJobTriggerPublisher = (
  inngestClient: Inngest,
): IJobTriggerPublisher => {
  return {
    publish: async (trigger: { name: string; data: Object }) => {
      const res = await inngestClient.send({
        name: trigger.name,
        data: trigger.data,
      });

      const id = res.ids[0];
      if (!id) {
        throw new Error("No event id returned from inngest");
      }

      return { eventId: id };
    },
  };
};
