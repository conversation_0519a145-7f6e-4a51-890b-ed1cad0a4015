import * as aws from '@pulumi/aws';
import * as pulumi from '@pulumi/pulumi';
import * as esbuild from 'esbuild';
import * as fflate from 'fflate';
import * as fs from 'fs';
import * as path from 'path';


const esbuildDefaultOpts = {
    bundle: true,
    minify: true,
    sourcemap: false,
    platform: 'node',
    target: 'node18',
    outdir: 'nodeDist',
  };

const { bundle, minify, sourcemap, platform, target, outdir } = esbuildDefaultOpts

if (!outdir)
  throw new Error(
    'You must specify an outdir in esbuild options default to : build',
  );


esbuild.buildSync({
  entryPoints: ["./nodeFunctions/saveAudienceTargets.ts", "./nodeFunctions/uploadCreative.ts"],
  bundle,
  minify,
  sourcemap,
  platform,
  target,
  outdir,
});


// we zip the code in write the lambda.zip file
/*
const zipContent = fflate.zipSync({
  'index.js': fs.readFileSync(
    path.resolve(process.cwd(), outdir, outputFile),
  ),
});

fs.writeFileSync('lambda.zip', zipContent);
*/

