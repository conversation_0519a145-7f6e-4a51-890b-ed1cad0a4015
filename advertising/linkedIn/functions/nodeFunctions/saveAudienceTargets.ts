import * as z from "zod";

import { campaignHandlers } from "@kalos/database/handlers/campaign";
import { campaignGroupSegmentHandlers } from "@kalos/database/handlers/campaignGroupSegment";
import { campaignLinkedInAudienceTargetGroupHandlers } from "@kalos/database/handlers/campaignLinkedInAudienceTargetGroup";
import { campaignLinkedInAudienceTargetGroupTargetHandlers } from "@kalos/database/handlers/campaignLinkedInAudienceTargetGroupTarget";
import { linkedInCampaignAudienceTargetHandlers } from "@kalos/database/handlers/linkedInCampaignAudienceTarget";
import { segmentLinkedInAudienceTargetGroupPrefabGroupTargetHandlers } from "@kalos/database/handlers/segmentLinkedInAudienceTargetGroupPerfabGroupTarget";
import { segmentLinkedInAudienceTargetGroupPrefabHandlers } from "@kalos/database/handlers/segmentLinkedInAudienceTargetGroupPrefab";
import { segmentLinkedInAudienceTargetGroupPrefabGroupHandlers } from "@kalos/database/handlers/segmentLinkedInAudienceTargetGroupPrefabGroup";

const facetEntity = z.object({
  urn: z.string(),
  name: z.string(),
});
const eventDataSchema = z.object({
  Records: z.array(z.object({ body: z.string() })),
});

const bodySchema = z.object({
  vertical: z.optional(facetEntity).nullable(),
  number_of_employees: z.optional(facetEntity).nullable(),
  annual_revenue: z.optional(facetEntity).nullable(),
  job_titles: z
    .optional(z.object({ facet_entites: z.array(facetEntity) }))
    .nullable(),
  job_function: z.optional(facetEntity).nullable(),
  campaign_id: z.string(),
});

type Event = {
  Records: {
    body: unknown;
  }[];
};

export async function handler(event: Event, context: unknown) {
  console.log(event);
  const eventData = eventDataSchema.parse(event);
  for (const record of eventData.Records) {
    const data = bodySchema.parse(JSON.parse(record.body));
    const cgs = await campaignGroupSegmentHandlers.select.one.byCampaignId(
      data.campaign_id,
    );
    if (!cgs) {
      throw new Error("Campaign group segment not found");
    }

    const campaign = await campaignHandlers.select.one.byId(data.campaign_id);
    if (!campaign) {
      throw new Error("Campaign not found");
    }
    const prefabs =
      await segmentLinkedInAudienceTargetGroupPrefabHandlers.select.byManySegmentIdsWithGroupAndTarget(
        [cgs.campaign_group_segment.segmentId],
      );

    const prefab = prefabs.find(
      (each) => each.description === campaign.description,
    );
    if (prefab) {
      const targetGroupIds: string[] = [];
      const targetIds: string[] = [];
      for (const eachTargetGroup of prefab.targetGroups) {
        targetGroupIds.push(eachTargetGroup.targetGroupId);
        targetIds.push(...eachTargetGroup.targets.map((each) => each.targetId));
      }
      await Promise.all([
        segmentLinkedInAudienceTargetGroupPrefabGroupTargetHandlers.delete.many.byId(
          targetIds,
        ),
        segmentLinkedInAudienceTargetGroupPrefabGroupHandlers.delete.many.byId(
          targetGroupIds,
        ),
        segmentLinkedInAudienceTargetGroupPrefabHandlers.delete.many.byId([
          prefab.prefabId,
        ]),
      ]);
    }

    const newPrefab =
      await segmentLinkedInAudienceTargetGroupPrefabHandlers.insert.many([
        {
          segmentId: cgs.campaign_group_segment.segmentId,
          description: campaign.description,
        },
      ]);
    const newPrefabId = newPrefab[0].id;
    if (!newPrefabId) {
      throw new Error("Failed to insert new prefab");
    }

    if (data.vertical) {
      const group =
        await campaignLinkedInAudienceTargetGroupHandlers.insert.one({
          campaignId: data.campaign_id,
          facetUrn: "urn:li:adTargetingFacet:industries",
          facetName: "Industries",
        });

      const prefabGroup =
        await segmentLinkedInAudienceTargetGroupPrefabGroupHandlers.insert.many(
          [
            {
              segmentLinkedInAudienceTargetGroupPrefabId: newPrefabId,
              facetUrn: "urn:li:adTargetingFacet:industries",
              facetName: "Industries",
            },
          ],
        );
      let target = await linkedInCampaignAudienceTargetHandlers.select.one(
        data.vertical.urn,
      );
      if (!target) {
        target = await linkedInCampaignAudienceTargetHandlers.insert.one({
          linkedInFacetEntityUrn: data.vertical.urn,
          linkedInFacetUrn: "urn:li:adTargetingFacet:industries",
          name: data.vertical.name,
        });
      }
      await campaignLinkedInAudienceTargetGroupTargetHandlers.insert.many([
        {
          campaignLinkedInAudienceTargetGroupId: group.id,
          linkedInAudienceTargetId: target.id,
        },
      ]);
      if (prefabGroup[0] !== undefined) {
        await segmentLinkedInAudienceTargetGroupPrefabGroupTargetHandlers.insert.many(
          [
            {
              segmentLinkedInAudienceTargetGroupPrefabGroupId:
                prefabGroup[0].id,
              linkedinTargetAudienceId: target.id,
            },
          ],
        );
      }
      console.log("Inserted vertical");
    }
    if (data.annual_revenue) {
      const group =
        await campaignLinkedInAudienceTargetGroupHandlers.insert.one({
          campaignId: data.campaign_id,
          facetUrn: "urn:li:adTargetingFacet:revenue",
          facetName: "Revenue",
        });

      const prefabGroup =
        await segmentLinkedInAudienceTargetGroupPrefabGroupHandlers.insert.many(
          [
            {
              segmentLinkedInAudienceTargetGroupPrefabId: newPrefabId,
              facetUrn: "urn:li:adTargetingFacet:revenue",
              facetName: "Revenue",
            },
          ],
        );

      let target = await linkedInCampaignAudienceTargetHandlers.select.one(
        data.annual_revenue.urn,
      );
      if (!target) {
        target = await linkedInCampaignAudienceTargetHandlers.insert.one({
          linkedInFacetEntityUrn: data.annual_revenue.urn,
          linkedInFacetUrn: "urn:li:adTargetingFacet:revenue",
          name: data.annual_revenue.name,
        });
      }
      await campaignLinkedInAudienceTargetGroupTargetHandlers.insert.many([
        {
          campaignLinkedInAudienceTargetGroupId: group.id,
          linkedInAudienceTargetId: target.id,
        },
      ]);
      if (prefabGroup[0] !== undefined) {
        await segmentLinkedInAudienceTargetGroupPrefabGroupTargetHandlers.insert.many(
          [
            {
              segmentLinkedInAudienceTargetGroupPrefabGroupId:
                prefabGroup[0].id,
              linkedinTargetAudienceId: target.id,
            },
          ],
        );
      }

      console.log("Inserted annual revenue");
    }
    if (data.job_function) {
      const group =
        await campaignLinkedInAudienceTargetGroupHandlers.insert.one({
          campaignId: data.campaign_id,
          facetUrn: "urn:li:adTargetingFacet:jobFunctions",
          facetName: "Job Functions",
        });

      const prefabGroup =
        await segmentLinkedInAudienceTargetGroupPrefabGroupHandlers.insert.many(
          [
            {
              segmentLinkedInAudienceTargetGroupPrefabId: newPrefabId,
              facetUrn: "urn:li:adTargetingFacet:jobFunctions",
              facetName: "Functions",
            },
          ],
        );

      let target = await linkedInCampaignAudienceTargetHandlers.select.one(
        data.job_function.urn,
      );
      if (!target) {
        target = await linkedInCampaignAudienceTargetHandlers.insert.one({
          linkedInFacetEntityUrn: data.job_function.urn,
          linkedInFacetUrn: "urn:li:adTargetingFacet:jobFunctions",
          name: data.job_function.name,
        });
      }
      await campaignLinkedInAudienceTargetGroupTargetHandlers.insert.many([
        {
          campaignLinkedInAudienceTargetGroupId: group.id,
          linkedInAudienceTargetId: target.id,
        },
      ]);
      if (prefabGroup[0] !== undefined) {
        await segmentLinkedInAudienceTargetGroupPrefabGroupTargetHandlers.insert.many(
          [
            {
              segmentLinkedInAudienceTargetGroupPrefabGroupId:
                prefabGroup[0].id,
              linkedinTargetAudienceId: target.id,
            },
          ],
        );
      }
    }

    if (data.job_titles) {
      const group =
        await campaignLinkedInAudienceTargetGroupHandlers.insert.one({
          campaignId: data.campaign_id,
          facetUrn: "urn:li:adTargetingFacet:titles",
          facetName: "Titles",
        });

      const prefabGroup =
        await segmentLinkedInAudienceTargetGroupPrefabGroupHandlers.insert.many(
          [
            {
              segmentLinkedInAudienceTargetGroupPrefabId: newPrefabId,
              facetUrn: "urn:li:adTargetingFacet:titles",
              facetName: "Titles",
            },
          ],
        );
      for (const jt of data.job_titles.facet_entites) {
        let target = await linkedInCampaignAudienceTargetHandlers.select.one(
          jt.urn,
        );
        if (!target) {
          target = await linkedInCampaignAudienceTargetHandlers.insert.one({
            linkedInFacetEntityUrn: jt.urn,
            linkedInFacetUrn: "urn:li:adTargetingFacet:titles",
            name: jt.name,
          });
        }
        await campaignLinkedInAudienceTargetGroupTargetHandlers.insert.many([
          {
            campaignLinkedInAudienceTargetGroupId: group.id,
            linkedInAudienceTargetId: target.id,
          },
        ]);

        if (prefabGroup[0] !== undefined) {
          await segmentLinkedInAudienceTargetGroupPrefabGroupTargetHandlers.insert.many(
            [
              {
                segmentLinkedInAudienceTargetGroupPrefabGroupId:
                  prefabGroup[0].id,
                linkedinTargetAudienceId: target.id,
              },
            ],
          );
        }
      }
    }
    campaignHanlders.update.audiencePopulated(data.campaign_id, true);
  }
}
