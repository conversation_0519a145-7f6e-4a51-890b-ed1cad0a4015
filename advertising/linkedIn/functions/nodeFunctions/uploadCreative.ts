import type { APIGatewayProxyEventV2 } from "aws-lambda";
import {
  DeleteObjectCommand,
  PutObjectCommand,
  S3Client,
} from "@aws-sdk/client-s3";

import { adCreativeHandlers } from "@kalos/database/handlers/adCreative";

const s3 = new S3Client({});
export async function upload(event: APIGatewayProxyEventV2) {
  if (!event.body || !event.queryStringParameters?.filename) {
    return {
      statusCode: 400,
      body: JSON.stringify({
        message: "Missing file or filename parameter" + event.body,
      }),
    };
  }

  const fileName = event.queryStringParameters.filename;
  const fileContent = Buffer.from(event.body, "base64");

  const key = `${crypto.randomUUID()}-${fileName}`;
  const command = new PutObjectCommand({
    Key: key,
    Bucket: "kalos-arthurdvorkin-advertisingcreativebucket-bhvzcbmc",
    Body: fileContent,
  });
  await s3.send(command);
  await adCreativeHandlers.insert.one({
    fileName,
    organizationId: 1,
    s3BucketKey: key,
  });

  return {
    statusCode: 200,
  };
}

export async function deleteFile(event: APIGatewayProxyEventV2) {
  if (!event.queryStringParameters?.id) {
    return {
      statusCode: 400,
      body: JSON.stringify({
        message: "Missing file or filename parameter" + event.body,
      }),
    };
  }
  const id = event.queryStringParameters.id;
  const adCreative = await adCreativeHandlers.select.one.byId(id);
  if (!adCreative) {
    return {
      statusCode: 400,
      body: JSON.stringify({
        message: "ID does not exist" + event.body,
      }),
    };
  }
  const command = new DeleteObjectCommand({
    Key: adCreative.s3BucketKey,
    Bucket: "kalos-arthurdvorkin-advertisingcreativebucket-bhvzcbmc",
  });
  await s3.send(command);
  await adCreativeHandlers.delete.byId(id);
  return {
    statusCode: 200,
  };
}
