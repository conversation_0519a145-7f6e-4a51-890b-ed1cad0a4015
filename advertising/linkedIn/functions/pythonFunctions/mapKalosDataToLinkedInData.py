import os
from typing import List, Optional, Iterable, Type, TypeVar
from langfuse import Langfuse 
from langfuse.client import ChatPromptClient
from pydantic import BaseModel
from openai import OpenAI
from openai.types.chat.chat_completion_message_param import ChatCompletionMessageParam
import json
import boto3
from pinecone.grpc import PineconeGRPC as Pinecone

sqs = boto3.client('sqs')



class NumberOfEmployees(BaseModel):
    low_bound: Optional[int] = None
    high_bound: Optional[int] = None

class AnnualRevenue(BaseModel):
    low_bound: Optional[int] = None
    high_bound: Optional[int] = None


class RecordBody(BaseModel):
    vertical: Optional[str] = None
    vertical_entites: List[str]
    number_of_employees: Optional[NumberOfEmployees] = None
    annual_revenue: Optional[AnnualRevenue]= None
    job_function: Optional[str] = None
    job_seniority: Optional[str] = None
    job_titles: Optional[List[str]] = None
    campaign_id: str
    titles: bool



class FacetEntity(BaseModel):
    urn: str
    name: str

class JobTitle(BaseModel):
    urn: str
    name: str
    isEncompassed: bool

class JobTitleList(BaseModel):
    job_titles: List[JobTitle]

class FacetEntityList(BaseModel):
    facet_entites: List[FacetEntity]

class Response(BaseModel):
    vertical: Optional[FacetEntity] 
    number_of_employees: Optional[FacetEntity] 
    annual_revenue: Optional[FacetEntity] 
    campaign_id: str
    job_titles: Optional[FacetEntityList]
    job_function: Optional[FacetEntity]

OPENAI_API_KEY = os.environ["OPENAI_API_KEY"]
LANGFUSE_ADVERTISING_PUBLIC_KEY = os.environ["LANGFUSE_ADVERTISING_PUBLIC_KEY"]
LANGFUSE_ADVERTISING_SECRET_KEY = os.environ["LANGFUSE_ADVERTISING_SECRET_KEY"]
if not OPENAI_API_KEY:
  raise Exception ("OPENAI API KEY MISSING")
if not LANGFUSE_ADVERTISING_PUBLIC_KEY:
  raise Exception("LANGFUSE ADVERTISING PUBLIC KEY MISSING")
if not LANGFUSE_ADVERTISING_SECRET_KEY:
  raise Exception("LANGFUSE ADVERTISING SECRET KEY MISSING")
langfuse = Langfuse(public_key=LANGFUSE_ADVERTISING_PUBLIC_KEY, secret_key=LANGFUSE_ADVERTISING_SECRET_KEY)
openai = OpenAI(api_key=OPENAI_API_KEY)
kalos_to_entity_prompt = langfuse.get_prompt("map-kalos-field-value-linkedIn-facet-entity", type="chat")
job_title_prompt = langfuse.get_prompt("map-job-function-seniority-and-titles-to-linkedIn-job-title-facet-entities", type="chat")
pc_api_key = os.environ["PINECONE_API_KEY"]
if not pc_api_key:
    raise Exception("NO PINECONE API KEY FOUND")
pc = Pinecone(api_key=pc_api_key)
index = pc.Index("linkedin-facet-entities")

def langufuse_compiled_messages_to_openai_chat_completion_message_params(langfuse_messages):
  messages: Iterable[ChatCompletionMessageParam] = []
  for each_message in langfuse_messages:
    if each_message["role"] == "user":
      messages.append({"role": "user", "content": each_message["content"]})
    elif each_message["role"] == "system":
      messages.append({"role": "system", "content": each_message["content"]})
    else:
      raise Exception("Unkown Chat Completion Message Param" + each_message["role"])
  return messages

T = TypeVar('T', bound=BaseModel)

def get_embedding(text: str, model="text-embedding-ada-002", **kwargs) -> List[float]:
    return get_embeddings([text], model=model)[0]


def get_embeddings(texts: List[str], model="text-embedding-ada-002", **kwargs) -> List[List[float]]:
    try:
      response = openai.embeddings.create(input=texts, model=model, **kwargs)
      return  list(map(lambda each_data: each_data.embedding, response.data))
    except:
       raise Exception("Failed to Embed")
       


def unstructered_to_structured_text(input_text: str, tool_object: Type[T]):
  prompt = langfuse.get_prompt("save-data-to-database", type="chat", cache_ttl_seconds=1000)
  json_schema = json.dumps(tool_object.model_json_schema())
  compiled_prompt = prompt.compile(text=input_text, json_schema=json_schema)
  messages = langufuse_compiled_messages_to_openai_chat_completion_message_params(compiled_prompt)
  res = openai.chat.completions.create(
    model="gpt-4o",
    messages=messages,
    temperature=0,
    tools= [
      {
        "type": "function",
        "function": {
          "name": "submit_data",
          "description": "Submit structured data",
          "parameters": tool_object.model_json_schema()
        }
      }
    ],
    tool_choice={ "type": "function", "function": { "name": "submit_data"}}
  )
  tools = res.choices[0].message.tool_calls
  if (tools is None or len(tools) == 0 ):
    raise Exception("No tools found")
  tool = tools[0]
  try:
    tlz = tool_object.model_validate(json.loads(tool.function.arguments))
    return tlz
  except:
    print(tool)
    raise Exception("Invalid tool schema")


def get_openai_content_response(prompt_varibles: dict, 
                                   prompt_template: ChatPromptClient,
                                   model: str = "gpt-4-turbo", 
                                   temperature: float = 0.7,
                                   top_p: float = 1,
                                ): 
  compiled_prompt = prompt_template.compile(**prompt_varibles)
  messages = langufuse_compiled_messages_to_openai_chat_completion_message_params(compiled_prompt)
  res = openai.chat.completions.create(
    model=model,
    messages=messages,
    temperature=temperature,
    top_p=top_p
  )
  content = res.choices[0].message
  return content.content


def map_to_linkedIn_facet_entity(field: str, facet_urn: str, value: str):
  value_as_emb = get_embedding(value)
  vector_q_res = index.query(
    vector=value_as_emb,
    filter={
        "facet_urn": {"$eq": facet_urn}
    },
    top_k=25,
    include_metadata=True 
  )
  matches = vector_q_res["matches"]
  entites_as_str: str = "[\n" 
  for match in matches:
      urn = match["metadata"]["entity_urn"]
      name = match["metadata"]["name"]
      json_obj_for_str = json.dumps({"name": name, "urn": urn})
      entites_as_str += f'{json_obj_for_str}\n'
  entites_as_str += "]\n"

  res = get_openai_content_response(prompt_varibles={"field": field, "facets": entites_as_str, "value": value }, prompt_template=kalos_to_entity_prompt, model="gpt-4o", temperature=0)
  if res is None:
    raise Exception("NULL OPENAI RESPONSE")
  return unstructered_to_structured_text(input_text=res, tool_object=FacetEntity)

def map_function_to_title(titles: List[str], function: str, seniority: str):
  value_as_emb = get_embedding(f'{function} {seniority} {" ".join(titles)}')
  vector_q_res = index.query(
    vector=value_as_emb,
    filter={
        "facet_urn": {"$eq": "urn:li:adTargetingFacet:titles"}
    },
    top_k=50,
    include_metadata=True 
  )
  matches = vector_q_res["matches"]
  entites_as_str: str = "[\n" 
  for match in matches:
      urn = match["metadata"]["entity_urn"]
      name = match["metadata"]["name"]
      json_obj_for_str = json.dumps({"name": name, "urn": urn})
      entites_as_str += f'{json_obj_for_str}\n'
  entites_as_str += "]\n"

  res = get_openai_content_response(prompt_varibles={"crm_job_titles": "\n".join(titles), "function": function, "seniority": seniority, "facets": entites_as_str }, prompt_template=job_title_prompt, model="gpt-4o", temperature=0)
  if res is None:
    raise Exception("NULL OPENAI RESPONSE")
  structured_data = unstructered_to_structured_text(input_text=res, tool_object=JobTitleList)
  res_array: List[FacetEntity] = []
  for each_entity in structured_data.job_titles:
    if each_entity.isEncompassed:
      res_array.append(FacetEntity(urn=each_entity.urn, name=each_entity.name))
  return FacetEntityList(facet_entites=res_array) 




def map_kalos_to_linkedIn(data: RecordBody):
  # Get Vertical Entity
  vertical_entity: FacetEntity | None = None
  if data.vertical is not None:
    vertical_entity = map_to_linkedIn_facet_entity(field="Vertical", facet_urn="urn:li:adTargetingFacet:industries", value=data.vertical)

  # Get Number of Employees Entity
  numberOfEmployeesEntity: FacetEntity | None = None
  if data.number_of_employees is not None:
    numberOfEmployeesEntity =  map_to_linkedIn_facet_entity(field="Number of Employees", facet_urn="urn:li:adTargetingFacet:staffCountRanges", value=data.model_dump_json())

  # Get Annual Revenue Entity
  annualRevenueEntity: FacetEntity | None = None
  if data.annual_revenue is not None:
    annualRevenueEntity = map_to_linkedIn_facet_entity(field="Annual Revenue", facet_urn="urn:li:adTargetingFacet:revenue",  value=data.model_dump_json())

  # Get Job Title Entites
  job_titles_entities: FacetEntityList | None = None
  if data.job_function is not None and data.job_titles is not None and data.job_seniority is not None and data.titles is True:
      job_titles_entities = map_function_to_title(data.job_titles, data.job_function, data.job_seniority)

  # Get Job Function Entity
  job_function_entity: FacetEntity | None = None
  if data.job_function and data.titles is False:
    job_function_entity = map_to_linkedIn_facet_entity(field="Job Function", facet_urn="urn:li:adTargetingFacet:jobFunctions",  value=data.model_dump_json())


  return Response(vertical=vertical_entity, number_of_employees=numberOfEmployeesEntity, annual_revenue=annualRevenueEntity, campaign_id=data.campaign_id, job_titles=job_titles_entities, job_function=job_function_entity)



  
 
def handler(event, context):
    for record in event["Records"]:
      body = record["body"]
      if body is None:
          raise Exception("NO BODY IN SQS MESSAGE")
      validated_body = RecordBody.model_validate_json(body)
      res = map_kalos_to_linkedIn(validated_body)
      print(res.model_dump_json())
      sqs.send_message(MessageBody=res.model_dump_json(), QueueUrl=os.environ["Q_URL"])
      return {
        "statusCode": 200,
        "body": res.model_dump_json()
      }


