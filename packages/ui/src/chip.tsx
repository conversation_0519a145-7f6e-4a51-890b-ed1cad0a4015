import * as React from "react";

import { cn } from "./index";

export interface ChipProps extends React.HTMLAttributes<HTMLDivElement> {
    label: string;
    onRemove?: () => void;
    disabled?: boolean;
    removable?: boolean;
}

const Chip = React.forwardRef<HTMLDivElement, ChipProps>(
    ({ className, label, onRemove, disabled, removable = true, ...props }, ref) => {
        return (
            <div
                className={cn(
                    "inline-flex items-center justify-start rounded-md border border-gray-200 bg-white px-2 py-1 gap-1",
                    className
                )}
                ref={ref}
                {...props}
            >
                <span className="text-xs">{label}</span>
                {removable && onRemove && (
                    <button
                        type="button"
                        className={cn(
                            "ml-1 flex h-4 w-4 items-center justify-center rounded-sm transition-colors hover:bg-gray-100 focus:outline-none focus:ring-1 focus:ring-gray-400",
                            disabled && "cursor-not-allowed opacity-50"
                        )}
                        disabled={disabled}
                        onClick={onRemove}
                    >
                        <svg
                            className="h-3 w-3"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                            strokeWidth={2}
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                d="M6 18L18 6M6 6l12 12"
                            />
                        </svg>
                    </button>
                )}
            </div>
        );
    }
);

Chip.displayName = "Chip";

export { Chip }; 