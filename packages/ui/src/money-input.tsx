"use client";

import type { UseFormReturn } from "react-hook-form";
import * as React from "react";

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "./form";
import { cn } from "./index";
import { Input } from "./input";

const numberFormatter = new Intl.NumberFormat("en-US", {
  style: "decimal",
  minimumFractionDigits: 0,
  maximumFractionDigits: 0,
});

export interface MoneyInputProps {
  form: UseFormReturn<any>;
  name: string;
  label?: string;
  placeholder?: string;
  description?: string;
  className?: string;
}

/**
 * Specialized input component for monetary values
 *
 * Displays formatted numbers with commas for readability while storing
 * numeric values in the form state. Includes a visual dollar sign prefix.
 *
 * The component handles the conversion between the formatted string display
 * (with commas) and the numeric value stored in the form state. When the form
 * is submitted, the field value will be a number type.
 *
 * @param props - Component props
 * @param ref - Forwarded ref to the component
 * @returns A form field with monetary input capabilities
 */
const MoneyInput = React.forwardRef<HTMLDivElement, MoneyInputProps>(
  ({ form, name, label, placeholder, description, className }, ref) => {
const initialValue = form.getValues()[name]
  ? numberFormatter.format(form.getValues()[name])
  : "";

    /**
     * Formats input value, stripping non-digits and adding commas
     */
const [value, setValue] = React.useReducer((_: any, next: string) => {
  const digits = next.replace(/\D/g, "");
  return digits ? numberFormatter.format(Number(digits)) : "";
}, initialValue);

    /**
     * Converts formatted string value to number and updates the form
     */
    function handleChange(realChangeFn: Function, formattedValue: string) {
      const digits = formattedValue.replace(/\D/g, "");
      const realValue = Number(digits);
      realChangeFn(realValue);
    }

    return (
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => {
          field.value = value;
          const _change = field.onChange;

          return (
            <FormItem ref={ref} className={cn("", className)}>
              {label && <FormLabel>{label}</FormLabel>}
              <FormControl>
                <div className="relative flex w-full items-center">
                  <span className="absolute left-3 text-sm text-gray-500">
                    $
                  </span>
                  <Input
                    placeholder={placeholder}
                    type="text"
                    {...field}
                    onChange={(ev) => {
                      const inputElement = ev.target as HTMLInputElement;
                      setValue(inputElement.value);
                      handleChange(_change, inputElement.value);
                    }}
                    value={value}
                    className="bg-background pl-6"
                  />
                </div>
              </FormControl>
              {description && <FormMessage>{description}</FormMessage>}
              <FormMessage />
            </FormItem>
          );
        }}
      />
    );
  },
);

MoneyInput.displayName = "MoneyInput";

export { MoneyInput };
