import { ErrorNotificationService } from "../../../backend/src/modules/shared/errorNotification.service";

export interface LinkedInErrorContext {
  organizationId?: number;
  endpoint: string;
  method?: string;
  attempt?: number;
  metadata?: Record<string, any>;
}

export class LinkedInErrorHandler {
  private errorNotificationService: ErrorNotificationService;

  constructor() {
    this.errorNotificationService = new ErrorNotificationService();
  }

  async handleApiError(
    error: any,
    context: LinkedInErrorContext,
    statusCode: number
  ): Promise<void> {
    try {
      // Handle specific status codes with more detailed notifications
      if (statusCode === 429) {
        await this.errorNotificationService.notifyLinkedInRateLimitExceeded(
          context.organizationId,
          context.endpoint,
          statusCode,
          {
            ...context.metadata,
            method: context.method,
            attempt: context.attempt,
          }
        );
      } else {
        // Handle general API failures
        await this.errorNotificationService.notifyLinkedInApiFailure(
          context.organizationId,
          context.endpoint,
          error,
          statusCode,
          {
            ...context.metadata,
            method: context.method,
            attempt: context.attempt,
          }
        );
      }
    } catch (notificationError) {
      // Log notification errors but don't throw to avoid breaking the main flow
      console.error('[LinkedInErrorHandler] Failed to store error notification:', notificationError);
    }
  }

  async handleAuthError(
    error: any,
    context: LinkedInErrorContext,
    statusCode?: number
  ): Promise<void> {
    try {
      let errorDetails = "Authentication failed with unknown error";
      let extractedStatusCode = statusCode || 401; // Default to 401 if not provided
      
      if (typeof error === "string") {
        errorDetails = error;
      } else if (error?.message) {
        errorDetails = error.message;
      } else if (error?.error_description) {
        errorDetails = error.error_description;
      } else if (error?.errorMessage) {
        errorDetails = error.errorMessage;
      } else if (error?.error) {
        errorDetails = error.error;
      }

      // Try to extract status code from error object if not explicitly provided
      if (!statusCode && error?.status) {
        extractedStatusCode = error.status;
      } else if (!statusCode && error?.statusCode) {
        extractedStatusCode = error.statusCode;
      }

      await this.errorNotificationService.notifyLinkedInAuthFailure(
        context.organizationId,
        "auth failure",
        errorDetails,
        extractedStatusCode,
        {
          ...context.metadata,
          endpoint: context.endpoint,
        }
      );
    } catch (notificationError) {
      console.error('[LinkedInErrorHandler] Failed to store auth error notification:', notificationError);
    }
  }

  async handleOAuthError(
    error: any,
    context: {
      organizationId?: number;
      stage: "authorization_code_exchange" | "client_initialization";
      metadata?: Record<string, any>;
    },
    statusCode?: number
  ): Promise<void> {
    try {
      let errorDetails = "OAuth flow failed with unknown error";
      let extractedStatusCode = statusCode || 401; // Default to 401 if not provided
      
      if (typeof error === "string") {
        errorDetails = error;
      } else if (error?.error_description) {
        errorDetails = error.error_description;
      } else if (error?.message) {
        errorDetails = error.message;
      }

      // Try to extract status code from error object if not explicitly provided
      if (!statusCode && error?.liStatus) {
        extractedStatusCode = error.liStatus;
      } else if (!statusCode && error?.status) {
        extractedStatusCode = error.status;
      } else if (!statusCode && error?.statusCode) {
        extractedStatusCode = error.statusCode;
      } else if (!statusCode && context.metadata?.httpStatus) {
        extractedStatusCode = context.metadata.httpStatus;
      }

      await this.errorNotificationService.notifyLinkedInAuthFailure(
        context.organizationId,
        `OAuth ${context.stage}`,
        errorDetails,
        extractedStatusCode,
        {
          ...context.metadata,
          oauthStage: context.stage,
        }
      );
    } catch (notificationError) {
      console.error('[LinkedInErrorHandler] Failed to store OAuth error notification:', notificationError);
    }
  }
}

// Singleton instance for easy import across the application
export const linkedInErrorHandler = new LinkedInErrorHandler(); 