import { baseAdvertisingController } from "../../../../../backend/src/modules/advertising/interfaceAdapters/controllers/_base_.controller";

// Use the controller for all operations
export const linkedInLeadFormRouter = {
  // Get a lead form by ID
  getById: baseAdvertisingController.linkedInLeadForm.getById,

  // Get a lead form by URN
  getByUrn: baseAdvertisingController.linkedInLeadForm.getByUrn,

  // Create a LinkedIn lead form
  create: baseAdvertisingController.linkedInLeadForm.create,

  // Get leads by form ID
  getLeadsByFormId:
    baseAdvertisingController.linkedInLeadForm.leads.getByFormId,

  // Get a lead by ID
  getLeadById: baseAdvertisingController.linkedInLeadForm.leads.getById,

  // Create a lead manually
  createLead: baseAdvertisingController.linkedInLeadForm.leads.create,
};
