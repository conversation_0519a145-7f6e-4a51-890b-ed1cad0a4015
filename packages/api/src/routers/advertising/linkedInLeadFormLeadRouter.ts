import { baseAdvertisingController } from "../../../../../backend/src/modules/advertising/interfaceAdapters/controllers/_base_.controller";

// Use the controller for all operations
export const linkedInLeadFormLeadRouter = {
  // Get leads by form ID
  getByFormId: baseAdvertisingController.linkedInLeadFormLead.getByFormId,

  // Get a lead by ID
  getById: baseAdvertisingController.linkedInLeadFormLead.getById,

  // Create a lead manually
  create: baseAdvertisingController.linkedInLeadFormLead.create,

  // Get all leads and opportunities by account ID
  getAllLeadsAndOpportunitiesByAccountId:
    baseAdvertisingController.linkedInLeadFormLead
      .getAllLeadsAndOpportunitiesByAccountId,

  // Get all leads and pipelines by account ID
  getLeadsAndPipelinesStatsByAccountId:
    baseAdvertisingController.linkedInLeadFormLead
      .getLeadsAndPipelinesStatsByAccountId,
};
